//package com.laoshu198838.service;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertFalse;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.never;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import java.lang.reflect.Field;
//import java.lang.reflect.Method;
//import java.time.DayOfWeek;
//import java.time.LocalDate;
//import java.time.ZoneId;
//import java.time.temporal.TemporalAdjusters;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Date;
//import java.util.List;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.Spy;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
//
//import jakarta.persistence.EntityManager;
//import jakarta.persistence.TypedQuery;
//import jakarta.persistence.criteria.CriteriaBuilder;
//import jakarta.persistence.criteria.CriteriaQuery;
//import jakarta.persistence.criteria.Root;
//
///**
// * 非诉讼债权逾期年限更新服务测试类
// * 测试NonLitigationOverdueUpdateService和NonLitigationOverdueUpdateTransactionalService的功能
// * <AUTHOR>
// */
//@ExtendWith(MockitoExtension.class)
//public class NonLitigationOverdueUpdateServiceTest {
//
//    @Mock
//    private EntityManager entityManager;
//
//    @Mock
//    private CriteriaBuilder criteriaBuilder;
//
//    @Mock
//    private CriteriaQuery<NonLitigationClaim> criteriaQuery;
//
//    @Mock
//    private Root<NonLitigationClaim> root;
//
//    @Mock
//    private TypedQuery<NonLitigationClaim> typedQuery;
//
//    @Spy
//    @InjectMocks
//    private NonLitigationOverdueUpdateTransactionalService transactionalService;
//
//    // 无需使用@Spy注解，改为在setup中手动创建实例
//    private NonLitigationOverdueUpdateService updateService;
//
//    @BeforeEach
//    public void setup() {
//        // 设置主类的依赖
//        updateService = new NonLitigationOverdueUpdateService(transactionalService);
//
//        // 使用反射设置EntityManager
//        ReflectionTestUtils.setField(transactionalService, "entityManager", entityManager);
//    }
//
//    /**
//     * 测试isFirstWorkingDayOfMonth方法 - 当天是工作日并且是本月第一个工作日
//     */
//    @Test
//    @DisplayName("测试isFirstWorkingDayOfMonth - 当天是第一个工作日")
//    public void testIsFirstWorkingDayOfMonthWhenItIs() throws Exception {
//        // 获取私有方法
//        Method method = NonLitigationOverdueUpdateService.class.getDeclaredMethod("isFirstWorkingDayOfMonth", LocalDate.class);
//        method.setAccessible(true);
//
//        // 找到一个确定是当月第一个工作日的日期
//        LocalDate firstDayOfMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
//
//        // 找到本月的第一个工作日
//        LocalDate firstWorkingDay = firstDayOfMonth;
//        while (firstWorkingDay.getDayOfWeek() == DayOfWeek.SATURDAY ||
//               firstWorkingDay.getDayOfWeek() == DayOfWeek.SUNDAY) {
//            firstWorkingDay = firstWorkingDay.plusDays(1);
//        }
//
//        // 调用私有方法
//        boolean result = (boolean) method.invoke(updateService, firstWorkingDay);
//        assertTrue(result, "本月第一个工作日应该被识别为第一个工作日");
//    }
//
//    /**
//     * 测试isFirstWorkingDayOfMonth方法 - 当天不是本月第一个工作日
//     */
//    @Test
//    @DisplayName("测试isFirstWorkingDayOfMonth - 当天不是第一个工作日")
//    public void testIsFirstWorkingDayOfMonthWhenItIsNot() throws Exception {
//        // 获取私有方法
//        Method method = NonLitigationOverdueUpdateService.class.getDeclaredMethod("isFirstWorkingDayOfMonth", LocalDate.class);
//        method.setAccessible(true);
//
//        // 获取本月第一个工作日
//        LocalDate firstDayOfMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
//
//        // 找到本月的第一个工作日
//        LocalDate firstWorkingDay = firstDayOfMonth;
//        while (firstWorkingDay.getDayOfWeek() == DayOfWeek.SATURDAY ||
//               firstWorkingDay.getDayOfWeek() == DayOfWeek.SUNDAY) {
//            firstWorkingDay = firstWorkingDay.plusDays(1);
//        }
//
//        // 测试第二个工作日
//        LocalDate secondWorkingDay = firstWorkingDay.plusDays(1);
//        // 如果是周末，加到下周一
//        while (secondWorkingDay.getDayOfWeek() == DayOfWeek.SATURDAY ||
//               secondWorkingDay.getDayOfWeek() == DayOfWeek.SUNDAY) {
//            secondWorkingDay = secondWorkingDay.plusDays(1);
//        }
//
//        // 调用私有方法
//        boolean result = (boolean) method.invoke(updateService, secondWorkingDay);
//        assertFalse(result, "第二个工作日不应该被识别为本月第一个工作日");
//    }
//
//    /**
//     * 测试scheduleOverdueYearUpdate方法 - 当天是第一个工作日
//     */
//    @Test
//    @DisplayName("测试scheduleOverdueYearUpdate - 当天是第一个工作日")
//    public void testScheduleOverdueYearUpdateWhenFirstWorkingDay() throws Exception {
//        // 为了不依赖NonLitigationOverdueUpdateService的实现细节，我们创建一个特殊的测试类
//        NonLitigationOverdueUpdateService testService = new NonLitigationOverdueUpdateService(transactionalService) {
//            @Override
//            public void scheduleOverdueYearUpdate() {
//                // 直接执行逻辑，跳过日期检查
//                try {
//                    transactionalService.updateOverdueYears();
//                } catch (Exception e) {
//                    // 处理异常
//                }
//            }
//        };
//
//        // 配置Mock
//        when(entityManager.getCriteriaBuilder()).thenReturn(criteriaBuilder);
//        when(criteriaBuilder.createQuery(NonLitigationClaim.class)).thenReturn(criteriaQuery);
//        when(criteriaQuery.from(NonLitigationClaim.class)).thenReturn(root);
//        when(criteriaQuery.select(root)).thenReturn(criteriaQuery);
//        when(entityManager.createQuery(criteriaQuery)).thenReturn(typedQuery);
//        when(typedQuery.getResultList()).thenReturn(new ArrayList<>());
//
//        // 模拟transactionalService.updateOverdueYears()返回5
//        when(transactionalService.updateOverdueYears()).thenReturn(5);
//
//        // 执行测试
//        testService.scheduleOverdueYearUpdate();
//
//        // 验证调用
//        verify(transactionalService, times(1)).updateOverdueYears();
//    }
//
//    /**
//     * 测试计算逾期年限分类方法
//     */
//    @Test
//    @DisplayName("测试calculateOverdueYearCategory方法")
//    public void testCalculateOverdueYearCategory() throws Exception {
//        // 获取私有方法
//        Method method = NonLitigationOverdueUpdateTransactionalService.class.getDeclaredMethod(
//                "calculateOverdueYearCategory", Date.class, Date.class);
//        method.setAccessible(true);
//
//        // 当前日期
//        Date currentDate = new Date();
//        LocalDate now = LocalDate.now();
//
//        // 测试不同逾期情况
//        // 1. 未到期
//        Date futureDate = Date.from(now.plusMonths(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
//        String result1 = (String) method.invoke(transactionalService, futureDate, currentDate);
//        assertEquals("", result1, "未到期应返回空字符串");
//
//        // 2. 逾期不到1年
//        Date lessThanOneYear = Date.from(now.minusMonths(6).atStartOfDay(ZoneId.systemDefault()).toInstant());
//        String result2 = (String) method.invoke(transactionalService, lessThanOneYear, currentDate);
//        assertEquals("1年以内", result2, "逾期不到1年应返回'1年以内'");
//
//        // 3. 逾期1-5年
//        Date threeYearsAgo = Date.from(now.minusYears(3).atStartOfDay(ZoneId.systemDefault()).toInstant());
//        String result3 = (String) method.invoke(transactionalService, threeYearsAgo, currentDate);
//        assertEquals("1-5年", result3, "逾期3年应返回'1-5年'");
//
//        // 4. 逾期5年以上
//        Date sixYearsAgo = Date.from(now.minusYears(6).atStartOfDay(ZoneId.systemDefault()).toInstant());
//        String result4 = (String) method.invoke(transactionalService, sixYearsAgo, currentDate);
//        assertEquals("5年以上", result4, "逾期6年应返回'5年以上'");
//    }
//
//    /**
//     * 测试updateOverdueYears方法 - 有更新的情况
//     */
//    @Test
//    @DisplayName("测试updateOverdueYears - 有更新")
//    public void testUpdateOverdueYearsWithUpdates() {
//        // 准备测试数据
//        NonLitigationClaim claim1 = new NonLitigationClaim();
//        claim1.setId(1);
//        claim1.setDebtor("测试债务人1");
//        claim1.setDueDate(Date.from(LocalDate.now().minusYears(2).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim1.setOverdueYear("1年以内"); // 原分类不正确，应该是"1-5年"
//
//        NonLitigationClaim claim2 = new NonLitigationClaim();
//        claim2.setId(2);
//        claim2.setDebtor("测试债务人2");
//        claim2.setDueDate(Date.from(LocalDate.now().minusYears(6).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim2.setOverdueYear("1-5年"); // 原分类不正确，应该是"5年以上"
//
//        NonLitigationClaim claim3 = new NonLitigationClaim();
//        claim3.setId(3);
//        claim3.setDebtor("测试债务人3");
//        claim3.setDueDate(Date.from(LocalDate.now().minusMonths(6).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim3.setOverdueYear("1年以内"); // 分类正确，不需要更新
//
//        List<NonLitigationClaim> claims = Arrays.asList(claim1, claim2, claim3);
//
//        // 配置Mock
//        when(entityManager.getCriteriaBuilder()).thenReturn(criteriaBuilder);
//        when(criteriaBuilder.createQuery(NonLitigationClaim.class)).thenReturn(criteriaQuery);
//        when(criteriaQuery.from(NonLitigationClaim.class)).thenReturn(root);
//        when(criteriaQuery.select(root)).thenReturn(criteriaQuery);
//        when(entityManager.createQuery(criteriaQuery)).thenReturn(typedQuery);
//        when(typedQuery.getResultList()).thenReturn(claims);
//
//        // 执行测试
//        int updatedCount = transactionalService.updateOverdueYears();
//
//        // 验证结果
//        assertEquals(2, updatedCount, "应该有2条记录被更新");
//        verify(entityManager, times(1)).merge(claim1);
//        verify(entityManager, times(1)).merge(claim2);
//        verify(entityManager, never()).merge(claim3);
//        assertEquals("1-5年", claim1.getOverdueYear(), "债务人1的逾期年限应该更新为'1-5年'");
//        assertEquals("5年以上", claim2.getOverdueYear(), "债务人2的逾期年限应该更新为'5年以上'");
//        assertEquals("1年以内", claim3.getOverdueYear(), "债务人3的逾期年限应该保持不变");
//    }
//
//    /**
//     * 测试updateOverdueYears方法 - 处理空到期日的情况
//     */
//    @Test
//    @DisplayName("测试updateOverdueYears - 处理空到期日")
//    public void testUpdateOverdueYearsWithNullDueDate() {
//        // 准备测试数据 - 到期日为null
//        NonLitigationClaim claimWithNullDueDate = new NonLitigationClaim();
//        claimWithNullDueDate.setId(4);
//        claimWithNullDueDate.setDebtor("测试债务人4");
//        claimWithNullDueDate.setDueDate(null);
//        claimWithNullDueDate.setOverdueYear("");
//
//        List<NonLitigationClaim> claims = Arrays.asList(claimWithNullDueDate);
//
//        // 配置Mock
//        when(entityManager.getCriteriaBuilder()).thenReturn(criteriaBuilder);
//        when(criteriaBuilder.createQuery(NonLitigationClaim.class)).thenReturn(criteriaQuery);
//        when(criteriaQuery.from(NonLitigationClaim.class)).thenReturn(root);
//        when(criteriaQuery.select(root)).thenReturn(criteriaQuery);
//        when(entityManager.createQuery(criteriaQuery)).thenReturn(typedQuery);
//        when(typedQuery.getResultList()).thenReturn(claims);
//
//        // 执行测试
//        int updatedCount = transactionalService.updateOverdueYears();
//
//        // 验证结果
//        assertEquals(0, updatedCount, "不应该有记录被更新");
//        verify(entityManager, never()).merge(any(NonLitigationClaim.class));
//        assertEquals("", claimWithNullDueDate.getOverdueYear(), "逾期年限应该保持不变");
//    }
//
//    /**
//     * 测试手动触发更新功能
//     */
//    @Test
//    @DisplayName("手动触发整个更新流程测试")
//    public void testManualTriggerUpdate() {
//        // 准备测试数据
//        List<NonLitigationClaim> claims = createTestData();
//
//        // 配置Mock
//        when(entityManager.getCriteriaBuilder()).thenReturn(criteriaBuilder);
//        when(criteriaBuilder.createQuery(NonLitigationClaim.class)).thenReturn(criteriaQuery);
//        when(criteriaQuery.from(NonLitigationClaim.class)).thenReturn(root);
//        when(criteriaQuery.select(root)).thenReturn(criteriaQuery);
//        when(entityManager.createQuery(criteriaQuery)).thenReturn(typedQuery);
//        when(typedQuery.getResultList()).thenReturn(claims);
//
//        // 执行测试 - 直接调用更新方法
//        int updatedCount = transactionalService.updateOverdueYears();
//
//        // 验证结果
//        assertEquals(3, updatedCount, "应该有3条记录被更新");
//        verify(entityManager, times(3)).merge(any(NonLitigationClaim.class));
//    }
//
//    /**
//     * 创建测试数据
//     */
//    private List<NonLitigationClaim> createTestData() {
//        List<NonLitigationClaim> testData = new ArrayList<>();
//        LocalDate now = LocalDate.now();
//
//        // 创建测试记录1 - 需要从"" -> "1年以内"
//        NonLitigationClaim claim1 = new NonLitigationClaim();
//        claim1.setId(101);
//        claim1.setDebtor("测试公司A");
//        claim1.setCreditor("测试债权人X");
//        claim1.setDueDate(Date.from(now.minusMonths(6).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim1.setOverdueYear("");
//        testData.add(claim1);
//
//        // 创建测试记录2 - 需要从"1年以内" -> "1-5年"
//        NonLitigationClaim claim2 = new NonLitigationClaim();
//        claim2.setId(102);
//        claim2.setDebtor("测试公司B");
//        claim2.setCreditor("测试债权人Y");
//        claim2.setDueDate(Date.from(now.minusYears(2).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim2.setOverdueYear("1年以内");
//        testData.add(claim2);
//
//        // 创建测试记录3 - 需要从"1-5年" -> "5年以上"
//        NonLitigationClaim claim3 = new NonLitigationClaim();
//        claim3.setId(103);
//        claim3.setDebtor("测试公司C");
//        claim3.setCreditor("测试债权人Z");
//        claim3.setDueDate(Date.from(now.minusYears(6).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim3.setOverdueYear("1-5年");
//        testData.add(claim3);
//
//        // 创建测试记录4 - 分类已正确，不需要更新
//        NonLitigationClaim claim4 = new NonLitigationClaim();
//        claim4.setId(104);
//        claim4.setDebtor("测试公司D");
//        claim4.setCreditor("测试债权人X");
//        claim4.setDueDate(Date.from(now.minusYears(6).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim4.setOverdueYear("5年以上");
//        testData.add(claim4);
//
//        // 创建测试记录5 - 未到期，不需要更新
//        NonLitigationClaim claim5 = new NonLitigationClaim();
//        claim5.setId(105);
//        claim5.setDebtor("测试公司E");
//        claim5.setCreditor("测试债权人Y");
//        claim5.setDueDate(Date.from(now.plusMonths(3).atStartOfDay(ZoneId.systemDefault()).toInstant()));
//        claim5.setOverdueYear("");
//        testData.add(claim5);
//
//        return testData;
//    }
//}