package com.laoshu198838.service;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.List;

/**
 * 本年度累计回收金额更新服务
 * 负责更新减值准备表、诉讼表和非诉讼表中每项债权的本年度累计回收金额
 * 计算方法：本年度累计回收 = 前面月份的本月处置债权合计数
 *
 * <AUTHOR>
 */
@Service
public class AnnualRecoveryService {

    private static final Logger logger = LoggerFactory.getLogger(AnnualRecoveryService.class);

    // SQL查询参数常量
    private static final String PARAM_CREDITOR = "creditor";
    private static final String PARAM_DEBTOR = "debtor";
    private static final String PARAM_YEAR = "year";
    private static final String PARAM_MONTH = "month";
    private static final String PARAM_RECOVERY_AMOUNT = "recoveryAmount";
    private static final String PARAM_IS_LITIGATION = "isLitigation";

    // 实体属性常量
    private static final String PROP_CREDITOR = "id.creditor";
    private static final String PROP_DEBTOR = "id.debtor";

    // 日志消息常量
    private static final String LOG_INCOMPLETE_DATA = "记录数据不完整，跳过处理: 债权人={}, 债务人={}";

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 构造函数
     */
    public AnnualRecoveryService() {
        logger.info("AnnualRecoveryService 初始化完成");
    }

    /**
     * 更新减值准备表的本年度累计回收金额
     * 计算方法：本年度累计回收 = 前面月份的本月处置债权合计数
     *
     * @return 更新的记录数量
     */
    @Transactional(noRollbackFor = Exception.class)
    public int updateImpairmentAnnualRecovery() {
        logger.info("开始执行减值准备表本年度累计回收金额更新");

        YearMonth currentYearMonth = YearMonth.now();
        int currentYear = currentYearMonth.getYear();
        int currentMonth = currentYearMonth.getMonthValue();

        int updatedRecords = 0;

        try {
            // 只处理2025年及以后的债权
            if (currentYear < 2025) {
                logger.info("当前年份为{}，小于2025年，不处理减值准备表的本年度累计回收金额", currentYear);
                return 0;
            }

            // 直接执行原生SQL，对有本月处置债权的记录进行更新
            // 这种方式可以绕过JPA实体映射可能导致的问题
            logger.info("开始直接查询2025年及以后有本月处置债权的记录");

            // 1. 首先查询有本月处置债权的记录，仅包括2025年及以后的记录
            String findSql = "SELECT 债权人, 债务人, 年份, 月份, 本月处置债权, 是否涉诉 " +
                             "FROM 减值准备表 " +
                             "WHERE 本月处置债权 IS NOT NULL AND 本月处置债权 > 0 " +
                             "AND 年份 >= 2025";

            logger.info("执行查询SQL: {}", findSql);
            List<?> records = entityManager.createNativeQuery(findSql).getResultList();
            logger.info("找到 {} 条有本月处置债权的记录需要更新本年度累计回收金额", records.size());

            // 2. 处理每条有本月处置债权的记录
            for (Object record : records) {
                try {
                    Object[] fields = (Object[]) record;
                    String creditor = (String) fields[0]; // 债权人
                    String debtor = (String) fields[1];   // 债务人
                    Integer year = (Integer) fields[2];    // 年份
                    Integer month = (Integer) fields[3];   // 月份
                    BigDecimal disposalAmount = (BigDecimal) fields[4]; // 本月处置债权
                    String isLitigation = (String) fields[5]; // 是否涉诉

//                    logger.info("处理记录: 债权人={}, 债务人={}, 年={}, 月={}, 本月处置债权={}",
//                              creditor, debtor, year, month, disposalAmount);

                    // 3. 计算该债权人和债务人同年份累计的处置金额（按是否涉诉状态分别计算）
                    String recoverySql = "SELECT SUM(IFNULL(本月处置债权, 0)) FROM 减值准备表 " +
                                         "WHERE 债权人 = :" + PARAM_CREDITOR + " AND 债务人 = :" + PARAM_DEBTOR + " " +
                                         "AND 年份 = :" + PARAM_YEAR + " AND 月份 <= :" + PARAM_MONTH + " " +
                                         "AND 是否涉诉 = :" + PARAM_IS_LITIGATION;

                    Query recoveryQuery = entityManager.createNativeQuery(recoverySql);
                    recoveryQuery.setParameter(PARAM_CREDITOR, creditor);
                    recoveryQuery.setParameter(PARAM_DEBTOR, debtor);
                    recoveryQuery.setParameter(PARAM_YEAR, year);
                    recoveryQuery.setParameter(PARAM_MONTH, month);
                    recoveryQuery.setParameter(PARAM_IS_LITIGATION, isLitigation);

                    Object result = recoveryQuery.getSingleResult();
                    BigDecimal totalRecovery = (result != null) ?
                                               new BigDecimal(result.toString()) : BigDecimal.ZERO;

//                    logger.info("计算得到的本年度累计回收金额: 债权人={}, 债务人={}, 年={}, 月={}, 金额={}",
//                              creditor, debtor, year, month, totalRecovery);

                    // 4. 更新减值准备表的本年度累计回收金额（按是否涉诉状态精确匹配）
                    String updateSql = "UPDATE 减值准备表 SET 本年度累计回收 = :" + PARAM_RECOVERY_AMOUNT + " " +
                                       "WHERE 债权人 = :" + PARAM_CREDITOR + " AND 债务人 = :" + PARAM_DEBTOR + " " +
                                       "AND 年份 = :" + PARAM_YEAR + " AND 月份 = :" + PARAM_MONTH + " " +
                                       "AND 是否涉诉 = :" + PARAM_IS_LITIGATION;

                    Query updateQuery = entityManager.createNativeQuery(updateSql);
                    updateQuery.setParameter(PARAM_RECOVERY_AMOUNT, totalRecovery);
                    updateQuery.setParameter(PARAM_CREDITOR, creditor);
                    updateQuery.setParameter(PARAM_DEBTOR, debtor);
                    updateQuery.setParameter(PARAM_YEAR, year);
                    updateQuery.setParameter(PARAM_MONTH, month);
                    updateQuery.setParameter(PARAM_IS_LITIGATION, isLitigation);

                    int updated = updateQuery.executeUpdate();
                    if (updated > 0) {
                        updatedRecords++;
//                        logger.info("更新减值准备表本年度累计回收金额成功: 债权人={}, 债务人={}, 年={}, 月={}, 金额={}",
//                                    creditor, debtor, year, month, totalRecovery);
                    } else {
                        logger.warn("更新减值准备表本年度累计回收金额失败: 债权人={}, 债务人={}, 年={}, 月={}, 金额={}",
                                    creditor, debtor, year, month, totalRecovery);
                    }
                } catch (Exception e) {
                    logger.error("更新减值准备表记录的本年度累计回收金额时发生错误", e);
                    // 继续处理下一条记录，不中断整个过程
                }
            }

            logger.info("减值准备表本年度累计回收金额更新完成, 共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        } catch (Exception e) {
            logger.error("执行减值准备表本年度累计回收金额更新时发生错误", e);
            // 不抛出异常，返回已经更新的记录数量
            return updatedRecords;
        }
    }

    /**
     * 更新诉讼表的本年度累计回收金额
     * 计算方法：本年度累计回收 = 前面月份的本月处置债权合计数
     *
     * @return 更新的记录数量
     */
    @Transactional(noRollbackFor = Exception.class)
    public int updateLitigationAnnualRecovery() {
        logger.info("开始执行诉讼表本年度累计回收金额更新");

        YearMonth currentYearMonth = YearMonth.now();
        int currentYear = currentYearMonth.getYear();
        int currentMonth = currentYearMonth.getMonthValue();

        int updatedRecords = 0;

        try {
            // 只处理2025年及以后的债权
            if (currentYear < 2025) {
                logger.info("当前年份为{}\uff0c小于2025年\uff0c不处理诉讼表的本年度累计回收金额", currentYear);
                return 0;
            }

            // 查询当月诉讼表记录
            String jpql = "SELECT r FROM LitigationClaim r WHERE r.id.year = :year AND r.id.month = :month";

            var query = entityManager.createQuery(jpql);
            query.setParameter(PARAM_YEAR, currentYear);
            query.setParameter(PARAM_MONTH, currentMonth);

            List<?> records = query.getResultList();
            logger.info("找到 {} 条{}年{}月的诉讼表记录需要更新本年度累计回收金额",
                        records.size(), currentYear, currentMonth);

            for (Object obj : records) {
                try {
                    String creditor = getPropertyValue(obj, PROP_CREDITOR);
                    String debtor = getPropertyValue(obj, PROP_DEBTOR);

                    if (creditor == null || debtor == null) {
                        logger.warn(LOG_INCOMPLETE_DATA, creditor, debtor);
                        continue;
                    }

                    // 计算诉讼表自身当年前几个月的本月处置债权合计
                    String recoverySql = "SELECT SUM(本月处置债权) FROM 诉讼表 " +
                                         "WHERE 债权人 = :creditor AND 债务人 = :debtor " +
                                         "AND 年份 = :year AND 月份 <= :month";

                    Query recoveryQuery = entityManager.createNativeQuery(recoverySql);
                    recoveryQuery.setParameter(PARAM_CREDITOR, creditor);
                    recoveryQuery.setParameter(PARAM_DEBTOR, debtor);
                    recoveryQuery.setParameter(PARAM_YEAR, currentYear);
                    recoveryQuery.setParameter(PARAM_MONTH, currentMonth);

                    Object result = recoveryQuery.getSingleResult();
                    BigDecimal totalRecovery = (result != null) ?
                                               new BigDecimal(result.toString()) : BigDecimal.ZERO;

                    // 更新诉讼表的本年度累计回收金额
                    String updateSql = "UPDATE 诉讼表 SET 本年度累计回收 = :recoveryAmount " +
                                       "WHERE 债权人 = :creditor AND 债务人 = :debtor " +
                                       "AND 年份 = :year AND 月份 = :month";

                    Query updateQuery = entityManager.createNativeQuery(updateSql);
                    updateQuery.setParameter(PARAM_RECOVERY_AMOUNT, totalRecovery);
                    updateQuery.setParameter(PARAM_CREDITOR, creditor);
                    updateQuery.setParameter(PARAM_DEBTOR, debtor);
                    updateQuery.setParameter(PARAM_YEAR, currentYear);
                    updateQuery.setParameter(PARAM_MONTH, currentMonth);

                    int updated = updateQuery.executeUpdate();
                    if (updated > 0) {
                        updatedRecords++;
//                        logger.info("更新诉讼表本年度累计回收金额: 债权人={}, 债务人={}, 年={}, 月={}, 金额={}",
//                                    creditor, debtor, currentYear, currentMonth, totalRecovery);
                    }
                } catch (Exception e) {
                    logger.error("更新诉讼表记录的本年度累计回收金额时发生错误", e);
                    // 继续处理下一条记录，不中断整个过程
                }
            }

            logger.info("诉讼表本年度累计回收金额更新完成, 共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        } catch (Exception e) {
            logger.error("执行诉讼表本年度累计回收金额更新时发生错误", e);
            // 不抛出异常，返回已经更新的记录数量
            return updatedRecords;
        }
    }

    /**
     * 更新非诉讼表的本年度累计回收金额
     * 计算方法：本年度累计回收 = 前面月份的本月处置债权合计数
     *
     * @return 更新的记录数量
     */
    @Transactional(noRollbackFor = Exception.class)
    public int updateNonLitigationAnnualRecovery() {
        logger.info("开始执行非诉讼表本年度累计回收金额更新");

        YearMonth currentYearMonth = YearMonth.now();
        int currentYear = currentYearMonth.getYear();
        int currentMonth = currentYearMonth.getMonthValue();

        int updatedRecords = 0;

        try {
            // 只处理2025年及以后的债权
            if (currentYear < 2025) {
                logger.info("当前年份为{}\uff0c小于2025年\uff0c不处理非诉讼表的本年度累计回收金额", currentYear);
                return 0;
            }

            // 查询当月非诉讼表记录
            String jpql = "SELECT r FROM NonLitigationClaim r WHERE r.id.year = :year AND r.id.month = :month";

            var query = entityManager.createQuery(jpql);
            query.setParameter(PARAM_YEAR, currentYear);
            query.setParameter(PARAM_MONTH, currentMonth);

            List<?> records = query.getResultList();
            logger.info("找到 {} 条{}年{}月的非诉讼表记录需要更新本年度累计回收金额",
                        records.size(), currentYear, currentMonth);

            for (Object obj : records) {
                try {
                    String creditor = getPropertyValue(obj, PROP_CREDITOR);
                    String debtor = getPropertyValue(obj, PROP_DEBTOR);

                    if (creditor == null || debtor == null) {
                        logger.warn(LOG_INCOMPLETE_DATA, creditor, debtor);
                        continue;
                    }

                    // 计算非诉讼表自身当年前几个月的本月处置债权合计
                    String recoverySql = "SELECT SUM(本月处置债权) FROM 非诉讼表 " +
                                         "WHERE 债权人 = :creditor AND 债务人 = :debtor " +
                                         "AND 年份 = :year AND 月份 <= :month";

                    Query recoveryQuery = entityManager.createNativeQuery(recoverySql);
                    recoveryQuery.setParameter(PARAM_CREDITOR, creditor);
                    recoveryQuery.setParameter(PARAM_DEBTOR, debtor);
                    recoveryQuery.setParameter(PARAM_YEAR, currentYear);
                    recoveryQuery.setParameter(PARAM_MONTH, currentMonth);

                    Object result = recoveryQuery.getSingleResult();
                    BigDecimal totalRecovery = (result != null) ?
                                               new BigDecimal(result.toString()) : BigDecimal.ZERO;

                    // 更新非诉讼表的本年度累计回收金额
                    String updateSql = "UPDATE 非诉讼表 SET 本年度累计回收 = :recoveryAmount " +
                                       "WHERE 债权人 = :creditor AND 债务人 = :debtor " +
                                       "AND 年份 = :year AND 月份 = :month";

                    Query updateQuery = entityManager.createNativeQuery(updateSql);
                    updateQuery.setParameter(PARAM_RECOVERY_AMOUNT, totalRecovery);
                    updateQuery.setParameter(PARAM_CREDITOR, creditor);
                    updateQuery.setParameter(PARAM_DEBTOR, debtor);
                    updateQuery.setParameter(PARAM_YEAR, currentYear);
                    updateQuery.setParameter(PARAM_MONTH, currentMonth);

                    int updated = updateQuery.executeUpdate();
                    if (updated > 0) {
                        updatedRecords++;
//                        logger.info("更新非诉讼表本年度累计回收金额: 债权人={}, 债务人={}, 年={}, 月={}, 金额={}",
//                                    creditor, debtor, currentYear, currentMonth, totalRecovery);
                    }
                } catch (Exception e) {
                    logger.error("更新非诉讼表记录的本年度累计回收金额时发生错误", e);
                    // 继续处理下一条记录，不中断整个过程
                }
            }

            logger.info("非诉讼表本年度累计回收金额更新完成, 共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        } catch (Exception e) {
            logger.error("执行非诉讼表本年度累计回收金额更新时发生错误", e);
            // 不抛出异常，返回已经更新的记录数量
            return updatedRecords;
        }
    }

    // 该方法已被移除，因为现在每个表直接使用自身的本月处置债权字段计算累计回收金额

    /**
     * 从对象中获取属性值，支持嵌套属性（如"id.creditor"）
     *
     * @param obj      目标对象
     * @param property 属性名称，支持嵌套属性（如"id.creditor"）
     * @return 属性值，如果无法获取则返回null
     */
    private String getPropertyValue(Object obj, String property) {
        try {
            if (obj == null || property == null || property.isEmpty()) {
                return null;
            }

            String[] parts = property.split("\\.");
            Object value = obj;

            for (String part : parts) {
                if (value == null) {
                    return null;
                }

                // 获取getter方法名
                String getterMethod = "get" + part.substring(0, 1).toUpperCase() + part.substring(1);

                try {
                    value = value.getClass().getMethod(getterMethod).invoke(value);
                } catch (Exception e) {
                    logger.error("无法获取属性 {} 的值，错误: {}", property, e.getMessage());
                    return null;
                }
            }

            return value != null ? value.toString() : null;
        } catch (Exception e) {
            logger.error("获取属性值时发生错误: {}", e.getMessage());
            return null;
        }
    }
}
