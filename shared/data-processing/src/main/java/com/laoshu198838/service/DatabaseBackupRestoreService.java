package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 数据库备份和恢复服务
 * 1. 每天16:00自动备份MySQL数据库的所有表
 * 2. 当项目路径包含ExternalSSD-2T时，自动从备份恢复数据库
 * <AUTHOR>
 */
@Service
@RestController
public class DatabaseBackupRestoreService {
    private static final Logger logger = LoggerFactory.getLogger(DatabaseBackupRestoreService.class);

    // 数据库配置信息
    @Value("${spring.datasource.url}")
    private String dbUrl;

    @Value("${spring.datasource.username}")
    private String dbUsername;

    @Value("${spring.datasource.password}")
    private String dbPassword;

    // 备份目录 - 从配置文件读取
    @Value("${app.backup.path:./backups}") // 默认为项目根目录下的backups文件夹
    private String backupDir;

    @PostConstruct
    public void init() {
        // 创建备份目录（如果不存在）
        createBackupDirectoryIfNotExists();

        // 检查是否需要从备份恢复数据
        checkAndRestoreDatabase();
    }

    /**
     * 确保备份目录存在
     */
    private void createBackupDirectoryIfNotExists() {
        File directory = new File(backupDir);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (created) {
                logger.info("成功创建备份目录: {}", backupDir);
            } else {
                logger.error("无法创建备份目录: {}", backupDir);
            }
        }
    }

    /**
     * 每天16:00执行数据库备份
     */
    @Scheduled(cron = "0 0 16 * * ?")
    public void scheduledBackupDatabase() {
        backupDatabase();
    }
    
    /**
     * 手动触发备份 - 访问 http://localhost:端口/api/trigger-backup 即可
     * @return 备份结果消息
     */
    @GetMapping("/api/trigger-backup")
    public String triggerBackup() {
        try {
            backupDatabase();
            return "数据库备份已触发，请检查日志获取详细信息";
        } catch (Exception e) {
            logger.error("手动触发备份失败", e);
            return "备份触发失败: " + e.getMessage();
        }
    }
    
    /**
     * 执行数据库备份
     */
    public void backupDatabase() {
        logger.info("开始执行数据库备份任务...");

        try {
            // 从JDBC URL中提取数据库名称（处理URL编码）
            String dbName = extractDatabaseName(dbUrl);

            // 创建带有时间戳的备份文件名
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String backupFileName = dbName + "_" + timestamp + ".sql";
            String backupFilePath = backupDir + File.separator + backupFileName;

            // 检查备份文件目录是否存在，如不存在则创建
            File backupFileDir = new File(backupDir);
            if (!backupFileDir.exists()) {
                boolean created = backupFileDir.mkdirs();
                if (!created) {
                    throw new RuntimeException("无法创建备份目录: " + backupDir);
                }
            }
            
            // 检查备份目录权限
            if (!backupFileDir.canWrite()) {
                throw new RuntimeException("没有写入备份目录的权限: " + backupDir);
            }
            
            logger.info("备份文件将保存到: {}，数据库名称: {}", backupFileName, dbName);
            
            // 执行mysqldump命令进行备份
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "mysqldump",
                    "--host=localhost",
                    "--port=3306",
                    "--user=" + dbUsername,
                    "--password=" + dbPassword,
                    "--default-character-set=utf8mb4",
                    "--result-file=" + backupFileName,
                    dbName
            );
            
            // 重定向错误流，以便我们可以捕获和记录它
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();
            
            // 读取命令输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                // 验证备份文件是否成功创建
                File backupFile = new File(backupFileName);
                if (backupFile.exists() && backupFile.length() > 0) {
                    logger.info("数据库备份成功，备份文件: {}，文件大小: {} 字节", backupFileName, backupFile.length());
                } else {
                    logger.error("数据库备份过程执行成功，但备份文件不存在或为空: {}", backupFileName);
                }
            } else {
                logger.error("数据库备份失败，退出代码: {}，输出: {}", exitCode, output.toString());
                
                // 尝试检查mysqldump是否可用
                checkMysqlDumpAvailability();
            }
        } catch (Exception e) {
            logger.error("执行数据库备份过程中发生错误", e);
        }
    }

    /**
     * 检查是否需要从备份恢复数据库
     */
    private void checkAndRestoreDatabase() {
        String currentPath = System.getProperty("user.dir");
        logger.info("当前项目路径: {}", currentPath);

        // 检查路径是否包含ExternalSSD-2T
        if (currentPath.contains("ExternalSSD-2T")) {
            logger.info("检测到在ExternalSSD-2T上运行项目，准备从备份恢复数据库...");
            restoreLatestBackup();
        } else {
            logger.info("当前不在ExternalSSD-2T上运行，跳过数据库恢复");
        }
    }

    /**
     * 从最新的备份文件恢复数据库
     */
    private void restoreLatestBackup() {
        try {
            File backupDirectory = new File(this.backupDir);
            File[] backupFiles = backupDirectory.listFiles((dir, name) -> name.endsWith(".sql"));

            if (backupFiles == null || backupFiles.length == 0) {
                logger.warn("未找到可用的备份文件");
                return;
            }

            // 查找最新的备份文件
            File latestBackup = backupFiles[0];
            for (File file : backupFiles) {
                if (file.lastModified() > latestBackup.lastModified()) {
                    latestBackup = file;
                }
            }

            logger.info("找到最新的备份文件: {}", latestBackup.getName());

            // 从JDBC URL中提取数据库名称
            String dbName = extractDatabaseName(dbUrl);

            // 执行mysql命令恢复数据库
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "mysql",
                    "--host=localhost",
                    "--port=3306",
                    "--user=" + dbUsername,
                    "--password=" + dbPassword,
                    "--default-character-set=utf8mb4",
                    dbName
            );

            // 将SQL文件重定向到mysql进程的输入
            Process process = processBuilder.start();

            // 将备份文件内容写入mysql进程的输入流
            Files.copy(latestBackup.toPath(), process.getOutputStream());
            process.getOutputStream().close();

            int exitCode = process.waitFor();

            if (exitCode == 0) {
                logger.info("数据库恢复成功，使用备份文件: {}", latestBackup.getAbsolutePath());
            } else {
                logger.error("数据库恢复失败，退出代码: {}", exitCode);
                // 读取错误输出
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        logger.error("恢复错误: {}", line);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("执行数据库恢复过程中发生错误", e);
        }
    }

    /**
     * 从JDBC URL中提取数据库名称
     */
    private String extractDatabaseName(String jdbcUrl) {
        try {
            // 用于调试输出，查看原始URL内容
            System.out.println("JDBC URL: " + jdbcUrl);

            // 检查URL是否包含协议部分
            if (!jdbcUrl.startsWith("jdbc:")) {
                return "database"; // 返回默认名称
            }

            // 查找最后一个斜杠的位置（可能是服务器名称之后）
            int lastSlashIndex = jdbcUrl.lastIndexOf('/');

            // 如果没找到斜杠，返回默认名称
            if (lastSlashIndex == -1) {
                return "database";
            }

            int startIndex = lastSlashIndex + 1; // 数据库名称开始位置

            // 查找参数的开始位置（?符号）
            int endIndex = jdbcUrl.indexOf('?', startIndex);

            String dbName;
            if (endIndex == -1) { // 没有参数部分
                dbName = jdbcUrl.substring(startIndex);
            } else if (endIndex > startIndex) { // 有参数部分且索引有效
                dbName = jdbcUrl.substring(startIndex, endIndex);
            } else { // 异常情况
                dbName = "database";
            }

            System.out.println("提取的数据库名称: " + dbName);
            return dbName;
        } catch (Exception e) {
            System.err.println("解析数据库URL时出错: " + e.getMessage());
            return "overdue_debt_db"; // 返回一个默认的数据库名称
        }
    }

    /**
     * 检查mysqldump命令是否可用
     */
    private void checkMysqlDumpAvailability() {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("mysqldump", "--version");
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();

            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            int exitCode = process.waitFor();
            if (exitCode == 0) {
                logger.info("mysqldump 可用: {}", output.toString().trim());
            } else {
                logger.error("mysqldump 命令不可用，请确保已安装MySQL客户端工具: {}", output.toString().trim());
            }
        } catch (Exception e) {
            logger.error("检查mysqldump可用性时出错，请确保已安装MySQL客户端工具", e);
        }
    }

    /**
     * 手动执行备份
     */
    public String manualBackup() {
        try {
            backupDatabase();
            return "手动备份操作已执行，请查看日志获取详细信息";
        } catch (Exception e) {
            logger.error("手动备份过程中发生错误", e);
            return "备份操作失败: " + e.getMessage();
        }
    }

    /**
     * 手动执行恢复
     */
    public String manualRestore() {
        try {
            restoreLatestBackup();
            return "手动恢复操作已执行，请查看日志获取详细信息";
        } catch (Exception e) {
            logger.error("手动恢复过程中发生错误", e);
            return "恢复操作失败: " + e.getMessage();
        }
    }
}
