package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 逾期债权数据更新主服务
 * 统一入口，协调各类债务表的更新操作
 * 负责定义所有定时任务的调度策略，并委托给具体服务执行
 *
 * <AUTHOR>
 */
@Service
public class OverdueDebtUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(OverdueDebtUpdateService.class);

    private final NonLitigationOverdueUpdateService nonLitigationService;

    private final LitigationOverdueUpdateService litigationService;

    private final ImpairmentReserveService impairmentReserveService;

    private final AnnualRecoveryService annualRecoveryService;

    public OverdueDebtUpdateService(
            NonLitigationOverdueUpdateService nonLitigationService,
            LitigationOverdueUpdateService litigationService,
            ImpairmentReserveService impairmentReserveService,
            AnnualRecoveryService annualRecoveryService) {
        this.nonLitigationService = nonLitigationService;
        this.litigationService = litigationService;
        this.impairmentReserveService = impairmentReserveService;
        this.annualRecoveryService = annualRecoveryService;
        logger.info("OverdueDebtUpdateService 初始化完成");
    }

    /**
     * 定时任务 - 每天凌晨1点执行数据更新
     * 更新非诉讼债权逾期年限、诉讼债权逾期年限、减值准备表数据等
     *
     * 使用改进的事务配置，避免autoCommit冲突
     * autoCommit冲突已解决，重新启用定时任务
     *
     * 测试环境可改为每3分钟执行一次
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void scheduleOverdueYearUpdate() {
        executeDataUpdate();
    }

    /**
     * 手动执行数据更新（用于测试或手动触发）
     */
    public void executeDataUpdate() {
        logger.info("开始执行定期数据更新服务");
        // 1、非诉讼表更新
        // (1) 非诉讼表逾期年限更新
        int updatedCount = nonLitigationService.updateOverdueYears();
        logger.info("非诉讼债权逾期年限更新完成，共更新 {} 条记录", updatedCount);

        // (2) 非诉讼表上月数据更新到本月
        int createdRecords = nonLitigationService.copyLastMonthData();
        logger.info("非诉讼表上月数据更新到本月任务完成，共创建 {} 条记录", createdRecords);

        // (3) 非诉讼表本年度累计回收金额数据判断和更新
        int updatedRecords = nonLitigationService.updateAnnualCumulativeRecovery();
        logger.info("非诉讼表本年度累计回收金额数据判断和更新完成，共更新 {} 条记录", updatedRecords);


        // 2、诉讼表更新
        // (1)诉讼表上月数据更新到本月
        int ligitationnCreatedRecords = litigationService.copyLastMonthData();
        logger.info("诉讼表上月数据更新到本月任务完成，共创建 {} 条记录", ligitationnCreatedRecords);
        // (2)诉讼表逾期年限更新
        int ligitationnUpdatedCount = litigationService.updateOverdueYears();
        logger.info("诉讼债权逾期年限更新完成，共更新 {} 条记录", ligitationnUpdatedCount);
        // (3) 诉讼表本年度累计回收金额数据判断和更新
        int litigationUpdatedRecords = litigationService.updateAnnualCumulativeRecovery();
        logger.info("诉讼表本年度累计回收金额数据判断和更新完成，共更新 {} 条记录", litigationUpdatedRecords);

        // 3、减值准备表更新
        // (1）减值准备表上月数据更新到本月
        int impairmentCreatedRecords = impairmentReserveService.copyLastMonthData();
        logger.info("减值准备表上月数据更新到本月任务完成，共创建 {} 条记录", impairmentCreatedRecords);
        // (2）减值准备表数据一致性更新和是否全额计提坏账判断
        // a. 确保计提减值金额与本月末余额相等
        // b. 如果本月末债权余额为零，则本月末余额也应为零
        // c. 判断是否全额计提坏账: 如果本月末债权余额 = 本月末余额，则为"是"，否则为"否"
        int updatedRecords2 = impairmentReserveService.updateImpairmentAmountConsistency();
        logger.info("减值准备表数据一致性检查及全额计提坏账判断完成，共更新 {} 条记录", updatedRecords2);
        // (3）减值准备累计回款更新
        int impairmentAnnualRecoveryUpdates = impairmentReserveService.updateImpairmentAnnualRecovery();
        logger.info("减值准备累计回款更新完成，共更新 {} 条记录", impairmentAnnualRecoveryUpdates);
        // (4）减值准备表数据一致性检查
        // 检查和调整两项内容：
        // a. 确保本月的上月末余额与上个月的本月末余额相等
        // b. 确保本月的本月末余额不超过本月末债权余额
        int balanceConsistencyUpdates = impairmentReserveService.checkAndAdjustImpairmentBalanceConsistency();
        logger.info("减值准备表数据一致性检查完成，共更新 {} 条记录", balanceConsistencyUpdates);
        // 4、更新所有表的本年度累计回收金额
        // 计算每项债权的本年度累计回收 = 前面月份的本月处置债权合计数
        updateAnnualRecoveryForAllTables();
        // (5) 减值准备表本年度累计回收金额数据判断和更新
        int impairmentUpdatedRecords = impairmentReserveService.updateAnnualCumulativeRecovery();
        logger.info("减值准备表本年度累计回收金额数据判断和更新完成，共更新 {} 条记录", impairmentUpdatedRecords);

        logger.info("定期数据更新服务执行完成");
    }

    /**
     * 更新减值准备表、诉讼表和非诉讼表中每项债权的本年度累计回收金额
     * 计算方法：本年度累计回收 = 前面月份的本月处置债权合计数
     * 例如：2025年5月的本年度累计回收 = 1月到5月的本月处置债权合计数
     */
    private void updateAnnualRecoveryForAllTables() {
        logger.info("开始更新所有表的本年度累计回收金额");

        try {
            // 1. 更新减值准备表的本年度累计回收
            int impairmentUpdates = annualRecoveryService.updateImpairmentAnnualRecovery();
            logger.info("减值准备表本年度累计回收更新完成，共更新 {} 条记录", impairmentUpdates);

            // 2. 更新诉讼表的本年度累计回收
            int litigationUpdates = annualRecoveryService.updateLitigationAnnualRecovery();
            logger.info("诉讼表本年度累计回收更新完成，共更新 {} 条记录", litigationUpdates);

            // 3. 更新非诉讼表的本年度累计回收
            int nonLitigationUpdates = annualRecoveryService.updateNonLitigationAnnualRecovery();
            logger.info("非诉讼表本年度累计回收更新完成，共更新 {} 条记录", nonLitigationUpdates);

            int totalUpdates = impairmentUpdates + litigationUpdates + nonLitigationUpdates;
            logger.info("所有表的本年度累计回收金额更新完成，共更新 {} 条记录", totalUpdates);
        } catch (Exception e) {
            logger.error("更新本年度累计回收金额时发生错误", e);
        }
    }
}