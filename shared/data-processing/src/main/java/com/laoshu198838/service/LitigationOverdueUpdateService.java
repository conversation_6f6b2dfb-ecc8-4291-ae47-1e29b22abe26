package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.LitigationClaim.LitigationCompositeKey;
import com.laoshu198838.repository.overdue_debt.LitigationClaimRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 诉讼债权逾期更新服务异常类
 */
class LitigationOverdueUpdateException extends RuntimeException {

    public LitigationOverdueUpdateException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 诉讼债权逾期年限更新服务
 * 负责更新诉讼表中的逾期年限字段、处理月度数据复制和监控新增表变更
 * 注意：定时任务已移至 OverdueDebtUpdateService 统一管理
 * 根据债权到期时间计算逾期年限并分类为：1年以内、1-5年、5年以上
 *
 * <AUTHOR>
 */
@Service
public class LitigationOverdueUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(LitigationOverdueUpdateService.class);

    private final LitigationOverdueUpdateTransactionalService transactionalService;

    /**
     * 构造函数，注入依赖服务
     *
     * @param transactionalService 事务处理服务
     */
    public LitigationOverdueUpdateService(
            LitigationOverdueUpdateTransactionalService transactionalService) {
        this.transactionalService = transactionalService;
        logger.info("LitigationOverdueUpdateService 初始化完成");
    }

    /**
     * 更新诉讼债权逾期年限
     * 此方法由 OverdueDebtUpdateService 调用
     * 原来的定时任务逻辑已移至 OverdueDebtUpdateService
     *
     * @return 更新的记录数
     */
    public int updateOverdueYears() {
        logger.info("开始执行诉讼债权逾期年限更新");
        try {
            int updatedCount = transactionalService.updateOverdueYears();
            logger.info("诉讼债权逾期年限更新完成，共更新 {} 条记录", updatedCount);
            return updatedCount;
        } catch (LitigationOverdueUpdateException e) {
            // 重新抛出已经包装好的异常
            throw e;
        } catch (Exception e) {
            logger.error("执行诉讼债权逾期年限更新任务时发生错误: {}", e.getMessage(), e);
            throw new LitigationOverdueUpdateException("诉讼债权逾期年限更新失败", e);
        }
    }

    /**
     * 复制上月数据到当月
     * 此方法由 OverdueDebtUpdateService 调用
     * 原来的定时任务逻辑已移至 OverdueDebtUpdateService
     *
     * @return 新创建的记录数
     */
    public int copyLastMonthData() {
        return copyLastMonthData(null);
    }

    /**
     * 复制上月数据到指定月份
     * 如果目标月份为null，则使用当前月份
     *
     * @param targetYearMonthStr 目标年月，格式为 "yyyy-MM"，例如 "2025-05"
     * @return 新创建的记录数
     */
    public int copyLastMonthData(String targetYearMonthStr) {
        logger.info("开始执行诉讼表月份数据更新");
        try {
            int createdRecords = transactionalService.copyLastMonthData();
            logger.info("诉讼表月份数据更新完成，共创建 {} 条记录", createdRecords);
            return createdRecords;
        } catch (LitigationOverdueUpdateException e) {
            // 重新抛出已经包装好的异常
            throw e;
        } catch (Exception e) {
            logger.error("执行诉讼表月份数据更新任务时发生错误: {}", e.getMessage(), e);
            throw new LitigationOverdueUpdateException("诉讼表月份数据更新失败", e);
        }
    }

    /**
     * 更新本年度累计回收金额
     * 此方法由 OverdueDebtUpdateService 调用
     *
     * @return 更新的记录数
     */
    public int updateAnnualCumulativeRecovery() {
        logger.info("开始执行诉讼表本年度累计回收金额更新");
        try {
            int updatedRecords = transactionalService.updateAnnualCumulativeRecovery();
            logger.info("诉讼表本年度累计回收金额更新完成，共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        } catch (Exception e) {
            logger.error("执行诉讼表本年度累计回收金额更新时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 诉讼债权逾期年限更新事务服务
     * 负责处理涉及数据库事务的操作
     * 分离到单独服务类中以支持正确的事务代理行为
     *
     * <AUTHOR>
     */
    @Service
    @Transactional
    public static class LitigationOverdueUpdateTransactionalService {

        private static final Logger logger = LoggerFactory.getLogger(LitigationOverdueUpdateTransactionalService.class);

        /**
         * 逾期年限分类常量
         */
        private static final String OVERDUE_LESS_THAN_ONE_YEAR = "1年以内";
        private static final String OVERDUE_ONE_TO_FIVE_YEARS = "1-5年";
        private static final String OVERDUE_MORE_THAN_FIVE_YEARS = "5年以上";

        private final LitigationClaimRepository litigationClaimRepository;

        @Autowired
        public LitigationOverdueUpdateTransactionalService(LitigationClaimRepository litigationClaimRepository) {
            this.litigationClaimRepository = litigationClaimRepository;
        }

        /**
         * 更新所有诉讼债权记录的逾期年限
         *
         * @return 更新的记录数
         */
        @Transactional
        public int updateOverdueYears() {
            logger.info("开始查询所有诉讼债权记录...");

            // 使用JpaRepository提供的findAll方法查询所有诉讼债权记录
            List<LitigationClaim> claims = litigationClaimRepository.findAll();
            logger.info("共查询到 {} 条诉讼债权记录", claims.size());

            int updatedCount = 0;
            Date currentDate = new Date();

            for (LitigationClaim claim : claims) {
                Date dueDate = claim.getDueDate();

                // 如果到期日为空，跳过此记录
                if (dueDate == null) {
//                    logger.warn("债权ID [{}] 到期日期为空，跳过逾期年限更新", claim.getId());
                    continue;
                }

                // 计算逾期年限
                String overdueYearCategory = calculateOverdueYearCategory(dueDate, currentDate);

                // 如果逾期年限分类发生变化，则更新记录
                if (!overdueYearCategory.equals(claim.getOverdueYear())) {
                    claim.setOverdueYear(overdueYearCategory);
                    litigationClaimRepository.save(claim);
                    updatedCount++;

                    logger.info("更新债权ID [{}]，债务人 [{}]，债权到期时间 [{}]，逾期年限为 [{}]",
                                claim.getId(), claim.getId().getDebtor(), dueDate, overdueYearCategory);
                }
            }

            return updatedCount;
        }

        /**
         * 复制上月数据到本月，同时更新上月末数据
         * 检查当前年月的数据是否存在，如果不存在则从最近的月份复制数据
         *
         * @return 新增的记录数
         */
        @Transactional
        public int copyLastMonthData() {
            // 获取当前年月
            YearMonth currentYearMonth = YearMonth.now();

            // 统计创建的记录数
            int totalCreatedRecords = 0;

            // 查找上一个有数据的月份
            YearMonth lastDataYearMonth = findLastDataYearMonth(currentYearMonth);

            if (lastDataYearMonth == null) {
                logger.warn("未找到任何历史诉讼表数据，无法进行月度更新");
                return 0;
            }

            logger.info("找到最近的诉讼表数据月份: {}", lastDataYearMonth);

            // 从找到的最近月份开始，逐月更新到当前月份
            YearMonth targetYearMonth = lastDataYearMonth.plusMonths(1);

            while (!targetYearMonth.isAfter(currentYearMonth)) {
                logger.info("开始更新 {} 月份的诉讼表数据...", targetYearMonth);

                // 从上个月复制数据并调整
                int createdCount = copyAndAdjustMonthlyData(lastDataYearMonth, targetYearMonth);

                if (createdCount > 0) {
                    logger.info("成功为 {} 创建 {} 条诉讼表记录", targetYearMonth, createdCount);
                    totalCreatedRecords += createdCount;

                    // 更新lastDataYearMonth为当前处理的月份，继续处理下一个月
                    lastDataYearMonth = targetYearMonth;
                    targetYearMonth = targetYearMonth.plusMonths(1);
                } else if (createdCount == -1) {
                    // 目标月份已有数据，跳过并继续处理下一个月
                    logger.info("月份 {} 已有数据，将继续处理下一个月", targetYearMonth);
                    targetYearMonth = targetYearMonth.plusMonths(1);
                } else {
                    logger.warn("为 {} 创建诉讼表记录失败，中止更新流程", targetYearMonth);
                    break;
                }
            }
            return totalCreatedRecords;
        }

        public int updateAnnualCumulativeRecovery() {
            logger.info("开始执行诉讼表本年度累计回收金额更新");
            int totalUpdated = litigationClaimRepository.updateAnnualCumulativeRecovery();
            logger.info("诉讼表本年度累计回收金额更新结束，共更新记录数：" + totalUpdated);
            return totalUpdated;
        }

        /**
         * 计算逾期年限分类
         *
         * @param dueDate     到期日期
         * @param currentDate 当前日期
         * @return 逾期年限分类
         */
        private String calculateOverdueYearCategory(Date dueDate, Date currentDate) {
            // 如果还未到期，返回空字符串
            if (dueDate.after(currentDate)) {
                return "";
            }

            // 将Date转换为LocalDate进行计算
            LocalDate dueDateLocal = dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDateLocal = currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            // 计算两个日期之间的年数差
            long yearsBetween = ChronoUnit.YEARS.between(dueDateLocal, currentDateLocal);

            // 根据年数差确定逾期年限分类
            if (yearsBetween < 1) {
                return OVERDUE_LESS_THAN_ONE_YEAR;
            } else if (yearsBetween < 5) {
                return OVERDUE_ONE_TO_FIVE_YEARS;
            } else {
                return OVERDUE_MORE_THAN_FIVE_YEARS;
            }
        }

        /**
         * 查找最近的诉讼表数据月份
         *
         * @param currentYearMonth 当前年月
         * @return 最近的有数据的年月，如果没有数据返回null
         */
        private YearMonth findLastDataYearMonth(YearMonth currentYearMonth) {
            // 最多往前查找36个月
            for (int i = 0; i < 36; i++) {
                YearMonth yearMonth = currentYearMonth.minusMonths(i);

                // 检查此年月是否有数据
                if (hasDataForYearMonth(yearMonth)) {
                    return yearMonth;
                }
            }

            return null;
        }

        /**
         * 检查指定年月是否有诉讼表数据
         *
         * @param yearMonth 需要检查的年月
         * @return 是否有数据
         */
        private boolean hasDataForYearMonth(YearMonth yearMonth) {
            String yearMonthStr = formatYearMonth(yearMonth);

            try {
                // 直接使用计数方法检查是否存在数据
                long count = litigationClaimRepository.countByYearAndMonth(yearMonth.getYear(), yearMonth.getMonthValue());
                logger.info("查询年月 {} 的数据条数: {}", yearMonthStr, count);
                return count > 0;
            } catch (Exception e) {
                logger.error("查询年月 {} 的数据时发生错误: {}", yearMonthStr, e.getMessage());

                // 如果计数方法出错，尝试使用findByYearAndMonth方法
                try {
                    List<LitigationClaim> claims = litigationClaimRepository.findByYearAndMonth(yearMonth.getYear(), yearMonth.getMonthValue());
                    boolean hasData = !claims.isEmpty();
                    logger.info("使用查询方法检查年月 {} 是否有数据: {}", yearMonthStr, hasData);
                    return hasData;
                } catch (Exception e2) {
                    logger.error("使用查询方法检查年月 {} 的数据时也发生错误: {}", yearMonthStr, e2.getMessage());
                    return false;
                }
            }
        }

        /**
         * 格式化年月
         *
         * @param yearMonth 年月
         * @return 格式化后的年月字符串
         */
        private String formatYearMonth(YearMonth yearMonth) {
            return yearMonth.getYear() + "年" + String.format("%02d", yearMonth.getMonthValue()) + "月";
        }

        /**
         * 从源月份复制诉讼表数据到目标月份，并进行数据调整
         *
         * @param sourceYearMonth 源月份
         * @param targetYearMonth 目标月份
         * @return 创建的记录数
         */
        private int copyAndAdjustMonthlyData(YearMonth sourceYearMonth, YearMonth targetYearMonth) {
            // 检查目标月份是否已有数据
            boolean hasData = hasDataForYearMonth(targetYearMonth);
            logger.info("检查目标月份 {} 是否已有数据: {}", targetYearMonth, hasData);

            if (hasData) {
                logger.info("目标月份 {} 已存在数据，跳过复制", targetYearMonth);
                // 修改逻辑：不再返回0，而是返回-1表示跳过
                return -1;
            }

            // 获取源月份的数据
            List<LitigationClaim> sourceClaims = getClaimsForYearMonth(sourceYearMonth);

            if (sourceClaims.isEmpty()) {
                logger.warn("源月份 {} 没有数据，无法复制", sourceYearMonth);
                return 0;
            }

            // 创建新记录并调整数据
            List<LitigationClaim> newClaims = new ArrayList<>();

            for (LitigationClaim sourceClaim : sourceClaims) {
                // 创建新记录
                LitigationClaim newClaim = new LitigationClaim();

                // 创建并设置复合主键
                LitigationCompositeKey newKey = new LitigationCompositeKey();
                newKey.setCreditor(sourceClaim.getId().getCreditor());
                newKey.setDebtor(sourceClaim.getId().getDebtor());
                newKey.setPeriod(sourceClaim.getId().getPeriod());
                newKey.setYear(targetYearMonth.getYear());
                newKey.setMonth(targetYearMonth.getMonthValue());
                newClaim.setId(newKey);

                // 设置月份字段
//                newClaim.setMonth(targetYearMonth.getMonthValue());
                // 复制基本字段
                newClaim.setLitigationCase(sourceClaim.getLitigationCase()); // 复制诉讼案件名称
                newClaim.setDueDate(sourceClaim.getDueDate());
                newClaim.setOverdueYear(sourceClaim.getOverdueYear());
                newClaim.setManagementCompany(sourceClaim.getManagementCompany());
                newClaim.setResponsiblePerson(sourceClaim.getResponsiblePerson());
                newClaim.setArrangement(sourceClaim.getArrangement());
                newClaim.setClaimType(sourceClaim.getClaimType());

                // 数据调整：将上月末数据设置为本月上月末数据
                newClaim.setLastMonthDebtBalance(sourceClaim.getCurrentMonthDebtBalance());

                // 保持当月数据和上月相同
                newClaim.setCurrentMonthDebtBalance(sourceClaim.getCurrentMonthDebtBalance());
                newClaim.setLitigationPrincipal(sourceClaim.getLitigationPrincipal());
                newClaim.setLitigationInterest(sourceClaim.getLitigationInterest());

                // 复制其他关键数据
                newClaim.setAnnualRecoveryTarget(sourceClaim.getAnnualRecoveryTarget());

                // 检查是否跨年，如果跨年则年度累计回收金额重置为0
                if (targetYearMonth.getYear() != sourceYearMonth.getYear()) {
                    // 跨年了，年度累计回收重置为0
                    newClaim.setAnnualCumulativeRecovery(BigDecimal.ZERO);
                    logger.info("诉讼表数据检测到年份变更: {}年 -> {}年，年度累计回收金额重置为0",
                                sourceYearMonth.getYear(), targetYearMonth.getYear());
                } else {
                    // 同年内，复制原有累计值
                    newClaim.setAnnualCumulativeRecovery(sourceClaim.getAnnualCumulativeRecovery());
                }

                // 新月份的新增和处置默认为0
                newClaim.setCurrentMonthNewDebt(BigDecimal.ZERO);
                newClaim.setCurrentMonthDisposalDebt(BigDecimal.ZERO);

                // 只有没有备注的行才添加新备注，有备注的保持原样
                if (sourceClaim.getRemark() == null || sourceClaim.getRemark().isEmpty()) {
                    newClaim.setRemark("");
                } else {
                    // 完全保留原始备注，不修改
                    newClaim.setRemark(sourceClaim.getRemark());
                }

                // 复制诉讼相关字段
                newClaim.setLitigationOccurredPrincipal(sourceClaim.getLitigationOccurredPrincipal());
                newClaim.setLitigationInterestFee(sourceClaim.getLitigationInterestFee());
                newClaim.setLitigationFee(sourceClaim.getLitigationFee());
                newClaim.setIntermediaryFee(sourceClaim.getIntermediaryFee());
                newClaim.setFinalJudgmentAmount(sourceClaim.getFinalJudgmentAmount());
                newClaim.setExecutionAmount(sourceClaim.getExecutionAmount());
                newClaim.setActualExecutionAmount(sourceClaim.getActualExecutionAmount());
                newClaim.setActualPaymentFee(sourceClaim.getActualPaymentFee());

                // 添加到集合
                newClaims.add(newClaim);
            }

            // 批量持久化新记录
            int count = 0;
            for (LitigationClaim claim : newClaims) {
                try {
                    litigationClaimRepository.save(claim);
                    count++;

                    // 每100条记录日志一次
                    if (count % 100 == 0) {
                        logger.info("已处理 {} 条记录...", count);
                    }
                } catch (Exception e) {
                    logger.error("保存记录时发生错误: {}，债务人：{}", e.getMessage(), claim.getId().getDebtor());
                }
            }

            // 最终日志
            logger.info("所有记录处理完成，共 {} 条", count);

            return count;
        }

        /**
         * 获取指定年月的诉讼表数据
         *
         * @param yearMonth 年月
         * @return 诉讼表记录列表
         */
        private List<LitigationClaim> getClaimsForYearMonth(YearMonth yearMonth) {
            try {
                // 使用仓库方法查询指定年月的数据
                List<LitigationClaim> results = litigationClaimRepository.findByYearAndMonth(yearMonth.getYear(), yearMonth.getMonthValue());
                logger.info("查询 {}年{}月 的数据返回 {} 条记录", yearMonth.getYear(), yearMonth.getMonthValue(), results.size());
                return results;
            } catch (Exception e) {
                logger.error("查询 {}年{}月 月数据时发生错误: {}", yearMonth.getYear(), yearMonth.getMonthValue(), e.getMessage());
                return new ArrayList<>();
            }
        }

    }
}
