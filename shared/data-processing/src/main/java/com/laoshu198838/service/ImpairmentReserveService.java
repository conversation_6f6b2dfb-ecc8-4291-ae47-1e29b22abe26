package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 减值准备表更新服务异常类
 */
class ImpairmentReserveServiceException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;

    public ImpairmentReserveServiceException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 减值准备表更新服务
 * 负责更新减值准备表中相关字段、处理月度数据复制和监控新增表变更
 *
 * <AUTHOR>
 */
@Service
public class ImpairmentReserveService {

    private static final Logger logger = LoggerFactory.getLogger(ImpairmentReserveService.class);

    private final ImpairmentReserveTransactionalService transactionalService;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 构造函数，注入依赖服务
     *
     * @param transactionalService 事务处理服务
     */
    public ImpairmentReserveService(ImpairmentReserveTransactionalService transactionalService) {
        this.transactionalService = transactionalService;
        logger.info("ImpairmentReserveService 初始化完成");
    }

    /**
     * 复制上月数据到当月
     * 此方法可由定时任务或其他服务调用
     *
     * @return 新创建的记录数
     */
    public int copyLastMonthData() {
        logger.info("开始执行减值准备表月份数据更新");
        try {
            int createdRecords = transactionalService.copyLastMonthData();
            logger.info("减值准备表月份数据更新完成，共创建 {} 条记录", createdRecords);
            return createdRecords;
        } catch (ImpairmentReserveServiceException e) {
            // 重新抛出已经包装好的异常
            throw e;
        } catch (Exception e) {
            logger.error("执行减值准备表月份数据更新任务时发生错误: {}", e.getMessage(), e);
            throw new ImpairmentReserveServiceException("减值准备表月份数据更新失败", e);
        }
    }

    /**
     * 监控新增表数据变更并同步更新到减值准备表
     * 此方法可由定时任务或其他服务调用
     */

    /**
     * 更新减值准备表数据一致性
     * 实现多个功能：
     * 1. 确保计提减值金额与本月末余额相等
     * 2. 如果本月末债权余额为零，则确保本月末余额也为零
     * 3. 判断是否全额计提坏账(如果本月末债权余额 = 本月末余额，则为"是"，否则为"否")
     *
     * @return 更新的记录数量
     */
    public int updateImpairmentAmountConsistency() {
        logger.info("开始执行减值准备表数据一致性检查及是否全额计提坏账判断");
        try {
            int updatedRecords = transactionalService.updateImpairmentAmountConsistency();
            logger.info("减值准备表数据一致性检查及是否全额计提坏账判断完成，共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        } catch (ImpairmentReserveServiceException e) {
            // 重新抛出已经包装好的异常
            throw e;
        } catch (Exception e) {
            logger.error("执行减值准备表数据一致性检查及是否全额计提坏账判断时发生错误: {}", e.getMessage(), e);
            throw new ImpairmentReserveServiceException("减值准备表数据一致性检查及是否全额计提坏账判断失败", e);
        }
    }

    /**
     * 检查和调整减值准备表数据一致性
     * 实现两个需求：
     * 1. 本月的上月末余额与上个月的本月末余额相等
     * 2. 本月的本月末余额不超过本月末债权余额
     * <p>
     * 从2024年12月开始校验，以2024年12月的数据为基准
     *
     * @return 更新的记录数量
     */
    public int checkAndAdjustImpairmentBalanceConsistency() {
        logger.info("开始执行减值准备表数据一致性检查和调整");
        try {
            int updatedRecords = transactionalService.checkAndAdjustImpairmentBalanceConsistency();
            logger.info("减值准备表数据一致性检查和调整完成，共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        } catch (ImpairmentReserveServiceException e) {
            // 重新抛出已经包装好的异常
            throw e;
        } catch (Exception e) {
            logger.error("执行减值准备表数据一致性检查和调整时发生错误: {}", e.getMessage(), e);
            throw new ImpairmentReserveServiceException("减值准备表数据一致性检查和调整失败", e);
        }
    }

    /**
     * 更新减值准备累计回款金额
     * 实现以下逻辑：
     * 1. 如果上月末余额大于本月末债权余额：
     * - 将(本月末债权余额-本月末余额)的值填入本月增减
     * - 根据本月增减数据调整本月末余额
     * - 将(上月末余额-本月末债权余额)的金额填入减值准备本年度累计回款金额
     * 2. 如果月末余额小于或等于本月末债权余额：
     * - 减值准备本年度累计回款金额堑0
     *
     * @return 更新的记录数量
     */
    public int updateImpairmentAnnualRecovery() {
        logger.info("开始执行减值准备累计回款金额更新");
        try {
            int updatedRecords = transactionalService.updateImpairmentAnnualRecovery();
            logger.info("减值准备累计回款金额更新完成，共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        } catch (ImpairmentReserveServiceException e) {
            // 重新抛出已经包装好的异常
            throw e;
        } catch (Exception e) {
            logger.error("执行减值准备累计回款金额更新时发生错误: {}", e.getMessage(), e);
            throw new ImpairmentReserveServiceException("减值准备累计回款金额更新失败", e);
        }
    }

    /**
     * 根据债务人名称查询最新的减值准备数据
     *
     * @param debtor 债务人名称
     * @return 包含减值准备数据的Map，如果未找到则返回空Map
     */
    public Map<String, Object> findLatestImpairmentDataByDebtor(String debtor) {
        logger.info("根据债务人查询最新减值准备数据: 债务人={}", debtor);

        if (debtor == null || debtor.trim().isEmpty()) {
            logger.warn("债务人参数为空");
            return new HashMap<>();
        }

        try {
            // 构建查询，按年份和月份降序排列获取最新记录
            String jpql = "SELECT i FROM ImpairmentReserve i WHERE i.id.debtor = :debtor ORDER BY i.id.year DESC, i.id.month DESC";
            TypedQuery<ImpairmentReserve> query = entityManager.createQuery(jpql, ImpairmentReserve.class);
            query.setParameter("debtor", debtor.trim());
            query.setMaxResults(1); // 只获取最新的一条记录

            List<ImpairmentReserve> results = query.getResultList();

            if (results.isEmpty()) {
                logger.warn("未找到债务人[{}]的减值准备记录", debtor);
                return new HashMap<>();
            }

            ImpairmentReserve latestRecord = results.get(0);
            logger.info("找到债务人[{}]的最新减值准备记录: 年={}, 月={}",
                        debtor, latestRecord.getId().getYear(), latestRecord.getId().getMonth());

            // 构建结果Map
            Map<String, Object> result = new HashMap<>();

            // 获取减值准备表中的字段值
            result.put("previousMonthBalance", latestRecord.getPreviousMonthBalance() != null ?
                                               latestRecord.getPreviousMonthBalance() : BigDecimal.ZERO);
            result.put("currentMonthNewDebt", latestRecord.getCurrentMonthNewDebt() != null ?
                                              latestRecord.getCurrentMonthNewDebt() : BigDecimal.ZERO);
            result.put("currentMonthDisposeDebt", latestRecord.getCurrentMonthDisposeDebt() != null ?
                                                  latestRecord.getCurrentMonthDisposeDebt() : BigDecimal.ZERO);
            result.put("currentMonthBalance", latestRecord.getCurrentMonthBalance() != null ?
                                              latestRecord.getCurrentMonthBalance() : BigDecimal.ZERO);

            // 添加其他可能需要的信息
            result.put("managementCompany", latestRecord.getManagementCompany());
            result.put("period", latestRecord.getId().getPeriod());
            result.put("isLitigation", latestRecord.getId().getIsLitigation());

            logger.info("成功查询到债务人[{}]的减值准备记录, 返回字段: {}", debtor, result.keySet());
            return result;
        } catch (Exception e) {
            logger.error("查询债务人[{}]的减值准备数据时发生异常: {}", debtor, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    public int updateAnnualCumulativeRecovery() {
        logger.info("开始执行减值准备本年度累计回款金额更新");
        int updatedRecords = transactionalService.updateAnnualCumulativeRecovery();
        logger.info("减值准备本年度累计回款金额更新完成，共更新 {} 条记录", updatedRecords);
        return updatedRecords;
    }

    /**
     * 减值准备表更新事务服务
     * 负责处理涉及数据库事务的操作
     * 分离到单独服务类中以支持正确的事务代理行为
     *
     * <AUTHOR>
     */
    @Service
    @Transactional
    public static class ImpairmentReserveTransactionalService {

        private static final Logger logger = LoggerFactory.getLogger(ImpairmentReserveTransactionalService.class);

        // 查询参数常量
        private static final String PARAM_YEAR = "year";
        private static final String PARAM_MONTH = "month";
        private static final String SQL_WHERE_YEAR_MONTH = " r.id.year = :" + PARAM_YEAR + " AND r.id.month = :" + PARAM_MONTH;

        @PersistenceContext
        private EntityManager entityManager;

        private final ImpairmentReserveRepository impairmentReserveRepository;

        @Autowired
        public ImpairmentReserveTransactionalService(ImpairmentReserveRepository impairmentReserveRepository) {
            this.impairmentReserveRepository = impairmentReserveRepository;
        }

        @Transactional
        public int updateAnnualCumulativeRecovery() {
            logger.info("开始执行非诉讼表本年度累计回收金额更新");
            int totalUpdated = impairmentReserveRepository.updateAnnualCumulativeRecovery();
            logger.info("非诉讼表本年度累计回收金额更新结束，共更新记录数：" + totalUpdated);
            return totalUpdated;
        }

        /**
         * 复制上月数据到本月，同时更新上月末数据
         * 检查当前年月的数据是否存在，如果不存在则从最近的月份复制数据
         *
         * @return 新增的记录数
         */
        @Transactional
        public int copyLastMonthData() {
            // 获取当前年月
            YearMonth currentYearMonth = YearMonth.now();

            // 统计创建的记录数
            int totalCreatedRecords = 0;

            // 查找上一个有数据的月份
            YearMonth lastDataYearMonth = findLastDataYearMonth(currentYearMonth);

            if (lastDataYearMonth == null) {
                logger.warn("未找到任何历史减值准备表数据，无法进行月度更新");
                return 0;
            }

            logger.info("找到最近的减值准备表数据月份: {}", lastDataYearMonth);

            // 从找到的最近月份开始，逐月更新到当前月份
            YearMonth targetYearMonth = lastDataYearMonth.plusMonths(1);

            while (!targetYearMonth.isAfter(currentYearMonth)) {
                logger.info("开始更新 {} 月份的减值准备表数据...", targetYearMonth);

                // 从上个月复制数据并调整
                int createdCount = copyAndAdjustMonthlyData(lastDataYearMonth, targetYearMonth);

                if (createdCount > 0) {
                    logger.info("成功为 {} 创建 {} 条减值准备表记录", targetYearMonth, createdCount);
                    totalCreatedRecords += createdCount;

                    // 更新lastDataYearMonth为当前处理的月份，继续处理下一个月
                    lastDataYearMonth = targetYearMonth;
                    targetYearMonth = targetYearMonth.plusMonths(1);
                } else if (createdCount == -1) {
                    // 目标月份已有数据，跳过并继续处理下一个月
                    logger.info("月份 {} 已有数据，将继续处理下一个月", targetYearMonth);
                    targetYearMonth = targetYearMonth.plusMonths(1);
                } else {
                    logger.warn("为 {} 创建减值准备表记录失败，中止更新流程", targetYearMonth);
                    break;
                }
            }

            return totalCreatedRecords;
        }

        /**
         * 查找最近的减值准备表数据月份
         *
         * @param currentYearMonth 当前年月
         * @return 最近的有数据的年月，如果没有数据返回null
         */
        private YearMonth findLastDataYearMonth(YearMonth currentYearMonth) {
            // 最多往前查找36个月
            for (int i = 0; i < 36; i++) {
                YearMonth yearMonth = currentYearMonth.minusMonths(i);

                // 检查此年月是否有数据
                if (hasDataForYearMonth(yearMonth)) {
                    return yearMonth;
                }
            }

            return null;
        }

        /**
         * 检查指定年月是否有减值准备表数据
         *
         * @param yearMonth 需要检查的年月
         * @return 是否有数据
         */
        private boolean hasDataForYearMonth(YearMonth yearMonth) {
            String yearMonthStr = formatYearMonth(yearMonth);

            // 修改查询：同时检查年份和月份
            String jpql = "SELECT COUNT(r) FROM ImpairmentReserve r WHERE " +
                          "r.id.year = :" + PARAM_YEAR + " AND r.id.month = :" + PARAM_MONTH;

            try {
                TypedQuery<Long> query = entityManager.createQuery(jpql, Long.class);
                query.setParameter(PARAM_YEAR, yearMonth.getYear());
                query.setParameter(PARAM_MONTH, yearMonth.getMonthValue());

                Long count = query.getSingleResult();

                logger.info("查询年月 {} 的数据条数: {}", yearMonthStr, count);
                return count > 0;
            } catch (Exception e) {
                logger.error("查询年月 {} 的数据时发生错误: {}", yearMonthStr, e.getMessage());

                // 尝试使用原生SQL查询
                try {
                    String nativeQuery = "SELECT COUNT(*) FROM 减值准备表 WHERE 年份 = ? AND 月份 = ?";
                    Query nativeQueryObj = entityManager.createNativeQuery(nativeQuery);
                    nativeQueryObj.setParameter(1, yearMonth.getYear());
                    nativeQueryObj.setParameter(2, yearMonth.getMonthValue());

                    Number count = (Number) nativeQueryObj.getSingleResult();

                    logger.info("使用原生SQL查询年月 {} 的数据条数: {}", yearMonthStr, count);
                    return count.longValue() > 0;
                } catch (Exception e2) {
                    logger.error("使用原生SQL查询年月 {} 的数据时也发生错误: {}", yearMonthStr, e2.getMessage());

                    // 如果查询出错，尝试简单查询当前年月的所有数据
                    try {
                        String simpleQuery = "SELECT COUNT(r) FROM ImpairmentReserve r WHERE " + SQL_WHERE_YEAR_MONTH;
                        TypedQuery<Long> query = entityManager.createQuery(simpleQuery, Long.class);
                        query.setParameter(PARAM_YEAR, yearMonth.getYear());
                        query.setParameter(PARAM_MONTH, yearMonth.getMonthValue());

                        Long count = query.getSingleResult();

                        logger.info("简单查询年月 {}年{}月 的数据条数: {}", yearMonth.getYear(), yearMonth.getMonthValue(), count);
                        return count > 0;
                    } catch (Exception e3) {
                        logger.error("简单查询年月 {}年{}月 的数据时也发生错误: {}", yearMonth.getYear(), yearMonth.getMonthValue(), e3.getMessage());
                        return false;
                    }
                }
            }
        }

        /**
         * 格式化年月
         *
         * @param yearMonth 年月
         * @return 格式化后的年月字符串
         */
        private String formatYearMonth(YearMonth yearMonth) {
            return yearMonth.getYear() + "年" + String.format("%02d", yearMonth.getMonthValue()) + "月";
        }

        // 生成复合 key：债权人|债务人|是否涉诉|期间
        private String compositeKey(ImpairmentReserve r) {
            ImpairmentReserve.ImpairmentReserveKey k = r.getId();
            return String.join("|",
                               k.getCreditor(),
                               k.getDebtor(),
                               k.getIsLitigation(),
                               k.getPeriod()
                              );
        }

        /**
         * 更新减值准备表数据一致性
         * 实现多个功能：
         * 1. 确保计提减值金额与本月末余额相等
         * 2. 如果本月末债权余额为零，则确保本月末余额也为零
         * 3. 设置"是否全额计提坏账"字段：如果本月末债权余额 = 本月末余额，则为"是"，否则为"否"
         *
         * @return 更新的记录数量
         */
        @Transactional
        public int updateImpairmentAmountConsistency() {
            int updatedRecords = 0;
            int consistencyUpdates = 0;
            int impairmentFlagUpdates = 0;

            logger.info("开始检查减值准备表数据一致性和是否全额计提坏账");

            // 查询所有减值准备记录（不限于当月）
            String jpql = "SELECT r FROM ImpairmentReserve r";
            TypedQuery<ImpairmentReserve> query = entityManager.createQuery(jpql, ImpairmentReserve.class);

            List<ImpairmentReserve> reserveRecords = query.getResultList();
            logger.info("找到 {} 条减值准备记录需要检查", reserveRecords.size());

            for (ImpairmentReserve record : reserveRecords) {
                boolean updated = false;
                StringBuilder updateInfo = new StringBuilder();

                // 1. 检查计提减值金额与本月末余额是否相等
                BigDecimal currentMonthAmount = record.getCurrentMonthAmount();
                BigDecimal impairmentAmount = record.getImpairmentAmount();

                if (currentMonthAmount != null && (impairmentAmount == null ||
                                                   currentMonthAmount.compareTo(impairmentAmount) != 0)) {
                    // 更新计提减值金额为本月末余额
                    BigDecimal oldAmount = impairmentAmount;
                    record.setImpairmentAmount(currentMonthAmount);

                    // 记录变更信息
                    updateInfo.append("计提减值金额: ")
                            .append(oldAmount != null ? oldAmount : "null")
                            .append(" -> ")
                            .append(currentMonthAmount);

                    updated = true;
                }

                // 2. 检查如果本月末债权余额为零，则确保本月末余额也为零
                BigDecimal currentMonthBalance = record.getCurrentMonthBalance();

                if (currentMonthBalance != null && currentMonthBalance.compareTo(BigDecimal.ZERO) == 0 &&
                    currentMonthAmount != null && currentMonthAmount.compareTo(BigDecimal.ZERO) != 0) {

                    // 计算需要的本月增减金额，使得本月末余额为零
                    BigDecimal previousMonthBalance = record.getPreviousMonthBalance() != null ?
                                                      record.getPreviousMonthBalance() : BigDecimal.ZERO;
                    BigDecimal requiredChangeAmount = BigDecimal.ZERO.subtract(previousMonthBalance);

                    // 记录原值
                    BigDecimal oldMonthAmount = currentMonthAmount;
                    BigDecimal oldIncreaseDecrease = record.getCurrentMonthIncreaseDecrease();

                    // 更新值
                    record.setCurrentMonthAmount(BigDecimal.ZERO);
                    record.setCurrentMonthIncreaseDecrease(requiredChangeAmount);
                    record.setImpairmentAmount(BigDecimal.ZERO); // 同时更新计提减值金额

                    // 更新完数据后，重新赋值本月末余额和本月末债权余额
                    currentMonthAmount = BigDecimal.ZERO;

                    // 记录变更信息
                    if (updateInfo.length() > 0) {
                        updateInfo.append("; ");
                    }
                    updateInfo.append("本月末余额: ")
                            .append(oldMonthAmount)
                            .append(" -> 0; 本月增减: ")
                            .append(oldIncreaseDecrease != null ? oldIncreaseDecrease : "null")
                            .append(" -> ")
                            .append(requiredChangeAmount);

                    updated = true;
                    consistencyUpdates++;
                }

                // 3. 检查是否满足全额计提条件：
                // a. 当本月末债权余额为0时，直接填写"否"
                // b. 当本月末债权余额 = 本月末余额，且不为0时，填写"是"
                boolean isFullyImpaired = false;

                if (currentMonthBalance != null && currentMonthAmount != null) {
                    // 判断本月末债权余额是否为0
                    if (currentMonthBalance.compareTo(BigDecimal.ZERO) == 0) {
                        // 当本月末债权余额为0时，直接设置为"否"
                        isFullyImpaired = false;
                    } else {
                        // 当本月末债权余额不为0时，检查是否等于本月末余额
                        isFullyImpaired = currentMonthBalance.compareTo(currentMonthAmount) == 0;
                    }
                }

                // 更新是否全额计提坏账字段
                String newValue = isFullyImpaired ? "是" : "否";
                String oldValue = record.getIsAllImpaired();

                // 仅在需要更新时才更新
                if (oldValue == null || !oldValue.equals(newValue)) {
                    record.setIsAllImpaired(newValue);

                    // 记录变更信息
                    if (updateInfo.length() > 0) {
                        updateInfo.append("; ");
                    }
                    updateInfo.append("是否全额计提坏账: ")
                            .append(oldValue != null ? oldValue : "空")
                            .append(" -> ")
                            .append(newValue);

                    updated = true;
                    impairmentFlagUpdates++;
                }

                if (updated) {
                    // 标记更新时间
                    record.setUpdateTime(LocalDateTime.now());

                    // 更新记录
                    entityManager.merge(record);
                    updatedRecords++;

                    // 记录日志
                    logger.info("更新减值准备记录: 债权人={}, 债务人={}, 年={}, 月={}, 变更: {}",
                                record.getId().getCreditor(), record.getId().getDebtor(),
                                record.getId().getYear(), record.getId().getMonth(), updateInfo);
                }
            }

            logger.info("减值准备表数据一致性检查完成 (数据一致性更新: {}; 全额计提标记更新: {}), 共更新 {} 条记录",
                        consistencyUpdates, impairmentFlagUpdates, updatedRecords);
            return updatedRecords;
        }

        /**
         * 检查和调整减值准备表数据一致性
         * <p>
         * 1. 检查本月的上月末余额总额与上月的本月末余额总额是否相等；
         * 2. 如果不相等，逐条检查每项债权：
         * - 调整本月增减，使得 上月末余额 + 本月增减 = 本月末余额；
         * - 保持"本月末余额不得超过本月末债权余额"的校验；
         * 3. 返回更新的记录数量
         * </p>
         *
         * @return 更新的记录数量
         */
        @Transactional
        public int checkAndAdjustImpairmentBalanceConsistency() {
            YearMonth baseYearMonth = YearMonth.of(2024, 12);
            YearMonth currentYearMonth = YearMonth.now();
            int totalUpdated = 0;
            if (!hasDataForYearMonth(baseYearMonth)) {
                logger.warn("未找到基准月份{}的数据，跳过一致性检查", baseYearMonth);
                return 0;
            }
            YearMonth prevYM = baseYearMonth;
            YearMonth curYM = baseYearMonth.plusMonths(1);
            while (!curYM.isAfter(currentYearMonth)) {
                if (!hasDataForYearMonth(prevYM) || !hasDataForYearMonth(curYM)) {
                    prevYM = curYM;
                    curYM = curYM.plusMonths(1);
                    continue;
                }
                List<ImpairmentReserve> prevList = getReservesForYearMonth(prevYM);
                List<ImpairmentReserve> curList = getReservesForYearMonth(curYM);
                // sum 上月的本月末余额
                BigDecimal sumPrevCurrentAmount = prevList.stream()
                        .map(ImpairmentReserve::getCurrentMonthAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // sum 当月的上月末余额
                BigDecimal sumCurPrevBalance = curList.stream()
                        .map(ImpairmentReserve::getPreviousMonthBalance)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (sumPrevCurrentAmount.compareTo(sumCurPrevBalance) != 0) {
                    logger.info("{} 不一致: 上月合计={} vs 当月合计={}，开始逐条修正", curYM, sumPrevCurrentAmount, sumCurPrevBalance);
                    // 构造 Map
                    Map<String, ImpairmentReserve> prevMap = prevList.stream()
                            .collect(Collectors.toMap(this::compositeKey, Function.identity()));
                    for (ImpairmentReserve cur : curList) {
                        String key = compositeKey(cur);
                        ImpairmentReserve prev = prevMap.get(key);
                        if (prev == null) continue;
                        BigDecimal targetPrevBalance = prev.getCurrentMonthAmount();
                        BigDecimal oldPrev = cur.getPreviousMonthBalance();
                        if (targetPrevBalance == null) targetPrevBalance = BigDecimal.ZERO;
                        // 调整本月增减： newInc = 本月末余额 - targetPrevBalance
                        BigDecimal endAmount = cur.getCurrentMonthAmount() != null ? cur.getCurrentMonthAmount() : BigDecimal.ZERO;
                        BigDecimal newInc = endAmount.subtract(targetPrevBalance);
                        cur.setPreviousMonthBalance(targetPrevBalance);
                        cur.setCurrentMonthIncreaseDecrease(newInc);
                        // 保持本月末余额不超过本月末债权余额
                        BigDecimal maxAllowed = cur.getCurrentMonthBalance();
                        if (maxAllowed != null && endAmount.compareTo(maxAllowed) > 0) {
                            cur.setCurrentMonthAmount(maxAllowed);
                            cur.setCurrentMonthIncreaseDecrease(maxAllowed.subtract(targetPrevBalance));
                        }
                        entityManager.merge(cur);
                        totalUpdated++;
                    }
                }
                prevYM = curYM;
                curYM = curYM.plusMonths(1);
            }
            logger.info("一致性检查完成，共更新 {} 条记录", totalUpdated);
            return totalUpdated;
        }

        /**
         * 更新减值准备记录的本月增减和本月末余额
         * 实现以下逻辑：
         * - 如果上月末余额大于本月末债权余额：
         * 调整本月增减和本月末余额，使得本月末余额等于本月末债权余额
         * <p>
         * 注意：此方法不再计算或设置本年度累计回款金额，该值由updateAnnualRecoveryForAllTables方法统一计算
         *
         * @return 更新的记录数量
         */
        @Transactional
        public int updateImpairmentAnnualRecovery() {
            int updatedRecords = 0;

            logger.info("开始执行减值准备记录更新");

            // 查询当前年份的所有减值准备记录
            YearMonth currentYearMonth = YearMonth.now();
            int currentYear = currentYearMonth.getYear();

            String jpql = "SELECT r FROM ImpairmentReserve r WHERE r.id.year = :year";
            TypedQuery<ImpairmentReserve> query = entityManager.createQuery(jpql, ImpairmentReserve.class);
            query.setParameter("year", currentYear);

            List<ImpairmentReserve> reserveRecords = query.getResultList();
//            logger.info("找到 {} 条{}年的减值准备记录需要检查", reserveRecords.size(), currentYear);

            for (ImpairmentReserve record : reserveRecords) {
                boolean updated = false;
                StringBuilder updateInfo = new StringBuilder();

                // 获取上月末余额和本月末债权余额
                BigDecimal previousMonthBalance = record.getPreviousMonthBalance();
                BigDecimal currentMonthBalance = record.getCurrentMonthBalance();
                BigDecimal currentMonthAmount = record.getCurrentMonthAmount();

                if (previousMonthBalance == null || currentMonthBalance == null) {
//                    logger.warn("记录数据不完整，跳过处理: 债务人={}, 上月末余额={}, 本月末债权余额={}",
//                                record.getId().getDebtor(), previousMonthBalance, currentMonthBalance);
                    continue;
                }

                // 记录原值，用于日志
                BigDecimal oldIncreaseDecrease = record.getCurrentMonthIncreaseDecrease();

                // 处理更新逻辑，只在上月末余额大于本月末债权余额时进行调整
                if (previousMonthBalance.compareTo(currentMonthBalance) > 0) {
                    // 计算本月增减 = 本月末债权余额 - 上月末余额
                    // 这样会导致本月增减为负数，使得：上月末余额 + 本月增减 = 本月末余额 = 本月末债权余额
                    BigDecimal newIncreaseDecrease = currentMonthBalance.subtract(previousMonthBalance);

                    // 同时更新本月末余额等于本月末债权余额
                    BigDecimal oldMonthAmount = currentMonthAmount;
                    record.setCurrentMonthAmount(currentMonthBalance);

                    // 更新本月增减值
                    record.setCurrentMonthIncreaseDecrease(newIncreaseDecrease);

                    // 记录日志内容
                    updateInfo.append("本月增减: ")
                            .append(oldIncreaseDecrease != null ? oldIncreaseDecrease : "null")
                            .append(" -> ")
                            .append(newIncreaseDecrease);
                    updateInfo.append("; 本月末余额: ")
                            .append(oldMonthAmount != null ? oldMonthAmount : "null")
                            .append(" -> ")
                            .append(currentMonthBalance);

                    updated = true;
                }

                if (updated) {
                    // 标记更新时间
                    record.setUpdateTime(LocalDateTime.now());

                    // 更新记录
                    entityManager.merge(record);
                    updatedRecords++;

                    // 记录日志
//                    logger.info("更新减值准备记录: 债权人={}, 债务人={}, 年={}, 月={}, 变更: {}",
//                                record.getId().getCreditor(), record.getId().getDebtor(),
//                                record.getId().getYear(), record.getId().getMonth(), updateInfo);
                }
            }

            logger.info("减值准备记录更新完成, 共更新 {} 条记录", updatedRecords);
            return updatedRecords;
        }

        /**
         * 从源月份复制减值准备表数据到目标月份，并进行数据调整
         *
         * @param sourceYearMonth 源月份
         * @param targetYearMonth 目标月份
         * @return 创建的记录数
         */
        private int copyAndAdjustMonthlyData(YearMonth sourceYearMonth, YearMonth targetYearMonth) {
            // 检查目标月份是否已有数据
            boolean hasData = hasDataForYearMonth(targetYearMonth);
//            logger.info("检查目标月份 {} 是否已有数据: {}", targetYearMonth, hasData);

            if (hasData) {
//                logger.info("目标月份 {} 已存在数据，跳过复制", targetYearMonth);
                // 修改逻辑：不再返回0，而是返回-1表示跳过
                return -1;
            }

            // 获取源月份的数据
            List<ImpairmentReserve> sourceReserves = getReservesForYearMonth(sourceYearMonth);

            if (sourceReserves.isEmpty()) {
                logger.warn("源月份 {} 没有数据，无法复制", sourceYearMonth);
                return 0;
            }

            // 创建新记录并调整数据
            List<ImpairmentReserve> newReserves = new ArrayList<>();

            for (ImpairmentReserve sourceReserve : sourceReserves) {
                // 创建新记录
                ImpairmentReserve newReserve = new ImpairmentReserve();

                // 创建新主键
                ImpairmentReserve.ImpairmentReserveKey newKey = new ImpairmentReserve.ImpairmentReserveKey();
                ImpairmentReserve.ImpairmentReserveKey oldKey = sourceReserve.getId();
                if (oldKey != null) {
                    newKey.setCreditor(oldKey.getCreditor());
                    newKey.setDebtor(sourceReserve.getId().getDebtor());
                    // 注意：复制period字段，如果ImpairmentReserveKey没有setPeriod方法，
                    // 则需要确保字段在构造函数中设置或以其他方式处理
                    // sequence字段已被移除，不再调用setSequence方法
                    newKey.setIsLitigation(sourceReserve.getId().getIsLitigation());
                    // 设置年月和期间
                    newKey.setYear(sourceReserve.getId().getYear());
                    newKey.setMonth(sourceReserve.getId().getMonth());
                    newKey.setPeriod(oldKey.getPeriod());
                    newReserve.setId(newKey);
                } else {
                    logger.warn("源记录主键为空，跳过复制");
                    continue;
                }

                // 设置基本信息
                newKey.setYear(targetYearMonth.getYear());
                newKey.setMonth(targetYearMonth.getMonthValue());
                newReserve.setCaseName(sourceReserve.getCaseName());
                newReserve.setSubjectName(sourceReserve.getSubjectName());
                newReserve.setAmount20220430(sourceReserve.getAmount20220430());
                newReserve.setImpairmentAmount(sourceReserve.getImpairmentAmount());
                newReserve.setInitialImpairmentDate(sourceReserve.getInitialImpairmentDate());
                newReserve.setAnnualRecoveryTarget(sourceReserve.getAnnualRecoveryTarget());

                // 检查是否跨年，如果跨年则年度累计回收金额重置为0
                if (targetYearMonth.getYear() != sourceYearMonth.getYear()) {
                    // 跨年了，年度累计回收重置为0
                    newReserve.setAnnualCumulativeRecovery(BigDecimal.ZERO);
//                    logger.info("减值准备表数据检测到年份变更: {}年 -> {}年，年度累计回收金额重置为0",
//                                sourceYearMonth.getYear(), targetYearMonth.getYear());
                } else {
                    // 同年内，复制原有累计值
                    newReserve.setAnnualCumulativeRecovery(sourceReserve.getAnnualCumulativeRecovery());
                }

                newReserve.setManagementCompany(sourceReserve.getManagementCompany());
                newReserve.setIsAllImpaired(sourceReserve.getIsAllImpaired());
                newReserve.setDueDate(sourceReserve.getDueDate());

                // 数据调整：将上月末数据设置为本月上月末数据
                newReserve.setPreviousMonthBalance(sourceReserve.getCurrentMonthAmount());

                // 保持当月数据和上月相同，直到有新的更新
                newReserve.setCurrentMonthAmount(sourceReserve.getCurrentMonthAmount());
                newReserve.setCurrentMonthBalance(sourceReserve.getCurrentMonthBalance());

                // 新月份的增减默认为0
                newReserve.setCurrentMonthIncreaseDecrease(BigDecimal.ZERO);
                newReserve.setCurrentMonthNewDebt(BigDecimal.ZERO);
                newReserve.setCurrentMonthDisposeDebt(BigDecimal.ZERO);

                // 只有没有备注的行才添加新备注，有备注的保持原样
                if (sourceReserve.getRemark() == null || sourceReserve.getRemark().isEmpty()) {
                    newReserve.setRemark("");
                } else {
                    // 完全保留原始备注，不修改
                    newReserve.setRemark(sourceReserve.getRemark());
                }

                // 添加到集合
                newReserves.add(newReserve);
            }

            // 批量持久化新记录
            int count = 0;
            for (ImpairmentReserve reserve : newReserves) {
                try {
                    entityManager.persist(reserve);
                    count++;

                    // 每100条刷新一次，防止内存溢出
                    if (count % 100 == 0) {
                        entityManager.flush();
                        entityManager.clear();
//                        logger.info("已处理 {} 条记录...", count);
                    }
                } catch (Exception e) {
                    logger.error("保存记录时发生错误: {}，债务人：{}", e.getMessage(), reserve.getId().getDebtor());
                }
            }

            // 最后一次刷新
            try {
                entityManager.flush();
            } catch (Exception e) {
                logger.error("最终刷新时发生错误: {}", e.getMessage());
            }

            return count;
        }

        /**
         * 获取指定年月的减值准备表数据
         *
         * @param yearMonth 年月
         * @return 减值准备表记录列表
         */
        private List<ImpairmentReserve> getReservesForYearMonth(YearMonth yearMonth) {
            // 修改为同时匹配年份和月份
            String jpql = "SELECT r FROM ImpairmentReserve r WHERE" + SQL_WHERE_YEAR_MONTH;

            try {
                TypedQuery<ImpairmentReserve> query = entityManager.createQuery(jpql, ImpairmentReserve.class);
                query.setParameter("year", yearMonth.getYear());
                query.setParameter("month", yearMonth.getMonthValue());

                List<ImpairmentReserve> results = query.getResultList();
//                logger.info("查询 {}年{}月 的数据返回 {} 条记录", yearMonth.getYear(), yearMonth.getMonthValue(), results.size());
                return results;
            } catch (Exception e) {
//                logger.error("查询 {}年{}月 月数据时发生错误: {}", yearMonth.getYear(), yearMonth.getMonthValue(), e.getMessage());
                return new ArrayList<>();
            }
        }
    }
}
