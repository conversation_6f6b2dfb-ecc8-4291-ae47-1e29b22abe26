package com.laoshu198838.service.import_export;

// 从kingdee模块导入YamlUtils
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

// import static com.laoshu198838.KingdeeService.getCompanyInfo;
// import static com.laoshu198838.ReportDataService.getSingleReportData;
import com.laoshu198838.util.file.YamlUtils;

/**
 * 财务数据导入服务
 * 负责从金蝶系统导入财务报表数据
 *
 * <AUTHOR>
 */
@Service
public class FinancialDataImportService {

    private static final Logger logger = LoggerFactory.getLogger(FinancialDataImportService.class);

    /**
     * 保存资产负债表数据
     */
    public void saveBalanceSheetData() {
        logger.info("资产负债表数据保存成功");
    }

    /**
     * 保存利润表数据
     */
    public void saveIncomeStatementData() {
        logger.info("利润表数据保存成功");
    }

    /**
     * 保存现金流量表数据
     */
    public void saveCashFlowData() {
        try {
            // 读取公司信息
            // List<Map<String, String>> companyInfo = getCompanyInfo();
            List<Map<String, String>> companyInfo = new java.util.ArrayList<>();

            int total = companyInfo.size();
            // 初始化计数器
            int current = 0;
            String year = "FY2024";
            String[] reportType = {"3"};

            // 遍历每个公司
            for (Map<String, String> company : companyInfo) {
                current++;
                String name = company.get("name");
                String entity = company.get("entity");
                logger.info("处理公司进度: {} / {} {}", current, total, name);

                // 读取月份信息
                @SuppressWarnings("unchecked")
                List<Map<String, String>> months = YamlUtils.readSingleLevelYaml("params.yaml", "months") instanceof List ?
                        (List<Map<String, String>>) YamlUtils.readSingleLevelYaml("params.yaml", "months") :
                        null;

                // 遍历每个月份
                if (months == null) {
                    throw new RuntimeException("months is null");
                }

                for (Map<String, String> monthDict : months) {
                    String monthKey = monthDict.keySet().iterator().next();
                    String month = monthDict.get(monthKey);

                    try {
                        // 打印当前处理的月份键
                        logger.info("处理月份: {}", monthKey);
                        // 获取报告数据
                        // Map<String, Double> reportData = getSingleReportData(entity, year, month, reportType);
                        Map<String, Double> reportData = new java.util.HashMap<>();

                        // 输出提取的账号和对应的数字
                        logger.info("提取的账号数据:");
                        for (Map.Entry<String, Double> entry : reportData.entrySet()) {
                            logger.info("{}: {}", entry.getKey(), entry.getValue());
                        }
                    } catch (IllegalArgumentException | NullPointerException e) {
                        // 参数或空指针错误
                        logger.error("处理公司 {} 的 {} 月份数据时参数错误: {}", name, monthKey, e.getMessage());
                    } catch (RuntimeException e) {
                        // 运行时错误
                        logger.error("处理公司 {} 的 {} 月份数据时运行时错误: {}", name, monthKey, e.getMessage());
                    } catch (Exception e) {
                        // 其他错误
                        logger.error("处理公司 {} 的 {} 月份数据时出错: {}", name, monthKey, e.getMessage());
                    }
                }
            }
        } catch (IllegalArgumentException | NullPointerException e) {
            // 参数或空指针错误
            logger.error("参数错误: {}", e.getMessage(), e);
        } catch (RuntimeException e) {
            // 运行时错误
            logger.error("运行时错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            // 其他错误
            logger.error("发生错误: {}", e.getMessage(), e);
        }
        logger.info("现金流量表数据保存成功");
    }

    /**
     * 保存所有财务数据
     */
    public void saveAllFinancialData() {
        saveBalanceSheetData();
        saveIncomeStatementData();
        saveCashFlowData();
    }
}
