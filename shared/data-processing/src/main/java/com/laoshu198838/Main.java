package com.laoshu198838;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.SQLException;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class Main {

    // 连接到 MySQL 数据库
    public static Connection connectToMySQL(String host, String user, String password, String database) {
        try {
            String url = "jdbc:mysql://" + host + "/" + database + "?useSSL=false&serverTimezone=UTC";
            Connection connection = DriverManager.getConnection(url, user, password);
            System.out.println("成功连接到数据库");
            return connection;
        } catch (SQLException e) {
            System.err.println("连接错误: " + e.getMessage());
            return null;
        }
    }

    // 确保表存在
    public static void ensureTableExists(Connection connection, String tableName) throws SQLException {
        String createTableQuery = "CREATE TABLE IF NOT EXISTS " + tableName + " (" +
                "科目 VARCHAR(190) PRIMARY KEY" +
                ");";
        try (Statement statement = connection.createStatement()) {
            statement.execute(createTableQuery);
        }
    }

    // 确保列存在
    public static void ensureColumnExists(Connection connection, String tableName, String columnName) throws SQLException {
        String checkColumnQuery = "SHOW COLUMNS FROM " + tableName + " LIKE ?";
        try (PreparedStatement ps = connection.prepareStatement(checkColumnQuery)) {
            ps.setString(1, columnName);
            try (ResultSet rs = ps.executeQuery()) {
                if (!rs.next()) {
                    String alterTableQuery = "ALTER TABLE " + tableName + " ADD COLUMN " + columnName + " FLOAT DEFAULT NULL";
                    try (Statement statement = connection.createStatement()) {
                        statement.execute(alterTableQuery);
                    }
                }
            }
        }
    }

    // 插入或更新数据
    public static void insertOrUpdateData(Connection connection, String tableName, String columnName, Map<String, Double> data) throws SQLException {
        String selectQuery = "SELECT 科目 FROM " + tableName + " WHERE 科目 = ?";
        String insertQuery = "INSERT INTO " + tableName + " (科目, " + columnName + ") VALUES (?, ?)";
        String updateQuery = "UPDATE " + tableName + " SET " + columnName + " = ? WHERE 科目 = ?";

        for (Map.Entry<String, Double> entry : data.entrySet()) {
            String 科目 = entry.getKey();
            Double value = entry.getValue();

            try (PreparedStatement selectPs = connection.prepareStatement(selectQuery)) {
                selectPs.setString(1, 科目);
                try (ResultSet rs = selectPs.executeQuery()) {
                    if (rs.next()) {
                        // 更新数据
                        try (PreparedStatement updatePs = connection.prepareStatement(updateQuery)) {
                            updatePs.setDouble(1, value);
                            updatePs.setString(2, 科目);
                            updatePs.executeUpdate();
                        }
                    } else {
                        // 插入数据
                        try (PreparedStatement insertPs = connection.prepareStatement(insertQuery)) {
                            insertPs.setString(1, 科目);
                            insertPs.setDouble(2, value);
                            insertPs.executeUpdate();
                        }
                    }
                }
            }
        }
    }

    // 主方法
    public static void main(String[] args) {
        // 数据库信息
        String host = "localhost";
        String user = "root";
        String password = "Zlb&198838";
        String database = "kingdee";
        String tableName = "Balancesheet";

        // 数据示例
        Map<String, Double> data = Map.of(
                "购实商品、接受劳务支付的现金", 77374622.37,
                "经营活动现金流入小计", 86195156.67
        );

        // 动态列名
        String columnName = "2024年12月";

        // 连接到数据库
        try (Connection connection = connectToMySQL(host, user, password, database)) {
            if (connection == null) return;

            // 确保表存在
            ensureTableExists(connection, tableName);

            // 确保列存在
            ensureColumnExists(connection, tableName, columnName);

            // 插入或更新数据
            insertOrUpdateData(connection, tableName, columnName, data);

        } catch (SQLException e) {
            System.err.println("数据库操作错误: " + e.getMessage());
            System.out.println("数据库操作错误: " + e.getMessage());



        }
    }
}