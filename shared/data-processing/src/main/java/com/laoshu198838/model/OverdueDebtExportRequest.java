package com.laoshu198838.model;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.HashMap;

/**
 * 逾期债权导出请求模型
 * 封装导出逾期债权报表所需的参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OverdueDebtExportRequest {

    /**
     * 年份
     */
    private int year;

    /**
     * 月份
     */
    private int month;

    /**
     * 金额限制（万元）
     */
    private int amount;

    /**
     * 额外参数
     */
    private Map<String, Object> parameters;

    /**
     * 构造函数
     */
    public OverdueDebtExportRequest(int year, int month, int amount) {
        this.year = year;
        this.month = month;
        this.amount = amount;
        this.parameters = new HashMap<>();
    }

    /**
     * 构造函数（字符串参数）
     */
    public OverdueDebtExportRequest(String year, String month, String amount) {
        this.year = parseIntSafely(year, 2025);
        this.month = parseIntSafely(month, 1);
        this.amount = parseIntSafely(amount, 10);
        this.parameters = new HashMap<>();
    }

    /**
     * 添加参数
     */
    public OverdueDebtExportRequest addParameter(String key, Object value) {
        if (parameters == null) {
            parameters = new HashMap<>();
        }
        parameters.put(key, value);
        return this;
    }

    /**
     * 获取参数
     */
    public Object getParameter(String key) {
        return parameters != null ? parameters.get(key) : null;
    }

    /**
     * 获取参数（带默认值）
     */
    public Object getParameter(String key, Object defaultValue) {
        Object value = getParameter(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 安全解析整数
     */
    private int parseIntSafely(String value, int defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return year > 0 && month >= 1 && month <= 12 && amount > 0;
    }

    /**
     * 获取文件名
     */
    public String getFileName() {
        return String.format("%d年%02d月逾期债权清收统计表-万润科技汇总.xlsx", year, month);
    }

    /**
     * 转换为Map（用于模板填充）
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("year", year);
        map.put("month", month);
        map.put("amount", amount);
        map.put("fileName", getFileName());

        if (parameters != null) {
            map.putAll(parameters);
        }

        return map;
    }

    // 手动添加getter方法（解决Lombok兼容性问题）
    public int getYear() {
        return year;
    }

    public int getMonth() {
        return month;
    }

    public int getAmount() {
        return amount;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    // 手动添加setter方法
    public void setYear(int year) {
        this.year = year;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    @Override
    public String toString() {
        return String.format("OverdueDebtExportRequest{year=%d, month=%d, amount=%d}",
                           year, month, amount);
    }
}
