package com.laoshu198838.export.sql;

import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 表10专用SQL查询构建器
 * 债权处置明细表
 * 
 * <AUTHOR>
 */
@Component
public class Table10QueryBuilder implements QueryBuilder {
    
    @Override
    public String buildSql(String tableName, int year, int month, Map<String, Object> params) {
        return """
              WITH
                处置数据_1到当月 AS (
                  SELECT
                    债权人 COLLATE utf8mb4_general_ci AS 债权人,
                    债务人 COLLATE utf8mb4_general_ci AS 债务人,
                    是否涉诉 COLLATE utf8mb4_general_ci AS 是否涉诉,
                    期间 COLLATE utf8mb4_general_ci AS 期间,
                    管理公司 COLLATE utf8mb4_general_ci AS 管理公司,
                    SUM(每月处置金额) AS 累计处置金额,
                    SUM(现金处置) AS 累计现金回款,
                    MAX(CASE WHEN 分期还款 > 0 THEN '有分期还款' ELSE '' END) AS 分期还款说明,
                    MAX(CASE WHEN 资产抵债 > 0 THEN '有资产抵债' ELSE '' END) AS 资产抵债说明,
                    备注 COLLATE utf8mb4_general_ci AS 备注
                  FROM 处置表
                  WHERE 年份 = ? AND 月份 BETWEEN 1 AND ?
                  GROUP BY 债权人, 债务人, 是否涉诉, 期间, 管理公司, 备注
                ),
                处置数据_1到上月 AS (
                  SELECT
                    债权人 COLLATE utf8mb4_general_ci AS 债权人,
                    债务人 COLLATE utf8mb4_general_ci AS 债务人,
                    是否涉诉 COLLATE utf8mb4_general_ci AS 是否涉诉,
                    期间 COLLATE utf8mb4_general_ci AS 期间,
                    管理公司 COLLATE utf8mb4_general_ci AS 管理公司,
                    SUM(每月处置金额) AS 上月处置金额,
                    SUM(现金处置) AS 上月现金回款,
                    备注 COLLATE utf8mb4_general_ci AS 备注
                  FROM 处置表
                  WHERE 年份 = ? AND 月份 BETWEEN 1 AND ?
                  GROUP BY 债权人, 债务人, 是否涉诉, 期间, 管理公司, 备注
                ),
                合并数据 AS (
                  SELECT
                    a.债权人, a.债务人, a.是否涉诉, a.期间, a.管理公司,
                    a.累计处置金额, a.累计现金回款,
                    b.上月处置金额, b.上月现金回款,
                    CASE
                      WHEN a.分期还款说明 <> '' OR a.资产抵债说明 <> ''
                      THEN CONCAT(COALESCE(a.备注, ''), ' ', a.分期还款说明, ' ', a.资产抵债说明)
                      ELSE COALESCE(a.备注, b.备注, '')
                    END AS 备注
                  FROM 处置数据_1到当月 a
                  LEFT JOIN 处置数据_1到上月 b
                    ON a.债权人 = b.债权人 AND a.债务人 = b.债务人
                    AND a.是否涉诉 = b.是否涉诉 AND a.期间 = b.期间
                  UNION
                  SELECT
                    b.债权人, b.债务人, b.是否涉诉, b.期间, b.管理公司,
                    NULL AS 累计处置金额, NULL AS 累计现金回款,
                    b.上月处置金额, b.上月现金回款, b.备注
                  FROM 处置数据_1到上月 b
                  LEFT JOIN 处置数据_1到当月 a
                    ON a.债权人 = b.债权人 AND a.债务人 = b.债务人
                    AND a.是否涉诉 = b.是否涉诉 AND a.期间 = b.期间
                  WHERE a.债权人 IS NULL
                ),
                减值数据 AS (
                  SELECT
                    债权人 COLLATE utf8mb4_general_ci AS 债权人,
                    债务人 COLLATE utf8mb4_general_ci AS 债务人,
                    是否涉诉 COLLATE utf8mb4_general_ci AS 是否涉诉,
                    期间 COLLATE utf8mb4_general_ci AS 期间,
                    本月末债权余额
                  FROM 减值准备表
                  WHERE 年份 = ? AND 月份 = ?
                )
              SELECT
                m.债权人,
                m.债务人, 
                COALESCE(SUM(m.累计处置金额), 0) AS 累计处置金额,
                COALESCE(SUM(m.累计现金回款), 0) AS 累计现金回款,
                m.是否涉诉,
                CASE WHEN COALESCE(MIN(r.本月末债权余额), 0) = 0 THEN '是' ELSE '否' END AS 是否完全处置,
                COALESCE(SUM(m.上月处置金额), 0) AS 上月处置金额,
                COALESCE(SUM(m.上月现金回款), 0) AS 上月现金回款,
                m.期间,
                m.管理公司,
                GROUP_CONCAT(DISTINCT NULLIF(m.备注, '') SEPARATOR '; ') AS 备注
              FROM 合并数据 m
              LEFT JOIN 减值数据 r
                ON m.债权人 = r.债权人
                AND m.债务人 = r.债务人
                AND m.是否涉诉 = r.是否涉诉
                AND m.期间 = r.期间
              GROUP BY m.债权人, m.债务人, m.是否涉诉, m.期间, m.管理公司
              ORDER BY m.债权人, m.债务人;
              """;
    }
    
    @Override
    public List<Object> buildParameters(String tableName, int year, int month, Map<String, Object> params) {
        List<Object> parameters = new ArrayList<>();
        parameters.add(year);        // 年份，用于1到当月汇总
        parameters.add(month);       // 月份，用于1到当月汇总
        parameters.add(year);        // 年份，用于1到上月汇总
        parameters.add(month - 1);   // 上月（用于处置数据对比）
        parameters.add(year);        // 减值准备表年份
        parameters.add(month);       // 减值准备表月份
        return parameters;
    }
    
    @Override
    public boolean supports(String tableName) {
        return "表10-债权处置明细表".equals(tableName);
    }
}