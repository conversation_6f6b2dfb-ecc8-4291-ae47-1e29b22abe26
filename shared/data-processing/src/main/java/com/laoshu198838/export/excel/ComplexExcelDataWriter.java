package com.laoshu198838.export.excel;

import com.aspose.cells.Cell;
import com.aspose.cells.Cells;
import com.aspose.cells.Worksheet;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.laoshu198838.util.database.SqlUtils.getResultSetColumns;
import static com.laoshu198838.util.date.DateUtils.getPreviousYearAndMonth;
import static com.laoshu198838.util.file.ExcelUtils.putValueWithFormat;
import static com.laoshu198838.util.file.YamlUtils.readSingleLevelYaml;

/**
 * 复杂Excel数据写入器
 * 用于复杂表格（表7、表9、表10）的数据写入
 * 
 * <AUTHOR>
 */
@Component
public class ComplexExcelDataWriter implements ExcelDataWriter {
    
    private static final Set<String> SUPPORTED_TABLES = Set.of(
        "表7-10万元及以下应收债权明细表", 
        "表9-新增逾期债权明细表", 
        "表10-债权处置明细表"
    );
    
    @Override
    public void writeData(Worksheet worksheet, ResultSet resultSet, String tableName, 
                         int year, int month) throws Exception {
        Cells cells = worksheet.getCells();

        // 获取当前年份和月份对应的列映射
        AbstractMap.SimpleEntry<Map<String, String>, int[]> monthMapping = getColumnSet(year, month, resultSet);
        
        // 遍历查询结果，将数据写入 Excel
        while (resultSet.next()) {
            // 根据"期间"列，确定要写入 Excel 的目标行
            String periodValue = resultSet.getString("期间");
            if (periodValue == null || periodValue.isEmpty()) {
                continue; // 期间为空，跳过
            }
            
            // 获取目标行（针对不同表格使用不同的定位逻辑）
            int targetRow;
            if ("表9-新增逾期债权明细表".equals(tableName)) {
                targetRow = findTargetRowByPeriodForTable9(cells, periodValue);
            } else {
                targetRow = findTargetRowByPeriod(cells, periodValue);
            }

            // 判断是否需要向上复制一行（用于格式保持）
            copyRowFormatUp(cells, targetRow);
            
            // 将数据库中的有效列数据写入 Excel
            insertRowData(tableName, cells, targetRow, resultSet, monthMapping);
        }
    }
    
    @Override
    public boolean supports(String tableName) {
        return SUPPORTED_TABLES.contains(tableName);
    }
    
    /**
     * 生成上月数据列的映射表，并返回上月的年份和月份信息
     */
    private static AbstractMap.SimpleEntry<Map<String, String>, int[]> getColumnSet(int year, int month, ResultSet rs) throws SQLException {
        int[] prevYearAndMonth = getPreviousYearAndMonth(year, month);
        // 上月的月份数
        String prevMonthStr = prevYearAndMonth[1] + "月";

        Set<String> columnSet = getResultSetColumns(rs);
        Map<String, String> monthMapping = new HashMap<>();
        if (columnSet.contains(prevMonthStr)) {
            monthMapping.put("上月", prevMonthStr);
        }

        return new AbstractMap.SimpleEntry<>(monthMapping, prevYearAndMonth);
    }
    
    /**
     * 根据"期间"列的值，决定要写入 Excel 的哪一行（一般表格）
     */
    private int findTargetRowByPeriod(Cells cells, String periodValue) throws Exception {
        // 默认起始行
        int startRow = 4;
        // Excel 第二列（索引从 0 开始）
        int searchColumn = 1;

        try {
            switch (periodValue) {
                case "2022年430债权":
                    break;
                case "2022年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2022年430新增小计", searchColumn) + 1;
                    break;
                case "2023年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2022年新增小计", searchColumn) + 1;
                    break;
                case "2024年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2023年新增小计", searchColumn) + 1;
                    break;
                case "2025年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2024年新增小计", searchColumn) + 1;
                    break;
                case "存量新增债权":
                    break;
                default:
                    throw new IllegalArgumentException("未知的期间值: " + periodValue);
            }
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Excel 结构异常: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new Exception("查找目标行时发生错误，期间值: " + periodValue, e);
        }

        return findFirstEmptyRow(cells, startRow, searchColumn);
    }
    
    /**
     * 表9专用的查找目标行方法
     */
    private int findTargetRowByPeriodForTable9(Cells cells, String periodValue) throws Exception {
        // 默认起始行
        int startRow = 4;
        // Excel 第二列（索引从 0 开始）
        int searchColumn = 1;
        try {
            switch (periodValue) {
                case "存量新增债权":
                    break;
                case "2025年新增债权":
                    startRow = findRowByTextOrThrow(cells, "存量新增小计", searchColumn) + 1;
                    break;
                default:
                    throw new IllegalArgumentException("未知的期间值: " + periodValue);
            }
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Excel 结构异常: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new Exception("查找目标行时发生错误，期间值: " + periodValue, e);
        }

        return findFirstEmptyRow(cells, startRow, searchColumn);
    }
    
    /**
     * 在 Excel 中查找指定文本所在的行，若找不到则抛出异常
     */
    private int findRowByTextOrThrow(Cells cells, String searchText, int searchColumn) throws Exception {
        int rowIndex = findRowByText(cells, searchText, searchColumn);
        if (rowIndex == -1) {
            throw new Exception("未在 Excel 中找到文本: " + searchText);
        }
        return rowIndex;
    }
    
    /**
     * 在 Excel 中查找指定文本所在的行
     */
    private int findRowByText(Cells cells, String searchText, int searchColumn) {
        int maxRows = cells.getMaxDataRow();
        for (int row = 0; row <= maxRows; row++) {
            Cell cell = cells.get(row, searchColumn);
            if (cell != null && cell.getValue() != null) {
                String cellValue = cell.getStringValue().trim();
                if (searchText.equals(cellValue)) {
                    return row;
                }
            }
        }
        return -1;
    }
    
    /**
     * 从指定起始行查找第一个空单元格的行号
     */
    private int findFirstEmptyRow(Cells cells, int startRow, int colIndex) {
        int maxRows = cells.getMaxDataRow();
        for (int row = startRow; row <= maxRows; row++) {
            Cell cell = cells.get(row, colIndex);
            if (cell == null || cell.getValue() == null || cell.getStringValue().trim().isEmpty()) {
                return row;
            }
        }
        // 若没有空行，返回新行
        return maxRows + 1;
    }
    
    /**
     * 复制上一行的格式到当前行（如果当前行下没有空行）
     */
    private void copyRowFormatUp(Cells cells, int targetRow) throws Exception {
        if (targetRow <= 0) {
            return;
        }
        // 获取目标行第二列（索引1）的单元格
        Cell targetCell = cells.get(targetRow + 1, 1);
        // 如果目标单元格不为空，则复制上一行的格式到目标行
        if (targetCell != null && targetCell.getValue() != null) {
            // 插入一行到 targetRow 位置（即在当前行上方插入新行）
            cells.insertRow(targetRow);
            // 复制当前行（原来的当前行现在下移为 targetRow+1）的格式和内容到新插入的行 targetRow
            cells.copyRow(cells, targetRow + 1, targetRow);
        }
    }
    
    /**
     * 将数据库中的有效列数据写入 Excel 行
     */
    private void insertRowData(
            String databaseTableName,
            Cells cells,
            int rowIndex,
            ResultSet rs,
            AbstractMap.SimpleEntry<Map<String, String>, int[]> monthMappingEntry
                              ) throws Exception {
        // 读取 YAML 配置中的列映射
        @SuppressWarnings("unchecked")
        Map<String, Integer> columnMapping = (Map<String, Integer>) readSingleLevelYaml("columnmapping.yaml", databaseTableName);

        // 获取列名映射和上月的年份、月份
        assert columnMapping != null;
        Map<String, String> monthMapping = monthMappingEntry.getKey();

        // 插入序号
        insertSequenceNumber(cells, rowIndex);
        // 遍历 columnMapping，将数据填充到 Excel
        for (Map.Entry<String, Integer> entry : columnMapping.entrySet()) {
            String columnName = entry.getKey();
            int columnIndex = entry.getValue();
            // 获取目标单元格
            Cell cell = cells.get(rowIndex, columnIndex);

            // 处理数据映射
            String value;
            if ("上月".equals(columnName)) {
                String mappedColumn = monthMapping.get("上月");
                value = (mappedColumn != null) ? rs.getString(mappedColumn) : "0";
            } else {
                value = rs.getString(columnName);
            }

            // 特殊处理"本年度累计回收"字段，确保为null时显示为0
            if ("本年度累计回收".equals(columnName) || "累计清收".equals(columnName)) {
                if (value == null || value.isEmpty()) {
                    value = "0";
                }
            } else if ("0.00".equals(value)) {
                // 对于非本年度累计回收字段，如果值为0则不写入
                continue;
            }

            // 插入数据到 Excel
            putValueWithFormat(cell, value);
        }
    }
    
    /**
     * 在 Excel 第一列插入序号
     */
    private void insertSequenceNumber(Cells cells, int rowIndex) throws Exception {
        // 当前单元格
        Cell currentCell = cells.get(rowIndex, 0);
        // 上方单元格
        Cell aboveCell = (rowIndex > 0) ? cells.get(rowIndex - 1, 0) : null;
        // 默认值
        String newValue = "1";
        // 检查上方是否为合并单元格
        boolean isMerged = (aboveCell != null && aboveCell.isMerged());

        if (aboveCell != null && !isMerged) {
            // 获取上方单元格的值（去除空格）
            String aboveValue = aboveCell.getStringValue().trim();
            // 判断是否是纯数字字符串
            if (aboveValue.matches("\\d+")) {
                int sequence = Integer.parseInt(aboveValue) + 1;
                newValue = String.valueOf(sequence);
            }
        }
        // 插入序号
        currentCell.putValue(newValue);
    }
}