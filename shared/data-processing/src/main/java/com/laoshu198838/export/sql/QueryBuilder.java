package com.laoshu198838.export.sql;

import java.util.List;
import java.util.Map;

/**
 * SQL查询构建器接口
 * 
 * <AUTHOR>
 */
public interface QueryBuilder {
    
    /**
     * 构建SQL查询语句
     * 
     * @param tableName 表名
     * @param year 年份
     * @param month 月份
     * @param params 额外参数
     * @return SQL查询语句
     */
    String buildSql(String tableName, int year, int month, Map<String, Object> params);
    
    /**
     * 构建查询参数
     * 
     * @param tableName 表名
     * @param year 年份
     * @param month 月份
     * @param params 额外参数
     * @return 查询参数列表
     */
    List<Object> buildParameters(String tableName, int year, int month, Map<String, Object> params);
    
    /**
     * 是否支持指定的表名
     */
    boolean supports(String tableName);
}