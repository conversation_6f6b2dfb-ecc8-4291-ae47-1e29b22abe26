package com.laoshu198838.export.excel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * Excel数据写入器工厂
 * 
 * <AUTHOR>
 */
@Component
public class ExcelDataWriterFactory {
    
    private final List<ExcelDataWriter> dataWriters;
    
    @Autowired
    public ExcelDataWriterFactory(List<ExcelDataWriter> dataWriters) {
        this.dataWriters = dataWriters;
    }
    
    /**
     * 根据表名获取合适的数据写入器
     */
    public ExcelDataWriter getDataWriter(String tableName) {
        return dataWriters.stream()
                .filter(writer -> writer.supports(tableName))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("没有找到支持表名 '" + tableName + "' 的数据写入器"));
    }
}