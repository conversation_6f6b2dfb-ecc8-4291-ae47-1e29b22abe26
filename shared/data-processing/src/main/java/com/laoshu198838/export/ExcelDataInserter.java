package com.laoshu198838.export;

import com.aspose.cells.Cell;
import com.aspose.cells.Cells;
import com.aspose.cells.Worksheet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.laoshu198838.util.database.SqlUtils.getResultSetColumns;
import static com.laoshu198838.util.date.DateUtils.getPreviousYearAndMonth;
import static com.laoshu198838.util.file.ExcelUtils.putValueWithFormat;
import static com.laoshu198838.util.file.YamlUtils.readSingleLevelYaml;

/**
 * Excel数据插入器
 * 从原ExcelExportOverdueDebt中提取的数据插入逻辑
 *
 * <AUTHOR>
 */
@Component
public class ExcelDataInserter {

    private static final Logger logger = LoggerFactory.getLogger(ExcelDataInserter.class);

    /**
     * 将数据库查询结果插入到Excel工作表
     *
     * @param databaseTableName 数据库表名
     * @param year 年份
     * @param month 月份
     * @param sheet Excel工作表
     * @param rs 查询结果集
     * @throws Exception 插入异常
     */
    public void insertDataToExcel(String databaseTableName, int year, int month,
                                 Worksheet sheet, ResultSet rs) throws Exception {
        Cells cells = sheet.getCells();

        // 获取当前年份和月份对应的列映射
        AbstractMap.SimpleEntry<Map<String, String>, int[]> monthMapping = getColumnSet(year, month, rs);

        // 遍历查询结果，将数据写入Excel
        while (rs.next()) {
            // 根据"期间"列，确定要写入Excel的目标行
            String periodValue = rs.getString("期间");
            if (periodValue == null || periodValue.isEmpty()) {
                continue;
            }

            // 获取目标行
            int targetRow;
            if ("表9-新增逾期债权明细表".equals(databaseTableName)) {
                targetRow = findTargetRowByPeriod(databaseTableName, cells, periodValue);
            } else {
                targetRow = findTargetRowByPeriod(cells, periodValue);
            }

            // 判断是否需要向上复制一行（用于格式保持）
            copyRowFormatUp(cells, targetRow);

            // 将数据库中的有效列数据写入Excel
            insertRowData(databaseTableName, cells, targetRow, rs, monthMapping);
        }
    }

    /**
     * 生成上月数据列的映射表
     */
    private static AbstractMap.SimpleEntry<Map<String, String>, int[]> getColumnSet(int year, int month, ResultSet rs)
            throws SQLException {
        int[] prevYearAndMonth = getPreviousYearAndMonth(year, month);
        String prevMonthStr = prevYearAndMonth[1] + "月";

        Set<String> columnSet = getResultSetColumns(rs);
        Map<String, String> monthMapping = new HashMap<>();
        if (columnSet.contains(prevMonthStr)) {
            monthMapping.put("上月", prevMonthStr);
        }

        return new AbstractMap.SimpleEntry<>(monthMapping, prevYearAndMonth);
    }

    /**
     * 根据期间值查找目标行
     */
    private int findTargetRowByPeriod(Cells cells, String periodValue) throws Exception {
        int startRow = 4;
        int searchColumn = 1;

        try {
            switch (periodValue) {
                case "2022年430债权":
                    break;
                case "2022年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2022年430新增小计", searchColumn) + 1;
                    break;
                case "2023年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2022年新增小计", searchColumn) + 1;
                    break;
                case "2024年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2023年新增小计", searchColumn) + 1;
                    break;
                case "2025年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2024年新增小计", searchColumn) + 1;
                    break;
                case "存量新增债权":
                    break;
                default:
                    throw new IllegalArgumentException("未知的期间值: " + periodValue);
            }
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Excel结构异常: " + e.getMessage(), e);
        } catch (Exception e) {
            throw new Exception("查找目标行时发生错误，期间值: " + periodValue, e);
        }

        return startRow;
    }

    /**
     * 表9专用的查找目标行方法
     */
    private int findTargetRowByPeriod(String databaseTableName, Cells cells, String periodValue) throws Exception {
        int startRow = 4;
        int searchColumn = 1;

        try {
            switch (periodValue) {
                case "2025年新增债权":
                    startRow = findRowByTextOrThrow(cells, "2025年新增债权", searchColumn) + 1;
                    break;
                case "存量新增债权":
                    startRow = findRowByTextOrThrow(cells, "存量新增债权", searchColumn) + 1;
                    break;
                default:
                    throw new IllegalArgumentException("表9未知的期间值: " + periodValue);
            }
        } catch (Exception e) {
            throw new Exception("表9查找目标行时发生错误，期间值: " + periodValue, e);
        }

        return startRow;
    }

    /**
     * 复制行格式
     */
    private void copyRowFormatUp(Cells cells, int targetRow) throws Exception {
        try {
            if (targetRow <= 0) {
                return;
            }

            Cell targetCell = cells.get(targetRow + 1, 1);
            if (targetCell != null && targetCell.getValue() != null) {
                cells.insertRow(targetRow);
                cells.copyRow(cells, targetRow + 1, targetRow);
            }
        } catch (Exception e) {
            throw new Exception("复制行格式失败", e);
        }
    }

    /**
     * 插入行数据
     */
    private void insertRowData(String databaseTableName, Cells cells, int targetRow, ResultSet rs,
                              AbstractMap.SimpleEntry<Map<String, String>, int[]> monthMapping) throws Exception {
        try {
            // 插入序号
            insertSequenceNumber(cells, targetRow);

            // 插入具体数据列
            insertDataColumns(databaseTableName, cells, targetRow, rs, monthMapping);

        } catch (Exception e) {
            throw new Exception("插入行数据失败", e);
        }
    }

    /**
     * 插入序号
     */
    private void insertSequenceNumber(Cells cells, int rowIndex) throws Exception {
        try {
            Cell currentCell = cells.get(rowIndex, 0);
            Cell aboveCell = (rowIndex > 0) ? cells.get(rowIndex - 1, 0) : null;

            String newValue = "1";
            boolean isMerged = (aboveCell != null && aboveCell.isMerged());

            if (aboveCell != null && !isMerged && aboveCell.getValue() != null) {
                try {
                    int aboveValue = Integer.parseInt(aboveCell.getStringValue());
                    newValue = String.valueOf(aboveValue + 1);
                } catch (NumberFormatException e) {
                    newValue = "1";
                }
            }

            currentCell.setValue(newValue);
        } catch (Exception e) {
            throw new Exception("插入序号失败", e);
        }
    }

    /**
     * 插入数据列（从原ExcelExportOverdueDebt.insertRowData方法迁移）
     */
    private void insertDataColumns(String databaseTableName, Cells cells, int targetRow, ResultSet rs,
                                  AbstractMap.SimpleEntry<Map<String, String>, int[]> monthMapping) throws Exception {
        try {
            // 读取 YAML 配置中的列映射
            @SuppressWarnings("unchecked")
            Map<String, Integer> columnMapping = (Map<String, Integer>) readSingleLevelYaml("columnmapping.yaml", databaseTableName);

            if (columnMapping == null) {
                logger.debug("未找到表格 {} 的列映射配置", databaseTableName);
                return;
            }

            // 获取列名映射和上月的年份、月份
            Map<String, String> monthMap = monthMapping.getKey();

            // 遍历 columnMapping，将数据填充到 Excel
            for (Map.Entry<String, Integer> entry : columnMapping.entrySet()) {
                String columnName = entry.getKey();
                int columnIndex = entry.getValue();

                // 获取目标单元格
                Cell cell = cells.get(targetRow, columnIndex);

                // 处理数据映射
                String value;
                if ("上月".equals(columnName)) {
                    String mappedColumn = monthMap.get("上月");
                    value = (mappedColumn != null) ? rs.getString(mappedColumn) : "0";
                } else {
                    value = rs.getString(columnName);
                }

                // 特殊处理"本年度累计回收"字段，确保为null时显示为0
                if ("本年度累计回收".equals(columnName) || "累计清收".equals(columnName)) {
                    if (value == null || value.isEmpty()) {
                        value = "0";
                    }
                } else if ("0.00".equals(value)) {
                    // 对于非本年度累计回收字段，如果值为0则不写入
                    continue;
                }

                // 插入数据到 Excel
                putValueWithFormat(cell, value);
            }

        } catch (Exception e) {
            throw new Exception("插入数据列失败: " + e.getMessage(), e);
        }
    }



    /**
     * 根据文本查找行号
     */
    private int findRowByTextOrThrow(Cells cells, String searchText, int searchColumn) throws Exception {
        for (int row = 0; row < cells.getMaxDataRow() + 1; row++) {
            Cell cell = cells.get(row, searchColumn);
            if (cell != null && cell.getValue() != null) {
                String cellValue = cell.getStringValue();
                if (searchText.equals(cellValue)) {
                    return row;
                }
            }
        }
        throw new Exception("未找到文本: " + searchText);
    }
}
