package com.laoshu198838.export.excel;

import com.aspose.cells.Worksheet;
import java.sql.ResultSet;

/**
 * Excel数据写入器接口
 * 
 * <AUTHOR>
 */
public interface ExcelDataWriter {
    
    /**
     * 将ResultSet数据写入Excel工作表
     * 
     * @param worksheet Excel工作表
     * @param resultSet 查询结果集
     * @param tableName 表名（用于列映射）
     * @param year 年份
     * @param month 月份
     * @throws Exception 写入异常
     */
    void writeData(Worksheet worksheet, ResultSet resultSet, String tableName, 
                   int year, int month) throws Exception;
    
    /**
     * 是否支持指定的表名
     */
    boolean supports(String tableName);
}