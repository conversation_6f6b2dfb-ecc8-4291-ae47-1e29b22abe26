package com.laoshu198838.export.sql;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * SQL查询构建器工厂
 * 
 * <AUTHOR>
 */
@Component
public class QueryBuilderFactory {
    
    private final List<QueryBuilder> queryBuilders;
    
    @Autowired
    public QueryBuilderFactory(List<QueryBuilder> queryBuilders) {
        this.queryBuilders = queryBuilders;
    }
    
    /**
     * 根据表名获取合适的查询构建器
     */
    public QueryBuilder getQueryBuilder(String tableName) {
        return queryBuilders.stream()
                .filter(builder -> builder.supports(tableName))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("没有找到支持表名 '" + tableName + "' 的查询构建器"));
    }
}