package com.laoshu198838.export.sql;

import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 标准SQL查询构建器
 * 用于表3、表4、表5、表8等标准查询
 * 
 * <AUTHOR>
 */
@Component
public class StandardQueryBuilder implements QueryBuilder {
    
    private static final Set<String> SUPPORTED_TABLES = Set.of(
        "诉讼表", "非诉讼表", "减值准备表", "处置表"
    );
    
    @Override
    public String buildSql(String tableName, int year, int month, Map<String, Object> params) {
        if (!supports(tableName)) {
            throw new IllegalArgumentException("不支持的表名: " + tableName);
        }
        
        return "SELECT * FROM " + tableName + " WHERE 年份 = ? AND 月份 = ?";
    }
    
    @Override
    public List<Object> buildParameters(String tableName, int year, int month, Map<String, Object> params) {
        List<Object> parameters = new ArrayList<>();
        parameters.add(year);
        parameters.add(month);
        return parameters;
    }
    
    @Override
    public boolean supports(String tableName) {
        return SUPPORTED_TABLES.contains(tableName);
    }
}