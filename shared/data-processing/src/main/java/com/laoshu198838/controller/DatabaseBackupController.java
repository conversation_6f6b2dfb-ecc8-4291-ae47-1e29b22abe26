package com.laoshu198838.controller;

import com.laoshu198838.service.DatabaseBackupRestoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据库备份与恢复控制器
 * 提供手动触发备份和恢复的API接口
 */
@RestController
@RequestMapping("/api/database")
public class DatabaseBackupController {

    private final DatabaseBackupRestoreService backupRestoreService;

    @Autowired
    public DatabaseBackupController(DatabaseBackupRestoreService backupRestoreService) {
        this.backupRestoreService = backupRestoreService;
    }

    /**
     * 手动触发数据库备份
     * @return 操作结果
     */
    @PostMapping("/backup")
    public ResponseEntity<String> backupDatabase() {
        String result = backupRestoreService.manualBackup();
        return ResponseEntity.ok(result);
    }

    /**
     * 手动触发数据库恢复
     * @return 操作结果
     */
    @PostMapping("/restore")
    public ResponseEntity<String> restoreDatabase() {
        String result = backupRestoreService.manualRestore();
        return ResponseEntity.ok(result);
    }
}
