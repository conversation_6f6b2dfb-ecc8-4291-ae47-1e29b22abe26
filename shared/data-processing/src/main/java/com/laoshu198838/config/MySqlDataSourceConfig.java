package com.laoshu198838.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.zaxxer.hikari.HikariDataSource;

/**
 * MySQL 数据源配置类
 * 配置 data-processing 模块使用的数据源
 *
 * <AUTHOR>
 */
@Configuration
@Profile("data-processing") // 只在 data-processing profile 激活时启用
@EnableTransactionManagement
@EnableJpaRepositories(
    basePackages = "com.laoshu198838.repository.overdue_debt",
    entityManagerFactoryRef = "dataProcessingEntityManagerFactory",
    transactionManagerRef = "dataProcessingTransactionManager"
)
public class MySqlDataSourceConfig {

    /**
     * MySQL 数据源属性
     */
    @Bean
    @ConfigurationProperties("data-processing.datasource")
    public DataSourceProperties dataProcessingDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * MySQL 数据源
     */
    @Bean
    @ConfigurationProperties("data-processing.datasource.configuration")
    public DataSource dataProcessingDataSource() {
        return dataProcessingDataSourceProperties().initializeDataSourceBuilder()
                .type(HikariDataSource.class).build();
    }

    /**
     * MySQL 实体管理器工厂
     */
    @Bean
    public LocalContainerEntityManagerFactoryBean dataProcessingEntityManagerFactory(
            EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(dataProcessingDataSource())
                .packages("com.laoshu198838.entity.overdue_debt")
                .persistenceUnit("data-processing")
                .properties(java.util.Collections.singletonMap(
                        "hibernate.hbm2ddl.auto", "none"))
                .build();
    }

    /**
     * MySQL 事务管理器
     */
    @Bean
    public PlatformTransactionManager dataProcessingTransactionManager(
            @Qualifier("dataProcessingEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory.getObject());
    }
}
