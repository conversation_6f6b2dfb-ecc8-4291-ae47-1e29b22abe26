package com.laoshu198838.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * data-processing模块的Spring配置类
 * 确保Repository和Service能被其他模块正确扫描到
 * 
 * <AUTHOR>
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.laoshu198838.repository.overdue_debt")
@ComponentScan(basePackages = {
    "com.laoshu198838.service",
    "com.laoshu198838.repository"
})
@EntityScan(basePackages = "com.laoshu198838.entity.overdue_debt")
public class MysqlDataConfiguration {
    // 配置类，Spring会自动注册扫描到的Bean
}
