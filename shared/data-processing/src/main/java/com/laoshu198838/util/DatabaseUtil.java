package com.laoshu198838.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */

public class DatabaseUtil {
    // 数据库 URL、用户名和密码
    private static final String BASEURL = "***************************";
    private static final String USER = "root";
    private static final String PASSWORD = "Zlb&198838";

    // 加载驱动
    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Failed to load MySQL Driver!", e);
        }
    }

    // 获取数据库连接
    public static Connection getConnection(String databaseName) {
        String url = BASEURL + "/" + databaseName;
        try {
            return DriverManager.getConnection(url, USER, PASSWORD);
        } catch (SQLException e) {
            throw new RuntimeException("Failed to connect to the database!", e);
        }
    }

    // 关闭连接
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                //noinspection CallToPrintStackTrace
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
        Connection connection = DatabaseUtil.getConnection("overdue_debt_db");
        System.out.println(connection);
        DatabaseUtil.closeConnection(connection);
    }
}