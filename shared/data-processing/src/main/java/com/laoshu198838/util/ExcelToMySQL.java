package com.laoshu198838.util;

import com.aspose.cells.*;
import java.sql.*;
import java.io.*;

@Deprecated
public class ExcelToMySQL {
    public static void main(String[] args) {
        // Excel 文件路径作为参数传入
        String databaseName = "overdue_debt_db";
        String url = "***************************/" + databaseName + "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC";
        String username = "root"; // 替换为你的数据库用户名
        String password = "Zlb&198838"; // 替换为你的数据库密码
        String excelFilePath = "/Users/<USER>/Library/CloudStorage/OneDrive-个人/08.程序/FinancialSystem/mysql_data/src/main/resources/非诉表.xlsx";

        // 调用方法进行数据导入
        importExcelToMySQL(excelFilePath, url, username, password);
    }

    /**
     * 将 Excel 数据导入到 MySQL 中
     * @param excelFilePath Excel 文件路径
     * @param dbUrl 数据库连接 URL
     * @param dbUser 数据库用户名
     * @param dbPassword 数据库密码
     */
    public static void importExcelToMySQL(String excelFilePath, String dbUrl, String dbUser, String dbPassword) {
        try {
            // 创建数据库连接
            Connection conn = DriverManager.getConnection(dbUrl, dbUser, dbPassword);
            Statement stmt = conn.createStatement();

            // 加载 Excel 文件
            Workbook workbook = new Workbook(excelFilePath);
            // 获取第一个工作表
            Worksheet worksheet = workbook.getWorksheets().get(0);
            // 获取行数和列数
            int rowCount = worksheet.getCells().getMaxDataRow() + 1;
            int columnCount = worksheet.getCells().getMaxDataColumn() + 1;

            // 获取表头
            StringBuilder columns = new StringBuilder();
            StringBuilder createTableSQL = new StringBuilder();
            createTableSQL.append("CREATE TABLE IF NOT EXISTS `非诉表` (");

            for (int i = 0; i < columnCount; i++) {
                // 获取每个列的表头
                String columnName = worksheet.getCells().get(0, i).getStringValue();
                if (i > 0) columns.append(", ");
                columns.append(columnName);

                // 动态确定数据类型
                String dataType = getColumnDataType(worksheet, i, rowCount);
                createTableSQL.append("`").append(columnName.replace("`", "``")).append("` ").append(dataType).append(", ");
            }

            // 移除最后多余的逗号
            createTableSQL.setLength(createTableSQL.length() - 2);
            createTableSQL.append(");");

            // 执行表创建 SQL 语句
            stmt.executeUpdate(createTableSQL.toString());

            // 遍历 Excel 数据，构建 SQL 插入语句
            for (int i = 1; i < rowCount; i++) { // 跳过第一行（表头）
                StringBuilder values = new StringBuilder();
                for (int j = 0; j < columnCount; j++) {
                    // 获取单元格值
                    Cell cell = worksheet.getCells().get(i, j);
                    String cellValue = cell.getStringValue().trim();

                    // 处理 null 或空值
                    if (cellValue.isEmpty()) {
                        values.append("NULL");
                    } else {
                        // 判断数据类型并进行转换
                        if (isNumeric(cellValue)) {
                            values.append(cellValue); // 数字类型直接插入
                        } else {
                            values.append("'").append(cellValue.replace("'", "''")).append("'"); // 字符串类型，加上引号
                        }
                    }

                    // 添加逗号分隔
                    if (j < columnCount - 1) {
                        values.append(", ");
                    }
                }

                // 构建 SQL 插入语句
                String sql = "INSERT INTO `非诉表` (" + columns.toString() + ") VALUES (" + values.toString() + ")";
                stmt.executeUpdate(sql);
            }

            // 关闭连接
            stmt.close();
            conn.close();
            System.out.println("数据导入成功!");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断一个字符串是否为数字
     * @param str 要检查的字符串
     * @return true 如果是数字，false 否则
     */
    public static boolean isNumeric(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 根据 Excel 列的数据确定列的数据类型
     * @param worksheet Excel 工作表
     * @param columnIndex 列索引
     * @param rowCount 行数
     * @return 数据类型
     */
    public static String getColumnDataType(Worksheet worksheet, int columnIndex, int rowCount) {
        String dataType = "VARCHAR(190)"; // 默认设置为 VARCHAR
        for (int i = 1; i < rowCount; i++) {
            Cell cell = worksheet.getCells().get(i, columnIndex);
            String cellValue = cell.getStringValue().trim();
            if (!cellValue.isEmpty()) {
                if (isNumeric(cellValue)) {
                    try {
                        Double.parseDouble(cellValue);
                        dataType = "DECIMAL(15,2)"; // 如果是数字，则设置为 DECIMAL 类型
                        break;
                    } catch (NumberFormatException e) {
                        dataType = "VARCHAR(199)"; // 默认字符串类型
                    }
                } else if (cellValue.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                    // 判断是否是日期格式 yyyy-MM-dd
                    dataType = "DATE";
                }
            }
        }
        return dataType;
    }
}