-- 更新新增金额触发器
-- 此触发器在"新增表"中的月度金额数据更新时自动计算总和并更新到"新增金额"字段

DELIMITER //

CREATE TRIGGER trg_更新新增金额_before_update
BEFORE UPDATE ON 新增表
FOR EACH ROW
BEGIN
  IF NEW.`1月` != OLD.`1月` OR NEW.`2月` != OLD.`2月` OR NEW.`3月` != OLD.`3月` OR
     NEW.`4月` != OLD.`4月` OR NEW.`5月` != OLD.`5月` OR NEW.`6月` != OLD.`6月` OR
     NEW.`7月` != OLD.`7月` OR NEW.`8月` != OLD.`8月` OR NEW.`9月` != OLD.`9月` OR
     NEW.`10月` != OLD.`10月` OR NEW.`11月` != OLD.`11月` OR NEW.`12月` != OLD.`12月` THEN

     SET NEW.`新增金额` =
       IFNULL(NEW.`1月`, 0) + IFNULL(NEW.`2月`, 0) + IFNULL(NEW.`3月`, 0) +
       IFNULL(NEW.`4月`, 0) + IFNULL(NEW.`5月`, 0) + IFNULL(NEW.`6月`, 0) +
       IFNULL(NEW.`7月`, 0) + IFNULL(NEW.`8月`, 0) + IFNULL(NEW.`9月`, 0) +
       IFNULL(NEW.`10月`, 0) + IFNULL(NEW.`11月`, 0) + IFNULL(NEW.`12月`, 0);

  END IF;
END;
//

DELIMITER ;

-- 添加同样的INSERT触发器，以确保在插入新记录时也计算总和

DELIMITER //

CREATE TRIGGER trg_更新新增金额_before_insert
BEFORE INSERT ON 新增表
FOR EACH ROW
BEGIN
  SET NEW.`新增金额` =
    IFNULL(NEW.`1月`, 0) + IFNULL(NEW.`2月`, 0) + IFNULL(NEW.`3月`, 0) +
    IFNULL(NEW.`4月`, 0) + IFNULL(NEW.`5月`, 0) + IFNULL(NEW.`6月`, 0) +
    IFNULL(NEW.`7月`, 0) + IFNULL(NEW.`8月`, 0) + IFNULL(NEW.`9月`, 0) +
    IFNULL(NEW.`10月`, 0) + IFNULL(NEW.`11月`, 0) + IFNULL(NEW.`12月`, 0);
END;
//

DELIMITER ;
