<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- 继承父项目 -->
  <parent>
    <groupId>com.laoshu198838</groupId> <!-- 替换为父项目的 groupId -->
    <artifactId>FinancialSystem</artifactId> <!-- 父项目的 artifactId -->
    <version>1.0-SNAPSHOT</version> <!-- 父项目的版本 -->
    <relativePath>../../pom.xml</relativePath> <!-- 父项目 POM 文件的相对路径 -->
  </parent>

  <!-- 当前模块的信息 -->
  <artifactId>common</artifactId> <!-- 模块的名称 -->
  <packaging>jar</packaging> <!-- 如果是一个库模块，打包为 JAR -->

  <!-- 模块的依赖 -->
  <dependencies>
    <!-- 添加模块需要的依赖 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.12.0</version>
    </dependency>

    <!-- 用于解析 YAML 文件的工具库 -->
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
    </dependency>
    <!-- Aspose 依赖 -->
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-cells</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-words</artifactId>
      <classifier>jdk17</classifier>
    </dependency>
    <dependency>
      <groupId>com.aspose</groupId>
      <artifactId>aspose-slides</artifactId>
      <classifier>jdk16</classifier>
    </dependency>
    <dependency>
      <groupId>jakarta.persistence</groupId>
      <artifactId>jakarta.persistence-api</artifactId>
    </dependency>
    
    <!-- 添加Hibernate核心依赖 -->
    <dependency>
      <groupId>org.hibernate.orm</groupId>
      <artifactId>hibernate-core</artifactId>
    </dependency>
    
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-core</artifactId>
    </dependency>
      <dependency>
          <groupId>org.springframework.data</groupId>
          <artifactId>spring-data-jpa</artifactId>
      </dependency>
      <dependency>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
      </dependency>

      <!-- Jackson依赖 -->
      <dependency>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
      </dependency>

      <dependency>
          <groupId>com.fasterxml.jackson.dataformat</groupId>
          <artifactId>jackson-dataformat-yaml</artifactId>
      </dependency>

      <dependency>
          <groupId>com.fasterxml.jackson.datatype</groupId>
          <artifactId>jackson-datatype-jsr310</artifactId>
      </dependency>

      <!-- Spring Boot Starter -->
      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter</artifactId>
      </dependency>

      <!-- Spring Context for Configuration -->
      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-context</artifactId>
      </dependency>

      <!-- Jakarta Validation API (Bean Validation) -->
      <dependency>
          <groupId>jakarta.validation</groupId>
          <artifactId>jakarta.validation-api</artifactId>
      </dependency>

      <!-- Hibernate Validator Implementation -->
      <dependency>
          <groupId>org.hibernate.validator</groupId>
          <artifactId>hibernate-validator</artifactId>
      </dependency>

  </dependencies>

  <build>
    <plugins>
      <!-- Spring Boot Maven 插件配置 -->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <classifier>exec</classifier>
          <!-- 设置为true表示这是一个库模块，不是应用程序 -->
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
