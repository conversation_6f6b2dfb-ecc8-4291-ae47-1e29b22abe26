# 用于write_to_word的参数
sheet_names:
  - "表3-涉诉"
  - "表4-非涉诉"
  - "表5-减值准备"
  - "表6-风险准备金统计表"
  - "表7-10万及以下应收债权明细表"
  - "表8-临3表"
  - "表9-2024年新增逾期债权明细表"
  - "表10-处置债权明细表"

汇总表表名:
  二级公司简称: "二级公司"
  债权人名称: "各级子公司"
# 用于汇总求和的列
groupby_columns:
  - "二级公司简称"
  - "债权人名称"
  # - '债务人名称'

表3-涉诉:
  sheet_name: "表4-涉诉"
  columns_to_keep: [0, 1, 2, 3, 6, 16, 22, 23, 24]
  columns_names:
    - 案号
    - 债权人名称
    - 债务人名称
    - 上月末余额
    - 本月末逾期债权余额
    - 2024年处置金额
    - 债权类别
    - 二级公司简称
    - 债权所属期间
  rows_to_delele: [0, 1]
  delete_rows_if_any_rows_empty: ["债权人名称", "债务人名称"]
  number_columns:
    - 上月末余额
    - 本月末逾期债权余额
    - 2024年处置金额
  important_columns:
    - 本月末逾期债权余额
    - 2024年处置金额
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    - 6: 涉诉类债权
    - 16: 涉诉类债权处置

表4-非涉诉:
  sheet_name: "表4-非涉诉"
  columns_to_keep: [0, 1, 2, 6, 10, 14, 18, 21, 27, 28, 29]
  columns_names:
    - 序号
    - 债权人名称
    - 债务人名称
    - 2022年4月30日逾期债权余额
    - 上月末余额
    - 本月发生额
    - 本月末逾期债权余额
    - 2024年处置金额
    - 债权类别
    - 二级公司简称
    - 债权所属期间
  rows_to_delele: [0, 1]
  delete_rows_if_any_rows_empty: ["序号", "债权人名称"]
  number_columns:
    - 2022年4月30日逾期债权余额
    - 上月末余额
    - 本月发生额
    - 本月末逾期债权余额
    - 2024年处置金额
  important_columns:
    - 2022年4月30日逾期债权余额
    - 本月末逾期债权余额
    - 2024年处置金额
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    - 18: 非涉诉类债权
    - 21: 非涉诉类债权处置

表5-减值准备:
  sheet_name: "表5-减值准备"
  columns_to_keep: [0, 1, 2, 5, 8, 9, 10, 12, 15, 16]
  columns_names:
    - 序号
    - 债权人名称
    - 债务人名称
    - 本月末逾期债权余额
    - 上月末余额
    - 本月发生额
    - 本月末余额
    - 2024年处置金额
    - 二级公司简称
    - 债权所属期间
  rows_to_delele: [0, 1, 2]
  number_columns:
    - 本月末逾期债权余额
    - 上月末余额
    - 本月发生额
    - 本月末余额
    - 2024年处置金额
  important_columns:
    - 本月末逾期债权余额
    - 2024年处置金额
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    - I: 上月已计提减值的债权余额
    - J: 本月较上月减少
    - K: 本月已计提减值准备的债权余额

表5-减值准备（长投）:
  sheet_name: "表5-减值准备（长投）"
  columns_to_keep: [0, 1, 2, 6, 10, 14, 18, 21, 27, 28, 29]
  columns_names:
    - 序号
    - 债权人名称
    - 债务人名称
    - 本月末逾期债权余额
    - 上月末余额
    - 本月发生额
    - 本月末余额
    - 累计处置金额
    - 债权类别
    - 二级公司简称
    - 债权所属期间
  rows_to_delele: [0, 1]

表6-风险准备金统计表:
  sheet_name: "表6-风险准备金统计表"
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    - E: 预计计提风险准备金
    - I: 2022年计提
    - M: 2023年计提
    - Q: 2024年计提
    - BN: 预计发还金额
    - BR: 发还2023年
    - BT: 发还2024年
    - F: 2022年新增逾期债权
    - J: 2023年新增逾期债权
    - N: 2024年新增逾期债权

表7-10万及以下应收债权明细表:
  sheet_name: "表7-10万及以下应收债权明细表"
  columns_to_keep:
    [0, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21]
  columns_names: #修改后的列名
    - 序号
    - 债权人名称
    - 债务人名称
    - 2023年末逾期债权余额
    - 1月余额
    - 2月生额
    - 3月余额
    - 4月余额
    - 5月余额
    - 6月余额
    - 7月余额
    - 8月余额
    - 9月余额
    - 10月余额
    - 11月余额
    - 12月余额
    - 累计处置金额
    - 是否2024年新增
    - 是否涉诉
    - 二级公司简称
    - 债权所属期间
  rows_to_delele: [0, 1, 2]
  number_columns: # 需要进行汇总求和的列
    - 2023年末逾期债权余额
    - 1月余额
    - 2月生额
    - 3月余额
    - 4月余额
    - 5月余额
    - 6月余额
    - 7月余额
    - 8月余额
    - 9月余额
    - 10月余额
    - 11月余额
    - 12月余额
    - 累计处置金额
  important_columns:
    - 2023年末逾期债权余额
    - 累计处置金额
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    - 5: 对外债权余额
    - 12: 累计对外债权处置

表8-临3表:
  sheet_name: "表8-临3表"
  columns_to_keep: [0, 2, 3, 5, 6, 7, 8, 9, 10, 11, 16, 17, 19, 20]
  columns_names:
    - 序号
    - 债权人名称
    - 债务人名称
    - 2023年末逾期债权余额
    - 2024年新增债权
    - 2024年处置金额
    - 现金处置
    - 实物处置
    - 股权处置
    - 其他处置
    - 本月末逾期债权余额
    - 是否涉诉
    - 二级公司简称
    - 债权所属期间
  rows_to_delele: [0, 1, 2, 3]
  number_columns:
    - 2023年末逾期债权余额
    - 2024年新增债权
    - 2024年处置金额
    - 现金处置
    - 实物处置
    - 股权处置
    - 其他处置
    - 本月末逾期债权余额
  important_columns:
    - 2023年末逾期债权余额
    - 2024年新增债权
    - 2024年处置金额
    - 本月末逾期债权余额
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    - 5: 对外债权余额
    - 12: 累计对外债权处置

表9-2024年新增逾期债权明细表:
  sheet_name: "表9-2024年新增逾期债权明细表"
  columns_to_keep:
    [0, 2, 3, 6, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 67, 68, 69, 71]
  columns_names:
    - 序号
    - 债权人名称
    - 债务人名称
    - 是否涉诉
    - 1月新增
    - 2月新增
    - 3月新增
    - 4月新增
    - 5月新增
    - 6月新增
    - 7月新增
    - 8月新增
    - 9月新增
    - 10月新增
    - 11月新增
    - 12月新增
    - 2024年新增债权
    - 2024年新增累计回款
    - 2024年新增债权余额
    - 二级公司简称
  rows_to_delele: [0, 1, 2, 3]
  number_columns:
    - 1月新增
    - 2月新增
    - 3月新增
    - 4月新增
    - 5月新增
    - 6月新增
    - 7月新增
    - 8月新增
    - 9月新增
    - 10月新增
    - 11月新增
    - 12月新增
    - 2024年新增债权
    - 2024年新增累计回款
    - 2024年新增债权余额
  important_columns:
    - 2024年新增债权
    - 2024年新增累计回款
    - 2024年新增债权余额
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    # - 40: 本月新增债权
    - BP: 对外债权增加

表10-处置债权明细表:
  sheet_name: "表10-处置债权明细表"
  columns_to_keep: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
  columns_names:
    - 序号
    - 债权主体
    - 债务主体
    - 累计处置金额
    - 处置方式
    - 累计现金回款
    - 是否涉诉
    - 是否完全处理
    - 7月处置金额
    - 7月累计现金回款
    - 8月较7月处置金额变动
    - 8月较7月其他处置金额变动
    - 所属期间
    - 二级公司简称
  row_key_word:
    - 总计
  col_and_names: # excel中的列和需要存入表中的关键字，用于读取数据和存入数据
    - F: 累计现金回款
    - L: 当月对外债权处置
    - M: 当月现金回款
勾稽表:
  本月末逾期债权余额:
    - 表3-涉诉
    - 表4-非涉诉
    - 表5-减值准备
    - 表8-临3表
  2024年处置金额:
    - 表3-涉诉
    - 表4-非涉诉
    - 表5-减值准备
    - 表8-临3表
  2024年新增债权:
    - 表8-临3表
    - 表9-2024年新增逾期债权明细表
  # 2024年处置金额:
  #   - 表8-临3表
  #   - 表5-减值准备
# 债务人名称替换：
#   深圳万润科技股份有限公司诉重庆市园林工程建设有限公司合同纠纷案(丰都项目): 重庆市园林工程建设有限公司
#   中筑天佑科技有限公司诉呼和浩特市赛罕区城乡建设开发有限公司建设工程合同纠纷案: 呼和浩特市赛罕区城乡建设开发有限公司
#   深圳万润科技股份有限公司诉余江县万象新动投资管理中心（有限合伙）合同纠纷案: 余江县万象新动投资管理中心（有限合伙）
#   彩迅工业(深圳)有限公司:买卖合同纠纷案件: 彩迅工业(深圳)有限公司
#   北京市亿万无线信息技术有限公司与北京开课吧科技有限公司合同纠纷的案件: 北京开课吧科技有限公司
#   北京市亿万无线信息技术有限公司与上海雪萌网络科技有限公司合同纠纷的案件: 上海雪萌网络科技有限公司
#   北京市亿万无线信息技术有限公司与北京千钧互动网络技术有限公司合同纠纷的案件: 北京千钧互动网络技术有限公司
#   北京亿万无线信息技术有限公司诉杭州瞬息科技有限公司合同纠纷案: 杭州瞬息科技有限公司
#   北京亿万无线信息技术有限公司诉深圳市三体时代网络科技有限公司合同纠纷案: 深圳市三体时代网络科技有限公司
#   深圳万润科技股份有限公司诉重庆市园林工程建设有限公司合同纠纷案(丰都项目): 重庆市园林工程建设有限公司
#   北京市亿万无线信息技术有限公司与北京千钧互动网络技术有限公司合同纠纷的案件: 北京千钧互动网络技术有限公司
#   北京市亿万无线信息技术有限公司与一六八(海南)网络科技有限公司合同纠纷的案件: 一六八(海南)网络科技有限公司
#   北京市亿万无线信息技术有限公司与福建蕴实科技有限公司合同纠纷的案件: 福建蕴实科技有限公司
#   北京市亿万无线信息技术有限公司与海南上智捷网络科技有限公司合同纠纷的案件: 海南上智捷网络科技有限公司
#   北京市亿万无线信息技术有限公司与浙江载亿信息科技有限公司合同纠纷的案件: 浙江载亿信息科技有限公司
#   北京市亿万无线信息技术有限公司与陕西马可尼智能科技有限公司合同纠纷的案件: 陕西马可尼智能科技有限公司
#   北京市亿万无线信息技术有限公司与北京中清龙图网络技术有限公司合同纠纷的案件: 北京中清龙图网络技术有限公司
#   北京市亿万无线信息技术有限公司与北京慈云金蟾科技有限公司合同纠纷的案件: 北京慈云金蟾科技有限公司
#   北京市亿万无线信息技术有限公司与深圳市迅龙创威网络技术有限公司，深圳市决铭科技有限公司合同纠纷的案件: 深圳市迅龙创威网络技术有限公司
#   北京市亿万无线信息技术有限公司与武汉独为信达网络科技有限公司合同纠纷的案件: 武汉独为信达网络科技有限公司
#   中筑天佑科技有限公司诉广州辉晟商业管理有限公司建设工程合同纠纷案【案号：（2023）粤 0115 民初 2879 号】: 广州辉晟商业管理有限公司
#   北京亿万无线信息技术有限公司与北京极值科技有限公司、北京微方程科技有限公司、北京灵幂科技有限公司、齐坡合同纠纷: 北京极值科技有限公司
#   深圳万润科技股份有限公司与四川天都建设工程有限公司建设工程施工合同纠纷: 四川天都建设工程有限公司
#   北京亿万无线信息技术有限公司诉杭州玖讯网络科技有限公司合同纠纷案: 杭州玖讯网络科技有限公司
#   中筑天佑科技有限公司与太原市迎泽区城乡管理局建设工程施工合同纠纷案太原市迎泽区人民法院，（2023）晋0106民初8976号: 太原市迎泽区城乡管理局
#   北京亿万无线信息技术有限公司与北京极值科技有限公司、北京微方程科技有限公司、北京灵幂科技有限公司、齐坡合同纠纷: 北京极值科技有限公司
#   北京亿万无线信息技术有限公司与北京星明互动科技有限公司（广东协和化妆品研究开发有限公司）合同纠纷: 北京星明互动科技有限公司
#   北京亿万无线信息技术有限公司与深圳一盏灯传媒有限公司合同纠纷: 深圳一盏灯传媒有限公司
#   北京亿万无线信息技术有限公司诉杭州瞬息科技有限公司合同纠纷案: 杭州瞬息科技有限公司
#   北京亿万无线信息技术有限公司与北京嘉年木棉金辉创业服务有限公司合同纠纷: 北京嘉年木棉金辉创业服务有限公司
#   中筑天佑科技有限公司诉佛山市星亚物业发展有限公司土地租赁合同履约保证金退还纠纷案: 佛山市星亚物业发展有限公司
