package com.laoshu198838.dto.debt.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 用于接收前端提交数据的 DTO
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OverdueDebtAddDTO {

    // 1、基本信息
    // 债权人
    private String creditor;
    // 债务人
    private String debtor;

    // 2、管理信息
    // 管理公司
    private String managementCompany;
    // 科目名称
    private String subjectName;
    // 是否涉诉
    private String isLitigation;
    // 责任人
    private String responsiblePerson;

    // 3、金额信息
    // 新增逾期金额
    private BigDecimal overdueAmount;
    // 坏账准备计提金额
    private BigDecimal provisionAmount;

    // 4、债权信息
    // 债权类别
    private String debtCategory;
    // 债权性质
    private String debtNature;
    // 逾期所属年月，如"2025-05"
    private String overdueDate;
    // 新增年月，格式为"2025-05"
    private String addDate;
    // 期间信息，如"2022年430"
    private String period;

    // 5、诉讼信息
    // 案件名称
    private String caseName;
    // 案件名称（兼容旧版本）
    private String litigationName;
    // 诉讼主张相关信息
    private Object litigationClaim;

    /**
     * 获取诉讼主张信息
     *
     * @return 诉讼主张DTO对象
     */
    public LitigationClaimDTO getLitigationClaimDTO() {
        if (litigationClaim == null) {
            return new LitigationClaimDTO();
        }

        // 如果是数字，创建一个新的DTO对象并设置本金
        if (litigationClaim instanceof Number) {
            LitigationClaimDTO dto = new LitigationClaimDTO();
            dto.setPrincipal(((Number) litigationClaim).doubleValue());
            return dto;
        }

        // 如果是字符串，尝试解析为数字
        if (litigationClaim instanceof String) {
            try {
                double value = Double.parseDouble((String) litigationClaim);
                LitigationClaimDTO dto = new LitigationClaimDTO();
                dto.setPrincipal(value);
                return dto;
            } catch (NumberFormatException e) {
                // 如果无法解析，返回空对象
                return new LitigationClaimDTO();
            }
        }

        // 如果是已经是DTO对象，直接返回
        if (litigationClaim instanceof LitigationClaimDTO) {
            return (LitigationClaimDTO) litigationClaim;
        }

        // 如果是其他类型，返回空对象
        return new LitigationClaimDTO();
    }

    // 6、其他信息
    // 安排措施
    private String measures;
    // 是否为更新模式
    private Boolean updateMode;

    /**
     * 获取案件名称，兼容旧版本
     *
     * @return 案件名称
     */
    public String getLitigationName() {
        // 如果 caseName 存在，优先使用 caseName
        if (caseName != null && !caseName.isEmpty()) {
            return caseName;
        }
        // 否则返回 litigationName
        return litigationName;
    }
}
