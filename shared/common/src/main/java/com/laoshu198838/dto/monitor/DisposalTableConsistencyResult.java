package com.laoshu198838.dto.monitor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 处置表数据一致性检查结果DTO
 * 用于返回处置表数据一致性检查的结果
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class DisposalTableConsistencyResult {
    
    // 检查结果：每月处置金额是否等于现金处置+分期还款+资产抵债+其他方式求和
    private boolean amountEquals;
    
    // 汇总数据用于前端展示对比
    private SummaryData summaryData = new SummaryData();
    
    // 不一致的行数据明细
    private List<InconsistentDisposalRow> inconsistentRows = new ArrayList<>();
    
    // 前端需要的额外字段
    private BigDecimal totalAmount = BigDecimal.ZERO;
    private BigDecimal detailSum = BigDecimal.ZERO;
    
    /**
     * 汇总数据
     */
    @Getter
    @Setter
    public static class SummaryData {
        // 每月处置金额
        private BigDecimal debtAmount = BigDecimal.ZERO;
        
        // 处置方式求和
        private BigDecimal disposalSum = BigDecimal.ZERO;
    }
    
    /**
     * 不一致的处置表行数据
     */
    @Getter
    @Setter
    public static class InconsistentDisposalRow {
        // 债务人
        private String debtor;
        
        // 管理公司
        private String managementCompany;
        
        // 每月处置金额
        private BigDecimal debtAmount;
        
        // 现金处置
        private BigDecimal cashDisposal;
        
        // 分期还款
        private BigDecimal installmentRepayment;
        
        // 资产抵债
        private BigDecimal assetDebt;
        
        // 其他方式
        private BigDecimal otherWays;
        
        // 处置方式求和
        private BigDecimal disposalSum;
        
        // 差额（每月处置金额-处置方式求和）
        private BigDecimal difference;
    }
} 