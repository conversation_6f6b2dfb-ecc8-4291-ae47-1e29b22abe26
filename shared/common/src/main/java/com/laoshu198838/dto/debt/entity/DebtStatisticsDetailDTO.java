package com.laoshu198838.dto.debt.entity;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 债务统计详情数据传输对象
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class DebtStatisticsDetailDTO {
    // 新增债务详情列表
    private List<Map<String, Object>> newDebtDetailList;

    // 减少债务详情列表
    private List<Map<String, Object>> reductionDebtDetailList;

    // 本月处置债权
    private double currentMonthDisposeDebt;

    // 减值准备金额
    private double impairmentAmount;

    // 年月信息，格式为YYYY-MM
    private String yearMonth;
}
