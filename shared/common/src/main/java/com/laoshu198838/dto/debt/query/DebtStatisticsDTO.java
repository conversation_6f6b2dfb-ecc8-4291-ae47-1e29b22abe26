package com.laoshu198838.dto.debt.query;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class DebtStatisticsDTO {
    // Getters and Setters
    //    总处置金额
    private Double totalReductionAmount;
    //    期末债权余额
    private Double totalDebtBalance;
    //    存量债权期初余额
    private Double initialDebtBalance;
    //    存量债权处置金额
    private Double initialDebtReductionAmount;
    //    存量债权期末余额
    private Double initialDebtEndingBalance;
    //    新增债权金额
    private Double newDebtAmount;
    //    新增债权处置金额
    private Double newDebtReductionAmount;
    //    新增债权余额
    private Double newDebtBalance;
    //    累计新增债权按公司汇总
    private List<Map<String, Object>> newDebtSummaryByCompany;
    //    累计存量债权按公司汇总
    private List<Map<String, Object>> existingDebtSummaryByCompany;
    //    新增债权按公司月度汇总
    private List<Map<String, Object>> newDebtMonthSummaryByCompany;
    //    存量债权按公司月度汇总
    private List<Map<String, Object>> existingDebtMonthSummaryByCompany;
    //    新增债权按公司月度汇总
    private List<Map<String, Object>> monthNewReductionDebtByCompany;
    //    新增债权按公司月度汇总
    private List<Map<String, Object>> monthNewDebtByCompany;


}
