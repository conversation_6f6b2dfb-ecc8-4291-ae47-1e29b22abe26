package com.laoshu198838.dto.debt;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 债权删除结果DTO
 * 返回删除操作的结果信息
 *
 * <AUTHOR>
 */
@Data
@Builder
public class DebtDeletionResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 操作消息
     */
    private String message;

    /**
     * 删除操作ID（用于追踪）
     */
    private String deletionId;

    /**
     * 删除时间
     */
    private LocalDateTime deletionTime;

    /**
     * 受影响的记录信息
     */
    private Map<String, Object> affectedRecords;

    /**
     * 受影响的表
     */
    private String[] affectedTables;

    /**
     * 回滚信息（如果需要）
     */
    private String rollbackInfo;

    /**
     * 操作耗时（毫秒）
     */
    private Long executionTime;

    /**
     * 错误详情（如果失败）
     */
    private String errorDetail;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 审计日志ID
     */
    private Long auditLogId;

    /**
     * 创建成功结果
     */
    public static DebtDeletionResult success(String message) {
        return DebtDeletionResult.builder()
                .success(true)
                .message(message)
                .deletionId(UUID.randomUUID().toString())
                .deletionTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带详细信息）
     */
    public static DebtDeletionResult success(String message, Map<String, Object> affectedRecords) {
        return DebtDeletionResult.builder()
                .success(true)
                .message(message)
                .deletionId(UUID.randomUUID().toString())
                .deletionTime(LocalDateTime.now())
                .affectedRecords(affectedRecords)
                .build();
    }

    /**
     * 创建失败结果
     */
    public static DebtDeletionResult failure(String message) {
        return DebtDeletionResult.builder()
                .success(false)
                .message(message)
                .deletionTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果（带错误详情）
     */
    public static DebtDeletionResult failure(String message, String errorDetail, String errorCode) {
        return DebtDeletionResult.builder()
                .success(false)
                .message(message)
                .errorDetail(errorDetail)
                .errorCode(errorCode)
                .deletionTime(LocalDateTime.now())
                .build();
    }

    /**
     * 设置执行时间
     */
    public DebtDeletionResult withExecutionTime(long startTime) {
        this.executionTime = System.currentTimeMillis() - startTime;
        return this;
    }

    /**
     * 设置受影响的表
     */
    public DebtDeletionResult withAffectedTables(String... tables) {
        this.affectedTables = tables;
        return this;
    }

    /**
     * 设置审计日志ID
     */
    public DebtDeletionResult withAuditLogId(Long auditLogId) {
        this.auditLogId = auditLogId;
        return this;
    }
}