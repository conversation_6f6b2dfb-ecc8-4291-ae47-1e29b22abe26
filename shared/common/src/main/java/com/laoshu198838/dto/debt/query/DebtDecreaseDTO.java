package com.laoshu198838.dto.debt.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 债权处置汇总数据 DTO
 * 用于从减值准备表、处置表中合并数据返回给前端
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
public class DebtDecreaseDTO {

    private String creditor;

    private String debtor;

    private String managementCompany;

    private String period;

    private int month;

    private String isLitigation;

    private BigDecimal currentMonthDisposeDebt;

    private BigDecimal cashDisposal;

    private BigDecimal installmentRepayment;

    private BigDecimal assetDebt;

    private BigDecimal otherWays;


    /**
     * 用于JPQL查询的构造函数
     *
     * @param creditor                债权人
     * @param debtor                  债务人
     * @param managementCompany       管理公司
     * @param period                  期间
     * @param isLitigation            是否涉诉
     * @param currentMonthDisposeDebt 本月处置债权
     * @param cashDisposal            现金处置
     * @param installmentRepayment    分期还款
     * @param assetDebt               资产抵债
     * @param otherWays               其他方式
     */
    @SuppressWarnings("java:S107") // 抑制构造函数参数过多的警告，对于DTO类是必要的
    public DebtDecreaseDTO(String creditor, String debtor, String managementCompany,
                           String period, int month, String isLitigation, BigDecimal currentMonthDisposeDebt,
                           BigDecimal cashDisposal, BigDecimal installmentRepayment,
                           BigDecimal assetDebt, BigDecimal otherWays) {
        this.creditor = creditor;
        this.debtor = debtor;
        this.managementCompany = managementCompany;
        this.period = period;
        this.month = month;
        this.isLitigation = isLitigation;
        this.currentMonthDisposeDebt = currentMonthDisposeDebt;
        this.cashDisposal = cashDisposal;
        this.installmentRepayment = installmentRepayment;
        this.assetDebt = assetDebt;
        this.otherWays = otherWays;
    }
}
