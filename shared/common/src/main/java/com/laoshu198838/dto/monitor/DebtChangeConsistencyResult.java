package com.laoshu198838.dto.monitor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 债权变动一致性检查结果DTO
 * 用于展示不同表之间的新增债权数据一致性检查
 * 
 * <AUTHOR>
 */
public class DebtChangeConsistencyResult {
    
    private boolean isConsistent;
    private SummaryData summaryData;
    private List<TableDataRow> tableData;
    
    public DebtChangeConsistencyResult() {
        this.isConsistent = true;
        this.summaryData = new SummaryData();
        this.tableData = new ArrayList<>();
    }
    
    public boolean isConsistent() {
        return isConsistent;
    }
    
    public void setConsistent(boolean isConsistent) {
        this.isConsistent = isConsistent;
    }
    
    public SummaryData getSummaryData() {
        return summaryData;
    }
    
    public void setSummaryData(SummaryData summaryData) {
        this.summaryData = summaryData;
    }
    
    public List<TableDataRow> getTableData() {
        return tableData;
    }
    
    public void setTableData(List<TableDataRow> tableData) {
        this.tableData = tableData;
    }
    
    /**
     * 汇总数据
     */
    public static class SummaryData {
        private BigDecimal addTableAmount;          // 新增表金额
        private BigDecimal litigationTableAmount;   // 诉讼表金额
        private BigDecimal nonLitigationTableAmount; // 非诉讼表金额
        private BigDecimal impairmentReserveAmount; // 减值准备表金额
        private BigDecimal maxDifference;          // 最大差异值
        
        public SummaryData() {
            this.addTableAmount = BigDecimal.ZERO;
            this.litigationTableAmount = BigDecimal.ZERO;
            this.nonLitigationTableAmount = BigDecimal.ZERO;
            this.impairmentReserveAmount = BigDecimal.ZERO;
            this.maxDifference = BigDecimal.ZERO;
        }

        public BigDecimal getAddTableAmount() {
            return addTableAmount;
        }

        public void setAddTableAmount(BigDecimal addTableAmount) {
            this.addTableAmount = addTableAmount;
        }

        public BigDecimal getLitigationTableAmount() {
            return litigationTableAmount;
        }

        public void setLitigationTableAmount(BigDecimal litigationTableAmount) {
            this.litigationTableAmount = litigationTableAmount;
        }

        public BigDecimal getNonLitigationTableAmount() {
            return nonLitigationTableAmount;
        }

        public void setNonLitigationTableAmount(BigDecimal nonLitigationTableAmount) {
            this.nonLitigationTableAmount = nonLitigationTableAmount;
        }

        public BigDecimal getImpairmentReserveAmount() {
            return impairmentReserveAmount;
        }

        public void setImpairmentReserveAmount(BigDecimal impairmentReserveAmount) {
            this.impairmentReserveAmount = impairmentReserveAmount;
        }

        public BigDecimal getMaxDifference() {
            return maxDifference;
        }

        public void setMaxDifference(BigDecimal maxDifference) {
            this.maxDifference = maxDifference;
        }
    }
    
    /**
     * 表格数据行
     */
    public static class TableDataRow {
        private String debtorCreditor;              // 债务人/债权人
        private BigDecimal addTableAmount;          // 新增表金额
        private BigDecimal litigationTableAmount;   // 诉讼表金额
        private BigDecimal nonLitigationTableAmount; // 非诉讼表金额
        private BigDecimal impairmentReserveAmount; // 减值准备表金额
        private BigDecimal difference;              // 差异值
        private String consistencyStatus;           // 一致性状态
        
        public TableDataRow() {
            this.addTableAmount = BigDecimal.ZERO;
            this.litigationTableAmount = BigDecimal.ZERO;
            this.nonLitigationTableAmount = BigDecimal.ZERO;
            this.impairmentReserveAmount = BigDecimal.ZERO;
            this.difference = BigDecimal.ZERO;
        }

        public String getDebtorCreditor() {
            return debtorCreditor;
        }

        public void setDebtorCreditor(String debtorCreditor) {
            this.debtorCreditor = debtorCreditor;
        }

        public BigDecimal getAddTableAmount() {
            return addTableAmount;
        }

        public void setAddTableAmount(BigDecimal addTableAmount) {
            this.addTableAmount = addTableAmount;
        }

        public BigDecimal getLitigationTableAmount() {
            return litigationTableAmount;
        }

        public void setLitigationTableAmount(BigDecimal litigationTableAmount) {
            this.litigationTableAmount = litigationTableAmount;
        }

        public BigDecimal getNonLitigationTableAmount() {
            return nonLitigationTableAmount;
        }

        public void setNonLitigationTableAmount(BigDecimal nonLitigationTableAmount) {
            this.nonLitigationTableAmount = nonLitigationTableAmount;
        }

        public BigDecimal getImpairmentReserveAmount() {
            return impairmentReserveAmount;
        }

        public void setImpairmentReserveAmount(BigDecimal impairmentReserveAmount) {
            this.impairmentReserveAmount = impairmentReserveAmount;
        }

        public BigDecimal getDifference() {
            return difference;
        }

        public void setDifference(BigDecimal difference) {
            this.difference = difference;
        }

        public String getConsistencyStatus() {
            return consistencyStatus;
        }

        public void setConsistencyStatus(String consistencyStatus) {
            this.consistencyStatus = consistencyStatus;
        }
    }
}
