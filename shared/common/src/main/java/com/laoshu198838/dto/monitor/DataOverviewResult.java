package com.laoshu198838.dto.monitor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据总览结果DTO
 * 用于展示不同表之间的金额数据总览
 * 
 * <AUTHOR>
 */
public class DataOverviewResult {
    
    private Map<String, Map<String, BigDecimal>> data;
    
    public DataOverviewResult() {
        this.data = new HashMap<>();
        
        // 初始化三个维度的数据
        this.data.put("newAmount", new HashMap<>());
        this.data.put("disposedAmount", new HashMap<>());
        this.data.put("endingBalance", new HashMap<>());
    }
    
    public Map<String, Map<String, BigDecimal>> getData() {
        return data;
    }
    
    public void setData(Map<String, Map<String, BigDecimal>> data) {
        this.data = data;
    }
    
    /**
     * 设置某个维度某个表的金额
     * 
     * @param dimension 维度名称 (newAmount, disposedAmount, endingBalance)
     * @param tableName 表名称 (新增表, 诉讼表, 非诉讼表, 减值准备表)
     * @param amount 金额
     */
    public void setAmount(String dimension, String tableName, BigDecimal amount) {
        if (!this.data.containsKey(dimension)) {
            this.data.put(dimension, new HashMap<>());
        }
        
        this.data.get(dimension).put(tableName, amount);
    }
    
    /**
     * 获取某个维度某个表的金额
     * 
     * @param dimension 维度名称 (newAmount, disposedAmount, endingBalance)
     * @param tableName 表名称 (新增表, 诉讼表, 非诉讼表, 减值准备表)
     * @return 金额
     */
    public BigDecimal getAmount(String dimension, String tableName) {
        if (!this.data.containsKey(dimension)) {
            return BigDecimal.ZERO;
        }
        
        Map<String, BigDecimal> dimensionData = this.data.get(dimension);
        return dimensionData.getOrDefault(tableName, BigDecimal.ZERO);
    }
}
