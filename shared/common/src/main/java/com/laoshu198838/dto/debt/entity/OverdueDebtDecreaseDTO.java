package com.laoshu198838.dto.debt.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 用于接收前端提交的债权处置减少数据的 DTO
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OverdueDebtDecreaseDTO {

    // 债权人
    private String creditor;
    // 债务人
    private String debtor;
    // 管理公司
    private String managementCompany;
    // 年月信息（格式：YYYY-MM）
    private String yearMonth;
    // 是否涉诉
    private String isLitigation;
    // 期间
    private String period;
    // 总处置金额
    private Double dispositionAmount;
    // 现金处置金额
    private Double cashAmount;
    // 分期还款金额
    private Double installmentAmount;
    // 资产抵债金额
    private Double assetAmount;
    // 账务调整金额
    private Double adjustmentAmount;
    // 其他方式处置金额
    private Double otherAmount;
    // 备注
    private String remark;
}
