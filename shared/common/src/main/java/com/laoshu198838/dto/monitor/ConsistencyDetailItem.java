package com.laoshu198838.dto.monitor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 一致性检查明细项DTO
 * 用于展示每个债务人/债权人的金额明细
 * 
 * <AUTHOR>
 */
public class ConsistencyDetailItem {
    
    private RecordId id;
    private Map<String, BigDecimal> tableValues;
    private BigDecimal maxDifference;
    
    public ConsistencyDetailItem() {
        this.tableValues = new HashMap<>();
    }
    
    public RecordId getId() {
        return id;
    }
    
    public void setId(RecordId id) {
        this.id = id;
    }
    
    public Map<String, BigDecimal> getTableValues() {
        return tableValues;
    }
    
    public void setTableValues(Map<String, BigDecimal> tableValues) {
        this.tableValues = tableValues;
    }
    
    public BigDecimal getMaxDifference() {
        return maxDifference;
    }
    
    public void setMaxDifference(BigDecimal maxDifference) {
        this.maxDifference = maxDifference;
    }
    
    /**
     * 设置某个表的金额
     * 
     * @param tableName 表名称 (新增表, 诉讼表, 非诉讼表, 减值准备表)
     * @param amount 金额
     */
    public void setTableValue(String tableName, BigDecimal amount) {
        this.tableValues.put(tableName, amount);
    }
    
    /**
     * 获取某个表的金额
     * 
     * @param tableName 表名称 (新增表, 诉讼表, 非诉讼表, 减值准备表)
     * @return 金额
     */
    public BigDecimal getTableValue(String tableName) {
        return this.tableValues.getOrDefault(tableName, BigDecimal.ZERO);
    }
    
    /**
     * 记录ID类
     * 包含债权人、债务人和期间信息
     */
    public static class RecordId {
        private String creditor;
        private String debtor;
        private String period;
        
        public RecordId() {
        }
        
        public RecordId(String creditor, String debtor, String period) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
        }
        
        public String getCreditor() {
            return creditor;
        }
        
        public void setCreditor(String creditor) {
            this.creditor = creditor;
        }
        
        public String getDebtor() {
            return debtor;
        }
        
        public void setDebtor(String debtor) {
            this.debtor = debtor;
        }
        
        public String getPeriod() {
            return period;
        }
        
        public void setPeriod(String period) {
            this.period = period;
        }
    }
}
