package com.laoshu198838.dto.debt;

import com.laoshu198838.dto.debt.base.BaseDebtOperationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 债权删除DTO
 * 用于债权删除操作
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DebtDeletionDTO extends BaseDebtOperationDTO {

    /**
     * 删除原因
     */
    @NotBlank(message = "删除原因不能为空")
    @Size(min = 10, max = 500, message = "删除原因长度必须在10-500字符之间")
    private String deleteReason;

    /**
     * 删除类型
     */
    @NotBlank(message = "删除类型不能为空")
    @Pattern(regexp = "DELETE_ADDITION|DELETE_DISPOSAL", message = "删除类型无效")
    private String deletionType;

    /**
     * 案件名称（诉讼表用）
     */
    @Size(max = 100, message = "案件名称不能超过100个字符")
    private String caseName;

    /**
     * 处置方式明细（删除处置时使用）
     */
    private Map<String, BigDecimal> disposalDetails;

    /**
     * 现金处置
     */
    private BigDecimal cashDisposal;

    /**
     * 分期还款
     */
    private BigDecimal installmentRepayment;

    /**
     * 资产抵债
     */
    private BigDecimal assetDebt;

    /**
     * 其他方式
     */
    private BigDecimal otherWays;

    /**
     * 原始记录ID（如果需要追踪）
     */
    private String originalRecordId;

    /**
     * 是否需要审批
     */
    private Boolean requiresApproval;

    /**
     * 审批人ID
     */
    private String approverId;

    /**
     * 审批人姓名
     */
    private String approverName;

    /**
     * 审批意见
     */
    private String approvalComment;

    /**
     * 删除确认码（二次确认用）
     */
    private String confirmationCode;

    @Override
    public String getOperationType() {
        return deletionType;
    }

    @Override
    public void validateBusinessRules() {
        super.validateBusinessRules();
        
        // 如果是删除处置，验证处置金额分配
        if ("DELETE_DISPOSAL".equals(deletionType)) {
            validateDisposalDistribution();
        }
        
        // 确保金额为正数（后端会转为负数）
        if (getAmount() != null && getAmount().compareTo(BigDecimal.ZERO) < 0) {
            setAmount(getAmount().abs());
        }
    }

    /**
     * 验证处置金额分配
     */
    private void validateDisposalDistribution() {
        if (disposalDetails != null && !disposalDetails.isEmpty()) {
            BigDecimal total = BigDecimal.ZERO;
            
            for (BigDecimal value : disposalDetails.values()) {
                if (value != null) {
                    total = total.add(value);
                }
            }
            
            // 如果有明细，则总和应该等于amount
            if (total.compareTo(getAmount()) != 0) {
                throw new IllegalArgumentException(
                    String.format("处置金额分配不正确：总额=%s，各项之和=%s", 
                                 getAmount(), total)
                );
            }
        }
    }

    /**
     * 转换为负数金额（用于实际处理）
     */
    public BigDecimal getNegativeAmount() {
        return getAmount() != null ? getAmount().negate() : BigDecimal.ZERO;
    }

    /**
     * 是否是删除新增债权
     */
    public boolean isDeleteAddition() {
        return "DELETE_ADDITION".equals(deletionType);
    }

    /**
     * 是否是删除处置债权
     */
    public boolean isDeleteDisposal() {
        return "DELETE_DISPOSAL".equals(deletionType);
    }
}