package com.laoshu198838.util.data;

import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * 数据验证工具类
 * 
 * <p>提供通用的数据验证方法，包括金额验证、日期验证、业务数据验证等功能。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class ValidationUtils {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private ValidationUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    // 日期格式正则表达式
    private static final Pattern DATE_PATTERN_YYYY_MM_DD = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
    private static final Pattern DATE_PATTERN_YYYY_MM = Pattern.compile("^\\d{4}-\\d{2}$");
    private static final Pattern DATE_PATTERN_CHINESE = Pattern.compile("^\\d{4}年\\d{1,2}月(\\d{1,2}日)?$");
    
    /**
     * 验证金额是否有效（非null且大于等于0）
     * 
     * @param amount 待验证的金额
     * @return 如果金额有效则返回true，否则返回false
     */
    public static boolean isValidAmount(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) >= 0;
    }
    
    /**
     * 验证日期字符串是否有效
     * 支持格式：yyyy-MM-dd, yyyy-MM, yyyy年MM月, yyyy年MM月dd日
     * 
     * @param dateStr 待验证的日期字符串
     * @return 如果日期格式有效则返回true，否则返回false
     */
    public static boolean isValidDateString(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return false;
        }
        
        return DATE_PATTERN_YYYY_MM_DD.matcher(dateStr).matches() ||
               DATE_PATTERN_YYYY_MM.matcher(dateStr).matches() ||
               DATE_PATTERN_CHINESE.matcher(dateStr).matches();
    }
    
    /**
     * 验证债权人和债务人是否有效（非空且不为null）
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @return 如果债权人和债务人都有效则返回true，否则返回false
     */
    public static boolean isValidCreditorDebtor(String creditor, String debtor) {
        return StringUtils.isNotBlank(creditor) && StringUtils.isNotBlank(debtor);
    }
    
    /**
     * 验证年份是否有效（在合理范围内）
     * 
     * @param year 年份
     * @return 如果年份有效则返回true，否则返回false
     */
    public static boolean isValidYear(Integer year) {
        return year != null && year >= 2000 && year <= 2100;
    }
    
    /**
     * 验证月份是否有效（1-12）
     * 
     * @param month 月份
     * @return 如果月份有效则返回true，否则返回false
     */
    public static boolean isValidMonth(Integer month) {
        return month != null && month >= 1 && month <= 12;
    }
    
    /**
     * 验证是否涉诉字段是否有效
     * 
     * @param isLitigation 是否涉诉
     * @return 如果字段有效则返回true，否则返回false
     */
    public static boolean isValidLitigationStatus(String isLitigation) {
        return "是".equals(isLitigation) || "否".equals(isLitigation);
    }
}
