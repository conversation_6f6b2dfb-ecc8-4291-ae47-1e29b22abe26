package com.laoshu198838.util.database;

import java.sql.*;
import java.util.HashSet;
import java.util.Set;

/**
 * SQL工具类
 * 
 * <p>提供SQL查询和结果集处理的通用工具方法。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class SqlUtils {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private SqlUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * 获取数据库 `ResultSet` 中的所有列名
     *
     * @param rs  数据库查询结果集
     * @return    包含所有列名的 Set<String>
     * @throws SQLException  如果获取元数据失败
     */
    public static Set<String> getResultSetColumns(ResultSet rs) throws SQLException {
        ResultSetMetaData metaData = rs.getMetaData();
        Set<String> columnSet = new HashSet<>();
        for (int i = 1; i <= metaData.getColumnCount(); i++) {
            columnSet.add(metaData.getColumnName(i));
        }
        return columnSet;
    }

    /**
     * 执行 SQL 查询，返回 ResultSet
     * 
     * <p>注意：调用者负责关闭返回的ResultSet和相关的PreparedStatement</p>
     *
     * @param connection 数据库连接
     * @param sql SQL 语句
     * @param params SQL 参数
     * @return 查询结果 ResultSet
     * @throws SQLException SQL 异常
     */
    public static ResultSet executeQuery(Connection connection, String sql, Object... params) throws SQLException {
        PreparedStatement ps = connection.prepareStatement(sql);
        // 设置参数
        for (int i = 0; i < params.length; i++) {
            ps.setObject(i + 1, params[i]);
        }
        return ps.executeQuery();
    }
}
