package com.laoshu198838.util.file;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件操作工具类
 *
 * <p>提供文件查找、路径解析、文件列表获取等常用文件操作功能。
 * 主要用于处理项目中的资源文件、配置文件和数据文件的访问。</p>
 *
 * <h3>主要功能：</h3>
 * <ul>
 *   <li>根据文件名在classpath中查找文件</li>
 *   <li>获取指定目录下特定扩展名的文件列表</li>
 *   <li>递归搜索文件</li>
 *   <li>文件路径解析和URL解码</li>
 * </ul>
 *
 * <h3>使用场景：</h3>
 * <ul>
 *   <li>配置文件加载（如YAML、Properties文件）</li>
 *   <li>Excel文件批量处理</li>
 *   <li>资源文件访问</li>
 *   <li>文件系统遍历</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class FileUtils {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private FileUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * 获取指定目录下的所有同类型的文件
     *
     * <p>在指定的相对路径目录中查找所有具有指定扩展名的文件。
     * 只搜索直接子文件，不进行递归搜索。</p>
     *
     * @param relativeDirectoryPath 相对于项目根目录的路径
     * @param extension 文件扩展名（不包含点号，如"xlsx"、"java"）
     * @return 包含所有匹配文件的File对象列表
     *
     * @example
     * <pre>
     * // 获取data目录下的所有Excel文件
     * List&lt;File&gt; excelFiles = getFiles("data", "xlsx");
     * </pre>
     */
    public static List<File> getFiles(String relativeDirectoryPath, String extension) {
        // 获取项目的根目录
        String projectRoot = System.getProperty("user.dir");

        // 构建相对路径
        File directory = new File(projectRoot, relativeDirectoryPath);
        List<File> excelFiles = new ArrayList<>();

        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles((dir, name) -> name.endsWith("." + extension));
            if (files != null) {
                Collections.addAll(excelFiles, files);
            }
        }

        return excelFiles;
    }

    /**
     * 在classpath中根据文件名查找文件路径
     *
     * <p>使用ClassLoader在classpath中查找指定名称的文件，
     * 主要用于查找resources目录下的配置文件、参数文件等。</p>
     *
     * <p>查找顺序：</p>
     * <ol>
     *   <li>当前模块的resources目录</li>
     *   <li>依赖jar包中的resources目录</li>
     *   <li>系统classpath中的其他位置</li>
     * </ol>
     *
     * @param fileName 要查找的文件名（包含扩展名）
     * @return 文件的绝对路径，如果未找到则返回null
     *
     * @example
     * <pre>
     * // 查找配置文件
     * String configPath = findFile("application.yml");
     * String paramsPath = findFile("params.yaml");
     * </pre>
     */
    public static String findFile(String fileName) {
        try {
            ClassLoader classLoader = FileUtils.class.getClassLoader();
            URL resourceUrl = classLoader.getResource(fileName);

            if (resourceUrl != null) {
                String path = URLDecoder.decode(resourceUrl.getPath(), StandardCharsets.UTF_8.name());
                return path;
            } else {
                System.out.println("无法找到资源:" + fileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
    
    /**
     * 获取指定目录下所有指定类型的文件路径
     * 
     * @param relativeDirectoryPath 相对目录路径
     * @param extension 文件扩展名
     * @param recursively 是否递归搜索子目录
     * @return 文件路径列表
     */
    public static List<String> getFilesByExtension(String relativeDirectoryPath, String extension, boolean recursively) {
        // 获取项目的根目录
        String projectRoot = System.getProperty("user.dir");

        // 构建相对路径
        File directory = new File(projectRoot, relativeDirectoryPath);

        List<String> filePaths = new ArrayList<>();

        // 调用递归方法进行遍历
        searchFilesRecursively(directory, extension, filePaths, recursively);
        return filePaths;
    }
    
    /**
     * 递归搜索指定目录及子目录中的文件
     * 
     * @param directory 搜索目录
     * @param extension 文件扩展名
     * @param filePaths 结果列表
     * @param recursively 是否递归
     */
    private static void searchFilesRecursively(File directory, String extension, List<String> filePaths, boolean recursively) {
        // 检查目录是否存在并且是否为文件夹
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();

            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory() && recursively) {
                        // 如果是目录且recursively为真，递归调用
                        searchFilesRecursively(file, extension, filePaths, true);
                    } else if (file.isFile() && file.getName().endsWith(extension)) {
                        // 如果是文件且符合扩展名，添加到列表
                        filePaths.add(file.getAbsolutePath());
                    }
                }
            }
        }
    }
    
    /**
     * 递归方法在目录及其子目录中查找文件
     * 
     * @param dir 搜索目录
     * @param fileName 文件名
     * @return 找到的文件，如果未找到则返回null
     */
    private static File searchFile(File dir, String fileName) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (fileName.equals(file.getName())) {
                    return file;
                } else if (file.isDirectory()) {
                    // 递归查找子目录
                    File found = searchFile(file, fileName);
                    if (found != null) {
                        return found;
                    }
                }
            }
        }
        return null;
    }
}
