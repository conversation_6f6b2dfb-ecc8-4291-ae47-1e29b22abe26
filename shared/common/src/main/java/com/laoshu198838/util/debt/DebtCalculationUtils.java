package com.laoshu198838.util.debt;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 债权计算工具类
 * 提供债权管理中的各种金额计算方法
 *
 * <AUTHOR>
 */
@Slf4j
public class DebtCalculationUtils {

    /**
     * 默认小数位数
     */
    private static final int DEFAULT_SCALE = 2;

    /**
     * 默认舍入模式
     */
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 安全的加法运算
     * 处理null值，null被视为0
     */
    public static BigDecimal safeAdd(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return BigDecimal.ZERO;
        }
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.add(b);
    }

    /**
     * 安全的减法运算
     * 处理null值，null被视为0
     */
    public static BigDecimal safeSubtract(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return BigDecimal.ZERO;
        }
        if (a == null) {
            return b.negate();
        }
        if (b == null) {
            return a;
        }
        return a.subtract(b);
    }

    /**
     * 计算本月末债权余额
     * 公式：上月末余额 + 本月新增 - 本月处置
     */
    public static BigDecimal calculateMonthEndBalance(
            BigDecimal lastMonthBalance,
            BigDecimal monthlyAddition,
            BigDecimal monthlyDisposal) {
        
        BigDecimal balance = safeAdd(lastMonthBalance, monthlyAddition);
        balance = safeSubtract(balance, monthlyDisposal);
        
        log.debug("计算本月末余额: {} = {} + {} - {}", 
                 balance, lastMonthBalance, monthlyAddition, monthlyDisposal);
        
        return balance.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 计算本年度累计回收
     * 根据债权人、债务人、期间等信息累加本年度所有处置金额
     */
    public static BigDecimal calculateAnnualRecovery(
            BigDecimal[] monthlyDisposals) {
        
        BigDecimal total = BigDecimal.ZERO;
        for (BigDecimal disposal : monthlyDisposals) {
            total = safeAdd(total, disposal);
        }
        
        return total.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 计算减值准备金额
     * 默认按50%计算，如果有特定比例则使用特定比例
     */
    public static BigDecimal calculateImpairmentAmount(
            BigDecimal debtBalance, 
            BigDecimal impairmentRate) {
        
        if (debtBalance == null || debtBalance.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal rate = impairmentRate != null ? impairmentRate : new BigDecimal("0.5");
        BigDecimal impairment = debtBalance.multiply(rate);
        
        return impairment.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 验证上月末债权余额平衡
     * 诉讼表：上月末债权余额 = 涉诉债权本金 + 涉诉债权应收利息
     */
    public static boolean validateLitigationBalance(
            BigDecimal lastMonthBalance,
            BigDecimal principal,
            BigDecimal interest) {
        
        BigDecimal calculated = safeAdd(principal, interest);
        BigDecimal difference = safeSubtract(lastMonthBalance, calculated).abs();
        
        // 允许0.01的误差
        return difference.compareTo(new BigDecimal("0.01")) <= 0;
    }

    /**
     * 调整诉讼表本金和利息
     * 如果余额不平衡，默认调整本金
     */
    public static BigDecimal[] adjustPrincipalAndInterest(
            BigDecimal targetBalance,
            BigDecimal currentPrincipal,
            BigDecimal currentInterest) {
        
        BigDecimal currentTotal = safeAdd(currentPrincipal, currentInterest);
        
        if (validateLitigationBalance(targetBalance, currentPrincipal, currentInterest)) {
            return new BigDecimal[]{currentPrincipal, currentInterest};
        }
        
        // 保持利息不变，调整本金
        BigDecimal adjustedPrincipal = safeSubtract(targetBalance, currentInterest);
        
        log.info("调整诉讼表本金: 原本金={}, 调整后本金={}, 利息保持={}", 
                currentPrincipal, adjustedPrincipal, currentInterest);
        
        return new BigDecimal[]{adjustedPrincipal, currentInterest};
    }

    /**
     * 计算处置金额分配
     * 按比例分配到不同处置方式
     */
    public static BigDecimal[] distributeDisposalAmount(
            BigDecimal totalAmount,
            BigDecimal cashRatio,
            BigDecimal installmentRatio,
            BigDecimal assetRatio,
            BigDecimal otherRatio) {
        
        // 确保比例和为1
        BigDecimal totalRatio = safeAdd(safeAdd(cashRatio, installmentRatio), 
                                       safeAdd(assetRatio, otherRatio));
        
        if (totalRatio.compareTo(BigDecimal.ONE) != 0) {
            log.warn("处置比例之和不等于1: {}", totalRatio);
        }
        
        BigDecimal cash = totalAmount.multiply(cashRatio != null ? cashRatio : BigDecimal.ZERO);
        BigDecimal installment = totalAmount.multiply(installmentRatio != null ? installmentRatio : BigDecimal.ZERO);
        BigDecimal asset = totalAmount.multiply(assetRatio != null ? assetRatio : BigDecimal.ZERO);
        BigDecimal other = totalAmount.multiply(otherRatio != null ? otherRatio : BigDecimal.ZERO);
        
        return new BigDecimal[]{
            cash.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE),
            installment.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE),
            asset.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE),
            other.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE)
        };
    }

    /**
     * 判断是否全额计提坏账
     * 如果本月末债权余额 = 本月末减值准备余额，则为全额计提
     */
    public static boolean isFullyImpaired(
            BigDecimal debtBalance,
            BigDecimal impairmentBalance) {
        
        if (debtBalance == null || impairmentBalance == null) {
            return false;
        }
        
        BigDecimal difference = safeSubtract(debtBalance, impairmentBalance).abs();
        return difference.compareTo(new BigDecimal("0.01")) <= 0;
    }

    /**
     * 转换负数金额
     * 用于删除操作，确保金额为负数
     */
    public static BigDecimal toNegativeAmount(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return amount.abs().negate();
    }

    /**
     * 转换正数金额
     * 确保金额为正数
     */
    public static BigDecimal toPositiveAmount(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return amount.abs();
    }

    /**
     * 检查金额是否为负数
     */
    public static boolean isNegative(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 格式化金额显示
     * 保留2位小数
     */
    public static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        return amount.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE).toString();
    }
}