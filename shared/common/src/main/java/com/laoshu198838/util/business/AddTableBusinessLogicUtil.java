package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 新增表业务逻辑工具类
 * 
 * 负责处理新增表相关的业务计算和验证逻辑，包括：
 * 1. 处置金额自动计算（从处置表筛选累计金额）
 * 2. 债权余额计算公式（新增金额 - 处置金额 = 债权余额）
 * 3. 期间字段生成（年份+新增债权）
 * 4. 月度金额汇总计算
 * 
 * <AUTHOR>
 */
@Component
public class AddTableBusinessLogicUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(AddTableBusinessLogicUtil.class);
    
    /**
     * 计算处置金额
     * 从处置表中筛选并累计对应债权人、债务人的处置金额
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param disposalRecords 处置记录列表
     * @return 累计处置金额
     */
    public BigDecimal calculateDisposalAmount(String creditor, String debtor, 
                                            String period, String year,
                                            List<OverdueDebtDecrease> disposalRecords) {
        if (disposalRecords == null || disposalRecords.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalDisposal = BigDecimal.ZERO;
        
        for (OverdueDebtDecrease record : disposalRecords) {
            // 匹配债权人、债务人、期间和年份
            if (creditor.equals(record.getCreditor()) &&
                debtor.equals(record.getDebtor()) &&
                period.equals(record.getPeriod()) &&
                String.valueOf(record.getYear()).equals(year)) {
                
                // 累加每月处置金额
                if (record.getMonthlyReduceAmount() != null) {
                    totalDisposal = totalDisposal.add(record.getMonthlyReduceAmount());
                }
            }
        }
        
        logger.debug("计算处置金额: 债权人={}, 债务人={}, 期间={}, 年份={}, 累计处置金额={}", 
                    creditor, debtor, period, year, totalDisposal);
        
        return totalDisposal;
    }
    
    /**
     * 计算债权余额
     * 公式：新增金额 - 处置金额 = 债权余额
     * 
     * @param newDebtAmount 新增金额
     * @param disposalAmount 处置金额
     * @return 债权余额
     */
    public BigDecimal calculateDebtBalance(BigDecimal newDebtAmount, BigDecimal disposalAmount) {
        if (newDebtAmount == null) {
            newDebtAmount = BigDecimal.ZERO;
        }
        if (disposalAmount == null) {
            disposalAmount = BigDecimal.ZERO;
        }
        
        BigDecimal debtBalance = newDebtAmount.subtract(disposalAmount);
        
        logger.debug("计算债权余额: 新增金额={}, 处置金额={}, 债权余额={}", 
                    newDebtAmount, disposalAmount, debtBalance);
        
        return debtBalance;
    }
    
    /**
     * 生成期间字段
     * 格式：年份 + "新增债权"
     * 
     * @param year 年份
     * @return 期间字符串
     */
    public String generatePeriod(String year) {
        if (year == null || year.trim().isEmpty()) {
            throw new IllegalArgumentException("年份不能为空");
        }
        
        String period = year + "新增债权";
        logger.debug("生成期间字段: 年份={}, 期间={}", year, period);
        
        return period;
    }
    
    /**
     * 计算年度新增总额
     * 将1月到12月的金额相加
     * 注意：这个逻辑已经通过数据库触发器实现，此方法用于验证或手动计算
     * 
     * @param addRecord 新增表记录
     * @return 年度新增总额
     */
    public BigDecimal calculateAnnualNewAmount(OverdueDebtAdd addRecord) {
        if (addRecord == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal total = BigDecimal.ZERO;
        
        // 累加1月到12月的金额
        BigDecimal[] monthlyAmounts = {
            addRecord.getAmountJan(), addRecord.getAmountFeb(), addRecord.getAmountMar(),
            addRecord.getAmountApr(), addRecord.getAmountMay(), addRecord.getAmountJun(),
            addRecord.getAmountJul(), addRecord.getAmountAug(), addRecord.getAmountSep(),
            addRecord.getAmountOct(), addRecord.getAmountNov(), addRecord.getAmountDec()
        };
        
        for (BigDecimal amount : monthlyAmounts) {
            if (amount != null) {
                total = total.add(amount);
            }
        }
        
        logger.debug("计算年度新增总额: 债权人={}, 债务人={}, 总额={}", 
                    addRecord.getCreditor(), addRecord.getDebtor(), total);
        
        return total;
    }
    
    /**
     * 验证新增表数据的完整性
     * 
     * @param addRecord 新增表记录
     * @return 验证结果，true表示通过
     */
    public boolean validateAddTableData(OverdueDebtAdd addRecord) {
        if (addRecord == null) {
            logger.warn("新增表记录为空");
            return false;
        }
        
        // 验证必要字段
        if (addRecord.getCreditor() == null || addRecord.getCreditor().trim().isEmpty()) {
            logger.warn("债权人不能为空");
            return false;
        }
        
        if (addRecord.getDebtor() == null || addRecord.getDebtor().trim().isEmpty()) {
            logger.warn("债务人不能为空");
            return false;
        }
        
        if (addRecord.getYear() == null || addRecord.getYear().trim().isEmpty()) {
            logger.warn("年份不能为空");
            return false;
        }
        
        // 验证新增金额与月度金额的一致性
        BigDecimal calculatedTotal = calculateAnnualNewAmount(addRecord);
        BigDecimal recordedTotal = addRecord.getNewOverdueDebtAmount();
        
        if (recordedTotal != null && calculatedTotal.compareTo(recordedTotal) != 0) {
            logger.warn("新增金额与月度金额汇总不一致: 记录值={}, 计算值={}", 
                       recordedTotal, calculatedTotal);
            return false;
        }
        
        logger.debug("新增表数据验证通过: 债权人={}, 债务人={}", 
                    addRecord.getCreditor(), addRecord.getDebtor());
        
        return true;
    }
    
    /**
     * 更新新增表的处置金额和债权余额
     * 
     * @param addRecord 新增表记录
     * @param disposalRecords 处置记录列表
     */
    public void updateDisposalAndBalance(OverdueDebtAdd addRecord, List<OverdueDebtDecrease> disposalRecords) {
        if (addRecord == null) {
            logger.warn("新增表记录为空，无法更新处置金额和债权余额");
            return;
        }
        
        // 计算处置金额
        BigDecimal disposalAmount = calculateDisposalAmount(
            addRecord.getCreditor(),
            addRecord.getDebtor(),
            addRecord.getPeriod(),
            addRecord.getYear(),
            disposalRecords
        );
        
        // 更新处置金额相关字段
        addRecord.setCashDisposal(disposalAmount);
        
        // 计算并更新债权余额
        BigDecimal debtBalance = calculateDebtBalance(
            addRecord.getNewOverdueDebtAmount(),
            disposalAmount
        );
        addRecord.setDebtBalance(debtBalance);
        
        logger.info("更新新增表处置金额和债权余额: 债权人={}, 债务人={}, 处置金额={}, 债权余额={}", 
                   addRecord.getCreditor(), addRecord.getDebtor(), disposalAmount, debtBalance);
    }
}
