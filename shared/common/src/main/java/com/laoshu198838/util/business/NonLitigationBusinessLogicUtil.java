package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 非诉讼表业务逻辑工具类
 * 
 * 负责处理非诉讼表相关的业务计算和验证逻辑，包括：
 * 1. 本金双重计算公式验证
 *    - 公式1：上月末本金 + 本月本金增减 = 本月末本金
 *    - 公式2：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金
 * 2. 利息和违约金完整计算逻辑
 *    - 上月末利息 + 本月利息增减 = 本月末利息
 *    - 上月末违约金 + 本月违约金增减 = 本月末违约金
 * 3. 本年度累计回收计算
 * 4. 逾期年限计算
 * 5. 期间字段生成
 * 
 * <AUTHOR>
 */
@Component
public class NonLitigationBusinessLogicUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(NonLitigationBusinessLogicUtil.class);
    
    /**
     * 验证和计算本金双重公式
     * 公式1：上月末本金 + 本月本金增减 = 本月末本金（已实现）
     * 公式2：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金（需新增）
     * 如果两个公式结果不一致，使用公式2的结果，并调整本月本金增减
     * 
     * @param nonLitigationClaim 非诉讼表记录
     */
    public void validateAndCalculatePrincipalBalances(NonLitigationClaim nonLitigationClaim) {
        if (nonLitigationClaim == null) {
            logger.warn("非诉讼表记录为空，无法进行本金计算");
            return;
        }
        
        BigDecimal lastMonthPrincipal = nonLitigationClaim.getLastMonthPrincipal();
        BigDecimal principalIncrease = nonLitigationClaim.getCurrentMonthPrincipalIncreaseDecrease();
        BigDecimal newDebt = nonLitigationClaim.getCurrentMonthNewDebt();
        BigDecimal disposedDebt = nonLitigationClaim.getCurrentMonthDisposedDebt();
        
        // 设置默认值
        if (lastMonthPrincipal == null) lastMonthPrincipal = BigDecimal.ZERO;
        if (principalIncrease == null) principalIncrease = BigDecimal.ZERO;
        if (newDebt == null) newDebt = BigDecimal.ZERO;
        if (disposedDebt == null) disposedDebt = BigDecimal.ZERO;
        
        // 公式1计算结果：上月末本金 + 本月本金增减 = 本月末本金
        BigDecimal calculatedByFormula1 = lastMonthPrincipal.add(principalIncrease);
        
        // 公式2计算结果：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金
        BigDecimal calculatedByFormula2 = lastMonthPrincipal.add(newDebt).subtract(disposedDebt);
        
        // 验证两个公式的结果是否一致
        if (calculatedByFormula1.compareTo(calculatedByFormula2) != 0) {
            logger.info("本金双重公式结果不一致，使用公式2结果并调整本月本金增减: 债权人={}, 债务人={}, 公式1结果={}, 公式2结果={}", 
                       nonLitigationClaim.getId().getCreditor(), nonLitigationClaim.getId().getDebtor(), 
                       calculatedByFormula1, calculatedByFormula2);
            
            // 使用公式2的结果
            nonLitigationClaim.setCurrentMonthPrincipal(calculatedByFormula2);
            
            // 调整本月本金增减
            BigDecimal adjustedIncrease = calculatedByFormula2.subtract(lastMonthPrincipal);
            nonLitigationClaim.setCurrentMonthPrincipalIncreaseDecrease(adjustedIncrease);
        } else {
            // 两个公式结果一致，使用公式1的结果
            nonLitigationClaim.setCurrentMonthPrincipal(calculatedByFormula1);
            logger.debug("本金双重公式结果一致: 债权人={}, 债务人={}, 本月末本金={}", 
                        nonLitigationClaim.getId().getCreditor(), nonLitigationClaim.getId().getDebtor(), 
                        calculatedByFormula1);
        }
    }
    
    /**
     * 计算利息和违约金余额
     * 上月末利息 + 本月利息增减 = 本月末利息
     * 上月末违约金 + 本月违约金增减 = 本月末违约金
     * 
     * @param nonLitigationClaim 非诉讼表记录
     */
    public void calculateInterestAndPenaltyBalances(NonLitigationClaim nonLitigationClaim) {
        if (nonLitigationClaim == null) {
            logger.warn("非诉讼表记录为空，无法计算利息和违约金");
            return;
        }
        
        // 计算利息：上月末利息 + 本月利息增减 = 本月末利息
        BigDecimal lastMonthInterest = nonLitigationClaim.getLastMonthInterest();
        BigDecimal interestIncrease = nonLitigationClaim.getCurrentMonthInterestIncreaseDecrease();
        
        if (lastMonthInterest == null) lastMonthInterest = BigDecimal.ZERO;
        if (interestIncrease == null) interestIncrease = BigDecimal.ZERO;
        
        BigDecimal currentMonthInterest = lastMonthInterest.add(interestIncrease);
        nonLitigationClaim.setCurrentMonthInterest(currentMonthInterest);
        
        // 计算违约金：上月末违约金 + 本月违约金增减 = 本月末违约金
        BigDecimal lastMonthPenalty = nonLitigationClaim.getLastMonthPenalty();
        BigDecimal penaltyIncrease = nonLitigationClaim.getCurrentMonthPenaltyIncreaseDecrease();
        
        if (lastMonthPenalty == null) lastMonthPenalty = BigDecimal.ZERO;
        if (penaltyIncrease == null) penaltyIncrease = BigDecimal.ZERO;
        
        BigDecimal currentMonthPenalty = lastMonthPenalty.add(penaltyIncrease);
        nonLitigationClaim.setCurrentMonthPenalty(currentMonthPenalty);
        
        logger.debug("计算利息和违约金: 债权人={}, 债务人={}, 本月末利息={}, 本月末违约金={}", 
                    nonLitigationClaim.getId().getCreditor(), nonLitigationClaim.getId().getDebtor(), 
                    currentMonthInterest, currentMonthPenalty);
    }
    
    /**
     * 计算总债权余额
     * 总债权余额 = 本月末本金 + 本月末利息 + 本月末违约金
     * 
     * @param nonLitigationClaim 非诉讼表记录
     * @return 总债权余额
     */
    public BigDecimal calculateTotalDebtBalance(NonLitigationClaim nonLitigationClaim) {
        if (nonLitigationClaim == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal principal = nonLitigationClaim.getCurrentMonthPrincipal();
        BigDecimal interest = nonLitigationClaim.getCurrentMonthInterest();
        BigDecimal penalty = nonLitigationClaim.getCurrentMonthPenalty();
        
        if (principal == null) principal = BigDecimal.ZERO;
        if (interest == null) interest = BigDecimal.ZERO;
        if (penalty == null) penalty = BigDecimal.ZERO;
        
        BigDecimal totalBalance = principal.add(interest).add(penalty);
        
        logger.debug("计算总债权余额: 债权人={}, 债务人={}, 本金={}, 利息={}, 违约金={}, 总余额={}", 
                    nonLitigationClaim.getId().getCreditor(), nonLitigationClaim.getId().getDebtor(), 
                    principal, interest, penalty, totalBalance);
        
        return totalBalance;
    }
    
    /**
     * 计算本年度累计回收
     * 根据债权人、债务人、年份、期间确定唯一值后累加本月处置债权
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param year 年份
     * @param period 期间
     * @param currentMonthDisposed 本月处置债权
     * @param previousAnnualRecovery 之前的年度累计回收
     * @return 本年度累计回收
     */
    public BigDecimal calculateAnnualCumulativeRecovery(String creditor, String debtor, 
                                                       int year, String period,
                                                       BigDecimal currentMonthDisposed,
                                                       BigDecimal previousAnnualRecovery) {
        if (currentMonthDisposed == null) {
            currentMonthDisposed = BigDecimal.ZERO;
        }
        if (previousAnnualRecovery == null) {
            previousAnnualRecovery = BigDecimal.ZERO;
        }
        
        BigDecimal annualRecovery = previousAnnualRecovery.add(currentMonthDisposed);
        
        logger.debug("计算非诉讼表本年度累计回收: 债权人={}, 债务人={}, 本月处置={}, 累计回收={}", 
                    creditor, debtor, currentMonthDisposed, annualRecovery);
        
        return annualRecovery;
    }
    
    /**
     * 计算逾期年限
     * 根据债权到期时间和当前时间计算逾期年限
     * 
     * @param dueDate 到期时间
     * @param currentYear 当前年份
     * @param currentMonth 当前月份
     * @return 逾期年限分类（≤1年、1-5年、≥5年）
     */
    public String calculateOverdueYear(java.util.Date dueDate, int currentYear, int currentMonth) {
        if (dueDate == null) {
            return "未知";
        }
        
        // 简化计算：基于年份差异
        java.util.Calendar dueCal = java.util.Calendar.getInstance();
        dueCal.setTime(dueDate);
        int dueYear = dueCal.get(java.util.Calendar.YEAR);
        int dueMonth = dueCal.get(java.util.Calendar.MONTH) + 1; // Calendar月份从0开始
        
        // 计算逾期月数
        int overdueMonths = (currentYear - dueYear) * 12 + (currentMonth - dueMonth);
        
        if (overdueMonths <= 0) {
            return "未逾期";
        } else if (overdueMonths <= 12) {
            return "≤1年";
        } else if (overdueMonths <= 60) {
            return "1-5年";
        } else {
            return "≥5年";
        }
    }
    
    /**
     * 验证非诉讼表数据的完整性
     * 
     * @param nonLitigationClaim 非诉讼表记录
     * @return 验证结果，true表示通过
     */
    public boolean validateNonLitigationClaimData(NonLitigationClaim nonLitigationClaim) {
        if (nonLitigationClaim == null) {
            logger.warn("非诉讼表记录为空");
            return false;
        }
        
        // 验证必要字段
        if (nonLitigationClaim.getId() == null) {
            logger.warn("非诉讼表主键为空");
            return false;
        }
        
        // 验证本金双重公式的一致性
        BigDecimal lastMonthPrincipal = nonLitigationClaim.getLastMonthPrincipal();
        BigDecimal principalIncrease = nonLitigationClaim.getCurrentMonthPrincipalIncreaseDecrease();
        BigDecimal currentMonthPrincipal = nonLitigationClaim.getCurrentMonthPrincipal();
        BigDecimal newDebt = nonLitigationClaim.getCurrentMonthNewDebt();
        BigDecimal disposedDebt = nonLitigationClaim.getCurrentMonthDisposedDebt();
        
        if (lastMonthPrincipal != null && principalIncrease != null && currentMonthPrincipal != null) {
            // 验证公式1：上月末本金 + 本月本金增减 = 本月末本金
            BigDecimal calculatedByFormula1 = lastMonthPrincipal.add(principalIncrease);
            if (calculatedByFormula1.compareTo(currentMonthPrincipal) != 0) {
                logger.warn("本金公式1验证失败: 计算值={}, 记录值={}", calculatedByFormula1, currentMonthPrincipal);
                return false;
            }
            
            // 验证公式2（如果有新增和处置数据）
            if (newDebt != null && disposedDebt != null) {
                BigDecimal calculatedByFormula2 = lastMonthPrincipal.add(newDebt).subtract(disposedDebt);
                if (calculatedByFormula2.compareTo(currentMonthPrincipal) != 0) {
                    logger.warn("本金公式2验证失败: 计算值={}, 记录值={}", calculatedByFormula2, currentMonthPrincipal);
                    return false;
                }
            }
        }
        
        logger.debug("非诉讼表数据验证通过: 债权人={}, 债务人={}", 
                    nonLitigationClaim.getId().getCreditor(), nonLitigationClaim.getId().getDebtor());
        
        return true;
    }
    
    /**
     * 更新非诉讼表的完整计算逻辑
     * 
     * @param nonLitigationClaim 非诉讼表记录
     */
    public void updateCompleteCalculation(NonLitigationClaim nonLitigationClaim) {
        if (nonLitigationClaim == null) {
            logger.warn("非诉讼表记录为空，无法更新计算");
            return;
        }
        
        // 1. 验证和计算本金双重公式
        validateAndCalculatePrincipalBalances(nonLitigationClaim);
        
        // 2. 计算利息和违约金
        calculateInterestAndPenaltyBalances(nonLitigationClaim);
        
        logger.info("更新非诉讼表完整计算: 债权人={}, 债务人={}", 
                   nonLitigationClaim.getId().getCreditor(), 
                   nonLitigationClaim.getId().getDebtor());
    }
}
