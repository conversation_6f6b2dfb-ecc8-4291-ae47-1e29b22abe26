package com.laoshu198838.util.file;

import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * YAML文件操作工具类
 *
 * <p>提供YAML配置文件的读取和解析功能，支持单层和多层嵌套结构的数据访问。
 * 主要用于读取系统配置参数、业务参数等YAML格式的配置文件。</p>
 *
 * <h3>主要功能：</h3>
 * <ul>
 *   <li>读取YAML文件中的单层键值对</li>
 *   <li>读取YAML文件中的嵌套结构数据</li>
 *   <li>支持复杂数据类型（List、Map等）</li>
 *   <li>异常处理和错误日志记录</li>
 * </ul>
 *
 * <h3>支持的YAML结构：</h3>
 * <pre>
 * # 单层结构
 * database_name: "财务数据库"
 * port: 3306
 *
 * # 嵌套结构
 * database:
 *   host: "localhost"
 *   port: 3306
 *   name: "finance_db"
 *
 * # 列表结构
 * companies:
 *   - name: "公司A"
 *     code: "001"
 *   - name: "公司B"
 *     code: "002"
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 * @see org.yaml.snakeyaml.Yaml
 */
public class YamlUtils {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private YamlUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * 日志记录器，用于记录YAML文件操作过程中的信息和错误
     */
    private static final Logger LOGGER = Logger.getLogger(YamlUtils.class.getName());

    /**
     * 读取YAML文件中的单层参数
     *
     * <p>从指定的YAML文件中读取第一层级的键值对数据。
     * 适用于简单的配置参数读取，如数据库名称、端口号等。</p>
     *
     * <p>支持的数据类型：</p>
     * <ul>
     *   <li>String - 字符串值</li>
     *   <li>Integer - 整数值</li>
     *   <li>Boolean - 布尔值</li>
     *   <li>List - 列表数据</li>
     *   <li>Map - 映射数据</li>
     * </ul>
     *
     * @param fileName YAML文件名（需要在classpath中）
     * @param key 要读取的键名
     * @return 键对应的值，如果键不存在或文件读取失败则返回null
     *
     * @example
     * <pre>
     * // 读取数据库配置
     * String dbName = (String) readSingleLevelYaml("config.yaml", "database_name");
     * Integer port = (Integer) readSingleLevelYaml("config.yaml", "port");
     *
     * // 读取列表数据
     * List&lt;String&gt; companies = (List&lt;String&gt;) readSingleLevelYaml("params.yaml", "companies");
     * </pre>
     */
    public static Object readSingleLevelYaml(String fileName, String key) {
        Yaml yaml = new Yaml();
        // 利用工具方法获取文件路径
        String filePath = FileUtils.findFile(fileName);
        // 检查文件路径是否有效
        if (filePath == null || filePath.isEmpty()) {
            System.out.println("文件路径无效，未找到文件: " + fileName);
            return null;
        }

        try (InputStream in = new FileInputStream(filePath)) {
            // 加载 YAML 文件
            Map<String, Object> data = yaml.load(in);
            if (data == null) {
                System.err.println("YAML 文件为空或格式无效:" + fileName);
                return null;
            }

            // 获取指定键的值
            Object value = data.get(key);
            if (value == null) {
                System.err.println("键 " + key + " 不存在于 YAML 文件: " + fileName);
            }
            return value;
        } catch (FileNotFoundException e) {
            System.err.println("文件未找到:" + filePath);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "读取 YAML 文件时发生异常: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 读取YAML文件中的两层嵌套参数
     *
     * @param fileName YAML文件名
     * @param parentKey 父级键名
     * @param childKey 子级键名
     * @return 子级键对应的值，如果不存在则返回null
     */
    public static Object readTwoLevelYaml(String fileName, String parentKey, String childKey) {
        Yaml yaml = new Yaml();

        // 获取文件路径
        String filePath = FileUtils.findFile(fileName);
        // 检查文件路径有效性
        if (filePath == null || filePath.isEmpty()) {
            System.err.println("文件路径无效，未找到文件: " + fileName);
            return null;
        }

        try (InputStream in = new FileInputStream(filePath)) {
            // 加载 YAML 文件
            Map<String, Object> data = yaml.load(in);
            if (data == null || !data.containsKey(parentKey)) {
                System.err.println("父级键 '" + parentKey + "' 不存在于 YAML 文件: " + fileName);
                return null;
            }

            // 获取父级键对应的值
            Object parentValue = data.get(parentKey);
            if (!(parentValue instanceof Map)) {
                System.err.println("父级键 '" + parentKey + "' 的值不是一个嵌套对象 (Map): " + fileName);
                return null;
            }

            // 检查子级键并获取值
            @SuppressWarnings("unchecked")
            Map<String, Object> childMap = (Map<String, Object>) parentValue;
            Object childValue = childMap.get(childKey);
            if (childValue == null) {
                System.err.println("子级键 '" + childKey + "' 不存在于父级键 '" + parentKey + "' 下: " + fileName);
            }

            return childValue;

        } catch (FileNotFoundException e) {
            System.err.println("文件未找到: " + filePath);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "读取 YAML 文件时发生异常: " + e.getMessage(), e);
        }
        return null;
    }
}
