package com.laoshu198838.util.debt;

import com.laoshu198838.entity.overdue_debt.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;

/**
 * 五表更新辅助工具类
 * 提供统一的五表更新逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FiveTableUpdateHelper {

    /**
     * 更新上下文，包含更新所需的所有信息
     */
    public static class UpdateContext {
        private String creditor;
        private String debtor;
        private String managementCompany;
        private String isLitigation;
        private String period;
        private Integer year;
        private Integer month;
        private BigDecimal amount;
        private String operationType; // ADD, DISPOSE, DELETE_ADD, DELETE_DISPOSE
        private String remark;
        private LocalDateTime operationTime;

        // Getters and setters
        public String getCreditor() { return creditor; }
        public void setCreditor(String creditor) { this.creditor = creditor; }
        
        public String getDebtor() { return debtor; }
        public void setDebtor(String debtor) { this.debtor = debtor; }
        
        public String getManagementCompany() { return managementCompany; }
        public void setManagementCompany(String managementCompany) { this.managementCompany = managementCompany; }
        
        public String getIsLitigation() { return isLitigation; }
        public void setIsLitigation(String isLitigation) { this.isLitigation = isLitigation; }
        
        public String getPeriod() { return period; }
        public void setPeriod(String period) { this.period = period; }
        
        public Integer getYear() { return year; }
        public void setYear(Integer year) { this.year = year; }
        
        public Integer getMonth() { return month; }
        public void setMonth(Integer month) { this.month = month; }
        
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        
        public String getOperationType() { return operationType; }
        public void setOperationType(String operationType) { this.operationType = operationType; }
        
        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
        
        public LocalDateTime getOperationTime() { return operationTime; }
        public void setOperationTime(LocalDateTime operationTime) { this.operationTime = operationTime; }
    }

    /**
     * 判断是否需要更新后续月份
     */
    public static boolean needsSubsequentMonthsUpdate(UpdateContext context) {
        int currentYear = LocalDate.now().getYear();
        int currentMonth = LocalDate.now().getMonthValue();
        
        return context.getYear() < currentYear || 
               (context.getYear() == currentYear && context.getMonth() < currentMonth);
    }

    /**
     * 获取需要更新的后续月份列表
     */
    public static Calendar[] getSubsequentMonths(UpdateContext context) {
        int currentYear = LocalDate.now().getYear();
        int currentMonth = LocalDate.now().getMonthValue();
        
        Calendar startCal = Calendar.getInstance();
        startCal.set(Calendar.YEAR, context.getYear());
        startCal.set(Calendar.MONTH, context.getMonth() - 1);
        startCal.add(Calendar.MONTH, 1); // 下一个月开始
        
        Calendar endCal = Calendar.getInstance();
        endCal.set(Calendar.YEAR, currentYear);
        endCal.set(Calendar.MONTH, currentMonth - 1);
        
        // 计算月份数量
        int monthCount = 0;
        Calendar tempCal = (Calendar) startCal.clone();
        while (!tempCal.after(endCal)) {
            monthCount++;
            tempCal.add(Calendar.MONTH, 1);
        }
        
        // 生成月份数组
        Calendar[] months = new Calendar[monthCount];
        tempCal = (Calendar) startCal.clone();
        for (int i = 0; i < monthCount; i++) {
            months[i] = (Calendar) tempCal.clone();
            tempCal.add(Calendar.MONTH, 1);
        }
        
        return months;
    }

    /**
     * 更新减值准备表逻辑
     */
    public static void updateImpairmentReserve(ImpairmentReserve reserve, UpdateContext context) {
        log.info("更新减值准备表: 债权人={}, 债务人={}, 年={}, 月={}", 
                context.getCreditor(), context.getDebtor(), context.getYear(), context.getMonth());
        
        BigDecimal lastMonthBalance = reserve.getLastMonthBalance() != null ? 
                                     reserve.getLastMonthBalance() : BigDecimal.ZERO;
        BigDecimal newDebt = reserve.getCurrentMonthNewDebt() != null ? 
                            reserve.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        BigDecimal disposedDebt = reserve.getCurrentMonthDisposeDebt() != null ? 
                                 reserve.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;
        
        // 根据操作类型更新金额
        switch (context.getOperationType()) {
            case "ADD":
                newDebt = DebtCalculationUtils.safeAdd(newDebt, context.getAmount());
                reserve.setCurrentMonthNewDebt(newDebt);
                break;
            case "DISPOSE":
                disposedDebt = DebtCalculationUtils.safeAdd(disposedDebt, context.getAmount());
                reserve.setCurrentMonthDisposeDebt(disposedDebt);
                break;
            case "DELETE_ADD":
                // 删除新增：减少新增金额（负数）
                newDebt = DebtCalculationUtils.safeAdd(newDebt, context.getAmount());
                reserve.setCurrentMonthNewDebt(newDebt);
                break;
            case "DELETE_DISPOSE":
                // 删除处置：减少处置金额（负数）
                disposedDebt = DebtCalculationUtils.safeAdd(disposedDebt, context.getAmount());
                reserve.setCurrentMonthDisposeDebt(disposedDebt);
                break;
        }
        
        // 重新计算本月末债权余额
        BigDecimal currentMonthBalance = DebtCalculationUtils.calculateMonthEndBalance(
            lastMonthBalance, newDebt, disposedDebt
        );
        reserve.setCurrentMonthBalance(currentMonthBalance);
        
        // 计算本月增减
        BigDecimal monthlyChange = DebtCalculationUtils.safeSubtract(newDebt, disposedDebt);
        reserve.setCurrentMonthIncreaseDecrease(monthlyChange);
        
        // 计算本月末减值准备余额
        BigDecimal lastProvisionBalance = reserve.getLastMonthAmount() != null ? 
                                         reserve.getLastMonthAmount() : BigDecimal.ZERO;
        BigDecimal currentProvisionBalance = DebtCalculationUtils.safeAdd(
            lastProvisionBalance, monthlyChange
        );
        reserve.setCurrentMonthAmount(currentProvisionBalance);
        
        // 计提减值金额 = 本月末余额
        reserve.setImpairmentAmount(currentProvisionBalance);
        
        // 判断是否全额计提坏账
        boolean isFullyImpaired = DebtCalculationUtils.isFullyImpaired(
            currentMonthBalance, currentProvisionBalance
        );
        reserve.setIsAllImpaired(isFullyImpaired ? "是" : "否");
        
        // 设置更新时间
        reserve.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 更新诉讼表逻辑
     */
    public static void updateLitigationClaim(LitigationClaim claim, UpdateContext context) {
        log.info("更新诉讼表: 债权人={}, 债务人={}, 年={}, 月={}", 
                context.getCreditor(), context.getDebtor(), context.getYear(), context.getMonth());
        
        BigDecimal lastMonthBalance = claim.getLastMonthDebtBalance() != null ? 
                                     claim.getLastMonthDebtBalance() : BigDecimal.ZERO;
        BigDecimal newDebt = claim.getCurrentMonthNewDebt() != null ? 
                            claim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        BigDecimal disposalDebt = claim.getCurrentMonthDisposalDebt() != null ? 
                                 claim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
        
        // 根据操作类型更新金额
        switch (context.getOperationType()) {
            case "ADD":
                newDebt = DebtCalculationUtils.safeAdd(newDebt, context.getAmount());
                claim.setCurrentMonthNewDebt(newDebt);
                break;
            case "DISPOSE":
                disposalDebt = DebtCalculationUtils.safeAdd(disposalDebt, context.getAmount());
                claim.setCurrentMonthDisposalDebt(disposalDebt);
                // 更新本年度累计回收
                BigDecimal annualRecovery = claim.getAnnualCumulativeRecovery() != null ?
                                           claim.getAnnualCumulativeRecovery() : BigDecimal.ZERO;
                claim.setAnnualCumulativeRecovery(DebtCalculationUtils.safeAdd(annualRecovery, context.getAmount()));
                break;
            case "DELETE_ADD":
                newDebt = DebtCalculationUtils.safeAdd(newDebt, context.getAmount());
                claim.setCurrentMonthNewDebt(newDebt);
                break;
            case "DELETE_DISPOSE":
                disposalDebt = DebtCalculationUtils.safeAdd(disposalDebt, context.getAmount());
                claim.setCurrentMonthDisposalDebt(disposalDebt);
                // 更新本年度累计回收（减少）
                BigDecimal currentRecovery = claim.getAnnualCumulativeRecovery() != null ?
                                            claim.getAnnualCumulativeRecovery() : BigDecimal.ZERO;
                claim.setAnnualCumulativeRecovery(DebtCalculationUtils.safeAdd(currentRecovery, context.getAmount()));
                break;
        }
        
        // 重新计算本月末债权余额
        BigDecimal currentMonthBalance = DebtCalculationUtils.calculateMonthEndBalance(
            lastMonthBalance, newDebt, disposalDebt
        );
        claim.setCurrentMonthDebtBalance(currentMonthBalance);
        
        // 验证和调整本金利息平衡
        if (!DebtCalculationUtils.validateLitigationBalance(
                lastMonthBalance, 
                claim.getLitigationPrincipal(), 
                claim.getLitigationInterest())) {
            
            BigDecimal[] adjusted = DebtCalculationUtils.adjustPrincipalAndInterest(
                lastMonthBalance,
                claim.getLitigationPrincipal(),
                claim.getLitigationInterest()
            );
            claim.setLitigationPrincipal(adjusted[0]);
            claim.setLitigationInterest(adjusted[1]);
        }
        
    }

    /**
     * 更新非诉讼表逻辑
     */
    public static void updateNonLitigationClaim(NonLitigationClaim claim, UpdateContext context) {
        log.info("更新非诉讼表: 债权人={}, 债务人={}, 年={}, 月={}", 
                context.getCreditor(), context.getDebtor(), context.getYear(), context.getMonth());
        
        BigDecimal lastMonthPrincipal = claim.getLastMonthPrincipal() != null ? 
                                       claim.getLastMonthPrincipal() : BigDecimal.ZERO;
        BigDecimal newDebt = claim.getCurrentMonthNewDebt() != null ? 
                            claim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        BigDecimal disposedDebt = claim.getCurrentMonthDisposedDebt() != null ? 
                                 claim.getCurrentMonthDisposedDebt() : BigDecimal.ZERO;
        
        // 根据操作类型更新金额
        switch (context.getOperationType()) {
            case "ADD":
                newDebt = DebtCalculationUtils.safeAdd(newDebt, context.getAmount());
                claim.setCurrentMonthNewDebt(newDebt);
                // 更新本月本金增减
                BigDecimal principalIncrease = claim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                                              claim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                claim.setCurrentMonthPrincipalIncreaseDecrease(
                    DebtCalculationUtils.safeAdd(principalIncrease, context.getAmount())
                );
                break;
            case "DISPOSE":
                disposedDebt = DebtCalculationUtils.safeAdd(disposedDebt, context.getAmount());
                claim.setCurrentMonthDisposedDebt(disposedDebt);
                // 更新本月本金增减（减少）
                BigDecimal principalDecrease = claim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                                              claim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                claim.setCurrentMonthPrincipalIncreaseDecrease(
                    DebtCalculationUtils.safeSubtract(principalDecrease, context.getAmount())
                );
                // 更新本年度累计回收
                BigDecimal annualRecovery = claim.getAnnualCumulativeRecovery() != null ?
                                           claim.getAnnualCumulativeRecovery() : BigDecimal.ZERO;
                claim.setAnnualCumulativeRecovery(DebtCalculationUtils.safeAdd(annualRecovery, context.getAmount()));
                break;
            case "DELETE_ADD":
                newDebt = DebtCalculationUtils.safeAdd(newDebt, context.getAmount());
                claim.setCurrentMonthNewDebt(newDebt);
                // 更新本月本金增减
                BigDecimal currentIncrease = claim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                                            claim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                claim.setCurrentMonthPrincipalIncreaseDecrease(
                    DebtCalculationUtils.safeAdd(currentIncrease, context.getAmount())
                );
                break;
            case "DELETE_DISPOSE":
                disposedDebt = DebtCalculationUtils.safeAdd(disposedDebt, context.getAmount());
                claim.setCurrentMonthDisposedDebt(disposedDebt);
                // 更新本月本金增减
                BigDecimal currentDecrease = claim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                                            claim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                claim.setCurrentMonthPrincipalIncreaseDecrease(
                    DebtCalculationUtils.safeAdd(currentDecrease, context.getAmount())
                );
                // 更新本年度累计回收（减少）
                BigDecimal currentRecovery = claim.getAnnualCumulativeRecovery() != null ?
                                            claim.getAnnualCumulativeRecovery() : BigDecimal.ZERO;
                claim.setAnnualCumulativeRecovery(DebtCalculationUtils.safeAdd(currentRecovery, context.getAmount()));
                break;
        }
        
        // 重新计算本月末本金
        BigDecimal currentMonthPrincipal = DebtCalculationUtils.calculateMonthEndBalance(
            lastMonthPrincipal, newDebt, disposedDebt
        );
        claim.setCurrentMonthPrincipal(currentMonthPrincipal);
        
        // 计算债权余额（本金+利息+违约金）
        BigDecimal currentMonthBalance = currentMonthPrincipal;
        if (claim.getCurrentMonthInterest() != null) {
            currentMonthBalance = DebtCalculationUtils.safeAdd(currentMonthBalance, claim.getCurrentMonthInterest());
        }
        if (claim.getCurrentMonthPenalty() != null) {
            currentMonthBalance = DebtCalculationUtils.safeAdd(currentMonthBalance, claim.getCurrentMonthPenalty());
        }
        claim.setCurrentMonthBalance(currentMonthBalance);
        
    }

    /**
     * 生成期间字符串
     */
    public static String generatePeriod(Integer year, String overdueDate) {
        if (overdueDate != null && !overdueDate.isEmpty()) {
            String overdueYear = overdueDate.split("-")[0];
            return overdueYear + "年430";
        } else {
            return year + "年新增债权";
        }
    }

    /**
     * 获取月份字段名称
     */
    public static String getMonthFieldName(int month) {
        switch (month) {
            case 1: return "january";
            case 2: return "february";
            case 3: return "march";
            case 4: return "april";
            case 5: return "may";
            case 6: return "june";
            case 7: return "july";
            case 8: return "august";
            case 9: return "september";
            case 10: return "october";
            case 11: return "november";
            case 12: return "december";
            default: throw new IllegalArgumentException("无效的月份: " + month);
        }
    }

    /**
     * 获取月份中文名称
     */
    public static String getMonthChineseName(int month) {
        switch (month) {
            case 1: return "1月";
            case 2: return "2月";
            case 3: return "3月";
            case 4: return "4月";
            case 5: return "5月";
            case 6: return "6月";
            case 7: return "7月";
            case 8: return "8月";
            case 9: return "9月";
            case 10: return "10月";
            case 11: return "11月";
            case 12: return "12月";
            default: throw new IllegalArgumentException("无效的月份: " + month);
        }
    }
}