package com.laoshu198838.util.data;

/**
 * 字符串工具类
 * 
 * <p>提供字符串相关的通用工具方法，包括验证、转换、格式化等功能。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class StringUtils {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private StringUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * 判断字符串是否能被解析为数值
     * 
     * @param str 待检查的字符串
     * @return 如果字符串可以解析为数值则返回true，否则返回false
     */
    public static boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 检查字符串是否为空或null
     * 
     * @param str 待检查的字符串
     * @return 如果字符串为null或空则返回true，否则返回false
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 检查字符串是否不为空且不为null
     * 
     * @param str 待检查的字符串
     * @return 如果字符串不为null且不为空则返回true，否则返回false
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }
}
