package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 字段映射工具类
 * 
 * 负责处理前端字段与后端表字段的映射关系，包括：
 * 1. 前端DTO与实体类之间的字段映射
 * 2. 不同表之间的字段映射和转换
 * 3. 数据类型转换和格式化
 * 4. 字段验证和默认值设置
 * 
 * <AUTHOR>
 */
@Component
public class FieldMappingUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(FieldMappingUtil.class);
    
    /**
     * 映射前端债权新增数据到新增表实体
     * 
     * @param frontendData 前端数据Map
     * @return 新增表实体
     */
    public OverdueDebtAdd mapToAddTableEntity(Map<String, Object> frontendData) {
        OverdueDebtAdd entity = new OverdueDebtAdd();
        
        // 设置主键
        OverdueDebtAdd.OverdueDebtAddKey key = new OverdueDebtAdd.OverdueDebtAddKey();
        key.setCreditor(getStringValue(frontendData, "creditor"));
        key.setDebtor(getStringValue(frontendData, "debtor"));
        key.setYear(getStringValue(frontendData, "year"));
        key.setIsLitigation(getBooleanAsString(frontendData, "isLitigation"));
        
        // 生成期间字段
        String period = key.getYear() + "新增债权";
        key.setPeriod(period);
        
        entity.setId(key);
        
        // 设置基础字段
        entity.setManagementCompany(getStringValue(frontendData, "managementCompany"));
        entity.setDueTime(getStringValue(frontendData, "dueTime"));
        entity.setSubjectName(getStringValue(frontendData, "subjectName"));
        entity.setDebtNature(getStringValue(frontendData, "debtNature"));
        entity.setDebtRisk(getStringValue(frontendData, "debtRisk"));
        entity.setResponsiblePerson(getStringValue(frontendData, "responsiblePerson"));
        entity.setRemark(getStringValue(frontendData, "remark"));
        
        // 设置月度金额字段
        entity.setAmountJan(getBigDecimalValue(frontendData, "january"));
        entity.setAmountFeb(getBigDecimalValue(frontendData, "february"));
        entity.setAmountMar(getBigDecimalValue(frontendData, "march"));
        entity.setAmountApr(getBigDecimalValue(frontendData, "april"));
        entity.setAmountMay(getBigDecimalValue(frontendData, "may"));
        entity.setAmountJun(getBigDecimalValue(frontendData, "june"));
        entity.setAmountJul(getBigDecimalValue(frontendData, "july"));
        entity.setAmountAug(getBigDecimalValue(frontendData, "august"));
        entity.setAmountSep(getBigDecimalValue(frontendData, "september"));
        entity.setAmountOct(getBigDecimalValue(frontendData, "october"));
        entity.setAmountNov(getBigDecimalValue(frontendData, "november"));
        entity.setAmountDec(getBigDecimalValue(frontendData, "december"));
        
        // 设置处置相关字段（初始为0）
        entity.setCashDisposal(BigDecimal.ZERO);
        entity.setInstallmentRepayment(BigDecimal.ZERO);
        entity.setAssetDebt(BigDecimal.ZERO);
        entity.setOtherMethods(BigDecimal.ZERO);
        
        // 设置时间戳
        entity.setUpdateTime(LocalDateTime.now());
        
        logger.debug("映射前端数据到新增表实体: 债权人={}, 债务人={}", 
                    entity.getCreditor(), entity.getDebtor());
        
        return entity;
    }
    
    /**
     * 映射前端债权处置数据到处置表实体
     * 
     * @param frontendData 前端数据Map
     * @return 处置表实体
     */
    public OverdueDebtDecrease mapToDisposalTableEntity(Map<String, Object> frontendData) {
        OverdueDebtDecrease entity = new OverdueDebtDecrease();
        
        // 设置主键
        OverdueDebtDecrease.OverdueDebtDecreaseKey key = new OverdueDebtDecrease.OverdueDebtDecreaseKey();
        key.setCreditor(getStringValue(frontendData, "creditor"));
        key.setDebtor(getStringValue(frontendData, "debtor"));
        key.setPeriod(getStringValue(frontendData, "period"));
        key.setIsLitigation(getBooleanAsString(frontendData, "isLitigation"));
        key.setYear(getIntValue(frontendData, "year"));
        key.setMonth(getBigDecimalValue(frontendData, "month"));
        
        entity.setId(key);
        
        // 设置基础字段
        entity.setManagementCompany(getStringValue(frontendData, "managementCompany"));
        entity.setDebtRisk(getStringValue(frontendData, "debtRisk"));
        entity.setCustomerCategory(getStringValue(frontendData, "customerCategory"));
        entity.setRemark(getStringValue(frontendData, "remark"));
        
        // 设置处置金额字段
        entity.setMonthlyReduceAmount(getBigDecimalValue(frontendData, "monthlyReduceAmount"));
        entity.setCashDisposal(getBigDecimalValue(frontendData, "cashDisposal"));
        entity.setInstallmentRepayment(getBigDecimalValue(frontendData, "installmentRepayment"));
        entity.setAssetDebt(getBigDecimalValue(frontendData, "assetDebt"));
        entity.setOtherWays(getBigDecimalValue(frontendData, "otherWays"));
        
        // 设置时间戳
        entity.setUpdateTime(LocalDateTime.now());
        
        logger.debug("映射前端数据到处置表实体: 债权人={}, 债务人={}", 
                    entity.getCreditor(), entity.getDebtor());
        
        return entity;
    }
    
    /**
     * 映射新增表数据到减值准备表实体
     * 
     * @param addRecord 新增表记录
     * @param isLitigation 是否涉诉
     * @param year 年份
     * @param month 月份
     * @return 减值准备表实体
     */
    public ImpairmentReserve mapAddToImpairmentReserve(OverdueDebtAdd addRecord, 
                                                      String isLitigation, int year, int month) {
        if (addRecord == null) {
            return null;
        }
        
        ImpairmentReserve entity = new ImpairmentReserve();
        
        // 设置主键
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(addRecord.getCreditor());
        key.setDebtor(addRecord.getDebtor());
        key.setPeriod(addRecord.getPeriod());
        key.setIsLitigation(isLitigation);
        key.setYear(year);
        key.setMonth(month);
        
        entity.setId(key);
        
        // 映射基础字段
        entity.setSubjectName(addRecord.getSubjectName());
        entity.setManagementCompany(addRecord.getManagementCompany());
        entity.setDebtNature(addRecord.getDebtNature());
        entity.setDebtType(addRecord.getDebtRisk());
        
        // 设置债权相关字段
        entity.setCurrentMonthNewDebt(addRecord.getNewOverdueDebtAmount());
        entity.setCurrentMonthBalance(addRecord.getNewOverdueDebtAmount());
        
        // 计算计提减值金额（通常为新增金额的50%）
        BigDecimal newAmount = addRecord.getNewOverdueDebtAmount();
        if (newAmount != null) {
            BigDecimal impairmentAmount = newAmount.multiply(new BigDecimal("0.5"));
            entity.setImpairmentAmount(impairmentAmount);
            entity.setCurrentMonthAmount(impairmentAmount);
        }
        
        // 设置时间戳
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateTime(LocalDateTime.now());
        
        logger.debug("映射新增表到减值准备表: 债权人={}, 债务人={}", 
                    entity.getId().getCreditor(), entity.getId().getDebtor());
        
        return entity;
    }
    
    /**
     * 映射新增表数据到诉讼表实体
     * 
     * @param addRecord 新增表记录
     * @param year 年份
     * @param month 月份
     * @return 诉讼表实体
     */
    public LitigationClaim mapAddToLitigationClaim(OverdueDebtAdd addRecord, int year, int month) {
        if (addRecord == null) {
            return null;
        }
        
        LitigationClaim entity = new LitigationClaim();
        
        // 设置主键
        LitigationClaim.LitigationCompositeKey key = new LitigationClaim.LitigationCompositeKey();
        key.setCreditor(addRecord.getCreditor());
        key.setDebtor(addRecord.getDebtor());
        key.setPeriod(addRecord.getPeriod());
        key.setYear(year);
        key.setMonth(month);
        
        entity.setId(key);
        
        // 映射基础字段
        entity.setManagementCompany(addRecord.getManagementCompany());
        entity.setSubjectName(addRecord.getSubjectName());
        entity.setDebtNature(addRecord.getDebtNature());
        entity.setDebtType(addRecord.getDebtRisk());
        entity.setResponsiblePerson(addRecord.getResponsiblePerson());
        entity.setRemark(addRecord.getRemark());
        
        // 生成案件名称
        String caseName = addRecord.getCreditor() + "诉" + addRecord.getDebtor();
        entity.setLitigationCase(caseName);
        
        // 设置债权相关字段
        entity.setCurrentMonthNewDebt(addRecord.getNewOverdueDebtAmount());
        entity.setCurrentMonthDebtBalance(addRecord.getNewOverdueDebtAmount());
        
        // 设置涉诉债权本金（默认为新增金额）
        entity.setLitigationPrincipal(addRecord.getNewOverdueDebtAmount());
        entity.setLitigationInterest(BigDecimal.ZERO);
        
        logger.debug("映射新增表到诉讼表: 债权人={}, 债务人={}, 案件名称={}", 
                    entity.getId().getCreditor(), entity.getId().getDebtor(), caseName);
        
        return entity;
    }
    
    /**
     * 映射新增表数据到非诉讼表实体
     * 
     * @param addRecord 新增表记录
     * @param year 年份
     * @param month 月份
     * @return 非诉讼表实体
     */
    public NonLitigationClaim mapAddToNonLitigationClaim(OverdueDebtAdd addRecord, int year, int month) {
        if (addRecord == null) {
            return null;
        }
        
        NonLitigationClaim entity = new NonLitigationClaim();
        
        // 设置主键
        NonLitigationClaim.NonLitigationCompositeKey key = new NonLitigationClaim.NonLitigationCompositeKey();
        key.setCreditor(addRecord.getCreditor());
        key.setDebtor(addRecord.getDebtor());
        key.setPeriod(addRecord.getPeriod());
        key.setYear(year);
        key.setMonth(month);
        
        entity.setId(key);
        
        // 映射基础字段
        entity.setManagementCompany(addRecord.getManagementCompany());
        entity.setSubjectName(addRecord.getSubjectName());
        entity.setCreditorNature(addRecord.getDebtNature());
        entity.setDebtType(addRecord.getDebtRisk());
        entity.setResponsiblePerson(addRecord.getResponsiblePerson());
        entity.setRemark(addRecord.getRemark());
        
        // 设置债权相关字段
        entity.setCurrentMonthNewDebt(addRecord.getNewOverdueDebtAmount());
        
        // 设置本金相关字段（默认新增金额全部为本金）
        entity.setCurrentMonthPrincipal(addRecord.getNewOverdueDebtAmount());
        entity.setCurrentMonthPrincipalIncreaseDecrease(addRecord.getNewOverdueDebtAmount());
        entity.setCurrentMonthInterest(BigDecimal.ZERO);
        entity.setCurrentMonthPenalty(BigDecimal.ZERO);
        
        logger.debug("映射新增表到非诉讼表: 债权人={}, 债务人={}", 
                    entity.getId().getCreditor(), entity.getId().getDebtor());
        
        return entity;
    }
    
    // 辅助方法
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString().trim() : null;
    }
    
    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("无法转换为BigDecimal: key={}, value={}", key, value);
            return BigDecimal.ZERO;
        }
    }
    
    private int getIntValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("无法转换为int: key={}, value={}", key, value);
            return 0;
        }
    }
    
    private String getBooleanAsString(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return "否";
        }
        if (value instanceof Boolean) {
            return ((Boolean) value) ? "是" : "否";
        }
        String strValue = value.toString().toLowerCase();
        return ("true".equals(strValue) || "是".equals(strValue) || "1".equals(strValue)) ? "是" : "否";
    }
}
