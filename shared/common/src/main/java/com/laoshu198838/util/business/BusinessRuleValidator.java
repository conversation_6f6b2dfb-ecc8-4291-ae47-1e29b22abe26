package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务规则验证工具类
 * 
 * 负责验证债务管理系统的各种业务规则，包括：
 * 1. 债权新增业务规则验证
 * 2. 债权处置业务规则验证
 * 3. 数据完整性业务规则验证
 * 4. 计算公式业务规则验证
 * 5. 状态转换业务规则验证
 * 
 * <AUTHOR>
 */
@Component
public class BusinessRuleValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(BusinessRuleValidator.class);
    
    /**
     * 业务规则验证结果
     */
    public static class BusinessRuleValidationResult {
        private boolean isValid;
        private List<String> violations;
        private List<String> warnings;
        
        public BusinessRuleValidationResult() {
            this.isValid = true;
            this.violations = new ArrayList<>();
            this.warnings = new ArrayList<>();
        }
        
        public void addViolation(String violation) {
            this.violations.add(violation);
            this.isValid = false;
        }
        
        public void addWarning(String warning) {
            this.warnings.add(warning);
        }
        
        // Getters
        public boolean isValid() { return isValid; }
        public List<String> getViolations() { return violations; }
        public List<String> getWarnings() { return warnings; }
    }
    
    /**
     * 验证债权新增业务规则
     * 
     * @param addRecord 新增表记录
     * @return 验证结果
     */
    public BusinessRuleValidationResult validateDebtAdditionRules(OverdueDebtAdd addRecord) {
        BusinessRuleValidationResult result = new BusinessRuleValidationResult();
        
        if (addRecord == null) {
            result.addViolation("新增表记录不能为空");
            return result;
        }
        
        // 规则1：债权人和债务人不能相同
        if (addRecord.getCreditor() != null && addRecord.getDebtor() != null &&
            addRecord.getCreditor().equals(addRecord.getDebtor())) {
            result.addViolation("债权人和债务人不能相同");
        }
        
        // 规则2：新增金额必须大于0
        BigDecimal newAmount = addRecord.getNewOverdueDebtAmount();
        if (newAmount == null || newAmount.compareTo(BigDecimal.ZERO) <= 0) {
            result.addViolation("新增债权金额必须大于0");
        }
        
        // 规则3：期间字段必须符合格式"年份+新增债权"
        String period = addRecord.getPeriod();
        String year = addRecord.getYear();
        if (period != null && year != null) {
            String expectedPeriod = year + "新增债权";
            if (!expectedPeriod.equals(period)) {
                result.addViolation(String.format("期间字段格式不正确，期望：%s，实际：%s", expectedPeriod, period));
            }
        }
        
        // 规则4：月度金额汇总应等于新增金额
        BigDecimal monthlyTotal = calculateMonthlyTotal(addRecord);
        if (newAmount != null && monthlyTotal.compareTo(newAmount) != 0) {
            result.addWarning(String.format("月度金额汇总与新增金额不一致，汇总：%s，新增：%s", monthlyTotal, newAmount));
        }
        
        // 规则5：债权余额应等于新增金额减去处置金额
        BigDecimal debtBalance = addRecord.getDebtBalance();
        BigDecimal disposalAmount = addRecord.getCashDisposal();
        if (newAmount != null && debtBalance != null && disposalAmount != null) {
            BigDecimal expectedBalance = newAmount.subtract(disposalAmount);
            if (debtBalance.compareTo(expectedBalance) != 0) {
                result.addViolation(String.format("债权余额计算错误，期望：%s，实际：%s", expectedBalance, debtBalance));
            }
        }
        
        logger.debug("债权新增业务规则验证完成: 债权人={}, 债务人={}, 违规数={}", 
                    addRecord.getCreditor(), addRecord.getDebtor(), result.getViolations().size());
        
        return result;
    }
    
    /**
     * 验证债权处置业务规则
     * 
     * @param disposalRecord 处置记录
     * @param relatedAddRecord 相关的新增表记录
     * @return 验证结果
     */
    public BusinessRuleValidationResult validateDebtDisposalRules(OverdueDebtDecrease disposalRecord,
                                                                 OverdueDebtAdd relatedAddRecord) {
        BusinessRuleValidationResult result = new BusinessRuleValidationResult();
        
        if (disposalRecord == null) {
            result.addViolation("处置表记录不能为空");
            return result;
        }
        
        // 规则1：处置金额必须大于0
        BigDecimal disposalAmount = disposalRecord.getMonthlyReduceAmount();
        if (disposalAmount == null || disposalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            result.addViolation("处置金额必须大于0");
        }
        
        // 规则2：处置金额不能超过债权余额
        if (relatedAddRecord != null && disposalAmount != null) {
            BigDecimal debtBalance = relatedAddRecord.getDebtBalance();
            if (debtBalance != null && disposalAmount.compareTo(debtBalance) > 0) {
                result.addViolation(String.format("处置金额不能超过债权余额，处置：%s，余额：%s", disposalAmount, debtBalance));
            }
        }
        
        // 规则3：处置方式分类金额总和应等于每月处置金额
        BigDecimal cashDisposal = getValueOrZero(disposalRecord.getCashDisposal());
        BigDecimal installment = getValueOrZero(disposalRecord.getInstallmentRepayment());
        BigDecimal assetDebt = getValueOrZero(disposalRecord.getAssetDebt());
        BigDecimal otherWays = getValueOrZero(disposalRecord.getOtherWays());
        
        BigDecimal totalBreakdown = cashDisposal.add(installment).add(assetDebt).add(otherWays);
        if (disposalAmount != null && totalBreakdown.compareTo(disposalAmount) != 0) {
            result.addViolation(String.format("处置方式分类金额总和不等于处置金额，总和：%s，处置：%s", 
                                            totalBreakdown, disposalAmount));
        }
        
        // 规则4：年份和月份必须合理
        Integer year = disposalRecord.getYear();
        BigDecimal month = disposalRecord.getMonth();
        if (year != null && (year < 2000 || year > 2100)) {
            result.addViolation("年份必须在2000-2100之间");
        }
        if (month != null && (month.compareTo(BigDecimal.ONE) < 0 || month.compareTo(new BigDecimal("12")) > 0)) {
            result.addViolation("月份必须在1-12之间");
        }
        
        logger.debug("债权处置业务规则验证完成: 债权人={}, 债务人={}, 违规数={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor(), result.getViolations().size());
        
        return result;
    }
    
    /**
     * 验证减值准备表业务规则
     * 
     * @param impairmentReserve 减值准备表记录
     * @return 验证结果
     */
    public BusinessRuleValidationResult validateImpairmentReserveRules(ImpairmentReserve impairmentReserve) {
        BusinessRuleValidationResult result = new BusinessRuleValidationResult();
        
        if (impairmentReserve == null) {
            result.addViolation("减值准备表记录不能为空");
            return result;
        }
        
        // 规则1：计提减值金额应等于本月末余额
        BigDecimal impairmentAmount = impairmentReserve.getImpairmentAmount();
        BigDecimal currentMonthAmount = impairmentReserve.getCurrentMonthAmount();
        if (impairmentAmount != null && currentMonthAmount != null && 
            impairmentAmount.compareTo(currentMonthAmount) != 0) {
            result.addViolation(String.format("计提减值金额应等于本月末余额，计提：%s，本月末：%s", 
                                            impairmentAmount, currentMonthAmount));
        }
        
        // 规则2：本月末余额应等于上月末余额加本月增减
        BigDecimal previousBalance = impairmentReserve.getPreviousMonthBalance();
        BigDecimal increaseDecrease = impairmentReserve.getCurrentMonthIncreaseDecrease();
        if (previousBalance != null && increaseDecrease != null && currentMonthAmount != null) {
            BigDecimal expectedAmount = previousBalance.add(increaseDecrease);
            if (currentMonthAmount.compareTo(expectedAmount) != 0) {
                result.addViolation(String.format("本月末余额计算错误，期望：%s，实际：%s", 
                                                expectedAmount, currentMonthAmount));
            }
        }
        
        // 规则3：是否全额计提坏账的判断逻辑
        String isFullImpairment = impairmentReserve.getIsAllImpaired();
        BigDecimal debtBalance = impairmentReserve.getCurrentMonthBalance();
        if (debtBalance != null && impairmentAmount != null) {
            boolean shouldBeFullImpairment = debtBalance.compareTo(BigDecimal.ZERO) > 0 && 
                                           impairmentAmount.compareTo(debtBalance) >= 0;
            String expectedValue = shouldBeFullImpairment ? "是" : "否";
            if (isFullImpairment != null && !expectedValue.equals(isFullImpairment)) {
                result.addWarning(String.format("是否全额计提坏账判断可能有误，期望：%s，实际：%s", 
                                              expectedValue, isFullImpairment));
            }
        }
        
        logger.debug("减值准备表业务规则验证完成: 债权人={}, 债务人={}, 违规数={}", 
                    impairmentReserve.getId().getCreditor(), impairmentReserve.getId().getDebtor(), 
                    result.getViolations().size());
        
        return result;
    }
    
    /**
     * 验证诉讼表业务规则
     * 
     * @param litigationClaim 诉讼表记录
     * @return 验证结果
     */
    public BusinessRuleValidationResult validateLitigationRules(LitigationClaim litigationClaim) {
        BusinessRuleValidationResult result = new BusinessRuleValidationResult();
        
        if (litigationClaim == null) {
            result.addViolation("诉讼表记录不能为空");
            return result;
        }
        
        // 规则1：案件名称应为"债权人诉债务人"格式
        String caseName = litigationClaim.getLitigationCase();
        String creditor = litigationClaim.getId().getCreditor();
        String debtor = litigationClaim.getId().getDebtor();
        if (caseName != null && creditor != null && debtor != null) {
            String expectedCaseName = creditor + "诉" + debtor;
            if (!expectedCaseName.equals(caseName)) {
                result.addWarning(String.format("案件名称格式建议调整，期望：%s，实际：%s", 
                                              expectedCaseName, caseName));
            }
        }
        
        // 规则2：上月末债权余额应等于涉诉债权本金加利息
        BigDecimal lastMonthBalance = litigationClaim.getLastMonthDebtBalance();
        BigDecimal principal = litigationClaim.getLitigationPrincipal();
        BigDecimal interest = litigationClaim.getLitigationInterest();
        if (lastMonthBalance != null && principal != null && interest != null) {
            BigDecimal expectedBalance = principal.add(interest);
            if (lastMonthBalance.compareTo(expectedBalance) != 0) {
                result.addWarning(String.format("上月末债权余额与本金+利息不一致，余额：%s，本金+利息：%s", 
                                              lastMonthBalance, expectedBalance));
            }
        }
        
        // 规则3：债权余额平衡计算
        BigDecimal currentMonthNewDebt = litigationClaim.getCurrentMonthNewDebt();
        BigDecimal currentMonthDisposal = litigationClaim.getCurrentMonthDisposalDebt();
        BigDecimal currentMonthBalance = litigationClaim.getCurrentMonthDebtBalance();
        if (lastMonthBalance != null && currentMonthNewDebt != null && 
            currentMonthDisposal != null && currentMonthBalance != null) {
            BigDecimal expectedBalance = lastMonthBalance.add(currentMonthNewDebt).subtract(currentMonthDisposal);
            if (currentMonthBalance.compareTo(expectedBalance) != 0) {
                result.addViolation(String.format("债权余额平衡计算错误，期望：%s，实际：%s", 
                                                expectedBalance, currentMonthBalance));
            }
        }
        
        logger.debug("诉讼表业务规则验证完成: 债权人={}, 债务人={}, 违规数={}", 
                    creditor, debtor, result.getViolations().size());
        
        return result;
    }
    
    /**
     * 验证非诉讼表业务规则
     * 
     * @param nonLitigationClaim 非诉讼表记录
     * @return 验证结果
     */
    public BusinessRuleValidationResult validateNonLitigationRules(NonLitigationClaim nonLitigationClaim) {
        BusinessRuleValidationResult result = new BusinessRuleValidationResult();
        
        if (nonLitigationClaim == null) {
            result.addViolation("非诉讼表记录不能为空");
            return result;
        }
        
        // 规则1：本金双重公式验证
        BigDecimal lastMonthPrincipal = nonLitigationClaim.getLastMonthPrincipal();
        BigDecimal principalIncrease = nonLitigationClaim.getCurrentMonthPrincipalIncreaseDecrease();
        BigDecimal currentMonthPrincipal = nonLitigationClaim.getCurrentMonthPrincipal();
        BigDecimal newDebt = nonLitigationClaim.getCurrentMonthNewDebt();
        BigDecimal disposedDebt = nonLitigationClaim.getCurrentMonthDisposedDebt();
        
        if (lastMonthPrincipal != null && principalIncrease != null && currentMonthPrincipal != null) {
            // 公式1验证
            BigDecimal formula1Result = lastMonthPrincipal.add(principalIncrease);
            if (formula1Result.compareTo(currentMonthPrincipal) != 0) {
                result.addViolation(String.format("本金公式1验证失败，期望：%s，实际：%s", 
                                                formula1Result, currentMonthPrincipal));
            }
            
            // 公式2验证（如果有数据）
            if (newDebt != null && disposedDebt != null) {
                BigDecimal formula2Result = lastMonthPrincipal.add(newDebt).subtract(disposedDebt);
                if (formula2Result.compareTo(currentMonthPrincipal) != 0) {
                    result.addWarning(String.format("本金公式2验证失败，期望：%s，实际：%s", 
                                                  formula2Result, currentMonthPrincipal));
                }
            }
        }
        
        // 规则2：利息计算验证
        BigDecimal lastMonthInterest = nonLitigationClaim.getLastMonthInterest();
        BigDecimal interestIncrease = nonLitigationClaim.getCurrentMonthInterestIncreaseDecrease();
        BigDecimal currentMonthInterest = nonLitigationClaim.getCurrentMonthInterest();
        if (lastMonthInterest != null && interestIncrease != null && currentMonthInterest != null) {
            BigDecimal expectedInterest = lastMonthInterest.add(interestIncrease);
            if (expectedInterest.compareTo(currentMonthInterest) != 0) {
                result.addViolation(String.format("利息计算错误，期望：%s，实际：%s", 
                                                expectedInterest, currentMonthInterest));
            }
        }
        
        // 规则3：违约金计算验证
        BigDecimal lastMonthPenalty = nonLitigationClaim.getLastMonthPenalty();
        BigDecimal penaltyIncrease = nonLitigationClaim.getCurrentMonthPenaltyIncreaseDecrease();
        BigDecimal currentMonthPenalty = nonLitigationClaim.getCurrentMonthPenalty();
        if (lastMonthPenalty != null && penaltyIncrease != null && currentMonthPenalty != null) {
            BigDecimal expectedPenalty = lastMonthPenalty.add(penaltyIncrease);
            if (expectedPenalty.compareTo(currentMonthPenalty) != 0) {
                result.addViolation(String.format("违约金计算错误，期望：%s，实际：%s", 
                                                expectedPenalty, currentMonthPenalty));
            }
        }
        
        logger.debug("非诉讼表业务规则验证完成: 债权人={}, 债务人={}, 违规数={}", 
                    nonLitigationClaim.getId().getCreditor(), nonLitigationClaim.getId().getDebtor(), 
                    result.getViolations().size());
        
        return result;
    }
    
    // 辅助方法
    private BigDecimal calculateMonthlyTotal(OverdueDebtAdd addRecord) {
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal[] months = {
            addRecord.getAmountJan(), addRecord.getAmountFeb(), addRecord.getAmountMar(),
            addRecord.getAmountApr(), addRecord.getAmountMay(), addRecord.getAmountJun(),
            addRecord.getAmountJul(), addRecord.getAmountAug(), addRecord.getAmountSep(),
            addRecord.getAmountOct(), addRecord.getAmountNov(), addRecord.getAmountDec()
        };
        
        for (BigDecimal month : months) {
            if (month != null) {
                total = total.add(month);
            }
        }
        
        return total;
    }
    
    private BigDecimal getValueOrZero(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }
}
