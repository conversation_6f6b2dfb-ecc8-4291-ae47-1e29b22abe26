package com.laoshu198838.util.debt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 债权数据验证工具类
 * 提供债权管理中的各种数据验证方法
 *
 * <AUTHOR>
 */
@Slf4j
public class DebtValidationUtils {

    /**
     * 期间格式正则表达式
     */
    private static final Pattern PERIOD_PATTERN = Pattern.compile("^\\d{4}年(新增债权|430)$");

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors;

        public ValidationResult() {
            this.valid = true;
            this.errors = new ArrayList<>();
        }

        public void addError(String error) {
            this.valid = false;
            this.errors.add(error);
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public String getErrorMessage() {
            return String.join("; ", errors);
        }
    }

    /**
     * 验证债权主键完整性
     */
    public static ValidationResult validateDebtPrimaryKey(
            String creditor, 
            String debtor, 
            String period,
            Integer year,
            Integer month) {
        
        ValidationResult result = new ValidationResult();
        
        if (!StringUtils.hasText(creditor)) {
            result.addError("债权人不能为空");
        }
        
        if (!StringUtils.hasText(debtor)) {
            result.addError("债务人不能为空");
        }
        
        if (!StringUtils.hasText(period)) {
            result.addError("期间不能为空");
        } else if (!isValidPeriod(period)) {
            result.addError("期间格式不正确，应为：YYYY年新增债权 或 YYYY年430");
        }
        
        if (year == null || year < 2000 || year > 2100) {
            result.addError("年份无效，应在2000-2100之间");
        }
        
        if (month != null && (month < 1 || month > 12)) {
            result.addError("月份无效，应在1-12之间");
        }
        
        return result;
    }

    /**
     * 验证期间格式
     */
    public static boolean isValidPeriod(String period) {
        if (!StringUtils.hasText(period)) {
            return false;
        }
        return PERIOD_PATTERN.matcher(period).matches();
    }

    /**
     * 验证金额有效性
     */
    public static ValidationResult validateAmount(BigDecimal amount, String fieldName) {
        ValidationResult result = new ValidationResult();
        
        if (amount == null) {
            result.addError(fieldName + "不能为空");
        } else if (amount.compareTo(BigDecimal.ZERO) == 0) {
            result.addError(fieldName + "不能为0");
        } else if (amount.scale() > 2) {
            result.addError(fieldName + "小数位数不能超过2位");
        }
        
        return result;
    }

    /**
     * 验证正数金额
     */
    public static ValidationResult validatePositiveAmount(BigDecimal amount, String fieldName) {
        ValidationResult result = validateAmount(amount, fieldName);
        
        if (result.isValid() && amount.compareTo(BigDecimal.ZERO) < 0) {
            result.addError(fieldName + "必须为正数");
        }
        
        return result;
    }

    /**
     * 验证删除金额（应为正数，后端会转为负数）
     */
    public static ValidationResult validateDeletionAmount(BigDecimal amount) {
        return validatePositiveAmount(amount, "删除金额");
    }

    /**
     * 验证是否涉诉字段
     */
    public static boolean isValidLitigationStatus(String isLitigation) {
        return "是".equals(isLitigation) || "否".equals(isLitigation);
    }

    /**
     * 验证年月组合的有效性
     */
    public static ValidationResult validateYearMonth(Integer year, Integer month) {
        ValidationResult result = new ValidationResult();
        
        if (year == null || year < 2000 || year > 2100) {
            result.addError("年份无效");
            return result;
        }
        
        if (month == null || month < 1 || month > 12) {
            result.addError("月份无效");
            return result;
        }
        
        // 不能超过当前月份
        YearMonth current = YearMonth.now();
        YearMonth target = YearMonth.of(year, month);
        
        if (target.isAfter(current)) {
            result.addError("不能操作未来月份的数据");
        }
        
        return result;
    }

    /**
     * 验证删除原因
     */
    public static ValidationResult validateDeleteReason(String reason) {
        ValidationResult result = new ValidationResult();
        
        if (!StringUtils.hasText(reason)) {
            result.addError("删除原因不能为空");
        } else if (reason.length() < 10) {
            result.addError("删除原因至少需要10个字符");
        } else if (reason.length() > 500) {
            result.addError("删除原因不能超过500个字符");
        }
        
        return result;
    }

    /**
     * 验证处置金额分配
     * 确保各项处置方式金额之和等于总处置金额
     */
    public static ValidationResult validateDisposalDistribution(
            BigDecimal totalAmount,
            BigDecimal cashDisposal,
            BigDecimal installmentRepayment,
            BigDecimal assetDebt,
            BigDecimal otherWays) {
        
        ValidationResult result = new ValidationResult();
        
        BigDecimal sum = BigDecimal.ZERO;
        sum = DebtCalculationUtils.safeAdd(sum, cashDisposal);
        sum = DebtCalculationUtils.safeAdd(sum, installmentRepayment);
        sum = DebtCalculationUtils.safeAdd(sum, assetDebt);
        sum = DebtCalculationUtils.safeAdd(sum, otherWays);
        
        BigDecimal difference = totalAmount.subtract(sum).abs();
        
        if (difference.compareTo(new BigDecimal("0.01")) > 0) {
            result.addError(String.format(
                "处置金额分配不正确：总额=%s，各项之和=%s，差额=%s",
                totalAmount, sum, difference
            ));
        }
        
        return result;
    }

    /**
     * 验证数据一致性
     * 减值准备表余额 = 诉讼表余额 + 非诉讼表余额
     */
    public static ValidationResult validateTableConsistency(
            BigDecimal impairmentBalance,
            BigDecimal litigationBalance,
            BigDecimal nonLitigationBalance) {
        
        ValidationResult result = new ValidationResult();
        
        BigDecimal calculated = DebtCalculationUtils.safeAdd(litigationBalance, nonLitigationBalance);
        BigDecimal difference = DebtCalculationUtils.safeSubtract(impairmentBalance, calculated).abs();
        
        if (difference.compareTo(new BigDecimal("0.01")) > 0) {
            result.addError(String.format(
                "五表数据不一致：减值准备表余额=%s，诉讼表+非诉讼表=%s，差额=%s",
                impairmentBalance, calculated, difference
            ));
        }
        
        return result;
    }

    /**
     * 验证操作类型
     */
    public static boolean isValidOperationType(String operationType) {
        return "DELETE_ADDITION".equals(operationType) || 
               "DELETE_DISPOSAL".equals(operationType);
    }

    /**
     * 验证管理公司
     */
    public static ValidationResult validateManagementCompany(String company) {
        ValidationResult result = new ValidationResult();
        
        if (!StringUtils.hasText(company)) {
            result.addError("管理公司不能为空");
        } else if (company.length() > 30) {
            result.addError("管理公司名称不能超过30个字符");
        }
        
        return result;
    }

    /**
     * 验证逾期年限分类
     */
    public static String validateAndGetOverdueCategory(LocalDate dueDate) {
        if (dueDate == null || dueDate.isAfter(LocalDate.now())) {
            return "";
        }
        
        long years = java.time.temporal.ChronoUnit.YEARS.between(dueDate, LocalDate.now());
        
        if (years < 1) {
            return "1年（含）以下";
        } else if (years >= 1 && years < 5) {
            return "1年-5年";
        } else {
            return "5年（含）以上";
        }
    }

    /**
     * 综合验证债权删除请求
     */
    public static ValidationResult validateDeletionRequest(
            String creditor,
            String debtor,
            String managementCompany,
            String isLitigation,
            String period,
            Integer year,
            Integer month,
            BigDecimal amount,
            String deleteReason,
            String operationType) {
        
        ValidationResult result = new ValidationResult();
        
        // 验证主键
        ValidationResult pkResult = validateDebtPrimaryKey(creditor, debtor, period, year, month);
        if (!pkResult.isValid()) {
            pkResult.getErrors().forEach(result::addError);
        }
        
        // 验证管理公司
        ValidationResult companyResult = validateManagementCompany(managementCompany);
        if (!companyResult.isValid()) {
            companyResult.getErrors().forEach(result::addError);
        }
        
        // 验证是否涉诉
        if (!isValidLitigationStatus(isLitigation)) {
            result.addError("是否涉诉只能为'是'或'否'");
        }
        
        // 验证金额
        ValidationResult amountResult = validateDeletionAmount(amount);
        if (!amountResult.isValid()) {
            amountResult.getErrors().forEach(result::addError);
        }
        
        // 验证删除原因
        ValidationResult reasonResult = validateDeleteReason(deleteReason);
        if (!reasonResult.isValid()) {
            reasonResult.getErrors().forEach(result::addError);
        }
        
        // 验证操作类型
        if (!isValidOperationType(operationType)) {
            result.addError("操作类型无效");
        }
        
        return result;
    }
}