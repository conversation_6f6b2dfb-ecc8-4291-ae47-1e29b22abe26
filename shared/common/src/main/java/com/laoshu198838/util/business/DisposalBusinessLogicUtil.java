package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.entity.overdue_debt.OverdueDebtDecrease;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 处置表业务逻辑工具类
 * 
 * 负责处理处置表相关的业务计算和验证逻辑，包括：
 * 1. 与新增表的联动更新机制
 * 2. 处置金额验证机制
 * 3. 处置方式分类记录
 * 4. 累加逻辑处理
 * 
 * <AUTHOR>
 */
@Component
public class DisposalBusinessLogicUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(DisposalBusinessLogicUtil.class);
    
    /**
     * 验证处置金额
     * 确保处置金额不超过对应的债权余额
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param disposalAmount 处置金额
     * @param addRecords 新增表记录列表
     * @return 验证结果，true表示通过
     */
    public boolean validateDisposalAmount(String creditor, String debtor, String period, 
                                        String year, BigDecimal disposalAmount,
                                        List<OverdueDebtAdd> addRecords) {
        if (disposalAmount == null || disposalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            logger.debug("处置金额为空或小于等于0，验证通过");
            return true;
        }
        
        // 查找对应的新增表记录
        OverdueDebtAdd matchedRecord = findMatchingAddRecord(creditor, debtor, period, year, addRecords);
        
        if (matchedRecord == null) {
            logger.warn("未找到对应的新增表记录: 债权人={}, 债务人={}, 期间={}, 年份={}", 
                       creditor, debtor, period, year);
            return false;
        }
        
        BigDecimal debtBalance = matchedRecord.getDebtBalance();
        if (debtBalance == null) {
            debtBalance = BigDecimal.ZERO;
        }
        
        // 验证处置金额不超过债权余额
        if (disposalAmount.compareTo(debtBalance) > 0) {
            logger.warn("处置金额超过债权余额: 债权人={}, 债务人={}, 处置金额={}, 债权余额={}", 
                       creditor, debtor, disposalAmount, debtBalance);
            return false;
        }
        
        logger.debug("处置金额验证通过: 债权人={}, 债务人={}, 处置金额={}, 债权余额={}", 
                    creditor, debtor, disposalAmount, debtBalance);
        
        return true;
    }
    
    /**
     * 查找匹配的新增表记录
     */
    private OverdueDebtAdd findMatchingAddRecord(String creditor, String debtor, String period, 
                                               String year, List<OverdueDebtAdd> addRecords) {
        if (addRecords == null || addRecords.isEmpty()) {
            return null;
        }
        
        for (OverdueDebtAdd record : addRecords) {
            if (record.getId() != null &&
                creditor.equals(record.getCreditor()) &&
                debtor.equals(record.getDebtor()) &&
                period.equals(record.getPeriod()) &&
                year.equals(record.getYear())) {
                return record;
            }
        }
        
        return null;
    }
    
    /**
     * 计算累计处置金额
     * 按债权人、债务人、期间、年份分组累计处置金额
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param disposalRecords 处置记录列表
     * @return 累计处置金额
     */
    public BigDecimal calculateCumulativeDisposalAmount(String creditor, String debtor, 
                                                       String period, String year,
                                                       List<OverdueDebtDecrease> disposalRecords) {
        if (disposalRecords == null || disposalRecords.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalDisposal = BigDecimal.ZERO;
        
        for (OverdueDebtDecrease record : disposalRecords) {
            if (creditor.equals(record.getCreditor()) &&
                debtor.equals(record.getDebtor()) &&
                period.equals(record.getPeriod()) &&
                String.valueOf(record.getYear()).equals(year)) {
                
                // 累加每月处置金额
                if (record.getMonthlyReduceAmount() != null) {
                    totalDisposal = totalDisposal.add(record.getMonthlyReduceAmount());
                }
            }
        }
        
        logger.debug("计算累计处置金额: 债权人={}, 债务人={}, 期间={}, 年份={}, 累计金额={}", 
                    creditor, debtor, period, year, totalDisposal);
        
        return totalDisposal;
    }
    
    /**
     * 计算处置方式分类金额
     * 将每月处置金额按处置方式分类
     * 
     * @param disposalRecord 处置记录
     * @return 处置方式分类是否合理
     */
    public boolean validateDisposalMethodBreakdown(OverdueDebtDecrease disposalRecord) {
        if (disposalRecord == null) {
            return false;
        }
        
        BigDecimal monthlyAmount = disposalRecord.getMonthlyReduceAmount();
        BigDecimal cashDisposal = disposalRecord.getCashDisposal();
        BigDecimal installmentRepayment = disposalRecord.getInstallmentRepayment();
        BigDecimal assetDebt = disposalRecord.getAssetDebt();
        BigDecimal otherWays = disposalRecord.getOtherWays();
        
        // 设置默认值
        if (monthlyAmount == null) monthlyAmount = BigDecimal.ZERO;
        if (cashDisposal == null) cashDisposal = BigDecimal.ZERO;
        if (installmentRepayment == null) installmentRepayment = BigDecimal.ZERO;
        if (assetDebt == null) assetDebt = BigDecimal.ZERO;
        if (otherWays == null) otherWays = BigDecimal.ZERO;
        
        // 计算分类金额总和
        BigDecimal totalBreakdown = cashDisposal.add(installmentRepayment).add(assetDebt).add(otherWays);
        
        // 验证分类金额总和是否等于每月处置金额
        boolean isValid = monthlyAmount.compareTo(totalBreakdown) == 0;
        
        if (!isValid) {
            logger.warn("处置方式分类金额不匹配: 债权人={}, 债务人={}, 每月处置金额={}, 分类总和={}", 
                       disposalRecord.getCreditor(), disposalRecord.getDebtor(), 
                       monthlyAmount, totalBreakdown);
        } else {
            logger.debug("处置方式分类验证通过: 债权人={}, 债务人={}, 金额={}", 
                        disposalRecord.getCreditor(), disposalRecord.getDebtor(), monthlyAmount);
        }
        
        return isValid;
    }
    
    /**
     * 更新新增表的处置金额和债权余额
     * 当处置表数据变更时，自动更新新增表的相关字段
     * 
     * @param addRecord 新增表记录
     * @param disposalRecords 处置记录列表
     */
    public void updateAddTableDisposalAndBalance(OverdueDebtAdd addRecord, 
                                               List<OverdueDebtDecrease> disposalRecords) {
        if (addRecord == null) {
            logger.warn("新增表记录为空，无法更新处置金额");
            return;
        }
        
        // 计算累计处置金额
        BigDecimal cumulativeDisposal = calculateCumulativeDisposalAmount(
            addRecord.getCreditor(),
            addRecord.getDebtor(),
            addRecord.getPeriod(),
            addRecord.getYear(),
            disposalRecords
        );
        
        // 更新新增表的处置金额
        addRecord.setCashDisposal(cumulativeDisposal);
        
        // 重新计算债权余额：新增金额 - 处置金额 = 债权余额
        BigDecimal newAmount = addRecord.getNewOverdueDebtAmount();
        if (newAmount == null) {
            newAmount = BigDecimal.ZERO;
        }
        
        BigDecimal newDebtBalance = newAmount.subtract(cumulativeDisposal);
        addRecord.setDebtBalance(newDebtBalance);
        
        logger.info("更新新增表处置金额和债权余额: 债权人={}, 债务人={}, 处置金额={}, 债权余额={}", 
                   addRecord.getCreditor(), addRecord.getDebtor(), cumulativeDisposal, newDebtBalance);
    }
    
    /**
     * 验证处置表数据的完整性
     * 
     * @param disposalRecord 处置记录
     * @return 验证结果，true表示通过
     */
    public boolean validateDisposalData(OverdueDebtDecrease disposalRecord) {
        if (disposalRecord == null) {
            logger.warn("处置表记录为空");
            return false;
        }
        
        // 验证必要字段
        if (disposalRecord.getId() == null) {
            logger.warn("处置表主键为空");
            return false;
        }
        
        // 验证处置金额不为负数
        BigDecimal monthlyAmount = disposalRecord.getMonthlyReduceAmount();
        if (monthlyAmount != null && monthlyAmount.compareTo(BigDecimal.ZERO) < 0) {
            logger.warn("每月处置金额不能为负数: 债权人={}, 债务人={}, 金额={}", 
                       disposalRecord.getCreditor(), disposalRecord.getDebtor(), monthlyAmount);
            return false;
        }
        
        // 验证处置方式分类
        if (!validateDisposalMethodBreakdown(disposalRecord)) {
            return false;
        }
        
        logger.debug("处置表数据验证通过: 债权人={}, 债务人={}", 
                    disposalRecord.getCreditor(), disposalRecord.getDebtor());
        
        return true;
    }
    
    /**
     * 处理处置表与其他表的联动更新
     * 
     * @param disposalRecord 处置记录
     * @param addRecords 新增表记录列表
     * @param allDisposalRecords 所有处置记录列表
     */
    public void processDisposalLinkageUpdate(OverdueDebtDecrease disposalRecord,
                                           List<OverdueDebtAdd> addRecords,
                                           List<OverdueDebtDecrease> allDisposalRecords) {
        if (disposalRecord == null) {
            logger.warn("处置记录为空，无法处理联动更新");
            return;
        }
        
        // 1. 验证处置数据
        if (!validateDisposalData(disposalRecord)) {
            logger.error("处置数据验证失败，停止联动更新");
            return;
        }
        
        // 2. 验证处置金额不超过债权余额
        if (!validateDisposalAmount(
            disposalRecord.getCreditor(),
            disposalRecord.getDebtor(),
            disposalRecord.getPeriod(),
            String.valueOf(disposalRecord.getYear()),
            disposalRecord.getMonthlyReduceAmount(),
            addRecords)) {
            logger.error("处置金额验证失败，停止联动更新");
            return;
        }
        
        // 3. 更新对应的新增表记录
        OverdueDebtAdd matchedAddRecord = findMatchingAddRecord(
            disposalRecord.getCreditor(),
            disposalRecord.getDebtor(),
            disposalRecord.getPeriod(),
            String.valueOf(disposalRecord.getYear()),
            addRecords
        );
        
        if (matchedAddRecord != null) {
            updateAddTableDisposalAndBalance(matchedAddRecord, allDisposalRecords);
        }
        
        logger.info("处置表联动更新完成: 债权人={}, 债务人={}", 
                   disposalRecord.getCreditor(), disposalRecord.getDebtor());
    }
}
