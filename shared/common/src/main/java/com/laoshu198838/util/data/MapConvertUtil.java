package com.laoshu198838.util.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.util.Map;

/**
 * Map转换工具类
 * 
 * <p>提供Map与对象之间的转换工具方法。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class MapConvertUtil {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private MapConvertUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    /**
     * 将Map转换为指定类型的对象
     * 
     * @param <T> 目标类型
     * @param map 源Map对象
     * @param clazz 目标类型的Class对象
     * @return 转换后的对象
     */
    public static <T> T convert(Map<String, Object> map, Class<T> clazz) {
        return mapper.convertValue(map, clazz);
    }
}
