package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 减值准备表业务逻辑工具类
 * 
 * 负责处理减值准备表相关的业务计算和验证逻辑，包括：
 * 1. 债权余额平衡计算（本月初债权余额+本月新增债权-本月处置债权=本月末债权余额）
 * 2. 减值准备本年度累计回收特殊计算（上月末余额-本月末债权余额=减值准备本年度累计回收）
 * 3. 汇总验证机制（减值准备表债权余额 = 诉讼表债权余额 + 非诉讼表债权余额）
 * 4. 计提减值金额计算
 * 5. 是否全额计提坏账判断
 * 
 * <AUTHOR>
 */
@Component
public class ImpairmentReserveBusinessLogicUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ImpairmentReserveBusinessLogicUtil.class);
    
    /**
     * 计算债权余额平衡
     * 公式：本月初债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额
     * 
     * @param lastMonthBalance 本月初债权余额（上月末债权余额）
     * @param currentMonthNewDebt 本月新增债权
     * @param currentMonthDisposeDebt 本月处置债权
     * @return 本月末债权余额
     */
    public BigDecimal calculateCurrentMonthDebtBalance(BigDecimal lastMonthBalance, 
                                                      BigDecimal currentMonthNewDebt,
                                                      BigDecimal currentMonthDisposeDebt) {
        if (lastMonthBalance == null) {
            lastMonthBalance = BigDecimal.ZERO;
        }
        if (currentMonthNewDebt == null) {
            currentMonthNewDebt = BigDecimal.ZERO;
        }
        if (currentMonthDisposeDebt == null) {
            currentMonthDisposeDebt = BigDecimal.ZERO;
        }
        
        BigDecimal currentMonthBalance = lastMonthBalance
            .add(currentMonthNewDebt)
            .subtract(currentMonthDisposeDebt);
        
        logger.debug("计算债权余额平衡: 上月末={}, 本月新增={}, 本月处置={}, 本月末={}", 
                    lastMonthBalance, currentMonthNewDebt, currentMonthDisposeDebt, currentMonthBalance);
        
        return currentMonthBalance;
    }
    
    /**
     * 计算减值准备本年度累计回收
     * 公式：上月末余额 - 本月末债权余额 = 减值准备本年度累计回收
     * 
     * @param previousMonthBalance 上月末余额（减值准备）
     * @param currentMonthDebtBalance 本月末债权余额
     * @return 减值准备本年度累计回收
     */
    public BigDecimal calculateImpairmentAnnualRecovery(BigDecimal previousMonthBalance, 
                                                       BigDecimal currentMonthDebtBalance) {
        if (previousMonthBalance == null) {
            previousMonthBalance = BigDecimal.ZERO;
        }
        if (currentMonthDebtBalance == null) {
            currentMonthDebtBalance = BigDecimal.ZERO;
        }
        
        BigDecimal annualRecovery = previousMonthBalance.subtract(currentMonthDebtBalance);
        
        logger.debug("计算减值准备本年度累计回收: 上月末余额={}, 本月末债权余额={}, 累计回收={}", 
                    previousMonthBalance, currentMonthDebtBalance, annualRecovery);
        
        return annualRecovery;
    }
    
    /**
     * 验证汇总关系
     * 验证：减值准备表债权余额 = 诉讼表债权余额 + 非诉讼表债权余额
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param month 月份
     * @param impairmentBalance 减值准备表债权余额
     * @param litigationClaims 诉讼表记录列表
     * @param nonLitigationClaims 非诉讼表记录列表
     * @return 验证结果，true表示汇总关系正确
     */
    public boolean validateSummaryRelation(String creditor, String debtor, String period, 
                                         int year, int month, BigDecimal impairmentBalance,
                                         List<LitigationClaim> litigationClaims,
                                         List<NonLitigationClaim> nonLitigationClaims) {
        
        // 计算诉讼表债权余额
        BigDecimal litigationBalance = calculateLitigationBalance(creditor, debtor, period, year, month, litigationClaims);
        
        // 计算非诉讼表债权余额
        BigDecimal nonLitigationBalance = calculateNonLitigationBalance(creditor, debtor, period, year, month, nonLitigationClaims);
        
        // 计算总和
        BigDecimal calculatedTotal = litigationBalance.add(nonLitigationBalance);
        
        // 验证汇总关系
        boolean isValid = impairmentBalance != null && impairmentBalance.compareTo(calculatedTotal) == 0;
        
        if (!isValid) {
            logger.warn("汇总关系验证失败: 债权人={}, 债务人={}, 减值准备表余额={}, 诉讼表余额={}, 非诉讼表余额={}, 计算总和={}", 
                       creditor, debtor, impairmentBalance, litigationBalance, nonLitigationBalance, calculatedTotal);
        } else {
            logger.debug("汇总关系验证通过: 债权人={}, 债务人={}, 余额={}", creditor, debtor, impairmentBalance);
        }
        
        return isValid;
    }
    
    /**
     * 计算诉讼表债权余额
     */
    private BigDecimal calculateLitigationBalance(String creditor, String debtor, String period, 
                                                 int year, int month, List<LitigationClaim> litigationClaims) {
        if (litigationClaims == null || litigationClaims.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalBalance = BigDecimal.ZERO;
        
        for (LitigationClaim claim : litigationClaims) {
            if (claim.getId() != null &&
                creditor.equals(claim.getId().getCreditor()) &&
                debtor.equals(claim.getId().getDebtor()) &&
                period.equals(claim.getId().getPeriod()) &&
                year == claim.getId().getYear() &&
                month == claim.getId().getMonth()) {
                
                if (claim.getCurrentMonthDebtBalance() != null) {
                    totalBalance = totalBalance.add(claim.getCurrentMonthDebtBalance());
                }
            }
        }
        
        return totalBalance;
    }
    
    /**
     * 计算非诉讼表债权余额
     */
    private BigDecimal calculateNonLitigationBalance(String creditor, String debtor, String period, 
                                                    int year, int month, List<NonLitigationClaim> nonLitigationClaims) {
        if (nonLitigationClaims == null || nonLitigationClaims.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalBalance = BigDecimal.ZERO;
        
        for (NonLitigationClaim claim : nonLitigationClaims) {
            if (claim.getId() != null &&
                creditor.equals(claim.getId().getCreditor()) &&
                debtor.equals(claim.getId().getDebtor()) &&
                period.equals(claim.getId().getPeriod()) &&
                year == claim.getId().getYear() &&
                month == claim.getId().getMonth()) {
                
                // 非诉讼表的债权余额 = 本月末本金 + 本月末利息 + 本月末违约金
                BigDecimal principal = claim.getCurrentMonthPrincipal() != null ? claim.getCurrentMonthPrincipal() : BigDecimal.ZERO;
                BigDecimal interest = claim.getCurrentMonthInterest() != null ? claim.getCurrentMonthInterest() : BigDecimal.ZERO;
                BigDecimal penalty = claim.getCurrentMonthPenalty() != null ? claim.getCurrentMonthPenalty() : BigDecimal.ZERO;
                
                BigDecimal claimBalance = principal.add(interest).add(penalty);
                totalBalance = totalBalance.add(claimBalance);
            }
        }
        
        return totalBalance;
    }
    
    /**
     * 判断是否全额计提坏账
     * 当本月末债权余额大于0且计提减值金额大于等于本月末债权余额时，判断为全额计提
     * 
     * @param currentMonthDebtBalance 本月末债权余额
     * @param impairmentAmount 计提减值金额
     * @return 是否全额计提坏账
     */
    public boolean isFullImpairment(BigDecimal currentMonthDebtBalance, BigDecimal impairmentAmount) {
        if (currentMonthDebtBalance == null || impairmentAmount == null) {
            return false;
        }
        
        boolean isFullImpairment = currentMonthDebtBalance.compareTo(BigDecimal.ZERO) > 0 &&
                                  impairmentAmount.compareTo(currentMonthDebtBalance) >= 0;
        
        logger.debug("判断是否全额计提坏账: 债权余额={}, 计提金额={}, 结果={}", 
                    currentMonthDebtBalance, impairmentAmount, isFullImpairment);
        
        return isFullImpairment;
    }
    
    /**
     * 计算本月增减
     * 公式：本月末余额 - 上月末余额 = 本月增减
     * 
     * @param currentMonthAmount 本月末余额
     * @param previousMonthBalance 上月末余额
     * @return 本月增减
     */
    public BigDecimal calculateCurrentMonthIncreaseDecrease(BigDecimal currentMonthAmount, 
                                                           BigDecimal previousMonthBalance) {
        if (currentMonthAmount == null) {
            currentMonthAmount = BigDecimal.ZERO;
        }
        if (previousMonthBalance == null) {
            previousMonthBalance = BigDecimal.ZERO;
        }
        
        BigDecimal increaseDecrease = currentMonthAmount.subtract(previousMonthBalance);
        
        logger.debug("计算本月增减: 本月末余额={}, 上月末余额={}, 本月增减={}", 
                    currentMonthAmount, previousMonthBalance, increaseDecrease);
        
        return increaseDecrease;
    }
    
    /**
     * 验证减值准备表数据的完整性
     * 
     * @param impairmentReserve 减值准备表记录
     * @return 验证结果，true表示通过
     */
    public boolean validateImpairmentReserveData(ImpairmentReserve impairmentReserve) {
        if (impairmentReserve == null) {
            logger.warn("减值准备表记录为空");
            return false;
        }
        
        // 验证必要字段
        if (impairmentReserve.getId() == null) {
            logger.warn("减值准备表主键为空");
            return false;
        }
        
        // 验证计提减值金额与本月末余额的关系
        BigDecimal impairmentAmount = impairmentReserve.getImpairmentAmount();
        BigDecimal currentMonthAmount = impairmentReserve.getCurrentMonthAmount();
        
        if (impairmentAmount != null && currentMonthAmount != null && 
            impairmentAmount.compareTo(currentMonthAmount) != 0) {
            logger.warn("计提减值金额与本月末余额不相等: 计提金额={}, 本月末余额={}", 
                       impairmentAmount, currentMonthAmount);
            return false;
        }
        
        logger.debug("减值准备表数据验证通过: 债权人={}, 债务人={}", 
                    impairmentReserve.getId().getCreditor(), impairmentReserve.getId().getDebtor());
        
        return true;
    }
}
