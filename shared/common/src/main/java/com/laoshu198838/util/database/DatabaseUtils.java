package com.laoshu198838.util.database;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * 数据库连接工具类
 * 
 * <p>提供数据库连接管理的通用工具方法。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class DatabaseUtils {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private DatabaseUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    // 直接定义数据库连接信息
    private static final String JDBC_URL_TEMPLATE = "*******************************************************************************************";
    private static final String JDBC_USER = "root";
    private static final String JDBC_PASSWORD = "Zlb&198838";

    /**
     * 获取数据库连接（支持传入数据库名称）
     *
     * @param dbName 目标数据库名称
     * @return Connection 数据库连接，如果失败则返回 null
     */
    public static Connection getConnection(String dbName) {
        if (dbName == null || dbName.trim().isEmpty()) {
            System.err.println("❌ [错误] 数据库名称不能为空！");
            return null;
        }

        String jdbcUrl = String.format(JDBC_URL_TEMPLATE, dbName);
        // 最大重试次数
        int retryCount = 3;
        while (retryCount > 0) {
            try {
                Connection connection = DriverManager.getConnection(jdbcUrl, JDBC_USER, JDBC_PASSWORD);
                System.out.println("✅ [成功] 连接到数据库：" + dbName);
                return connection;
            } catch (SQLException e) {
                retryCount--;
                System.err.println("⚠️ [警告] 连接数据库失败：" + dbName + "，剩余重试次数：" + retryCount);
                System.err.println("🔍 [错误详情] " + e.getMessage());

                if (retryCount == 0) {
                    System.err.println("❌ [错误] 数据库连接失败，已达到最大重试次数！");
                    // 让调用方处理
                    return null;
                }

                try {
                    // 休眠 2 秒再重试
                    Thread.sleep(2000);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        return null;
    }
}
