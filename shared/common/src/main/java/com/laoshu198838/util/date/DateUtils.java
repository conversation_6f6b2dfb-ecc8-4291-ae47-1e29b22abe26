package com.laoshu198838.util.date;

/**
 * 日期工具类
 * 
 * <p>提供日期相关的通用工具方法，包括日期计算、格式转换等功能。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class DateUtils {
    
    /**
     * 私有构造函数，防止实例化工具类
     */
    private DateUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * 计算上个月的年份和月份，处理跨年情况
     *
     * @param year  当前年
     * @param month 当前月
     * @return 数组 [上年, 上月]
     */
    public static int[] getPreviousYearAndMonth(int year, int month) {
        int prevMonth = month - 1;
        int prevYear = year;
        // 处理跨年情况
        if (prevMonth == 0) {
            prevMonth = 12;
            prevYear -= 1;
        }
        return new int[]{prevYear, prevMonth};
    }
}
