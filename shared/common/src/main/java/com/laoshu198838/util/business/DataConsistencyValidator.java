package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据一致性验证工具类
 * 
 * 负责验证五表之间的数据一致性，包括：
 * 1. 汇总关系验证（减值准备表 = 诉讼表 + 非诉讼表）
 * 2. 新增表与处置表的一致性验证
 * 3. 各表内部数据的一致性验证
 * 4. 跨表数据关联验证
 * 
 * <AUTHOR>
 */
@Component
public class DataConsistencyValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(DataConsistencyValidator.class);
    
    @Autowired
    private ImpairmentReserveBusinessLogicUtil impairmentReserveUtil;
    
    @Autowired
    private AddTableBusinessLogicUtil addTableUtil;
    
    @Autowired
    private LitigationBusinessLogicUtil litigationUtil;
    
    @Autowired
    private NonLitigationBusinessLogicUtil nonLitigationUtil;
    
    @Autowired
    private DisposalBusinessLogicUtil disposalUtil;
    
    /**
     * 数据一致性验证结果
     */
    public static class ValidationResult {
        private boolean isValid;
        private List<String> errors;
        private List<String> warnings;
        
        public ValidationResult() {
            this.isValid = true;
            this.errors = new ArrayList<>();
            this.warnings = new ArrayList<>();
        }
        
        public void addError(String error) {
            this.errors.add(error);
            this.isValid = false;
        }
        
        public void addWarning(String warning) {
            this.warnings.add(warning);
        }
        
        // Getters
        public boolean isValid() { return isValid; }
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
    }
    
    /**
     * 验证汇总关系
     * 减值准备表债权余额 = 诉讼表债权余额 + 非诉讼表债权余额
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param month 月份
     * @param impairmentReserve 减值准备表记录
     * @param litigationClaims 诉讼表记录列表
     * @param nonLitigationClaims 非诉讼表记录列表
     * @return 验证结果
     */
    public ValidationResult validateSummaryRelation(String creditor, String debtor, String period,
                                                   int year, int month,
                                                   ImpairmentReserve impairmentReserve,
                                                   List<LitigationClaim> litigationClaims,
                                                   List<NonLitigationClaim> nonLitigationClaims) {
        ValidationResult result = new ValidationResult();
        
        if (impairmentReserve == null) {
            result.addError("减值准备表记录为空");
            return result;
        }
        
        BigDecimal impairmentBalance = impairmentReserve.getCurrentMonthBalance();
        if (impairmentBalance == null) {
            impairmentBalance = BigDecimal.ZERO;
        }
        
        // 使用业务逻辑工具类验证汇总关系
        boolean isValid = impairmentReserveUtil.validateSummaryRelation(
            creditor, debtor, period, year, month, impairmentBalance,
            litigationClaims, nonLitigationClaims
        );
        
        if (!isValid) {
            result.addError(String.format("汇总关系验证失败: 债权人=%s, 债务人=%s, 减值准备表余额=%s", 
                                        creditor, debtor, impairmentBalance));
        }
        
        return result;
    }
    
    /**
     * 验证新增表与处置表的一致性
     * 
     * @param addRecord 新增表记录
     * @param disposalRecords 处置记录列表
     * @return 验证结果
     */
    public ValidationResult validateAddTableDisposalConsistency(OverdueDebtAdd addRecord,
                                                               List<OverdueDebtDecrease> disposalRecords) {
        ValidationResult result = new ValidationResult();
        
        if (addRecord == null) {
            result.addError("新增表记录为空");
            return result;
        }
        
        // 验证新增表数据完整性
        if (!addTableUtil.validateAddTableData(addRecord)) {
            result.addError("新增表数据验证失败");
        }
        
        // 计算处置金额
        BigDecimal calculatedDisposal = addTableUtil.calculateDisposalAmount(
            addRecord.getCreditor(),
            addRecord.getDebtor(),
            addRecord.getPeriod(),
            addRecord.getYear(),
            disposalRecords
        );
        
        // 验证债权余额计算
        BigDecimal calculatedBalance = addTableUtil.calculateDebtBalance(
            addRecord.getNewOverdueDebtAmount(),
            calculatedDisposal
        );
        
        BigDecimal recordedBalance = addRecord.getDebtBalance();
        if (recordedBalance != null && calculatedBalance.compareTo(recordedBalance) != 0) {
            result.addError(String.format("债权余额计算不一致: 计算值=%s, 记录值=%s", 
                                        calculatedBalance, recordedBalance));
        }
        
        return result;
    }
    
    /**
     * 验证诉讼表数据一致性
     * 
     * @param litigationClaim 诉讼表记录
     * @return 验证结果
     */
    public ValidationResult validateLitigationConsistency(LitigationClaim litigationClaim) {
        ValidationResult result = new ValidationResult();
        
        if (litigationClaim == null) {
            result.addError("诉讼表记录为空");
            return result;
        }
        
        // 使用业务逻辑工具类验证
        if (!litigationUtil.validateLitigationClaimData(litigationClaim)) {
            result.addError("诉讼表数据验证失败");
        }
        
        // 验证案件名称格式
        String expectedCaseName = litigationUtil.generateCaseName(
            litigationClaim.getId().getCreditor(),
            litigationClaim.getId().getDebtor(),
            litigationClaim.getLitigationCase()
        );
        
        if (!expectedCaseName.equals(litigationClaim.getLitigationCase())) {
            result.addWarning(String.format("案件名称格式可能需要调整: 当前=%s, 建议=%s", 
                                          litigationClaim.getLitigationCase(), expectedCaseName));
        }
        
        return result;
    }
    
    /**
     * 验证非诉讼表数据一致性
     * 
     * @param nonLitigationClaim 非诉讼表记录
     * @return 验证结果
     */
    public ValidationResult validateNonLitigationConsistency(NonLitigationClaim nonLitigationClaim) {
        ValidationResult result = new ValidationResult();
        
        if (nonLitigationClaim == null) {
            result.addError("非诉讼表记录为空");
            return result;
        }
        
        // 使用业务逻辑工具类验证
        if (!nonLitigationUtil.validateNonLitigationClaimData(nonLitigationClaim)) {
            result.addError("非诉讼表数据验证失败");
        }
        
        // 验证本金双重公式
        BigDecimal lastMonthPrincipal = nonLitigationClaim.getLastMonthPrincipal();
        BigDecimal principalIncrease = nonLitigationClaim.getCurrentMonthPrincipalIncreaseDecrease();
        BigDecimal currentMonthPrincipal = nonLitigationClaim.getCurrentMonthPrincipal();
        BigDecimal newDebt = nonLitigationClaim.getCurrentMonthNewDebt();
        BigDecimal disposedDebt = nonLitigationClaim.getCurrentMonthDisposedDebt();
        
        if (lastMonthPrincipal != null && principalIncrease != null && 
            newDebt != null && disposedDebt != null) {
            
            BigDecimal formula1Result = lastMonthPrincipal.add(principalIncrease);
            BigDecimal formula2Result = lastMonthPrincipal.add(newDebt).subtract(disposedDebt);
            
            if (formula1Result.compareTo(formula2Result) != 0) {
                result.addWarning(String.format("本金双重公式结果不一致: 公式1=%s, 公式2=%s", 
                                              formula1Result, formula2Result));
            }
        }
        
        return result;
    }
    
    /**
     * 验证减值准备表数据一致性
     * 
     * @param impairmentReserve 减值准备表记录
     * @return 验证结果
     */
    public ValidationResult validateImpairmentReserveConsistency(ImpairmentReserve impairmentReserve) {
        ValidationResult result = new ValidationResult();
        
        if (impairmentReserve == null) {
            result.addError("减值准备表记录为空");
            return result;
        }
        
        // 使用业务逻辑工具类验证
        if (!impairmentReserveUtil.validateImpairmentReserveData(impairmentReserve)) {
            result.addError("减值准备表数据验证失败");
        }
        
        // 验证本月增减计算
        BigDecimal currentMonthAmount = impairmentReserve.getCurrentMonthAmount();
        BigDecimal previousMonthBalance = impairmentReserve.getPreviousMonthBalance();
        BigDecimal currentMonthIncreaseDecrease = impairmentReserve.getCurrentMonthIncreaseDecrease();
        
        if (currentMonthAmount != null && previousMonthBalance != null && currentMonthIncreaseDecrease != null) {
            BigDecimal calculatedIncrease = impairmentReserveUtil.calculateCurrentMonthIncreaseDecrease(
                currentMonthAmount, previousMonthBalance);
            
            if (calculatedIncrease.compareTo(currentMonthIncreaseDecrease) != 0) {
                result.addError(String.format("本月增减计算不正确: 计算值=%s, 记录值=%s", 
                                            calculatedIncrease, currentMonthIncreaseDecrease));
            }
        }
        
        return result;
    }
    
    /**
     * 验证处置表数据一致性
     * 
     * @param disposalRecord 处置记录
     * @return 验证结果
     */
    public ValidationResult validateDisposalConsistency(OverdueDebtDecrease disposalRecord) {
        ValidationResult result = new ValidationResult();
        
        if (disposalRecord == null) {
            result.addError("处置表记录为空");
            return result;
        }
        
        // 使用业务逻辑工具类验证
        if (!disposalUtil.validateDisposalData(disposalRecord)) {
            result.addError("处置表数据验证失败");
        }
        
        return result;
    }
    
    /**
     * 全面验证五表数据一致性
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param year 年份
     * @param month 月份
     * @param addRecord 新增表记录
     * @param impairmentReserve 减值准备表记录
     * @param litigationClaims 诉讼表记录列表
     * @param nonLitigationClaims 非诉讼表记录列表
     * @param disposalRecords 处置记录列表
     * @return 验证结果
     */
    public ValidationResult validateAllTablesConsistency(String creditor, String debtor, String period,
                                                        int year, int month,
                                                        OverdueDebtAdd addRecord,
                                                        ImpairmentReserve impairmentReserve,
                                                        List<LitigationClaim> litigationClaims,
                                                        List<NonLitigationClaim> nonLitigationClaims,
                                                        List<OverdueDebtDecrease> disposalRecords) {
        ValidationResult result = new ValidationResult();
        
        // 1. 验证新增表与处置表一致性
        ValidationResult addTableResult = validateAddTableDisposalConsistency(addRecord, disposalRecords);
        result.getErrors().addAll(addTableResult.getErrors());
        result.getWarnings().addAll(addTableResult.getWarnings());
        
        // 2. 验证减值准备表一致性
        ValidationResult impairmentResult = validateImpairmentReserveConsistency(impairmentReserve);
        result.getErrors().addAll(impairmentResult.getErrors());
        result.getWarnings().addAll(impairmentResult.getWarnings());
        
        // 3. 验证诉讼表一致性
        if (litigationClaims != null) {
            for (LitigationClaim claim : litigationClaims) {
                ValidationResult litigationResult = validateLitigationConsistency(claim);
                result.getErrors().addAll(litigationResult.getErrors());
                result.getWarnings().addAll(litigationResult.getWarnings());
            }
        }
        
        // 4. 验证非诉讼表一致性
        if (nonLitigationClaims != null) {
            for (NonLitigationClaim claim : nonLitigationClaims) {
                ValidationResult nonLitigationResult = validateNonLitigationConsistency(claim);
                result.getErrors().addAll(nonLitigationResult.getErrors());
                result.getWarnings().addAll(nonLitigationResult.getWarnings());
            }
        }
        
        // 5. 验证处置表一致性
        if (disposalRecords != null) {
            for (OverdueDebtDecrease record : disposalRecords) {
                ValidationResult disposalResult = validateDisposalConsistency(record);
                result.getErrors().addAll(disposalResult.getErrors());
                result.getWarnings().addAll(disposalResult.getWarnings());
            }
        }
        
        // 6. 验证汇总关系
        ValidationResult summaryResult = validateSummaryRelation(
            creditor, debtor, period, year, month,
            impairmentReserve, litigationClaims, nonLitigationClaims
        );
        result.getErrors().addAll(summaryResult.getErrors());
        result.getWarnings().addAll(summaryResult.getWarnings());
        
        // 更新验证状态
        if (!result.getErrors().isEmpty()) {
            result.isValid = false;
        }
        
        logger.info("五表数据一致性验证完成: 债权人={}, 债务人={}, 验证结果={}, 错误数={}, 警告数={}", 
                   creditor, debtor, result.isValid, result.getErrors().size(), result.getWarnings().size());
        
        return result;
    }
}
