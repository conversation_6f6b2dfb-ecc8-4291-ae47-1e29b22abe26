package com.laoshu198838.util.business;

import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 诉讼表业务逻辑工具类
 * 
 * 负责处理诉讼表相关的业务计算和验证逻辑，包括：
 * 1. 修正案件名称格式（改为"债权人诉债务人"）
 * 2. 上月末债权余额验证和调整逻辑（上月末债权余额=涉诉债权本金+涉诉债权应收利息）
 * 3. 债权余额平衡计算（已正确实现，保持不变）
 * 4. 本年度累计回收计算
 * 5. 逾期年限计算
 * 
 * <AUTHOR>
 */
@Component
public class LitigationBusinessLogicUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(LitigationBusinessLogicUtil.class);
    
    /**
     * 生成案件名称
     * 格式：债权人诉债务人
     * 如果已有案件名称则保持不变，否则生成标准格式
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param inputCaseName 输入的案件名称
     * @return 标准格式的案件名称
     */
    public String generateCaseName(String creditor, String debtor, String inputCaseName) {
        // 如果已有案件名称且不为空，则保持不变
        if (inputCaseName != null && !inputCaseName.trim().isEmpty()) {
            return inputCaseName.trim();
        }
        
        // 验证债权人和债务人不为空
        if (creditor == null || creditor.trim().isEmpty()) {
            throw new IllegalArgumentException("债权人不能为空");
        }
        if (debtor == null || debtor.trim().isEmpty()) {
            throw new IllegalArgumentException("债务人不能为空");
        }
        
        String caseName = creditor.trim() + "诉" + debtor.trim();
        
        logger.debug("生成案件名称: 债权人={}, 债务人={}, 案件名称={}", creditor, debtor, caseName);
        
        return caseName;
    }
    
    /**
     * 验证和调整上月末债权余额
     * 验证：上月末债权余额 = 涉诉债权本金 + 涉诉债权应收利息
     * 如果不相等，默认调整本金，保持利息不变
     * 
     * @param litigationClaim 诉讼表记录
     */
    public void adjustPrincipalInterestBalance(LitigationClaim litigationClaim) {
        if (litigationClaim == null) {
            logger.warn("诉讼表记录为空，无法进行余额调整");
            return;
        }
        
        BigDecimal lastMonthBalance = litigationClaim.getLastMonthDebtBalance();
        BigDecimal principal = litigationClaim.getLitigationPrincipal();
        BigDecimal interest = litigationClaim.getLitigationInterest();
        
        // 如果任何一个值为null，则不进行调整
        if (lastMonthBalance == null || principal == null || interest == null) {
            logger.debug("存在空值，跳过余额调整: 上月末余额={}, 本金={}, 利息={}", 
                        lastMonthBalance, principal, interest);
            return;
        }
        
        BigDecimal calculatedBalance = principal.add(interest);
        
        // 如果上月末债权余额与本金+利息不相等，则调整本金
        if (lastMonthBalance.compareTo(calculatedBalance) != 0) {
            BigDecimal adjustedPrincipal = lastMonthBalance.subtract(interest);
            
            // 确保调整后的本金不为负数
            if (adjustedPrincipal.compareTo(BigDecimal.ZERO) < 0) {
                logger.warn("调整后本金为负数，将本金设为0，利息调整为上月末余额: 债权人={}, 债务人={}", 
                           litigationClaim.getId().getCreditor(), litigationClaim.getId().getDebtor());
                litigationClaim.setLitigationPrincipal(BigDecimal.ZERO);
                litigationClaim.setLitigationInterest(lastMonthBalance);
            } else {
                litigationClaim.setLitigationPrincipal(adjustedPrincipal);
                logger.info("调整诉讼表本金: 债权人={}, 债务人={}, 原本金={}, 调整后本金={}", 
                           litigationClaim.getId().getCreditor(), litigationClaim.getId().getDebtor(), 
                           principal, adjustedPrincipal);
            }
        } else {
            logger.debug("上月末债权余额与本金+利息相等，无需调整: 债权人={}, 债务人={}, 余额={}", 
                        litigationClaim.getId().getCreditor(), litigationClaim.getId().getDebtor(), lastMonthBalance);
        }
    }
    
    /**
     * 计算本月末债权余额
     * 公式：上月末债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额
     * 注意：这个逻辑已经正确实现，此方法用于验证或手动计算
     * 
     * @param lastMonthBalance 上月末债权余额
     * @param currentMonthNewDebt 本月新增债权
     * @param currentMonthDisposalDebt 本月处置债权
     * @return 本月末债权余额
     */
    public BigDecimal calculateCurrentMonthDebtBalance(BigDecimal lastMonthBalance,
                                                      BigDecimal currentMonthNewDebt,
                                                      BigDecimal currentMonthDisposalDebt) {
        if (lastMonthBalance == null) {
            lastMonthBalance = BigDecimal.ZERO;
        }
        if (currentMonthNewDebt == null) {
            currentMonthNewDebt = BigDecimal.ZERO;
        }
        if (currentMonthDisposalDebt == null) {
            currentMonthDisposalDebt = BigDecimal.ZERO;
        }
        
        BigDecimal currentMonthBalance = lastMonthBalance
            .add(currentMonthNewDebt)
            .subtract(currentMonthDisposalDebt);
        
        logger.debug("计算诉讼表本月末债权余额: 上月末={}, 本月新增={}, 本月处置={}, 本月末={}", 
                    lastMonthBalance, currentMonthNewDebt, currentMonthDisposalDebt, currentMonthBalance);
        
        return currentMonthBalance;
    }
    
    /**
     * 计算本年度累计回收
     * 根据债权人、债务人、是否涉诉、年份、期间确定唯一值后累加本月处置债权
     * 
     * @param creditor 债权人
     * @param debtor 债务人
     * @param isLitigation 是否涉诉
     * @param year 年份
     * @param period 期间
     * @param currentMonthDisposal 本月处置债权
     * @param previousAnnualRecovery 之前的年度累计回收
     * @return 本年度累计回收
     */
    public BigDecimal calculateAnnualCumulativeRecovery(String creditor, String debtor, 
                                                       String isLitigation, int year, String period,
                                                       BigDecimal currentMonthDisposal,
                                                       BigDecimal previousAnnualRecovery) {
        if (currentMonthDisposal == null) {
            currentMonthDisposal = BigDecimal.ZERO;
        }
        if (previousAnnualRecovery == null) {
            previousAnnualRecovery = BigDecimal.ZERO;
        }
        
        BigDecimal annualRecovery = previousAnnualRecovery.add(currentMonthDisposal);
        
        logger.debug("计算诉讼表本年度累计回收: 债权人={}, 债务人={}, 本月处置={}, 累计回收={}", 
                    creditor, debtor, currentMonthDisposal, annualRecovery);
        
        return annualRecovery;
    }
    
    /**
     * 计算逾期年限
     * 根据债权到期时间和当前时间计算逾期年限
     * 
     * @param dueDate 到期时间
     * @param currentYear 当前年份
     * @param currentMonth 当前月份
     * @return 逾期年限分类（≤1年、1-5年、≥5年）
     */
    public String calculateOverdueYear(java.util.Date dueDate, int currentYear, int currentMonth) {
        if (dueDate == null) {
            return "未知";
        }
        
        // 简化计算：基于年份差异
        java.util.Calendar dueCal = java.util.Calendar.getInstance();
        dueCal.setTime(dueDate);
        int dueYear = dueCal.get(java.util.Calendar.YEAR);
        int dueMonth = dueCal.get(java.util.Calendar.MONTH) + 1; // Calendar月份从0开始
        
        // 计算逾期月数
        int overdueMonths = (currentYear - dueYear) * 12 + (currentMonth - dueMonth);
        
        if (overdueMonths <= 0) {
            return "未逾期";
        } else if (overdueMonths <= 12) {
            return "≤1年";
        } else if (overdueMonths <= 60) {
            return "1-5年";
        } else {
            return "≥5年";
        }
    }
    
    /**
     * 验证诉讼表数据的完整性
     * 
     * @param litigationClaim 诉讼表记录
     * @return 验证结果，true表示通过
     */
    public boolean validateLitigationClaimData(LitigationClaim litigationClaim) {
        if (litigationClaim == null) {
            logger.warn("诉讼表记录为空");
            return false;
        }
        
        // 验证必要字段
        if (litigationClaim.getId() == null) {
            logger.warn("诉讼表主键为空");
            return false;
        }
        
        // 验证债权余额平衡计算
        BigDecimal lastMonthBalance = litigationClaim.getLastMonthDebtBalance();
        BigDecimal currentMonthNewDebt = litigationClaim.getCurrentMonthNewDebt();
        BigDecimal currentMonthDisposal = litigationClaim.getCurrentMonthDisposalDebt();
        BigDecimal currentMonthBalance = litigationClaim.getCurrentMonthDebtBalance();
        
        if (lastMonthBalance != null && currentMonthNewDebt != null && 
            currentMonthDisposal != null && currentMonthBalance != null) {
            
            BigDecimal calculatedBalance = calculateCurrentMonthDebtBalance(
                lastMonthBalance, currentMonthNewDebt, currentMonthDisposal);
            
            if (calculatedBalance.compareTo(currentMonthBalance) != 0) {
                logger.warn("诉讼表债权余额平衡计算不正确: 计算值={}, 记录值={}", 
                           calculatedBalance, currentMonthBalance);
                return false;
            }
        }
        
        logger.debug("诉讼表数据验证通过: 债权人={}, 债务人={}", 
                    litigationClaim.getId().getCreditor(), litigationClaim.getId().getDebtor());
        
        return true;
    }
    
    /**
     * 更新诉讼表的案件名称和余额调整
     * 
     * @param litigationClaim 诉讼表记录
     */
    public void updateCaseNameAndBalance(LitigationClaim litigationClaim) {
        if (litigationClaim == null || litigationClaim.getId() == null) {
            logger.warn("诉讼表记录或主键为空，无法更新");
            return;
        }
        
        // 更新案件名称
        String caseName = generateCaseName(
            litigationClaim.getId().getCreditor(),
            litigationClaim.getId().getDebtor(),
            litigationClaim.getLitigationCase()
        );
        litigationClaim.setLitigationCase(caseName);
        
        // 调整本金和利息余额
        adjustPrincipalInterestBalance(litigationClaim);
        
        logger.info("更新诉讼表案件名称和余额: 债权人={}, 债务人={}, 案件名称={}", 
                   litigationClaim.getId().getCreditor(), 
                   litigationClaim.getId().getDebtor(), 
                   caseName);
    }
}
