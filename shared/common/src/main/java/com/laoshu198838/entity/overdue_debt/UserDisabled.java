package com.laoshu198838.entity.overdue_debt;

import java.util.Collection;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import lombok.Getter;
import lombok.Setter;

/**
 * 用户实体类
 *
 * <p>表示系统中的用户信息，包含用户的基本信息、认证信息和权限关联。
 * 用于用户登录认证、权限控制和用户管理等功能。</p>
 *
 * <h3>数据库映射：</h3>
 * <ul>
 *   <li>表名：users</li>
 *   <li>主键：id（自增长）</li>
 *   <li>唯一约束：username（用户名唯一）</li>
 * </ul>
 *
 * <h3>关联关系：</h3>
 * <ul>
 *   <li>多对一关系：User → Role（用户角色关联）</li>
 *   <li>外键：role_id</li>
 * </ul>
 *
 * <h3>安全特性：</h3>
 * <ul>
 *   <li>密码加密存储（BCrypt）</li>
 *   <li>支持账户启用/禁用</li>
 *   <li>集成Spring Security UserDetails接口</li>
 *   <li>权限基于角色进行控制</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 * @see Role
 * @see org.springframework.security.core.userdetails.UserDetails
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
// 临时禁用：避免与用户系统数据库的实体冲突，防止Spring错误地映射到overdue_debt_db.users表
// @Entity
// @Table(name = "users")
public class UserDisabled implements UserDetails {

    /**
     * 用户唯一标识ID
     * 数据库主键，自动递增
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户登录名
     * 系统中唯一，用于用户登录认证
     * 长度限制：1-50个字符
     */
    @Column(name= "username",unique = true,nullable = false,length = 50)
    private String username;

    /**
     * 用户真实姓名
     * 用于显示和标识用户身份
     * 长度限制：1-50个字符
     */
    @Column(name= "name",nullable = false,length = 50)
    private String name;

    /**
     * 用户密码（加密存储）
     * 使用BCrypt算法加密存储
     * 长度限制：最大150个字符（包含加密后的哈希值）
     */
    @Column(name= "password",nullable = false,length = 150)
    private String password;

    /**
     * 用户所属公司名称
     * 可选字段，用于多公司环境下的用户分组
     * 长度限制：最大50个字符
     */
    @Column(name= "companyname",length = 50)
    private String companyname;

    /**
     * 用户所属部门
     * 可选字段，用于部门级别的权限控制
     * 长度限制：最大50个字符
     */
    @Column(name= "department",length = 50)
    private String department;

    private static final String STATUS_ACTIVE = "ACTIVE";
    private static final String DEFAULT_COMPANY = "所有公司";
    private static final String DEFAULT_DEPARTMENT = "所有部门";

    @Column(name = "status",length = 50)
    private String status = STATUS_ACTIVE;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "role_id")
    private Role role; // 关联 Role 实体

    @Override
    public boolean isAccountNonExpired() {
        // 根据实际情况返回
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return STATUS_ACTIVE.equals(status);
    }

    public UserDisabled(String username, String password) {
        this.username = username;
        this.password = password;
        this.status = STATUS_ACTIVE;
        this.companyname = DEFAULT_COMPANY;
        this.department = DEFAULT_DEPARTMENT;
    }

    public UserDisabled(String username, String password, String name, String companyname, String department, Role role) {
        this.username = username;
        this.password = password;
        this.name = name;
        this.companyname = companyname;
        this.department = department;
        this.role = role;
        this.status = STATUS_ACTIVE;
    }

    public UserDisabled() {
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // Spring Security 需要 "ROLE_" 前缀
        return List.of(() -> "ROLE_" + role.getRoleName());
    }
}
