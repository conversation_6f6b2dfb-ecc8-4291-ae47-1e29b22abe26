package com.laoshu198838.entity.overdue_debt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 债务跟踪记录实体类
 * 用于存储和管理债务跟踪数据
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "debt_tracking_record")
@IdClass(DebtTrackingRecord.DebtTrackingKey.class)
public class DebtTrackingRecord implements Serializable {

    @Id
    @Column(name = "creditor", nullable = false, length = 100)
    private String creditor;

    @Id
    @Column(name = "debtor", nullable = false, length = 100)
    private String debtor;

    @Id
    @Column(name = "period", nullable = false, length = 7)
    private String period;

    @Id
    @Column(name = "is_litigation", nullable = false, length = 1)
    private String isLitigation;

    @Column(name = "year")
    private Integer year;

    @Column(name = "customer_category", length = 50)
    private String customerCategory;

    @Column(name = "debt_risk_type", length = 50)
    private String debtRiskType;

    @Column(name = "debt_amount", precision = 20, scale = 2)
    private BigDecimal debtAmount;

    @Column(name = "management_company", length = 100)
    private String managementCompany;

    @Column(name = "cash_disposal", precision = 20, scale = 2)
    private BigDecimal cashDisposal;

    @Column(name = "installment_repayment", precision = 20, scale = 2)
    private BigDecimal installmentRepayment;

    @Column(name = "asset_debt", precision = 20, scale = 2)
    private BigDecimal assetDebt;

    @Column(name = "other_ways", precision = 20, scale = 2)
    private BigDecimal otherWays;

    @Column(name = "total_reduce_amount", precision = 20, scale = 2)
    private BigDecimal totalReduceAmount;

    @Column(name = "status", length = 1)
    private String status;

    @Column(name = "create_time", length = 19)
    private String createTime;

    @Column(name = "update_time", length = 19)
    private String updateTime;

    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 默认构造方法
     */
    public DebtTrackingRecord() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        this.createTime = now.format(formatter);
        this.updateTime = this.createTime;

        // 设置年份 (从期间字段提取)
        if (this.period != null && this.period.length() >= 4) {
            try {
                this.year = Integer.parseInt(this.period.substring(0, 4));
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }
    }

    // ========== Getters and Setters ==========

    public void setPeriod(String period) {
        this.period = period;

        // 更新年份
        if (period != null && period.length() >= 4) {
            try {
                this.year = Integer.parseInt(period.substring(0, 4));
            } catch (NumberFormatException e) {
                // 忽略解析错误
            }
        }
    }

    @PreUpdate
    public void preUpdate() {
        this.updateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 债务跟踪记录专用的复合主键类
     */
    @Getter
    @Setter
    @EqualsAndHashCode
    public static class DebtTrackingKey implements Serializable {

        private static final long serialVersionUID = 1L;

        private String creditor;
        private String debtor;
        private String period;
        private String isLitigation;

        // 默认无参构造函数
        public DebtTrackingKey() {}

        // 带参数的构造函数，用于创建复合键实例
        public DebtTrackingKey(String creditor, String debtor, String period, String isLitigation) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
        }

        // Getter 和 Setter 方法
        public String getCreditor() {
            return creditor;
        }

        public void setCreditor(String creditor) {
            this.creditor = creditor;
        }

        public String getDebtor() {
            return debtor;
        }

        public void setDebtor(String debtor) {
            this.debtor = debtor;
        }

        public String getPeriod() {
            return period;
        }

        public void setPeriod(String period) {
            this.period = period;
        }

        public String getIsLitigation() {
            return isLitigation;
        }

        public void setIsLitigation(String isLitigation) {
            this.isLitigation = isLitigation;
        }
    }
}
