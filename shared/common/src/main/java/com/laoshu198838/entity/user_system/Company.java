package com.laoshu198838.entity.user_system;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 公司信息实体类
 * 对应user_system数据库的companies表
 * 
 * <AUTHOR>
 */
@Entity(name = "UserSystemCompany")
@Table(name = "companies", schema = "user_system")
public class Company {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "company_name", unique = true, nullable = false, length = 50)
    private String companyName;

    @Column(name = "company_code", unique = true, length = 20)
    private String companyCode;

    @Column(name = "company_short_name", length = 20)
    private String companyShortName;

    @Column(name = "parent_company_id")
    private Long parentCompanyId;

    @Column(name = "level")
    private Integer level = 1;

    @Column(name = "is_management_company")
    private Boolean isManagementCompany = false;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private CompanyStatus status = CompanyStatus.active;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 枚举定义
    public enum CompanyStatus {
        active, inactive
    }

    // 构造函数
    public Company() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public Company(String companyName, String companyCode, Boolean isManagementCompany) {
        this();
        this.companyName = companyName;
        this.companyCode = companyCode;
        this.isManagementCompany = isManagementCompany;
    }

    // JPA生命周期回调
    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyShortName() {
        return companyShortName;
    }

    public void setCompanyShortName(String companyShortName) {
        this.companyShortName = companyShortName;
    }

    public Long getParentCompanyId() {
        return parentCompanyId;
    }

    public void setParentCompanyId(Long parentCompanyId) {
        this.parentCompanyId = parentCompanyId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getIsManagementCompany() {
        return isManagementCompany;
    }

    public void setIsManagementCompany(Boolean isManagementCompany) {
        this.isManagementCompany = isManagementCompany;
    }

    public CompanyStatus getStatus() {
        return status;
    }

    public void setStatus(CompanyStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "Company{" +
                "id=" + id +
                ", companyName='" + companyName + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", companyShortName='" + companyShortName + '\'' +
                ", parentCompanyId=" + parentCompanyId +
                ", level=" + level +
                ", isManagementCompany=" + isManagementCompany +
                ", status=" + status +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Company company = (Company) o;
        return id != null ? id.equals(company.id) : company.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}