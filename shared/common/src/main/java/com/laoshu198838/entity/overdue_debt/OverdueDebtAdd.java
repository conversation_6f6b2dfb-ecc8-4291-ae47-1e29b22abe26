package com.laoshu198838.entity.overdue_debt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;

/**
 * OverdueDebtAdd实体类，对应数据库表 新增表
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "新增表") // 对应数据库中的表名
public class OverdueDebtAdd {

    @EmbeddedId
    private OverdueDebtAddKey id;

    @Column(name = "序号", nullable = false)
    private int sequence;

    @Column(name = "管理公司", nullable = false, length = 30) // 对应数据库的列名
    private String managementCompany;

    @Column(name = "到期时间", length = 30) // 对应数据库的列名
    private String dueTime;

    @Column(name = "科目名称", length = 30) // 对应数据库的列名
    private String subjectName;

    @Column(name = "债权性质", length = 30) // 对应数据库的列名
    private String debtNature;

    @Column(name = "债权风险类型",  length = 30) // 对应数据库的列名
    private String debtRisk;

    @Column(name = "客户情况分类", length = 30) // 对应数据库的列名
    private String customerCategory;

    @Column(name = "1月", nullable = false) // 对应数据库的列名
    private BigDecimal amountJan = BigDecimal.ZERO;

    @Column(name = "2月", nullable = false) // 对应数据库的列名
    private BigDecimal amountFeb = BigDecimal.ZERO;

    @Column(name = "3月", nullable = false) // 对应数据库的列名
    private BigDecimal amountMar = BigDecimal.ZERO;

    @Column(name = "4月", nullable = false) // 对应数据库的列名
    private BigDecimal amountApr = BigDecimal.ZERO;

    @Column(name = "5月", nullable = false) // 对应数据库的列名
    private BigDecimal amountMay = BigDecimal.ZERO;

    @Column(name = "6月", nullable = false) // 对应数据库的列名
    private BigDecimal amountJun = BigDecimal.ZERO;

    @Column(name = "7月", nullable = false) // 对应数据库的列名
    private BigDecimal amountJul = BigDecimal.ZERO;

    @Column(name = "8月", nullable = false) // 对应数据库的列名
    private BigDecimal amountAug = BigDecimal.ZERO;

    @Column(name = "9月", nullable = false) // 对应数据库的列名
    private BigDecimal amountSep = BigDecimal.ZERO;

    @Column(name = "10月", nullable = false) // 对应数据库的列名
    private BigDecimal amountOct = BigDecimal.ZERO;

    @Column(name = "11月", nullable = false) // 对应数据库的列名
    private BigDecimal amountNov = BigDecimal.ZERO;

    @Column(name = "12月", nullable = false) // 对应数据库的列名
    private BigDecimal amountDec = BigDecimal.ZERO;

    @Column(name = "处置金额", nullable = false) // 对应数据库的列名
    private BigDecimal reductionAmount = BigDecimal.ZERO;

    @Column(name = "现金处置", nullable = false) // 对应数据库的列名
    private BigDecimal cashDisposal = BigDecimal.ZERO;

    @Column(name = "分期还款", nullable = false) // 对应数据库的列名
    private BigDecimal installmentRepayment = BigDecimal.ZERO;

    @Column(name = "资产抵债", nullable = false) // 对应数据库的列名
    private BigDecimal assetDebt = BigDecimal.ZERO;

    @Column(name = "其他方式", nullable = false) // 对应数据库的列名
    private BigDecimal otherMethods = BigDecimal.ZERO;

    @Column(name = "新增金额", nullable = false) // 对应数据库的列名
    private BigDecimal newOverdueDebtAmount = BigDecimal.ZERO;

    @Column(name = "债权余额", nullable = false) // 对应数据库的列名
    private BigDecimal debtBalance = BigDecimal.ZERO;

    @Column(name = "更新时间")
    private LocalDateTime updateTime;

    @Column(name = "备注", length = 190) // 对应数据库的列名
    private String remark;

    @Column(name = "责任人", length = 30) // 对应数据库的列名
    private String responsiblePerson;

    // 使用布尔值设置
    public void setIsLitigation(String isLitigation) {
        // 将 "是" 转为 true，"否" 转为 false
        if (this.id != null) {
            this.id.setIsLitigation("是".equals(isLitigation) ? "是" : "否");
        }
    }

    @PrePersist
    @PreUpdate
    public void updateTimestamp() {
        this.updateTime = LocalDateTime.now();
    }

    // 委托方法，将字段访问委托给内部的复合主键类
    public String getCreditor() {
        return id != null ? id.getCreditor() : null;
    }

    public void setCreditor(String creditor) {
        if (id == null) {
            id = new OverdueDebtAddKey();
        }
        id.setCreditor(creditor);
    }

    public String getDebtor() {
        return id != null ? id.getDebtor() : null;
    }

    public void setDebtor(String debtor) {
        if (id == null) {
            id = new OverdueDebtAddKey();
        }
        id.setDebtor(debtor);
    }

    public String getPeriod() {
        return id != null ? id.getPeriod() : null;
    }

    public void setPeriod(String period) {
        if (id == null) {
            id = new OverdueDebtAddKey();
        }
        id.setPeriod(period);
    }

    public String getYear() {
        return id != null ? id.getYear() : null;
    }

    public void setYear(String year) {
        if (id == null) {
            id = new OverdueDebtAddKey();
        }
        id.setYear(year);
    }

    public String getIsLitigation() {
        return id != null ? id.getIsLitigation() : null;
    }

    /**
     * 新增表专用的复合主键类
     * <p>
     * 该类封装“新增表”的联合主键字段，
     * 包括债权人、债务人、期间、是否涉诉和年份等信息。
     * </p>
     */
    @Getter
    @Setter
    @EqualsAndHashCode
    @Embeddable
    public static class OverdueDebtAddKey implements Serializable {

        @Column(name = "债权人")
        private String creditor;

        @Column(name = "债务人")
        private String debtor;

        @Column(name = "期间")
        private String period;

        @Column(name = "是否涉诉")
        private String isLitigation;

        @Column(name = "年份")
        private String year;

        // 默认无参构造函数
        public OverdueDebtAddKey() {}

        // 带参数的构造函数，用于创建复合键实例
        public OverdueDebtAddKey(String creditor, String debtor, String period, String isLitigation) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            // 尝试从期间中提取年份
            if (period != null && period.length() >= 4) {
                this.year = period.substring(0, 4);
            }
        }

        // 带年份的构造函数
        public OverdueDebtAddKey(String creditor, String debtor, String period, String isLitigation, String year) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            this.year = year;
        }
    }
}
