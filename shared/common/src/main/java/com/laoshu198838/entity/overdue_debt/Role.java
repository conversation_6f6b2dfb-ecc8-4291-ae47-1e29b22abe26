package com.laoshu198838.entity.overdue_debt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;

/**
 * 角色实体类
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "roles")
public class Role implements GrantedAuthority {
    private static final long serialVersionUID = 1L;
    
    @Id
    @Column(name = "role_id")
    private Integer roleId;
    
    @Column(name = "role_name", unique = true, nullable = false, length = 50)
    // 角色名称，如 "USER"、"ADMIN"
    private String roleName;

    @Override
    public String getAuthority() {
        // 假设name字段存储权限标识（如ROLE_ADMIN）
        return "ROLE_" + this.roleName;
    }
}