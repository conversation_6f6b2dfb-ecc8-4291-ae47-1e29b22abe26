package com.laoshu198838.entity.overdue_debt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "处置表") // 对应数据库中的表名
public class OverdueDebtDecrease {

    @EmbeddedId
    private OverdueDebtDecreaseKey id;

    @Column(name = "序号", nullable = false, length = 30) // 对应数据库的列名
    private int sequence;

    @Column(name = "管理公司", nullable = false, length = 30) // 对应数据库的列名
    private String managementCompany;

    @Column(name = "债权风险类型", length = 30) // 对应数据库的列名
    private String debtRisk;

    @Column(name = "客户情况分类", length = 30) // 对应数据库的列名
    private String customerCategory;

    @Column(name = "每月处置金额") // 对应数据库的列名
    private BigDecimal monthlyReduceAmount = BigDecimal.ZERO;

    @Column(name = "现金处置") // 对应数据库的列名
    private BigDecimal cashDisposal = BigDecimal.ZERO;

    @Column(name = "分期还款") // 对应数据库的列名
    private BigDecimal installmentRepayment = BigDecimal.ZERO;

    @Column(name = "资产抵债") // 对应数据库的列名
    private BigDecimal assetDebt = BigDecimal.ZERO;

    @Column(name = "其他方式") // 对应数据库的列名
    private BigDecimal otherWays = BigDecimal.ZERO;

    // 累计处置金额在数据库中不存在，因此注释掉该映射
    @Transient // 标记为非持久化字段
    private BigDecimal totalReduceAmount = BigDecimal.ZERO;

    @Column(name = "备注", length = 190) // 对应数据库的列名
    private String remark;

    @Column(name = "更新时间", nullable = false) // 对应数据库的列名
    private LocalDateTime updateTime;

    // 委托方法，将字段访问委托给内部的复合主键类
    public String getCreditor() {
        return id != null ? id.getCreditor() : null;
    }

    public void setCreditor(String creditor) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        id.setCreditor(creditor);
    }

    public String getDebtor() {
        return id != null ? id.getDebtor() : null;
    }

    public void setDebtor(String debtor) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        id.setDebtor(debtor);
    }

    public String getPeriod() {
        return id != null ? id.getPeriod() : null;
    }

    public void setPeriod(String period) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        id.setPeriod(period);
    }

    public int getYear() {
        return id != null ? id.getYear() : 0;
    }

    public void setYear(int year) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        id.setYear(year);
    }

    public void setYear(BigDecimal year) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        if (year != null) {
            id.setYear(year.intValue());
        }
    }

    public String getIsLitigation() {
        return id != null ? id.getIsLitigation() : null;
    }

    public void setIsLitigation(String isLitigation) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        id.setIsLitigation(isLitigation);
    }

    public BigDecimal getMonth() {
        return id != null ? id.getMonth() : null;
    }

    public void setMonth(BigDecimal month) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        id.setMonth(month);
    }

    // 添加月份的int类型getter和setter，方便处理
    public int getMonthValue() {
        return id != null && id.getMonth() != null ? id.getMonth().intValue() : 0;
    }

    public void setMonthValue(int month) {
        if (id == null) {
            id = new OverdueDebtDecreaseKey();
        }
        id.setMonth(BigDecimal.valueOf(month));
    }

    /**
     * 处置表专用的复合主键类
     * <p>
     * 该类封装“处置表”的联合主键字段，
     * 包括债权人、债务人、期间、是否涉诉、年份和月份等信息。
     * </p>
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @EqualsAndHashCode
    @Embeddable
    public static class OverdueDebtDecreaseKey implements Serializable {

        private static final long serialVersionUID = 1L;

        @Column(name = "债权人", length = 50)
        private String creditor;

        @Column(name = "债务人", length = 50)
        private String debtor;

        @Column(name = "期间", length = 20)
        private String period;

        @Column(name = "是否涉诉", length = 10)
        private String isLitigation;

        @Column(name = "年份", length = 4)
        private int year;

        @Column(name = "月份")
        private BigDecimal month;

        // 默认无参构造函数
        public OverdueDebtDecreaseKey() {}

        // 带参数的构造函数，用于创建复合键实例
        public OverdueDebtDecreaseKey(String creditor, String debtor, String period, String isLitigation, int year, BigDecimal month) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            this.year = year;
            this.month = month;
        }

        // 重载构造函数，接受int类型的月份
        public OverdueDebtDecreaseKey(String creditor, String debtor, String period, String isLitigation, int year, int month) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            this.year = year;
            this.month = BigDecimal.valueOf(month);
        }
    }
}
