package com.laoshu198838.entity.overdue_debt;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 汇总表实体类
 * 对应数据库中的汇总表
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "汇总表")
public class OverdueDebtSummary {

    @EmbeddedId
    private OverdueDebtSummaryKey id;

    @Column(name = "序号", nullable = false,length = 30)
    private int sequence;

    @Column(name = "管理公司", nullable = false, length = 30)
    private String managementCompany;

    @Column(name = "债权风险类型", length = 30)
    private String debtRiskType;

    @Column(name = "客户情况分类", length = 30)
    private String customerCategory;

    @Column(name = "数据更新年月", length = 30)
    private String dataUpdateYearMonth;

    @Column(name = "2022年4月30日余额", nullable = false)
    private BigDecimal amount20220430;

    @Column(name = "年初逾期债权余额")
    private BigDecimal yearInitialDebtBalance;

    @Column(name = "本年增加债权金额")
    private BigDecimal annualIncreaseDebtAmount;

    @Column(name = "本年减少债权金额")
    private BigDecimal annualReductionBalance;

    @Column(name = "本年期末债权余额")
    private BigDecimal debtBalance;

    @Column(name = "减值初始计提日")
    private Date initialImpairmentDate;

    @Column(name = "年初减值准备金额")
    private BigDecimal yearInitialImpairmentAmount;

    @Column(name = "本年减值准备变化金额")
    private BigDecimal annualImpairmentChange;

    @Column(name = "是否全额计提坏账", length = 30)
    private String isAllBadDebt;

    @Column(name = "备注", length = 30)
    private String remark;

    // 委托方法，将字段访问委托给内部的复合主键类
    public String getCreditor() {
        return id != null ? id.getCreditor() : null;
    }

    public void setCreditor(String creditor) {
        if (id == null) {
            id = new OverdueDebtSummaryKey();
        }
        id.setCreditor(creditor);
    }

    public String getDebtor() {
        return id != null ? id.getDebtor() : null;
    }

    public void setDebtor(String debtor) {
        if (id == null) {
            id = new OverdueDebtSummaryKey();
        }
        id.setDebtor(debtor);
    }

    public String getPeriod() {
        return id != null ? id.getPeriod() : null;
    }

    public void setPeriod(String period) {
        if (id == null) {
            id = new OverdueDebtSummaryKey();
        }
        id.setPeriod(period);
    }

    public Integer getYear() {
        return id != null ? id.getYear() : null;
    }

    public void setYear(Integer year) {
        if (id == null) {
            id = new OverdueDebtSummaryKey();
        }
        id.setYear(year);
    }

    public String getIsLitigation() {
        return id != null ? id.getIsLitigation() : null;
    }

    public void setIsLitigation(String isLitigation) {
        if (id == null) {
            id = new OverdueDebtSummaryKey();
        }
        id.setIsLitigation(isLitigation);
    }

    /**
     * 汇总表专用的复合主键类
     * <p>
     * 该类封装“汇总表”的联合主键字段，
     * 包括债权人、债务人、期间和是否涉诉等信息。
     * </p>
     */
    @Getter
    @Setter
    @EqualsAndHashCode
    @Embeddable
    public static class OverdueDebtSummaryKey implements Serializable {

        private static final long serialVersionUID = 1L;

        @Column(name = "债权人")
        private String creditor;

        @Column(name = "债务人")
        private String debtor;

        @Column(name = "期间")
        private String period;

        @Column(name = "是否涉诉")
        private String isLitigation;

        @Column(name = "年份")
        private Integer year;

        // 默认无参构造函数
        public OverdueDebtSummaryKey() {}

        // 带参数的构造函数，用于创建复合键实例
        public OverdueDebtSummaryKey(String creditor, String debtor, String period, String isLitigation) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            // 尝试从期间中提取年份
            if (period != null && period.length() >= 4) {
                try {
                    this.year = Integer.parseInt(period.substring(0, 4));
                } catch (NumberFormatException e) {
                    this.year = 0;
                }
            }
        }

        // 带年份的构造函数
        public OverdueDebtSummaryKey(String creditor, String debtor, String period, String isLitigation, Integer year) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.period = period;
            this.isLitigation = isLitigation;
            this.year = year;
        }
    }
}
