package com.laoshu198838.entity.overdue_debt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * 减值准备表实体类
 * 对应数据库中的减值准备表
 * <AUTHOR>
 */
// @记住，这个是减值准备表的实体类
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Entity
@Table(name = "减值准备表")
public class ImpairmentReserve {

    @EmbeddedId
    private ImpairmentReserveKey id;

    @Column(name = "序号", nullable = false, insertable = false, updatable = false)
    private int sequence;

    @Column(name = "案件名称",length = 100)
    private String caseName;

    @Column(name = "科目名称", length = 50)
    private String subjectName;

    @Column(name = "2022年4月30日债权金额")
    private BigDecimal amount20220430;

    @Column(name = "本月初债权余额")
    private BigDecimal lastMonthBalance;

    @Column(name = "本月末债权余额")
    private BigDecimal currentMonthBalance;

    @Column(name = "计提减值金额")
    private BigDecimal impairmentAmount;

    @Column(name = "初始计提日期")
    private Date initialImpairmentDate;

    @Column(name = "上月末余额")
    private BigDecimal previousMonthBalance;

    @Column(name = "本月增减")
    private BigDecimal currentMonthIncreaseDecrease;

    @Column(name = "本月末余额")
    private BigDecimal currentMonthAmount;

    @Column(name = "本年度回收目标")
    private BigDecimal annualRecoveryTarget;

    @Column(name = "本年度累计回收")
    private BigDecimal annualCumulativeRecovery;

    @Column(name = "备注", length = 190)
    private String remark;

    @Column(name = "管理公司",length = 50)
    private String managementCompany;

    @Column(name = "是否全额计提坏账", length = 50)
    private String isAllImpaired;

    @Column(name = "到期时间")
    private Date dueDate;
    
    @Column(name = "更新时间")
    private LocalDateTime updateTime;

    @Column(name = "本月新增债权")
    private BigDecimal currentMonthNewDebt;

    @Column(name = "本月处置债权")
    private BigDecimal currentMonthDisposeDebt;

    @Column(name = "创建时间")
    private LocalDateTime createTime;

    @Column(name = "债权性质", length = 30)
    private String debtNature;
    
    @Column(name = "债权类型", length = 30)
    private String debtType;
    
    @PrePersist
    public void prePersist() {
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        this.updateTime = LocalDateTime.now();
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 获取上月末金额
     * 用于业务逻辑计算
     */
    public BigDecimal getLastMonthAmount() {
        return this.previousMonthBalance;
    }

    /**
     * 设置上月末金额
     * 用于业务逻辑计算
     */
    public void setLastMonthAmount(BigDecimal lastMonthAmount) {
        this.previousMonthBalance = lastMonthAmount;
    }

    /**
     * 静态内部类：减值准备复合主键
     * <p>
     * 该类封装“减值准备表”的联合主键字段，
     * 包括债权人、债务人、年份、月份、是否涉诉以及期间等信息。
     * </p>
     */
    @Embeddable
    @Getter
    @Setter
    public static class ImpairmentReserveKey implements Serializable {

        @Column(name = "债权人", length = 50)
        private String creditor;

        @Column(name = "债务人", length = 50)
        private String debtor;

        @Column(name = "年份")
        private int year;

        @Column(name = "月份")
        private int month;

        @Column(name = "是否涉诉", length = 10)
        private String isLitigation;

        @Column(name = "期间", length = 50)
        private String period;

        /**
         * 无参构造函数，供 JPA 使用
         */
        public ImpairmentReserveKey() {}

        /**
         * 带参构造函数，初始化主要字段
         *
         * @param creditor     债权人
         * @param debtor       债务人
         * @param year         年份
         * @param month        月份
         * @param isLitigation 是否涉诉
         */
        public ImpairmentReserveKey(String creditor, String debtor, int year, int month, String isLitigation) {
            this.creditor = creditor;
            this.debtor = debtor;
            this.year = year;
            this.month = month;
            this.isLitigation = isLitigation;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ImpairmentReserveKey that = (ImpairmentReserveKey) o;
            return year == that.year &&
                   month == that.month &&
                   Objects.equals(creditor, that.creditor) &&
                   Objects.equals(debtor, that.debtor) &&
                   Objects.equals(isLitigation, that.isLitigation);
        }

        @Override
        public int hashCode() {
            return Objects.hash(creditor, debtor, year, month, isLitigation);
        }
    }

}
