package com.laoshu198838.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

/**
 * 检查配置类
 * 负责加载和解析checks.yaml配置文件
 * 
 * <AUTHOR>
 */
@Configuration
public class ChecksConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(ChecksConfiguration.class);
    
    /**
     * 加载检查配置
     * 
     * @return 检查配置对象
     */
    @Bean
    public ChecksConfig checksConfig() {
        try {
            ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
            ClassPathResource resource = new ClassPathResource("checks.yaml");
            try (InputStream inputStream = resource.getInputStream()) {
                return mapper.readValue(inputStream, ChecksConfig.class);
            }
        } catch (IOException e) {
            logger.error("无法加载checks.yaml配置文件", e);
            // 返回一个空的配置对象，避免空指针异常
            return new ChecksConfig();
        }
    }
    
    /**
     * 检查配置类
     * 对应checks.yaml文件的结构
     */
    public static class ChecksConfig {
        private List<CheckItem> checks;
        
        public List<CheckItem> getChecks() {
            return checks;
        }
        
        public void setChecks(List<CheckItem> checks) {
            this.checks = checks;
        }
    }
    
    /**
     * 检查项配置
     * 对应checks.yaml中的每个检查项
     */
    public static class CheckItem {
        private String key;
        private String label;
        private Map<String, String> tables;
        
        public String getKey() {
            return key;
        }
        
        public void setKey(String key) {
            this.key = key;
        }
        
        public String getLabel() {
            return label;
        }
        
        public void setLabel(String label) {
            this.label = label;
        }
        
        public Map<String, String> getTables() {
            return tables;
        }
        
        public void setTables(Map<String, String> tables) {
            this.tables = tables;
        }
    }
}
