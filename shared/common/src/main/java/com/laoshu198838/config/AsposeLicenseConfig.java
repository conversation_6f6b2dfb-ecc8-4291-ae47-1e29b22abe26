package com.laoshu198838.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.io.InputStream;

/**
 * Aspose 许可证配置类
 * 
 * <p>负责在应用启动时初始化 Aspose 相关组件的许可证，避免评估版水印。</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class AsposeLicenseConfig {

    private static final Logger logger = LoggerFactory.getLogger(AsposeLicenseConfig.class);
    
    // 许可证文件路径
    private static final String LICENSE_PATH = "license.xml";
    
    // 静态标记，确保许可证只初始化一次
    private static volatile boolean licenseInitialized = false;
    
    /**
     * Spring 启动后初始化许可证
     */
    @PostConstruct
    public void initializeLicense() {
        if (!licenseInitialized) {
            synchronized (AsposeLicenseConfig.class) {
                if (!licenseInitialized) {
                    loadAsposeLicense();
                    licenseInitialized = true;
                }
            }
        }
    }
    
    /**
     * 静态方法，确保许可证被加载（用于非Spring环境）
     */
    public static void ensureLicenseLoaded() {
        if (!licenseInitialized) {
            synchronized (AsposeLicenseConfig.class) {
                if (!licenseInitialized) {
                    new AsposeLicenseConfig().loadAsposeLicense();
                    licenseInitialized = true;
                }
            }
        }
    }
    
    /**
     * 加载 Aspose 许可证
     */
    private void loadAsposeLicense() {
        try {
            logger.info("开始初始化 Aspose 许可证...");
            
            // 初始化 Aspose.Cells 许可证
            initializeCellsLicense();
            
            // 初始化 Aspose.Words 许可证
            initializeWordsLicense();
            
            // 初始化 Aspose.Slides 许可证  
            initializeSlidesLicense();
            
            logger.info("Aspose 许可证初始化完成");
            
        } catch (Exception e) {
            logger.warn("Aspose 许可证初始化失败，将使用评估模式: {}", e.getMessage());
        }
    }
    
    /**
     * 初始化 Aspose.Cells 许可证
     */
    private void initializeCellsLicense() {
        try {
            com.aspose.cells.License cellsLicense = new com.aspose.cells.License();
            InputStream licenseStream = getClass().getClassLoader().getResourceAsStream(LICENSE_PATH);
            
            if (licenseStream != null) {
                cellsLicense.setLicense(licenseStream);
                logger.info("Aspose.Cells 许可证加载成功");
                licenseStream.close();
            } else {
                // 尝试从外部文件路径加载
                cellsLicense.setLicense("config/license.xml");
                logger.info("Aspose.Cells 许可证从外部路径加载成功");
            }
        } catch (Exception e) {
            logger.warn("Aspose.Cells 许可证加载失败: {}", e.getMessage());
        }
    }
    
    /**
     * 初始化 Aspose.Words 许可证
     */
    private void initializeWordsLicense() {
        try {
            com.aspose.words.License wordsLicense = new com.aspose.words.License();
            InputStream licenseStream = getClass().getClassLoader().getResourceAsStream(LICENSE_PATH);
            
            if (licenseStream != null) {
                wordsLicense.setLicense(licenseStream);
                logger.info("Aspose.Words 许可证加载成功");
                licenseStream.close();
            } else {
                // 尝试从外部文件路径加载
                wordsLicense.setLicense("config/license.xml");
                logger.info("Aspose.Words 许可证从外部路径加载成功");
            }
        } catch (Exception e) {
            logger.warn("Aspose.Words 许可证加载失败: {}", e.getMessage());
        }
    }
    
    /**
     * 初始化 Aspose.Slides 许可证
     */
    private void initializeSlidesLicense() {
        try {
            com.aspose.slides.License slidesLicense = new com.aspose.slides.License();
            InputStream licenseStream = getClass().getClassLoader().getResourceAsStream(LICENSE_PATH);
            
            if (licenseStream != null) {
                slidesLicense.setLicense(licenseStream);
                logger.info("Aspose.Slides 许可证加载成功");
                licenseStream.close();
            } else {
                // 尝试从外部文件路径加载
                slidesLicense.setLicense("config/license.xml");
                logger.info("Aspose.Slides 许可证从外部路径加载成功");
            }
        } catch (Exception e) {
            logger.warn("Aspose.Slides 许可证加载失败: {}", e.getMessage());
        }
    }
}