package com.laoshu198838.model.datamonitor.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 新增表数据一致性检查结果DTO
 * 用于返回新增表数据一致性检查的结果
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class AddTableConsistencyResult {
    
    // 检查结果：月度数据(1-12月)求和是否等于新增金额列求和
    private boolean monthlySumEqualsTotal;
    
    // 检查结果：处置金额是否等于现金处置+分期还款+资产抵债+其他方式求和
    private boolean disposalAmountEquals;
    
    // 检查结果：新增金额-处置金额是否等于债权余额
    private boolean balanceEquals;
    
    // 汇总数据用于前端展示对比
    private SummaryData summaryData = new SummaryData();
    
    // 不一致的行数据明细
    private List<InconsistentAddRow> inconsistentRows = new ArrayList<>();
    
    /**
     * 汇总数据
     */
    @Getter
    @Setter
    public static class SummaryData {
        // 月度求和
        private BigDecimal monthlySum = BigDecimal.ZERO;
        
        // 新增金额
        private BigDecimal addAmount = BigDecimal.ZERO;
        
        // 处置金额
        private BigDecimal disposalAmount = BigDecimal.ZERO;
        
        // 处置明细求和
        private BigDecimal disposalDetailSum = BigDecimal.ZERO;
        
        // 计算的余额(新增金额-处置金额)
        private BigDecimal calculatedBalance = BigDecimal.ZERO;
        
        // 实际余额
        private BigDecimal actualBalance = BigDecimal.ZERO;
    }
    
    /**
     * 不一致的新增表行数据
     */
    @Getter
    @Setter
    public static class InconsistentAddRow {
        // 债务人
        private String debtor;
        
        // 管理公司
        private String managementCompany;
        
        // 月度数据求和
        private BigDecimal monthlySum;
        
        // 新增金额
        private BigDecimal addAmount;
        
        // 处置金额
        private BigDecimal disposalAmount;
        
        // 处置明细求和（现金处置+分期还款+资产抵债+其他方式）
        private BigDecimal disposalDetailSum;
        
        // 新增金额-处置金额
        private BigDecimal addMinusDisposal;
        
        // 债权余额
        private BigDecimal balance;
        
        // 不一致类型
        private String inconsistencyType;
    }
} 