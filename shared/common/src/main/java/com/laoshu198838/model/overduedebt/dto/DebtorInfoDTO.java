package com.laoshu198838.model.overduedebt.dto;

import java.io.Serializable;

/**
 * 债务人信息DTO - 用于查询债务人处置信息接口
 * <AUTHOR>
 */
public class DebtorInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 基本信息
    private String creditor;            // 债权人
    private String debtor;              // 债务人
    private String managementCompany;   // 管理公司
    private String period;              // 期间
    private String isLitigation;        // 是否涉诉
    private String yearMonth;           // 年月，格式为"YYYY-MM"

    // 金额信息，使用double而不是BigDecimal，避免序列化问题
    private double debtAmount;          // 债权金额
    private double cashDisposal;        // 现金处置
    private double installmentRepayment; // 分期还款
    private double assetDebt;           // 资产抵债
    private double otherWays;           // 其他方式
    private double reductionAmount;     // 减值金额

    // 减值准备表特有字段
    private double impairmentAmount;     // 计提减值金额
    private double currentMonthNewDebt;  // 本月新增债权
    private double currentMonthDisposeDebt; // 本月处置债权

    // 无参构造函数
    public DebtorInfoDTO() {
        // 默认值初始化
        this.creditor = "";
        this.debtor = "";
        this.managementCompany = "";
        this.period = "";
        this.isLitigation = "否";
        this.yearMonth = "";
        this.debtAmount = 0.0;
        this.cashDisposal = 0.0;
        this.installmentRepayment = 0.0;
        this.assetDebt = 0.0;
        this.otherWays = 0.0;
        this.reductionAmount = 0.0;
        this.impairmentAmount = 0.0;
        this.currentMonthNewDebt = 0.0;
        this.currentMonthDisposeDebt = 0.0;
    }

    // Getters 和 Setters
    public String getCreditor() {
        return creditor;
    }

    public void setCreditor(String creditor) {
        this.creditor = creditor;
    }

    public String getDebtor() {
        return debtor;
    }

    public void setDebtor(String debtor) {
        this.debtor = debtor;
    }

    public String getManagementCompany() {
        return managementCompany;
    }

    public void setManagementCompany(String managementCompany) {
        this.managementCompany = managementCompany;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getIsLitigation() {
        return isLitigation;
    }

    public void setIsLitigation(String isLitigation) {
        this.isLitigation = isLitigation;
    }

    public double getDebtAmount() {
        return debtAmount;
    }

    public void setDebtAmount(double debtAmount) {
        this.debtAmount = debtAmount;
    }

    public double getCashDisposal() {
        return cashDisposal;
    }

    public void setCashDisposal(double cashDisposal) {
        this.cashDisposal = cashDisposal;
    }

    public double getInstallmentRepayment() {
        return installmentRepayment;
    }

    public void setInstallmentRepayment(double installmentRepayment) {
        this.installmentRepayment = installmentRepayment;
    }

    public double getAssetDebt() {
        return assetDebt;
    }

    public void setAssetDebt(double assetDebt) {
        this.assetDebt = assetDebt;
    }

    public double getOtherWays() {
        return otherWays;
    }

    public void setOtherWays(double otherWays) {
        this.otherWays = otherWays;
    }

    public double getReductionAmount() {
        return reductionAmount;
    }

    public void setReductionAmount(double reductionAmount) {
        this.reductionAmount = reductionAmount;
    }

    public String getYearMonth() {
        return yearMonth;
    }

    public void setYearMonth(String yearMonth) {
        this.yearMonth = yearMonth;
    }

    public double getImpairmentAmount() {
        return impairmentAmount;
    }

    public void setImpairmentAmount(double impairmentAmount) {
        this.impairmentAmount = impairmentAmount;
    }

    public double getCurrentMonthNewDebt() {
        return currentMonthNewDebt;
    }

    public void setCurrentMonthNewDebt(double currentMonthNewDebt) {
        this.currentMonthNewDebt = currentMonthNewDebt;
    }

    public double getCurrentMonthDisposeDebt() {
        return currentMonthDisposeDebt;
    }

    public void setCurrentMonthDisposeDebt(double currentMonthDisposeDebt) {
        this.currentMonthDisposeDebt = currentMonthDisposeDebt;
    }
}
