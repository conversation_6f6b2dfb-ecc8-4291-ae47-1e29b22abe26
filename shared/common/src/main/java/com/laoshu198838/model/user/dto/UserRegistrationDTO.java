package com.laoshu198838.model.user.dto;

import lombok.*;

/**
 * 用户注册数据传输对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class UserRegistrationDTO {
    // 必填字段
    private String username;
    private String password;
    private String name;
    private String company;
    private String department;
    private String passwordConfirm;
    
    // 可选字段
    private String email;
    private String phone;
    private String position;
    private String employeeId;
    private String remarks;
}
