package com.laoshu198838.model.user.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 用户数据传输对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
public class UserDTO {
    // 基础必填字段
    private Long id;
    private String username;
    private String name;
    private String company;
    private String department;
    private String status;
    private String role;
    private Integer roleId;
    
    // 新增可选字段
    private String email;
    private String phone;
    private String position;
    private String employeeId;
    private String remarks;
    private LocalDateTime lastLoginTime;
    
    // 时间字段
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}