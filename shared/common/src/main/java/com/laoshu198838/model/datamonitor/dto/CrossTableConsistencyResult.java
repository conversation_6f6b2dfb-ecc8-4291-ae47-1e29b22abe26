package com.laoshu198838.model.datamonitor.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 跨表一致性检查结果DTO
 * 用于展示不同表之间的金额数据一致性检查
 * 
 * <AUTHOR>
 */
public class CrossTableConsistencyResult {
    
    private boolean isConsistent;
    private List<CheckItemResult> checkResults;
    
    public CrossTableConsistencyResult() {
        this.isConsistent = true;
        this.checkResults = new ArrayList<>();
    }
    
    public boolean isConsistent() {
        return isConsistent;
    }
    
    public void setConsistent(boolean isConsistent) {
        this.isConsistent = isConsistent;
    }
    
    public List<CheckItemResult> getCheckResults() {
        return checkResults;
    }
    
    public void setCheckResults(List<CheckItemResult> checkResults) {
        this.checkResults = checkResults;
    }
    
    /**
     * 检查项结果
     * 对应每个检查项的结果
     */
    public static class CheckItemResult {
        private String key;
        private String label;
        private boolean isConsistent;
        private Map<String, BigDecimal> tableTotals;
        private BigDecimal maxDifference;
        private List<InconsistentRow> inconsistentRows;
        
        public CheckItemResult() {
            this.isConsistent = true;
            this.tableTotals = new HashMap<>();
            this.maxDifference = BigDecimal.ZERO;
            this.inconsistentRows = new ArrayList<>();
        }
        
        public String getKey() {
            return key;
        }
        
        public void setKey(String key) {
            this.key = key;
        }
        
        public String getLabel() {
            return label;
        }
        
        public void setLabel(String label) {
            this.label = label;
        }
        
        public boolean isConsistent() {
            return isConsistent;
        }
        
        public void setConsistent(boolean isConsistent) {
            this.isConsistent = isConsistent;
        }
        
        public Map<String, BigDecimal> getTableTotals() {
            return tableTotals;
        }
        
        public void setTableTotals(Map<String, BigDecimal> tableTotals) {
            this.tableTotals = tableTotals;
        }
        
        public BigDecimal getMaxDifference() {
            return maxDifference;
        }
        
        public void setMaxDifference(BigDecimal maxDifference) {
            this.maxDifference = maxDifference;
        }
        
        public List<InconsistentRow> getInconsistentRows() {
            return inconsistentRows;
        }
        
        public void setInconsistentRows(List<InconsistentRow> inconsistentRows) {
            this.inconsistentRows = inconsistentRows;
        }
    }
    
    /**
     * 不一致行数据
     * 记录每个债务人/债权人的不一致数据
     */
    public static class InconsistentRow {
        private String debtorCreditor;
        private Map<String, BigDecimal> tableValues;
        private BigDecimal maxDifference;
        
        public InconsistentRow() {
            this.tableValues = new HashMap<>();
            this.maxDifference = BigDecimal.ZERO;
        }
        
        public String getDebtorCreditor() {
            return debtorCreditor;
        }
        
        public void setDebtorCreditor(String debtorCreditor) {
            this.debtorCreditor = debtorCreditor;
        }
        
        public Map<String, BigDecimal> getTableValues() {
            return tableValues;
        }
        
        public void setTableValues(Map<String, BigDecimal> tableValues) {
            this.tableValues = tableValues;
        }
        
        public BigDecimal getMaxDifference() {
            return maxDifference;
        }
        
        public void setMaxDifference(BigDecimal maxDifference) {
            this.maxDifference = maxDifference;
        }
    }
}
