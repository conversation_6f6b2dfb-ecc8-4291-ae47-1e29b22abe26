package com.laoshu198838.exception;

/**
 * 处理数据异常
 * <AUTHOR>
 */
public class DataProcessingException extends RuntimeException {
    
    /**
     * 默认构造函数
     */
    public DataProcessingException() {
        super();
    }
    
    /**
     * 带消息的构造函数
     * 
     * @param message 错误信息
     */
    public DataProcessingException(String message) {
        super(message);
    }
    
    /**
     * 带消息和原因的构造函数
     * 
     * @param message 错误信息
     * @param cause 原始异常
     */
    public DataProcessingException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 带原因的构造函数
     * 
     * @param cause 原始异常
     */
    public DataProcessingException(Throwable cause) {
        super(cause);
    }
}
