package com.laoshu198838.aspect;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.config.DynamicDataSourceContextHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 数据源切换AOP切面
 * 根据@DataSource注解动态切换数据源
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@Order(1) // 确保在事务注解之前执行
public class DataSourceAspect {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceAspect.class);

    /**
     * 切点：所有标注了@DataSource注解的方法或类
     */
    @Pointcut("@annotation(com.laoshu198838.annotation.DataSource) || @within(com.laoshu198838.annotation.DataSource)")
    public void dataSourcePointcut() {
    }

    /**
     * 环绕通知：在方法执行前后切换数据源
     */
    @Around("dataSourcePointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = point.getTarget().getClass();

        // 优先检查方法级别的@DataSource注解
        DataSource dataSource = method.getAnnotation(DataSource.class);

        // 如果方法上没有，检查类级别的@DataSource注解
        if (dataSource == null) {
            dataSource = targetClass.getAnnotation(DataSource.class);
        }

        // 如果目标类是代理类，检查其接口上的@DataSource注解
        if (dataSource == null) {
            Class<?>[] interfaces = targetClass.getInterfaces();
            for (Class<?> intf : interfaces) {
                dataSource = intf.getAnnotation(DataSource.class);
                if (dataSource != null) {
                    break;
                }
            }
        }

        if (dataSource != null) {
            String dsKey = dataSource.value();
            DynamicDataSourceContextHolder.setDataSourceKey(dsKey);
            logger.info("切换到数据源: {} (方法: {}.{})", dsKey, targetClass.getSimpleName(), method.getName());
        }

        try {
            return point.proceed();
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceKey();
            logger.debug("清除数据源上下文");
        }
    }
}
