package com.laoshu198838.repository.user_system;

import com.laoshu198838.entity.user_system.UserCompanyPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户公司权限Repository
 * 
 * <AUTHOR>
 */
@Repository
public interface UserCompanyPermissionRepository extends JpaRepository<UserCompanyPermission, Long> {

    /**
     * 查找用户的所有有效权限
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN FETCH ucp.company c " +
           "WHERE ucp.user.id = :userId " +
           "AND ucp.status = 'active' " +
           "AND c.status = 'active' " +
           "AND (ucp.expiresAt IS NULL OR ucp.expiresAt > :now) " +
           "ORDER BY ucp.isDefault DESC, c.companyName")
    List<UserCompanyPermission> findValidPermissionsByUserId(@Param("userId") Long userId, @Param("now") LocalDateTime now);

    /**
     * 查找用户的所有权限（包括无效的）
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN FETCH ucp.company c " +
           "WHERE ucp.user.id = :userId " +
           "ORDER BY ucp.isDefault DESC, c.companyName")
    List<UserCompanyPermission> findAllPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否有访问指定公司的权限
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN ucp.company c " +
           "WHERE ucp.user.id = :userId " +
           "AND c.companyName = :companyName " +
           "AND ucp.status = 'active' " +
           "AND c.status = 'active' " +
           "AND (ucp.expiresAt IS NULL OR ucp.expiresAt > :now)")
    Optional<UserCompanyPermission> findValidPermissionByUserIdAndCompanyName(
            @Param("userId") Long userId, 
            @Param("companyName") String companyName, 
            @Param("now") LocalDateTime now);

    /**
     * 查找用户对特定公司的权限（不考虑有效性）
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN ucp.company c " +
           "WHERE ucp.user.id = :userId " +
           "AND c.id = :companyId")
    Optional<UserCompanyPermission> findByUserIdAndCompanyId(@Param("userId") Long userId, @Param("companyId") Long companyId);

    /**
     * 查找公司的所有权限授权记录
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN FETCH ucp.user u " +
           "JOIN FETCH ucp.company c " +
           "WHERE c.id = :companyId " +
           "ORDER BY ucp.createdAt DESC")
    List<UserCompanyPermission> findPermissionsByCompanyId(@Param("companyId") Long companyId);

    /**
     * 查找由特定用户授权的权限记录
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN FETCH ucp.user u " +
           "JOIN FETCH ucp.company c " +
           "WHERE ucp.grantedBy.id = :granterId " +
           "ORDER BY ucp.createdAt DESC")
    List<UserCompanyPermission> findPermissionsByGranterId(@Param("granterId") Long granterId);

    /**
     * 撤销用户对指定公司的权限
     */
    @Modifying
    @Query("UPDATE UserCompanyPermission ucp " +
           "SET ucp.status = 'suspended', ucp.updatedAt = :now " +
           "WHERE ucp.user.id = :userId " +
           "AND ucp.company.id = :companyId " +
           "AND ucp.status = 'active'")
    int revokePermission(@Param("userId") Long userId, @Param("companyId") Long companyId, @Param("now") LocalDateTime now);

    /**
     * 激活用户对指定公司的权限
     */
    @Modifying
    @Query("UPDATE UserCompanyPermission ucp " +
           "SET ucp.status = 'active', ucp.updatedAt = :now " +
           "WHERE ucp.user.id = :userId " +
           "AND ucp.company.id = :companyId " +
           "AND ucp.status = 'suspended'")
    int activatePermission(@Param("userId") Long userId, @Param("companyId") Long companyId, @Param("now") LocalDateTime now);

    /**
     * 删除用户的所有权限（用户删除时）
     */
    @Modifying
    @Query("DELETE FROM UserCompanyPermission ucp WHERE ucp.user.id = :userId")
    int deleteAllPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 查找即将过期的权限
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN FETCH ucp.user u " +
           "JOIN FETCH ucp.company c " +
           "WHERE ucp.expiresAt IS NOT NULL " +
           "AND ucp.expiresAt BETWEEN :now AND :expiryThreshold " +
           "AND ucp.status = 'active'")
    List<UserCompanyPermission> findExpiringPermissions(@Param("now") LocalDateTime now, @Param("expiryThreshold") LocalDateTime expiryThreshold);

    /**
     * 自动过期权限
     */
    @Modifying
    @Query("UPDATE UserCompanyPermission ucp " +
           "SET ucp.status = 'expired', ucp.updatedAt = :now " +
           "WHERE ucp.expiresAt < :now " +
           "AND ucp.status = 'active'")
    int expirePermissions(@Param("now") LocalDateTime now);

    /**
     * 统计用户权限数量
     */
    @Query("SELECT COUNT(ucp) FROM UserCompanyPermission ucp " +
           "WHERE ucp.user.id = :userId " +
           "AND ucp.status = 'active' " +
           "AND (ucp.expiresAt IS NULL OR ucp.expiresAt > :now)")
    long countValidPermissionsByUserId(@Param("userId") Long userId, @Param("now") LocalDateTime now);

    /**
     * 查找公司的所有活跃用户权限
     */
    @Query("SELECT ucp FROM UserCompanyPermission ucp " +
           "JOIN FETCH ucp.user u " +
           "WHERE ucp.company.id = :companyId " +
           "AND ucp.status = 'active' " +
           "AND u.status = 'ACTIVE' " +
           "AND (ucp.expiresAt IS NULL OR ucp.expiresAt > :now)")
    List<UserCompanyPermission> findActiveUserPermissionsByCompanyId(@Param("companyId") Long companyId, @Param("now") LocalDateTime now);
}