package com.laoshu198838.repository.user_system;

import com.laoshu198838.entity.user_system.Company;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 公司信息Repository
 * 
 * <AUTHOR>
 */
@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {

    /**
     * 根据公司名称查找公司
     */
    Optional<Company> findByCompanyName(String companyName);

    /**
     * 根据公司代码查找公司
     */
    Optional<Company> findByCompanyCode(String companyCode);

    /**
     * 查找所有活跃的公司
     */
    @Query("SELECT c FROM UserSystemCompany c WHERE c.status = 'active' ORDER BY c.isManagementCompany DESC, c.companyName")
    List<Company> findAllActiveCompanies();

    /**
     * 查找管理公司（如万润科技）
     */
    @Query("SELECT c FROM UserSystemCompany c WHERE c.isManagementCompany = true AND c.status = 'active'")
    List<Company> findManagementCompanies();

    /**
     * 根据公司名称模糊查询
     */
    @Query("SELECT c FROM UserSystemCompany c WHERE c.companyName LIKE %:keyword% AND c.status = 'active'")
    List<Company> findByCompanyNameContaining(@Param("keyword") String keyword);

    /**
     * 检查公司名称是否已存在
     */
    boolean existsByCompanyName(String companyName);

    /**
     * 检查公司代码是否已存在
     */
    boolean existsByCompanyCode(String companyCode);
}