package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.model.overduedebt.dto.query.DebtDecreaseDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 债权减值查询数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface DebtDecreaseQueryRepository
        extends JpaRepository<ImpairmentReserve, ImpairmentReserve.ImpairmentReserveKey> {

    /**
     * 根据年份查询减值准备与处置表拼接数据，按期间降序、债权人、债务人排序
     *
     * @param year 查询年份，格式与实体id.year一致
     * @return 债权减值明细列表
     */
    @Query("""
               SELECT new com.laoshu198838.model.overduedebt.dto.query.DebtDecreaseDTO(
                   i.id.creditor,
                   i.id.debtor,
                   i.managementCompany,
                   i.id.period,
                   i.id.month,
                   i.id.isLitigation,
                   i.currentMonthDisposeDebt,
                   d.cashDisposal,
                   d.installmentRepayment,
                   d.assetDebt,
                   d.otherWays
               )
               FROM ImpairmentReserve i
               JOIN OverdueDebtDecrease d ON
                   i.id.creditor      = d.id.creditor      AND
                   i.id.debtor        = d.id.debtor        AND
                   i.id.year          = d.id.year          AND
                   i.id.month         = d.id.month         AND
                   i.id.period        = d.id.period        AND
                   i.id.isLitigation  = d.id.isLitigation
               WHERE i.id.year = :year
                 AND i.currentMonthDisposeDebt <> 0
               ORDER BY i.id.month ASC, i.currentMonthDisposeDebt DESC
           """)
    List<DebtDecreaseDTO> findDebtDecreaseByYear(@Param("year") int year);
}
