package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd.OverdueDebtAddKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 逾期债权新增表数据访问接口
 * 统一的Repository，替代各模块中的重复Repository
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface OverdueDebtAddRepository extends JpaRepository<OverdueDebtAdd, OverdueDebtAddKey> {

    /**
     * 根据债权人和债务人查询记录
     * @param creditor 债权人
     * @param debtor 债务人
     * @return 匹配的记录列表
     */
    @Query("SELECT o FROM OverdueDebtAdd o WHERE (o.id.creditor LIKE %:creditor% OR :creditor IS NULL) AND (o.id.debtor LIKE %:debtor% OR :debtor IS NULL) ORDER BY o.id.period DESC")
    List<OverdueDebtAdd> findByCreditorAndDebtor(@Param("creditor") String creditor, @Param("debtor") String debtor);

    /**
     * 通过复合条件查询记录
     * @param creditor 债权人
     * @param debtor 债务人
     * @param period 期间
     * @param isLitigation 是否诉讼
     * @param year 年份
     * @return 匹配的记录
     */
    @Query("SELECT o FROM OverdueDebtAdd o WHERE o.id.creditor = ?1 AND o.id.debtor = ?2 AND o.id.period = ?3 AND o.id.isLitigation = ?4 AND o.id.year = ?5")
    OverdueDebtAdd findByCreditorAndDebtorAndPeriodAndIsLitigationAndYear(
        String creditor,
        String debtor,
        String period,
        String isLitigation,
        String year
    );
}
