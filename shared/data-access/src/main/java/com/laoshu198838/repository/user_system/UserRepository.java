package com.laoshu198838.repository.user_system;

import com.laoshu198838.entity.user_system.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户Repository接口
 * 对应user_system数据库的users表
 * 
 * <AUTHOR>
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据用户名和状态查找用户
     */
    Optional<User> findByUsernameAndStatus(String username, String status);

    /**
     * 根据公司名称查找用户列表
     */
    List<User> findByCompanyname(String companyname);

    /**
     * 根据公司名称和状态查找用户列表
     */
    List<User> findByCompanynameAndStatus(String companyname, String status);

    /**
     * 根据部门查找用户列表
     */
    List<User> findByDepartment(String department);

    /**
     * 根据角色ID查找用户列表
     */
    @Query("SELECT u FROM UserSystemUser u WHERE u.role.roleId = :roleId")
    List<User> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 查找所有活跃用户
     */
    List<User> findByStatus(String status);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据工号查找用户
     */
    Optional<User> findByEmployeeId(String employeeId);

    /**
     * 检查用户名是否已存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否已存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查工号是否已存在
     */
    boolean existsByEmployeeId(String employeeId);

    /**
     * 根据用户名模糊查询
     */
    @Query("SELECT u FROM UserSystemUser u WHERE u.username LIKE %:keyword% OR u.name LIKE %:keyword%")
    List<User> findByUsernameOrNameContaining(@Param("keyword") String keyword);

    /**
     * 查找指定时间后登录的用户
     */
    @Query("SELECT u FROM UserSystemUser u WHERE u.lastLoginTime >= :time")
    List<User> findByLastLoginTimeAfter(@Param("time") LocalDateTime time);

    /**
     * 查找管理员用户（万润科技的管理员）
     */
    @Query("SELECT u FROM UserSystemUser u WHERE u.role.roleId = 1 AND u.companyname = '万润科技' AND u.status = 'ACTIVE'")
    List<User> findManagementAdmins();

    /**
     * 根据公司名称和角色查找用户
     */
    @Query("SELECT u FROM UserSystemUser u WHERE u.companyname = :companyname AND u.role.roleId = :roleId")
    List<User> findByCompanynameAndRoleId(@Param("companyname") String companyname, @Param("roleId") Long roleId);

    /**
     * 统计公司用户数量
     */
    @Query("SELECT COUNT(u) FROM UserSystemUser u WHERE u.companyname = :companyname AND u.status = 'ACTIVE'")
    long countActiveUsersByCompany(@Param("companyname") String companyname);

    /**
     * 查找需要激活的用户（状态为PENDING）
     */
    List<User> findByStatusOrderByCreatedAtDesc(String status);
}