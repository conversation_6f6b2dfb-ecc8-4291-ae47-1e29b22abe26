package com.laoshu198838.repository.overdue_debt;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.DebtTrackingRecord;
import com.laoshu198838.entity.overdue_debt.DebtTrackingRecord.DebtTrackingKey;

/**
 * 债务跟踪记录仓库接口
 * 提供对债务跟踪记录的数据库访问方法
 * 统一的Repository，替代各模块中的重复Repository
 *
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface DebtTrackingRecordRepository extends JpaRepository<DebtTrackingRecord, DebtTrackingKey> {

    /**
     * 根据债权人、债务人、期间和诉讼状态查询记录
     *
     * @param creditor     债权人
     * @param debtor       债务人
     * @param period       期间
     * @param isLitigation 是否诉讼
     * @return 找到的记录，如果不存在则返回null
     */
    DebtTrackingRecord findByCreditorAndDebtorAndPeriodAndIsLitigation(
            String creditor, String debtor, String period, String isLitigation);
}
