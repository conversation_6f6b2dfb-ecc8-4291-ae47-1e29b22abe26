package com.laoshu198838.config;

import java.sql.Connection;
import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

/**
 * 动态数据源路由类
 * 根据当前线程上下文选择对应的数据源
 * 确保所有数据源的autoCommit配置一致
 *
 * <AUTHOR>
 */
public class DynamicDataSource extends AbstractRoutingDataSource {

    private static final Logger logger = LoggerFactory.getLogger(DynamicDataSource.class);

    @Override
    protected Object determineCurrentLookupKey() {
        return DynamicDataSourceContextHolder.getDataSourceKey();
    }

    @Override
    public Connection getConnection() throws SQLException {
        Connection connection = super.getConnection();
        boolean originalAutoCommit = connection.getAutoCommit();
        logger.info("获取数据库连接 - 原始autoCommit状态: {}", originalAutoCommit);

        // 确保所有连接都禁用autoCommit
        if (originalAutoCommit) {
            connection.setAutoCommit(false);
            logger.warn("连接autoCommit状态已从true修改为false");
        }

        logger.info("返回连接 - 当前autoCommit状态: {}", connection.getAutoCommit());
        return connection;
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        Connection connection = super.getConnection(username, password);
        boolean originalAutoCommit = connection.getAutoCommit();
        logger.info("获取数据库连接(带用户名) - 原始autoCommit状态: {}", originalAutoCommit);

        // 确保所有连接都禁用autoCommit
        if (originalAutoCommit) {
            connection.setAutoCommit(false);
            logger.warn("连接autoCommit状态已从true修改为false");
        }

        logger.info("返回连接(带用户名) - 当前autoCommit状态: {}", connection.getAutoCommit());
        return connection;
    }
}
