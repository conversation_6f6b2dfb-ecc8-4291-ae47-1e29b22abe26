package com.laoshu198838.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Arrays;

/**
 * 缓存配置类
 * 配置系统缓存管理器和缓存策略
 * 
 * <AUTHOR>
 */
// 缓存配置已禁用 - 为了解决JCache依赖问题
// @Configuration
// @EnableCaching
public class CacheConfig {

    /**
     * 主缓存管理器
     * 使用ConcurrentMapCacheManager作为默认缓存实现
     *
     * @return CacheManager 缓存管理器实例
     */
    // @Bean
    // @Primary
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        // 预定义缓存名称
        cacheManager.setCacheNames(Arrays.asList(
            // 一致性检查相关缓存
            "yearNewAmountSummary",
            "disposedAmountSummary", 
            "endingBalanceSummary",
            "initialBalanceSummary",
            
            // 债权数据缓存
            "debtSummaryData",
            "litigationData",
            "nonLitigationData",
            "impairmentData",
            
            // 用户相关缓存
            "userProfiles",
            "userPermissions",
            
            // 报表数据缓存
            "reportData",
            "exportData",
            
            // 统计数据缓存
            "statisticsData",
            "monthlyStatistics",
            "yearlyStatistics"
        ));
        
        // 允许运行时创建缓存
        cacheManager.setAllowNullValues(false);
        
        return cacheManager;
    }
}
