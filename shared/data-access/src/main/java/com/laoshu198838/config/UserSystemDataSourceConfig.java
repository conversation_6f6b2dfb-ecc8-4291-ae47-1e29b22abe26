package com.laoshu198838.config;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariDataSource;

/**
 * 用户系统数据源配置
 *
 * 专门为user_system数据库提供数据源配置，支持JPA Repository和JdbcTemplate查询。
 * 独立的EntityManagerFactory避免与主数据源的实体类冲突。
 *
 * 主要用途：
 * - 为Company、User等user_system实体提供JPA Repository支持
 * - 为UserSystemDetailsService提供数据源
 * - 支持用户认证和授权功能
 * - 支持公司架构管理功能
 *
 * <AUTHOR>
 */
@Configuration
public class UserSystemDataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(UserSystemDataSourceConfig.class);

    public UserSystemDataSourceConfig() {
        logger.info("=== UserSystemDataSourceConfig 配置类已初始化 - 提供user_system数据库数据源 ===");
        logger.info("=== @EnableJpaRepositories 配置: basePackages=com.laoshu198838.repository.user_system ===");
    }

    /**
     * 用户系统数据源属性配置
     */
    @Bean("userSystemDataSourceProperties")
    @ConfigurationProperties("spring.datasource.user-system")
    public DataSourceProperties userSystemDataSourceProperties() {
        logger.info("=== 创建 userSystemDataSourceProperties Bean ===");
        return new DataSourceProperties();
    }

    /**
     * 用户系统数据源
     */
    @Bean("userSystemDataSource")
    public DataSource userSystemDataSource() {
        DataSourceProperties properties = userSystemDataSourceProperties();
        
        logger.info("=== 创建用户系统数据源，URL: {} ===", properties.getUrl());
        
        // 使用HikariDataSource以确保autoCommit=false配置生效
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(properties.getUrl());
        dataSource.setUsername(properties.getUsername());
        dataSource.setPassword(properties.getPassword());
        dataSource.setDriverClassName(properties.getDriverClassName());

        // 连接池配置
        dataSource.setMaximumPoolSize(10);
        dataSource.setMinimumIdle(2);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(600000);
        dataSource.setMaxLifetime(1800000);

        // 确保autoCommit=false
        dataSource.setAutoCommit(false);
        dataSource.setConnectionInitSql("SET autocommit=0");

        logger.info("=== 用户系统数据源创建完成 ===");
        return dataSource;
    }

    /**
     * 用户系统实体管理器工厂
     */
    @Bean("userSystemEntityManagerFactory")
    @DependsOn("userSystemDataSource")
    public LocalContainerEntityManagerFactoryBean userSystemEntityManagerFactory(
            @Qualifier("userSystemDataSource") DataSource userSystemDataSource) {
        
        logger.info("=== 创建用户系统EntityManagerFactory ===");
        
        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setDataSource(userSystemDataSource);
        factory.setPackagesToScan("com.laoshu198838.entity.user_system");
        factory.setPersistenceUnitName("userSystemPersistenceUnit");
        
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setGenerateDdl(false);
        vendorAdapter.setShowSql(false);
        factory.setJpaVendorAdapter(vendorAdapter);

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.show_sql", false);
        properties.put("hibernate.format_sql", false);
        properties.put("hibernate.physical_naming_strategy",
                      "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");

        // 性能优化配置
        properties.put("hibernate.jdbc.batch_size", "50");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.jdbc.batch_versioned_data", "true");

        // 禁用缓存
        properties.put("hibernate.cache.use_second_level_cache", "false");
        properties.put("hibernate.cache.use_query_cache", "false");

        // 事务配置
        properties.put("hibernate.connection.autocommit", "false");
        
        // 确保实体被正确扫描
        properties.put("hibernate.archive.autodetection", "class");

        factory.setJpaPropertyMap(properties);
        
        logger.info("=== 用户系统EntityManagerFactory配置完成，扫描包: com.laoshu198838.entity.user_system ===");
        
        // 不要在这里调用 afterPropertiesSet()，让 Spring 容器自动处理
        return factory;
    }

    /**
     * 用户系统事务管理器
     */
    @Bean("userSystemTransactionManager")
    public PlatformTransactionManager userSystemTransactionManager(
            @Qualifier("userSystemEntityManagerFactory") LocalContainerEntityManagerFactoryBean userSystemEntityManagerFactory) {
        
        logger.info("=== 创建用户系统事务管理器 ===");
        
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(userSystemEntityManagerFactory.getObject());
        logger.info("=== 用户系统事务管理器配置完成 ===");
        return transactionManager;
    }

}