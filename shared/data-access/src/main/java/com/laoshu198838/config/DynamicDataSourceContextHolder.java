package com.laoshu198838.config;

/**
 * 动态数据源上下文持有者
 * 使用ThreadLocal保存当前线程的数据源标识
 * 
 * <AUTHOR>
 */
public class DynamicDataSourceContextHolder {

    private static final ThreadLocal<String> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置数据源标识
     * 
     * @param dataSourceKey 数据源标识
     */
    public static void setDataSourceKey(String dataSourceKey) {
        CONTEXT_HOLDER.set(dataSourceKey);
    }

    /**
     * 获取数据源标识
     * 
     * @return 数据源标识
     */
    public static String getDataSourceKey() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 清除数据源标识
     */
    public static void clearDataSourceKey() {
        CONTEXT_HOLDER.remove();
    }
}
