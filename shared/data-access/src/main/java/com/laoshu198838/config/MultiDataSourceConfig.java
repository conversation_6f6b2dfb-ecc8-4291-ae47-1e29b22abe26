package com.laoshu198838.config;

import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import com.zaxxer.hikari.HikariDataSource;

/**
 * 多数据源配置类
 *
 * <p>本配置类实现了系统的多数据源支持，允许应用程序同时连接和操作多个数据库。
 * 主要用于支持主业务数据库（overdue_debt_db）和第三方系统集成数据库（金蝶数据库）。</p>
 *
 * <h3>支持的数据源：</h3>
 * <ul>
 *   <li><strong>主数据源 (Primary)</strong>: overdue_debt_db
 *     <ul>
 *       <li>存储核心业务数据：诉讼表、非诉讼表、减值准备表等</li>
 *       <li>用户权限数据：用户、角色、权限等</li>
 *       <li>系统配置数据：参数配置、日志记录等</li>
 *     </ul>
 *   </li>
 *   <li><strong>第二数据源 (Secondary)</strong>: 金蝶数据库
 *     <ul>
 *       <li>金蝶ERP系统数据集成</li>
 *       <li>财务报表数据同步</li>
 *       <li>第三方系统数据交换</li>
 *     </ul>
 *   </li>
 * </ul>
 *
 * <h3>技术实现：</h3>
 * <ul>
 *   <li>使用HikariCP连接池管理数据库连接</li>
 *   <li>支持动态数据源切换</li>
 *   <li>JPA实体管理器工厂配置</li>
 *   <li>事务管理器配置</li>
 *   <li>自动配置数据源属性</li>
 * </ul>
 *
 * <h3>配置说明：</h3>
 * <p>数据源配置通过application.yml文件进行，支持以下配置项：</p>
 * <ul>
 *   <li>spring.datasource.primary.* - 主数据源配置</li>
 *   <li>spring.datasource.secondary.* - 第二数据源配置</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 * @see DynamicDataSource
 * @see DynamicDataSourceContextHolder
 */
@Configuration
@EnableJpaRepositories(
    basePackages = {
        "com.laoshu198838.repository.overdue_debt"
    },
    entityManagerFactoryRef = "entityManagerFactory",
    transactionManagerRef = "transactionManager"
)
public class MultiDataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(MultiDataSourceConfig.class);

    public MultiDataSourceConfig() {
        logger.info("MultiDataSourceConfig 配置类已初始化");
    }

    /**
     * 主数据源属性配置
     *
     * <p>创建并配置主数据源的属性对象，从application.yml中读取spring.datasource.primary.*配置。
     * 主数据源用于存储系统的核心业务数据。</p>
     *
     * @return DataSourceProperties 主数据源属性配置对象
     * @see org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
     */
    @Primary
    @Bean("primaryDataSourceProperties")
    @ConfigurationProperties("spring.datasource.primary")
    public DataSourceProperties primaryDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * 主数据源（overdue_debt_db）
     *
     * <p>创建主数据源实例，连接到overdue_debt_db。这是系统的默认数据源，
     * 用于处理所有核心业务数据的CRUD操作。</p>
     *
     * <p>包含的主要数据表：</p>
     * <ul>
     *   <li>诉讼表 - 诉讼债权数据</li>
     *   <li>非诉讼表 - 非诉讼债权数据</li>
     *   <li>减值准备表 - 减值准备数据</li>
     *   <li>用户表 - 系统用户信息</li>
     *   <li>角色表 - 用户角色权限</li>
     * </ul>
     *
     * @return DataSource 主数据源实例
     * @see javax.sql.DataSource
     */
    @Primary
    @Bean("primaryDataSource")
    public DataSource primaryDataSource() {
        DataSourceProperties properties = primaryDataSourceProperties();

        // 手动创建HikariDataSource以确保autoCommit=false配置生效
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(properties.getUrl());
        dataSource.setUsername(properties.getUsername());
        dataSource.setPassword(properties.getPassword());
        dataSource.setDriverClassName(properties.getDriverClassName());

        // 连接池配置
        dataSource.setMaximumPoolSize(20);
        dataSource.setMinimumIdle(5);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(600000);
        dataSource.setMaxLifetime(1800000);

        // 关键：强制禁用自动提交
        dataSource.setAutoCommit(false);

        // 添加连接初始化SQL，确保每个连接都禁用自动提交
        dataSource.setConnectionInitSql("SET autocommit=0");

        return dataSource;
    }

    /**
     * 第二数据源属性配置（金蝶数据库）
     *
     * <p>创建并配置第二数据源的属性对象，从application.yml中读取spring.datasource.secondary.*配置。
     * 第二数据源主要用于与金蝶ERP系统的数据集成。</p>
     *
     * @return DataSourceProperties 第二数据源属性配置对象
     */
    @Bean("secondaryDataSourceProperties")
    @ConfigurationProperties("spring.datasource.secondary")
    public DataSourceProperties secondaryDataSourceProperties() {
        return new DataSourceProperties();
    }

    // 用户系统数据源配置已移至 UserSystemDataSourceConfig 类中
    // 避免重复定义导致的 bean 创建冲突

    /**
     * 第二数据源（金蝶数据库）
     *
     * <p>创建第二数据源实例，连接到金蝶数据库。主要用于：</p>
     * <ul>
     *   <li>金蝶ERP系统数据同步</li>
     *   <li>财务报表数据集成</li>
     *   <li>第三方系统数据交换</li>
     *   <li>外部数据源查询和分析</li>
     * </ul>
     *
     * <p>注意：此数据源为只读访问，主要用于数据查询和报表生成，
     * 不建议进行数据修改操作。</p>
     *
     * @return DataSource 第二数据源实例
     */
    @Bean("secondaryDataSource")
    public DataSource secondaryDataSource() {
        DataSourceProperties properties = secondaryDataSourceProperties();

        // 手动创建HikariDataSource以确保autoCommit=false配置生效
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(properties.getUrl());
        dataSource.setUsername(properties.getUsername());
        dataSource.setPassword(properties.getPassword());
        dataSource.setDriverClassName(properties.getDriverClassName());

        // 连接池配置
        dataSource.setMaximumPoolSize(10);
        dataSource.setMinimumIdle(2);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(600000);
        dataSource.setMaxLifetime(1800000);

        // 关键：强制禁用自动提交
        dataSource.setAutoCommit(false);

        // 添加连接初始化SQL，确保每个连接都禁用自动提交
        dataSource.setConnectionInitSql("SET autocommit=0");

        return dataSource;
    }

    // 用户系统数据源已移至 UserSystemDataSourceConfig 类中
    // 此处需要从 Spring 容器中获取已创建的 bean

    /**
     * 动态数据源
     * 支持主数据源（overdue_debt_db）、第二数据源（金蝶数据库）和用户系统数据源（user_system数据库）
     */
    @Bean("dynamicDataSource")
    public DataSource dynamicDataSource(
            @Qualifier("userSystemDataSource") DataSource userSystemDataSource) {
        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        Map<Object, Object> dataSourceMap = new HashMap<>();

        // 获取数据源实例并确保autoCommit=false
        DataSource primary = primaryDataSource();
        DataSource secondary = secondaryDataSource();

        dataSourceMap.put("primary", primary);
        dataSourceMap.put("secondary", secondary);
        dataSourceMap.put("userSystem", userSystemDataSource);
        dynamicDataSource.setTargetDataSources(dataSourceMap);
        dynamicDataSource.setDefaultTargetDataSource(primary);
        return dynamicDataSource;
    }

    /**
     * 实体管理器工厂
     */
    @Primary
    @Bean("entityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            @Qualifier("dynamicDataSource") DataSource dynamicDataSource) {
        logger.info("=== 创建EntityManagerFactory Bean ===");
        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();

        // 使用动态数据源，确保autoCommit配置生效
        logger.info("EntityManagerFactory使用的数据源类型: {}", dynamicDataSource.getClass().getName());
        factory.setDataSource(dynamicDataSource);
        factory.setPackagesToScan(
            "com.laoshu198838.entity.overdue_debt"
        );
        factory.setJpaVendorAdapter(new HibernateJpaVendorAdapter());

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.MySQLDialect");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.show_sql", false);
        properties.put("hibernate.format_sql", false);
        properties.put("hibernate.physical_naming_strategy",
                      "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");

        // 性能优化配置
        properties.put("hibernate.jdbc.batch_size", "50");
        properties.put("hibernate.order_inserts", "true");
        properties.put("hibernate.order_updates", "true");
        properties.put("hibernate.jdbc.batch_versioned_data", "true");

        // 完全禁用缓存以避免JCache依赖问题
        properties.put("hibernate.cache.use_second_level_cache", "false");
        properties.put("hibernate.cache.use_query_cache", "false");

        // 简化事务配置 - 让Spring完全管理事务
        properties.put("hibernate.connection.autocommit", "false");

        // 连接池优化
        properties.put("hibernate.query.plan_cache_max_size", "2048");
        properties.put("hibernate.query.plan_parameter_metadata_max_size", "128");

        factory.setJpaPropertyMap(properties);

        return factory;
    }

    /**
     * 事务管理器
     */
    @Primary
    @Bean("transactionManager")
    public PlatformTransactionManager transactionManager(LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(entityManagerFactory.getObject());
        return transactionManager;
    }


}
