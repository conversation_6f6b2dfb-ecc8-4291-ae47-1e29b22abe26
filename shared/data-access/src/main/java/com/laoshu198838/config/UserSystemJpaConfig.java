package com.laoshu198838.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 用户系统 JPA 配置
 * 
 * 单独配置 user_system 数据库的 JPA repositories，避免与主数据源配置冲突。
 * 
 * <AUTHOR>
 */
@Configuration
@EnableJpaRepositories(
    basePackages = "com.laoshu198838.repository.user_system",
    entityManagerFactoryRef = "userSystemEntityManagerFactory",
    transactionManagerRef = "userSystemTransactionManager"
)
public class UserSystemJpaConfig {
    // 此配置类仅用于启用 JPA repositories
    // 实际的 EntityManagerFactory 和 TransactionManager 在 UserSystemDataSourceConfig 中定义
}