package com.laoshu198838.template;

import com.laoshu198838.config.ReportConfig;
import com.laoshu198838.exception.ReportException;
import java.util.List;

/**
 * 报表导出器接口
 * 定义报表导出的标准操作
 * 
 * @param <T> 导出数据的类型
 * <AUTHOR>
 */
public interface ReportExporter<T> {
    
    /**
     * 导出报表
     * 
     * @param data 要导出的数据
     * @param config 报表配置
     * @return 生成的报表文件字节数组
     * @throws ReportException 导出异常
     */
    byte[] export(T data, ReportConfig config) throws ReportException;
    
    /**
     * 获取报表类型
     * 
     * @return 报表类型标识
     */
    String getReportType();
    
    /**
     * 获取支持的导出格式
     * 
     * @return 支持的格式列表
     */
    List<String> getSupportedFormats();
    
    /**
     * 验证数据是否符合导出要求
     * 
     * @param data 要验证的数据
     * @param config 报表配置
     * @return true表示数据有效，false表示无效
     */
    boolean validateData(T data, ReportConfig config);
    
    /**
     * 获取导出器描述信息
     * 
     * @return 描述信息
     */
    String getDescription();
}
