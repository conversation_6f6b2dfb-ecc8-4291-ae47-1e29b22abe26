package com.laoshu198838.template;

import com.laoshu198838.exception.ReportException;
import java.util.Map;

/**
 * 报表模板接口
 * 定义报表模板的基本操作
 * 
 * <AUTHOR>
 */
public interface ReportTemplate {
    
    /**
     * 加载模板文件
     * 
     * @param templatePath 模板文件路径
     * @throws ReportException 模板加载异常
     */
    void loadTemplate(String templatePath) throws ReportException;
    
    /**
     * 填充数据到模板
     * 
     * @param data 要填充的数据
     * @throws ReportException 数据填充异常
     */
    void fillData(Map<String, Object> data) throws ReportException;
    
    /**
     * 生成报表文件
     * 
     * @return 生成的报表文件字节数组
     * @throws ReportException 报表生成异常
     */
    byte[] generate() throws ReportException;
    
    /**
     * 获取模板类型
     * 
     * @return 模板类型（如：EXCEL, PDF, WORD等）
     */
    String getTemplateType();
    
    /**
     * 获取支持的文件扩展名
     * 
     * @return 支持的文件扩展名数组
     */
    String[] getSupportedExtensions();
    
    /**
     * 验证模板是否有效
     * 
     * @return true表示模板有效，false表示无效
     */
    boolean isValid();
    
    /**
     * 清理资源
     */
    void cleanup();
}
