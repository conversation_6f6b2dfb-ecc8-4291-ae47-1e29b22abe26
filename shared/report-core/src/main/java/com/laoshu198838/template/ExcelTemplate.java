package com.laoshu198838.template;

import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.aspose.cells.Cells;
import com.aspose.cells.SaveFormat;
import com.laoshu198838.exception.ReportException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

/**
 * Excel模板实现类
 * 提供Excel报表模板的具体实现
 * 
 * <AUTHOR>
 */
@Component
public class ExcelTemplate implements ReportTemplate {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelTemplate.class);
    
    private Workbook workbook;
    private String templatePath;
    private boolean isLoaded = false;
    
    @Override
    public void loadTemplate(String templatePath) throws ReportException {
        try {
            this.templatePath = templatePath;
            ClassPathResource templateResource = new ClassPathResource(templatePath);
            
            if (!templateResource.exists()) {
                throw new ReportException(
                    ReportException.ErrorCodes.TEMPLATE_NOT_FOUND,
                    "模板文件不存在: " + templatePath
                );
            }
            
            this.workbook = new Workbook(templateResource.getInputStream());
            this.isLoaded = true;
            
            logger.info("成功加载Excel模板: {}", templatePath);
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.TEMPLATE_LOAD_ERROR,
                "加载Excel模板失败: " + templatePath,
                e
            );
        }
    }
    
    @Override
    public void fillData(Map<String, Object> data) throws ReportException {
        if (!isLoaded) {
            throw new ReportException(
                ReportException.ErrorCodes.TEMPLATE_LOAD_ERROR,
                "模板未加载，请先调用loadTemplate方法"
            );
        }
        
        try {
            // 基础数据填充逻辑
            // 这里可以根据需要扩展更复杂的数据填充逻辑
            fillBasicData(data);
            
            logger.debug("成功填充数据到Excel模板");
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.DATA_PROCESSING_ERROR,
                "填充数据到Excel模板失败",
                e
            );
        }
    }
    
    @Override
    public byte[] generate() throws ReportException {
        if (!isLoaded) {
            throw new ReportException(
                ReportException.ErrorCodes.TEMPLATE_LOAD_ERROR,
                "模板未加载，无法生成报表"
            );
        }
        
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.save(outputStream, SaveFormat.XLSX);
            byte[] result = outputStream.toByteArray();
            
            logger.info("成功生成Excel报表，大小: {} 字节", result.length);
            return result;
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.EXCEL_GENERATION_ERROR,
                "生成Excel报表失败",
                e
            );
        }
    }
    
    @Override
    public String getTemplateType() {
        return "EXCEL";
    }
    
    @Override
    public String[] getSupportedExtensions() {
        return new String[]{".xlsx", ".xls"};
    }
    
    @Override
    public boolean isValid() {
        return isLoaded && workbook != null;
    }
    
    @Override
    public void cleanup() {
        if (workbook != null) {
            try {
                workbook.dispose();
            } catch (Exception e) {
                logger.warn("清理Excel工作簿资源时发生异常", e);
            }
        }
        isLoaded = false;
        templatePath = null;
    }
    
    /**
     * 获取工作簿对象（供高级操作使用）
     */
    public Workbook getWorkbook() {
        return workbook;
    }
    
    /**
     * 获取指定名称的工作表
     */
    public Worksheet getWorksheet(String sheetName) throws ReportException {
        if (!isLoaded) {
            throw new ReportException("模板未加载");
        }
        
        Worksheet sheet = workbook.getWorksheets().get(sheetName);
        if (sheet == null) {
            throw new ReportException("工作表不存在: " + sheetName);
        }
        
        return sheet;
    }
    
    /**
     * 基础数据填充方法
     */
    private void fillBasicData(Map<String, Object> data) throws Exception {
        // 这里实现基础的数据填充逻辑
        // 可以根据数据的key-value对应关系填充到指定单元格
        
        if (data == null || data.isEmpty()) {
            return;
        }
        
        // 示例：填充标题信息
        if (data.containsKey("title")) {
            // 可以在这里实现标题填充逻辑
            logger.debug("填充标题: {}", data.get("title"));
        }
        
        // 示例：填充日期信息
        if (data.containsKey("date")) {
            // 可以在这里实现日期填充逻辑
            logger.debug("填充日期: {}", data.get("date"));
        }
    }
}
