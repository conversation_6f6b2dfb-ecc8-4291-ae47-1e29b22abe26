package com.laoshu198838.service;

import com.laoshu198838.config.ReportConfig;
import com.laoshu198838.exception.ReportException;
import com.laoshu198838.template.ExcelTemplate;
import com.laoshu198838.template.ReportTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 通用报表导出服务
 * 提供统一的报表导出入口
 * 
 * <AUTHOR>
 */
@Service
public class ReportExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ReportExportService.class);
    
    @Autowired
    private ExcelTemplate excelTemplate;
    
    @Autowired
    private ExcelBuilderService excelBuilderService;
    
    /**
     * 导出报表
     * 
     * @param data 报表数据
     * @param config 报表配置
     * @return 生成的报表文件字节数组
     * @throws ReportException 导出异常
     */
    public byte[] export(Map<String, Object> data, ReportConfig config) throws ReportException {
        logger.info("开始导出报表: {}", config.getName());
        
        try {
            // 1. 选择合适的模板
            ReportTemplate template = selectTemplate(config);
            
            // 2. 加载模板
            template.loadTemplate(config.getTemplate());
            
            // 3. 填充数据
            template.fillData(data);
            
            // 4. 生成报表
            byte[] result = template.generate();
            
            // 5. 清理资源
            template.cleanup();
            
            logger.info("成功导出报表: {}，大小: {} 字节", config.getName(), result.length);
            return result;
            
        } catch (Exception e) {
            logger.error("导出报表失败: {}", config.getName(), e);
            throw new ReportException("导出报表失败: " + config.getName(), e);
        }
    }
    
    /**
     * 导出简单报表
     * 
     * @param templatePath 模板路径
     * @param data 数据
     * @return 报表字节数组
     * @throws ReportException 导出异常
     */
    public byte[] exportSimple(String templatePath, Map<String, Object> data) throws ReportException {
        ReportConfig config = ReportConfig.create("simple-report", "SIMPLE");
        config.setTemplate(templatePath);
        return export(data, config);
    }
    
    /**
     * 根据配置选择合适的模板
     */
    private ReportTemplate selectTemplate(ReportConfig config) throws ReportException {
        String templatePath = config.getTemplate();
        
        if (templatePath == null || templatePath.isEmpty()) {
            throw new ReportException(
                ReportException.ErrorCodes.CONFIG_ERROR,
                "模板路径未配置"
            );
        }
        
        // 根据文件扩展名选择模板类型
        String extension = getFileExtension(templatePath);
        
        switch (extension.toLowerCase()) {
            case ".xlsx":
            case ".xls":
                return excelTemplate;
            default:
                throw new ReportException(
                    ReportException.ErrorCodes.CONFIG_ERROR,
                    "不支持的模板格式: " + extension
                );
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex);
    }
    
    /**
     * 验证配置
     */
    public boolean validateConfig(ReportConfig config) {
        if (config == null) {
            return false;
        }
        
        if (config.getName() == null || config.getName().trim().isEmpty()) {
            logger.warn("报表配置缺少名称");
            return false;
        }
        
        if (config.getTemplate() == null || config.getTemplate().trim().isEmpty()) {
            logger.warn("报表配置缺少模板路径");
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取支持的报表格式
     */
    public String[] getSupportedFormats() {
        return new String[]{"EXCEL"};
    }
}
