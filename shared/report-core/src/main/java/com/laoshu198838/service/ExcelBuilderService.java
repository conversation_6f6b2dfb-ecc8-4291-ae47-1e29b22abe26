package com.laoshu198838.service;

import com.aspose.cells.*;
import com.laoshu198838.config.ReportConfig;
import com.laoshu198838.exception.ReportException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * Excel构建服务
 * 提供Excel操作的通用方法
 * 
 * <AUTHOR>
 */
@Service
public class ExcelBuilderService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelBuilderService.class);
    
    /**
     * 将ResultSet数据插入到Excel工作表中
     * 
     * @param sheet Excel工作表
     * @param rs 结果集
     * @param config 报表配置
     * @throws ReportException 处理异常
     */
    public void insertDataToWorksheet(Worksheet sheet, ResultSet rs, ReportConfig config) throws ReportException {
        try {
            Cells cells = sheet.getCells();
            int rowIndex = getStartRow(config);
            
            while (rs.next()) {
                insertRowData(cells, rowIndex, rs, config);
                rowIndex++;
            }
            
            logger.debug("成功插入数据到工作表: {}", sheet.getName());
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.DATA_PROCESSING_ERROR,
                "插入数据到Excel工作表失败",
                e
            );
        }
    }
    
    /**
     * 应用格式化到指定单元格
     * 
     * @param cells 单元格集合
     * @param row 行号
     * @param col 列号
     * @param format 格式字符串
     * @throws ReportException 处理异常
     */
    public void applyFormatting(Cells cells, int row, int col, String format) throws ReportException {
        try {
            Cell cell = cells.get(row, col);
            Style style = cell.getStyle();
            
            if (format != null && !format.isEmpty()) {
                style.setCustom(format);
                cell.setStyle(style);
            }
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.EXCEL_GENERATION_ERROR,
                "应用格式化失败",
                e
            );
        }
    }
    
    /**
     * 复制行格式
     * 
     * @param cells 单元格集合
     * @param targetRow 目标行
     * @throws ReportException 处理异常
     */
    public void copyRowFormat(Cells cells, int targetRow) throws ReportException {
        try {
            if (targetRow <= 0) {
                return;
            }
            
            // 获取目标行第二列（索引1）的单元格
            Cell targetCell = cells.get(targetRow + 1, 1);
            
            // 如果目标单元格不为空，则复制上一行的格式到目标行
            if (targetCell != null && targetCell.getValue() != null) {
                // 插入一行到 targetRow 位置
                cells.insertRow(targetRow);
                // 复制格式
                cells.copyRow(cells, targetRow + 1, targetRow);
            }
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.EXCEL_GENERATION_ERROR,
                "复制行格式失败",
                e
            );
        }
    }
    
    /**
     * 插入序号到第一列
     * 
     * @param cells 单元格集合
     * @param rowIndex 行索引
     * @throws ReportException 处理异常
     */
    public void insertSequenceNumber(Cells cells, int rowIndex) throws ReportException {
        try {
            Cell currentCell = cells.get(rowIndex, 0);
            Cell aboveCell = (rowIndex > 0) ? cells.get(rowIndex - 1, 0) : null;
            
            String newValue = "1";
            boolean isMerged = (aboveCell != null && aboveCell.isMerged());
            
            if (aboveCell != null && !isMerged && aboveCell.getValue() != null) {
                try {
                    int aboveValue = Integer.parseInt(aboveCell.getStringValue());
                    newValue = String.valueOf(aboveValue + 1);
                } catch (NumberFormatException e) {
                    // 如果上方单元格不是数字，使用默认值1
                    newValue = "1";
                }
            }
            
            currentCell.setValue(newValue);
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.EXCEL_GENERATION_ERROR,
                "插入序号失败",
                e
            );
        }
    }
    
    /**
     * 设置单元格值并应用格式
     * 
     * @param cell 单元格
     * @param value 值
     * @throws ReportException 处理异常
     */
    public void putValueWithFormat(Cell cell, String value) throws ReportException {
        try {
            if (value == null || value.isEmpty()) {
                return;
            }
            
            // 尝试解析为数字
            try {
                double numValue = Double.parseDouble(value);
                cell.setValue(numValue);
            } catch (NumberFormatException e) {
                // 如果不是数字，作为字符串处理
                cell.setValue(value);
            }
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.EXCEL_GENERATION_ERROR,
                "设置单元格值失败",
                e
            );
        }
    }
    
    /**
     * 获取起始行号
     */
    private int getStartRow(ReportConfig config) {
        // 默认从第4行开始（索引3）
        if (config != null && config.getParameters() != null) {
            Object startRow = config.getParameters().get("startRow");
            if (startRow instanceof Integer) {
                return (Integer) startRow;
            }
        }
        return 3; // 默认值
    }
    
    /**
     * 插入行数据
     */
    private void insertRowData(Cells cells, int rowIndex, ResultSet rs, ReportConfig config) 
            throws SQLException, ReportException {
        
        if (config.getColumns() == null || config.getColumns().isEmpty()) {
            return;
        }
        
        // 插入序号
        insertSequenceNumber(cells, rowIndex);
        
        // 插入数据列
        for (int i = 0; i < config.getColumns().size(); i++) {
            ReportConfig.ColumnConfig column = config.getColumns().get(i);
            String value = rs.getString(column.getField());
            
            Cell cell = cells.get(rowIndex, i + 1); // +1是因为第0列是序号
            putValueWithFormat(cell, value);
            
            // 应用格式
            if (column.getFormat() != null) {
                applyFormatting(cells, rowIndex, i + 1, column.getFormat());
            }
        }
    }
}
