package com.laoshu198838.exception;

/**
 * 报表异常类
 * 用于处理报表生成过程中的各种异常情况
 * 
 * <AUTHOR>
 */
public class ReportException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private String errorMessage;
    
    public ReportException(String message) {
        super(message);
        this.errorMessage = message;
    }
    
    public ReportException(String message, Throwable cause) {
        super(message, cause);
        this.errorMessage = message;
    }
    
    public ReportException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    public ReportException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    /**
     * 常用错误代码
     */
    public static class ErrorCodes {
        public static final String TEMPLATE_NOT_FOUND = "REPORT_001";
        public static final String TEMPLATE_LOAD_ERROR = "REPORT_002";
        public static final String DATA_PROCESSING_ERROR = "REPORT_003";
        public static final String EXCEL_GENERATION_ERROR = "REPORT_004";
        public static final String SQL_BUILD_ERROR = "REPORT_005";
        public static final String CONFIG_ERROR = "REPORT_006";
        public static final String IO_ERROR = "REPORT_007";
    }
}
