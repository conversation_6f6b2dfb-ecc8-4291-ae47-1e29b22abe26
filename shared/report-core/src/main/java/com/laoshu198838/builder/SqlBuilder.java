package com.laoshu198838.builder;

import com.laoshu198838.exception.ReportException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * SQL构建器
 * 提供动态SQL构建功能
 * 
 * <AUTHOR>
 */
@Component
public class SqlBuilder {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlBuilder.class);
    
    private StringBuilder sql;
    private List<Object> parameters;
    
    public SqlBuilder() {
        this.sql = new StringBuilder();
        this.parameters = new ArrayList<>();
    }
    
    /**
     * 开始构建SELECT语句
     */
    public SqlBuilder select(String... columns) {
        sql.append("SELECT ");
        if (columns.length == 0) {
            sql.append("*");
        } else {
            sql.append(String.join(", ", columns));
        }
        return this;
    }
    
    /**
     * 添加FROM子句
     */
    public SqlBuilder from(String table) {
        sql.append(" FROM ").append(table);
        return this;
    }
    
    /**
     * 添加WHERE子句
     */
    public SqlBuilder where(String condition) {
        sql.append(" WHERE ").append(condition);
        return this;
    }
    
    /**
     * 添加AND条件
     */
    public SqlBuilder and(String condition) {
        sql.append(" AND ").append(condition);
        return this;
    }
    
    /**
     * 添加OR条件
     */
    public SqlBuilder or(String condition) {
        sql.append(" OR ").append(condition);
        return this;
    }
    
    /**
     * 添加GROUP BY子句
     */
    public SqlBuilder groupBy(String... columns) {
        sql.append(" GROUP BY ").append(String.join(", ", columns));
        return this;
    }
    
    /**
     * 添加ORDER BY子句
     */
    public SqlBuilder orderBy(String column) {
        sql.append(" ORDER BY ").append(column);
        return this;
    }
    
    /**
     * 添加ORDER BY DESC子句
     */
    public SqlBuilder orderByDesc(String column) {
        sql.append(" ORDER BY ").append(column).append(" DESC");
        return this;
    }
    
    /**
     * 添加LIMIT子句
     */
    public SqlBuilder limit(int count) {
        sql.append(" LIMIT ").append(count);
        return this;
    }
    
    /**
     * 添加参数
     */
    public SqlBuilder addParameter(Object parameter) {
        parameters.add(parameter);
        return this;
    }
    
    /**
     * 添加多个参数
     */
    public SqlBuilder addParameters(Object... params) {
        for (Object param : params) {
            parameters.add(param);
        }
        return this;
    }
    
    /**
     * 构建SQL字符串
     */
    public String build() {
        return sql.toString();
    }
    
    /**
     * 获取参数列表
     */
    public List<Object> getParameters() {
        return new ArrayList<>(parameters);
    }
    
    /**
     * 重置构建器
     */
    public SqlBuilder reset() {
        sql.setLength(0);
        parameters.clear();
        return this;
    }
    
    /**
     * 构建年份月份条件的SQL
     */
    public SqlBuilder addYearMonthCondition(String yearColumn, String monthColumn, int year, int month) {
        and(yearColumn + " = ?").addParameter(year);
        and(monthColumn + " = ?").addParameter(month);
        return this;
    }
    
    /**
     * 构建日期范围条件的SQL
     */
    public SqlBuilder addDateRangeCondition(String dateColumn, String startDate, String endDate) {
        if (startDate != null) {
            and(dateColumn + " >= ?").addParameter(startDate);
        }
        if (endDate != null) {
            and(dateColumn + " <= ?").addParameter(endDate);
        }
        return this;
    }
    
    /**
     * 构建IN条件的SQL
     */
    public SqlBuilder addInCondition(String column, List<Object> values) {
        if (values != null && !values.isEmpty()) {
            StringBuilder inClause = new StringBuilder(column).append(" IN (");
            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    inClause.append(", ");
                }
                inClause.append("?");
                addParameter(values.get(i));
            }
            inClause.append(")");
            and(inClause.toString());
        }
        return this;
    }
    
    /**
     * 构建LIKE条件的SQL
     */
    public SqlBuilder addLikeCondition(String column, String value) {
        if (value != null && !value.trim().isEmpty()) {
            and(column + " LIKE ?").addParameter("%" + value + "%");
        }
        return this;
    }
    
    /**
     * 静态工厂方法
     */
    public static SqlBuilder create() {
        return new SqlBuilder();
    }
    
    /**
     * 根据模板和参数构建SQL
     */
    public static SqlResult buildFromTemplate(String template, Map<String, Object> params) throws ReportException {
        try {
            // 这里可以实现更复杂的模板解析逻辑
            // 目前简单实现参数替换
            String sql = template;
            List<Object> parameters = new ArrayList<>();
            
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String placeholder = "#{" + entry.getKey() + "}";
                if (sql.contains(placeholder)) {
                    sql = sql.replace(placeholder, "?");
                    parameters.add(entry.getValue());
                }
            }
            
            return new SqlResult(sql, parameters);
            
        } catch (Exception e) {
            throw new ReportException(
                ReportException.ErrorCodes.SQL_BUILD_ERROR,
                "构建SQL失败",
                e
            );
        }
    }
    
    /**
     * SQL构建结果类
     */
    public static class SqlResult {
        private final String sql;
        private final List<Object> parameters;
        
        public SqlResult(String sql, List<Object> parameters) {
            this.sql = sql;
            this.parameters = parameters;
        }
        
        public String getSql() {
            return sql;
        }
        
        public List<Object> getParameters() {
            return parameters;
        }
        
        public Object[] getParameterArray() {
            return parameters.toArray();
        }
    }
}
