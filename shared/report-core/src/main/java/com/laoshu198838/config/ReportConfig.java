package com.laoshu198838.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 报表配置类
 * 用于管理报表的各种配置信息
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "report")
public class ReportConfig {
    
    /**
     * 报表名称
     */
    private String name;
    
    /**
     * 报表类型（SIMPLE, COMPLEX）
     */
    private String type;
    
    /**
     * 模板文件路径
     */
    private String template;
    
    /**
     * 数据源名称
     */
    private String datasource;
    
    /**
     * 报表描述
     */
    private String description;
    
    /**
     * 列配置
     */
    private List<ColumnConfig> columns;
    
    /**
     * 表格配置（用于复杂报表）
     */
    private List<TableConfig> tables;
    
    /**
     * 自定义参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 输出配置
     */
    private OutputConfig output;
    
    /**
     * 列配置类
     */
    @Data
    public static class ColumnConfig {
        private String name;
        private String field;
        private String type;
        private String format;
        private Integer width;
        private Boolean required;
        private String defaultValue;
    }
    
    /**
     * 表格配置类
     */
    @Data
    public static class TableConfig {
        private String name;
        private String databaseTable;
        private String excelSheet;
        private String exportMethod;
        private Map<String, Object> parameters;
    }
    
    /**
     * 输出配置类
     */
    @Data
    public static class OutputConfig {
        private String format;
        private String filename;
        private String encoding;
        private Boolean compress;
    }
    
    /**
     * 静态工厂方法，用于创建配置实例
     */
    public static ReportConfig create(String name, String type) {
        ReportConfig config = new ReportConfig();
        config.setName(name);
        config.setType(type);
        return config;
    }
}
