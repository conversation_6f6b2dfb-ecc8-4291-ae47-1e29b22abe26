-- ===================================
-- 用户系统数据库增强脚本
-- 用于实现公司级数据权限控制
-- ===================================

USE user_system;

-- 1. 修改users表，添加可选字段
ALTER TABLE users 
ADD COLUMN email VARCHAR(100) NULL COMMENT '邮箱地址（可选）',
ADD COLUMN phone VARCHAR(20) NULL COMMENT '手机号码（可选）',
ADD COLUMN position VARCHAR(50) NULL COMMENT '职位（可选）',
ADD COLUMN employee_id VARCHAR(30) NULL COMMENT '员工编号（可选）',
ADD COLUMN remarks TEXT NULL COMMENT '备注信息（可选）',
ADD COLUMN last_login_time TIMESTAMP NULL COMMENT '最后登录时间';

-- 2. 创建公司信息表（标准化公司名称）
CREATE TABLE IF NOT EXISTS companies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL UNIQUE COMMENT '公司名称',
    company_code VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL UNIQUE COMMENT '公司代码',
    is_management_company BOOLEAN DEFAULT FALSE COMMENT '是否为管理公司（如万润科技）',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '公司状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_company_name (company_name),
    INDEX idx_company_code (company_code)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT='公司信息表';

-- 3. 创建用户公司权限表（核心权限控制表）
CREATE TABLE IF NOT EXISTS user_company_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    company_id BIGINT NOT NULL COMMENT '公司ID',
    permission_type ENUM('read', 'write', 'admin') DEFAULT 'read' COMMENT '权限类型',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认权限（用户所属公司）',
    granted_by BIGINT NULL COMMENT '授权人ID',
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at TIMESTAMP NULL COMMENT '权限过期时间',
    status ENUM('active', 'suspended', 'expired') DEFAULT 'active' COMMENT '权限状态',
    remarks TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '授权备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_company (user_id, company_id),
    INDEX idx_user_id (user_id),
    INDEX idx_company_id (company_id),
    INDEX idx_granted_by (granted_by),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT='用户公司权限表';

-- 4. 创建数据访问日志表（审计用）
CREATE TABLE IF NOT EXISTS data_access_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    company_accessed VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '访问的公司数据',
    access_type ENUM('query', 'export', 'modify') NOT NULL COMMENT '访问类型',
    resource_type VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源类型（如debt_statistics）',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
    access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    success BOOLEAN DEFAULT TRUE COMMENT '访问是否成功',
    error_message TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息（如果有）',
    
    INDEX idx_user_id (user_id),
    INDEX idx_company_accessed (company_accessed),
    INDEX idx_access_time (access_time),
    INDEX idx_access_type (access_type)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT='数据访问日志表';

-- 5. 插入默认公司数据
INSERT IGNORE INTO companies (company_name, company_code, is_management_company) VALUES
('万润科技', 'WRT', TRUE),
('A公司', 'COMP_A', FALSE),
('B公司', 'COMP_B', FALSE),
('C公司', 'COMP_C', FALSE);

-- 6. 为现有用户创建默认权限（基于其companyname字段）
INSERT IGNORE INTO user_company_permissions (user_id, company_id, permission_type, is_default)
SELECT 
    u.id as user_id,
    c.id as company_id,
    CASE 
        WHEN u.role_id = 1 THEN 'admin'  -- 管理员角色
        ELSE 'write'                     -- 普通用户
    END as permission_type,
    TRUE as is_default
FROM users u
JOIN companies c ON u.companyname = c.company_name
WHERE u.companyname IS NOT NULL;

-- 7. 为万润科技的管理员用户添加所有公司的访问权限
INSERT IGNORE INTO user_company_permissions (user_id, company_id, permission_type, is_default, remarks)
SELECT 
    u.id as user_id,
    c.id as company_id,
    'admin' as permission_type,
    FALSE as is_default,
    '万润科技管理员默认权限' as remarks
FROM users u
CROSS JOIN companies c
WHERE u.companyname = '万润科技' 
AND u.role_id = 1  -- 管理员角色
AND c.company_name != '万润科技';  -- 排除自己公司

-- 8. 创建视图：用户权限概览
CREATE OR REPLACE VIEW user_permissions_view AS
SELECT 
    u.id as user_id,
    u.username,
    u.name,
    u.companyname as user_company,
    u.department,
    r.role_name,
    c.company_name as accessible_company,
    ucp.permission_type,
    ucp.is_default,
    ucp.status as permission_status,
    ucp.expires_at,
    grantor.username as granted_by_username,
    ucp.created_at as permission_created_at
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN user_company_permissions ucp ON u.id = ucp.user_id
LEFT JOIN companies c ON ucp.company_id = c.id
LEFT JOIN users grantor ON ucp.granted_by = grantor.id
WHERE u.status = 'ACTIVE'
ORDER BY u.username, c.company_name;

-- 9. 创建存储过程：获取用户可访问的公司列表
DELIMITER //
CREATE PROCEDURE GetUserAccessibleCompanies(IN userId BIGINT)
BEGIN
    SELECT 
        c.company_name,
        c.company_code,
        c.is_management_company,
        ucp.permission_type,
        ucp.is_default,
        ucp.expires_at,
        CASE 
            WHEN ucp.expires_at IS NULL THEN TRUE
            WHEN ucp.expires_at > NOW() THEN TRUE
            ELSE FALSE
        END as is_valid
    FROM user_company_permissions ucp
    JOIN companies c ON ucp.company_id = c.id
    WHERE ucp.user_id = userId 
    AND ucp.status = 'active'
    AND c.status = 'active'
    ORDER BY ucp.is_default DESC, c.company_name;
END //
DELIMITER ;

-- 10. 创建存储过程：验证用户是否有访问指定公司数据的权限
DELIMITER //
CREATE PROCEDURE CheckUserCompanyAccess(
    IN userId BIGINT, 
    IN companyName VARCHAR(50),
    OUT hasAccess BOOLEAN,
    OUT permissionType VARCHAR(10)
)
BEGIN
    DECLARE permission_count INT DEFAULT 0;
    
    SELECT COUNT(*), IFNULL(MAX(ucp.permission_type), 'none')
    INTO permission_count, permissionType
    FROM user_company_permissions ucp
    JOIN companies c ON ucp.company_id = c.id
    WHERE ucp.user_id = userId 
    AND c.company_name = companyName
    AND ucp.status = 'active'
    AND c.status = 'active'
    AND (ucp.expires_at IS NULL OR ucp.expires_at > NOW());
    
    SET hasAccess = (permission_count > 0);
END //
DELIMITER ;

-- 11. 创建触发器：记录权限变更日志
DELIMITER //
CREATE TRIGGER user_permission_audit_trigger
AFTER INSERT ON user_company_permissions
FOR EACH ROW
BEGIN
    INSERT INTO user_audit_logs (user_id, action, details, created_at)
    VALUES (
        NEW.user_id,
        'PERMISSION_GRANTED',
        CONCAT('授权访问公司ID: ', NEW.company_id, ', 权限类型: ', NEW.permission_type),
        NOW()
    );
END //
DELIMITER ;

-- 12. 显示执行结果
SELECT '数据库结构增强完成！' as message;
SELECT COUNT(*) as total_companies FROM companies;
SELECT COUNT(*) as total_permissions FROM user_company_permissions;
SELECT COUNT(*) as total_users FROM users;