-- 创建数据库
CREATE DATABASE IF NOT EXISTS `overdue_debt_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `kingdee` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `user_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 设置权限
GRANT ALL PRIVILEGES ON `overdue_debt_db`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `kingdee`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `user_system`.* TO 'root'@'%';
FLUSH PRIVILEGES;

-- 初始化user_system数据库
USE `user_system`;

-- 创建角色表
CREATE TABLE IF NOT EXISTS `roles` (
  `role_id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID，主键',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称，如USER、ADMIN',
  PRIMARY KEY (`role_id`),
  UNIQUE KEY `uk_role_name` (`role_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色表';

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `username` varchar(50) NOT NULL COMMENT '用户名，登录用',
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `password` varchar(150) NOT NULL COMMENT '密码（BCrypt加密）',
  `companyname` varchar(50) DEFAULT '所有公司' COMMENT '所属公司',
  `department` varchar(50) DEFAULT '所有部门' COMMENT '所属部门',
  `status` varchar(50) DEFAULT 'ACTIVE' COMMENT '用户状态：ACTIVE、INACTIVE、PENDING',
  `role_id` int NOT NULL COMMENT '角色ID，外键',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_company_dept` (`companyname`, `department`),
  KEY `fk_user_role` (`role_id`),
  CONSTRAINT `fk_user_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 插入默认角色数据
INSERT INTO `roles` (`role_id`, `role_name`) VALUES
(1, 'ADMIN'),
(2, 'USER'),
(3, 'VIEWER')
ON DUPLICATE KEY UPDATE
  `role_name` = VALUES(`role_name`);

-- 插入默认用户数据
-- 用户名: laoshu198838, 密码: Zlb&198838 (BCrypt加密)
-- 用户名: admin, 密码: admin123 (BCrypt加密)
INSERT INTO `users` (`username`, `name`, `password`, `companyname`, `department`, `status`, `role_id`) VALUES
('laoshu198838', '周先生', '$2a$10$7THXRE0rp/EtC3lmxyydV.RMZq8mMhL4lOP2PveMB1YOfRwk/Cpj.', '所有公司', '所有部门', 'ACTIVE', 1),
('admin', '系统管理员', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P8jskrvi.6hwMK', '所有公司', '所有部门', 'ACTIVE', 1)
ON DUPLICATE KEY UPDATE
  `name` = VALUES(`name`),
  `companyname` = VALUES(`companyname`),
  `department` = VALUES(`department`),
  `status` = VALUES(`status`),
  `role_id` = VALUES(`role_id`);
