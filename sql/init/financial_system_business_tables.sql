-- =====================================================
-- FinancialSystem 业务表结构初始化脚本
-- 基于JPA实体类定义创建业务表结构
-- 数据库: financial_system
-- =====================================================

USE `financial_system`;

-- =====================================================
-- 创建新增表 (OverdueDebtAdd)
-- =====================================================
CREATE TABLE IF NOT EXISTS `新增表` (
  `债权人` varchar(50) NOT NULL COMMENT '债权人',
  `债务人` varchar(50) NOT NULL COMMENT '债务人',
  `期间` varchar(20) NOT NULL COMMENT '期间',
  `是否涉诉` varchar(10) NOT NULL COMMENT '是否涉诉',
  `年份` varchar(4) NOT NULL COMMENT '年份',
  `序号` int NOT NULL COMMENT '序号',
  `管理公司` varchar(30) NOT NULL COMMENT '管理公司',
  `到期时间` varchar(30) COMMENT '到期时间',
  `科目名称` varchar(30) COMMENT '科目名称',
  `债权性质` varchar(30) COMMENT '债权性质',
  `债权风险类型` varchar(30) COMMENT '债权风险类型',
  `1月` decimal(19,2) DEFAULT 0.00 COMMENT '1月金额',
  `2月` decimal(19,2) DEFAULT 0.00 COMMENT '2月金额',
  `3月` decimal(19,2) DEFAULT 0.00 COMMENT '3月金额',
  `4月` decimal(19,2) DEFAULT 0.00 COMMENT '4月金额',
  `5月` decimal(19,2) DEFAULT 0.00 COMMENT '5月金额',
  `6月` decimal(19,2) DEFAULT 0.00 COMMENT '6月金额',
  `7月` decimal(19,2) DEFAULT 0.00 COMMENT '7月金额',
  `8月` decimal(19,2) DEFAULT 0.00 COMMENT '8月金额',
  `9月` decimal(19,2) DEFAULT 0.00 COMMENT '9月金额',
  `10月` decimal(19,2) DEFAULT 0.00 COMMENT '10月金额',
  `11月` decimal(19,2) DEFAULT 0.00 COMMENT '11月金额',
  `12月` decimal(19,2) DEFAULT 0.00 COMMENT '12月金额',
  `现金处置` decimal(19,2) NOT NULL DEFAULT 0.00 COMMENT '现金处置',
  `分期还款` decimal(19,2) NOT NULL DEFAULT 0.00 COMMENT '分期还款',
  `资产抵债` decimal(19,2) NOT NULL DEFAULT 0.00 COMMENT '资产抵债',
  `其他方式` decimal(19,2) NOT NULL DEFAULT 0.00 COMMENT '其他方式',
  `新增金额` decimal(19,2) NOT NULL DEFAULT 0.00 COMMENT '新增金额',
  `债权余额` decimal(19,2) NOT NULL DEFAULT 0.00 COMMENT '债权余额',
  `更新时间` datetime COMMENT '更新时间',
  `备注` varchar(190) COMMENT '备注',
  `责任人` varchar(30) COMMENT '责任人',
  PRIMARY KEY (`债权人`, `债务人`, `期间`, `是否涉诉`, `年份`),
  KEY `idx_management_company` (`管理公司`),
  KEY `idx_period` (`期间`),
  KEY `idx_year` (`年份`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='逾期债权新增表';

-- =====================================================
-- 创建处置表 (OverdueDebtDecrease)
-- =====================================================
CREATE TABLE IF NOT EXISTS `处置表` (
  `债权人` varchar(50) NOT NULL COMMENT '债权人',
  `债务人` varchar(50) NOT NULL COMMENT '债务人',
  `期间` varchar(20) NOT NULL COMMENT '期间',
  `是否涉诉` varchar(10) NOT NULL COMMENT '是否涉诉',
  `年份` int NOT NULL COMMENT '年份',
  `月份` decimal(19,2) NOT NULL COMMENT '月份',
  `序号` int NOT NULL COMMENT '序号',
  `管理公司` varchar(30) NOT NULL COMMENT '管理公司',
  `债权风险类型` varchar(30) COMMENT '债权风险类型',
  `客户情况分类` varchar(30) COMMENT '客户情况分类',
  `每月处置金额` decimal(19,2) DEFAULT 0.00 COMMENT '每月处置金额',
  `现金处置` decimal(19,2) DEFAULT 0.00 COMMENT '现金处置',
  `分期还款` decimal(19,2) DEFAULT 0.00 COMMENT '分期还款',
  `资产抵债` decimal(19,2) DEFAULT 0.00 COMMENT '资产抵债',
  `其他方式` decimal(19,2) DEFAULT 0.00 COMMENT '其他方式',
  `备注` varchar(190) COMMENT '备注',
  `更新时间` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`债权人`, `债务人`, `期间`, `是否涉诉`, `年份`, `月份`),
  KEY `idx_management_company` (`管理公司`),
  KEY `idx_period` (`期间`),
  KEY `idx_year` (`年份`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='债权处置表';

-- =====================================================
-- 创建诉讼表 (LitigationClaim)
-- =====================================================
CREATE TABLE IF NOT EXISTS `诉讼表` (
  `债权人` varchar(50) NOT NULL COMMENT '债权人',
  `债务人` varchar(50) NOT NULL COMMENT '债务人',
  `期间` varchar(20) NOT NULL COMMENT '期间',
  `年份` int NOT NULL COMMENT '年份',
  `序号` int NOT NULL COMMENT '序号',
  `诉讼案件` varchar(100) COMMENT '诉讼案件',
  `管理公司` varchar(30) COMMENT '管理公司',
  `科目名称` varchar(30) COMMENT '科目名称',
  `上月末债权余额` decimal(19,2) COMMENT '上月末债权余额',
  `涉诉债权本金` decimal(19,2) COMMENT '涉诉债权本金',
  `涉诉债权应收利息罚息服务费` decimal(19,2) COMMENT '涉诉债权应收利息罚息服务费',
  `本月末债权余额` decimal(19,2) COMMENT '本月末债权余额',
  `诉讼主张本金` decimal(19,2) COMMENT '诉讼主张本金',
  `诉讼主张应收利息及罚金` decimal(19,2) COMMENT '诉讼主张应收利息及罚金',
  `诉讼费` decimal(19,2) COMMENT '诉讼费',
  `中介费` decimal(19,2) COMMENT '中介费',
  `终审判决仲裁裁决调解和解金额` decimal(19,2) COMMENT '终审判决仲裁裁决调解和解金额',
  `申请执行金额` decimal(19,2) COMMENT '申请执行金额',
  `实际执行回款` decimal(19,2) COMMENT '实际执行回款',
  `实际支付费用` decimal(19,2) COMMENT '实际支付费用',
  `本年度回收目标` decimal(19,2) COMMENT '本年度回收目标',
  `本年度累计回收` decimal(19,2) COMMENT '本年度累计回收',
  `安排措施` varchar(190) COMMENT '安排措施',
  `责任人` varchar(30) COMMENT '责任人',
  `备注` varchar(190) COMMENT '备注',
  `逾期年限` varchar(30) COMMENT '逾期年限',
  `债权类别` varchar(30) COMMENT '债权类别',
  `更新时间` datetime COMMENT '更新时间',
  PRIMARY KEY (`债权人`, `债务人`, `期间`, `年份`),
  KEY `idx_management_company` (`管理公司`),
  KEY `idx_litigation_case` (`诉讼案件`),
  KEY `idx_period` (`期间`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='诉讼债权表';

-- =====================================================
-- 创建非诉讼表 (NonLitigationClaim)
-- =====================================================
CREATE TABLE IF NOT EXISTS `非诉讼表` (
  `债权人` varchar(50) NOT NULL COMMENT '债权人',
  `债务人` varchar(50) NOT NULL COMMENT '债务人',
  `期间` varchar(20) NOT NULL COMMENT '期间',
  `年份` int NOT NULL COMMENT '年份',
  `序号` int NOT NULL COMMENT '序号',
  `科目名称` varchar(30) COMMENT '科目名称',
  `债权性质` varchar(30) COMMENT '债权性质',
  `债权到期时间` varchar(30) COMMENT '债权到期时间',
  `2022年4月30日债权账面余额` decimal(19,2) COMMENT '2022年4月30日债权账面余额',
  `上月末本金` decimal(19,2) COMMENT '上月末本金',
  `上月末利息` decimal(19,2) COMMENT '上月末利息',
  `上月末违约金` decimal(19,2) COMMENT '上月末违约金',
  `本月本金增减` decimal(19,2) COMMENT '本月本金增减',
  `本月利息增减` decimal(19,2) COMMENT '本月利息增减',
  `本月违约金增减` decimal(19,2) COMMENT '本月违约金增减',
  `本月末本金` decimal(19,2) COMMENT '本月末本金',
  `本月末利息` decimal(19,2) COMMENT '本月末利息',
  `本月末违约金` decimal(19,2) COMMENT '本月末违约金',
  `下月回收预计` decimal(19,2) COMMENT '下月回收预计',
  `本年度回收目标` decimal(19,2) COMMENT '本年度回收目标',
  `本年度累计回收` decimal(19,2) COMMENT '本年度累计回收',
  `安排措施` varchar(190) COMMENT '安排措施',
  `责任人` varchar(30) COMMENT '责任人',
  `备注` varchar(190) COMMENT '备注',
  `逾期年限` varchar(30) COMMENT '逾期年限',
  `债权类别` varchar(30) COMMENT '债权类别',
  `管理公司` varchar(30) COMMENT '管理公司',
  `更新时间` datetime COMMENT '更新时间',
  PRIMARY KEY (`债权人`, `债务人`, `期间`, `年份`),
  KEY `idx_management_company` (`管理公司`),
  KEY `idx_period` (`期间`),
  KEY `idx_debt_nature` (`债权性质`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='非诉讼债权表';

-- =====================================================
-- 创建减值准备表 (ImpairmentReserve)
-- =====================================================
CREATE TABLE IF NOT EXISTS `减值准备表` (
  `债权人` varchar(50) NOT NULL COMMENT '债权人',
  `债务人` varchar(50) NOT NULL COMMENT '债务人',
  `年份` int NOT NULL COMMENT '年份',
  `月份` int NOT NULL COMMENT '月份',
  `是否涉诉` varchar(10) NOT NULL COMMENT '是否涉诉',
  `期间` varchar(20) NOT NULL COMMENT '期间',
  `序号` int NOT NULL COMMENT '序号',
  `案件名称` varchar(100) COMMENT '案件名称',
  `科目名称` varchar(50) COMMENT '科目名称',
  `2022年4月30日债权金额` decimal(19,2) COMMENT '2022年4月30日债权金额',
  `本月初债权余额` decimal(19,2) COMMENT '本月初债权余额',
  `本月末债权余额` decimal(19,2) COMMENT '本月末债权余额',
  `计提减值金额` decimal(19,2) COMMENT '计提减值金额',
  `初始计提日期` date COMMENT '初始计提日期',
  `上月末余额` decimal(19,2) COMMENT '上月末余额',
  `本月增减` decimal(19,2) COMMENT '本月增减',
  `本月末余额` decimal(19,2) COMMENT '本月末余额',
  `本年度回收目标` decimal(19,2) COMMENT '本年度回收目标',
  `本年度累计回收` decimal(19,2) COMMENT '本年度累计回收',
  `备注` varchar(190) COMMENT '备注',
  `管理公司` varchar(50) COMMENT '管理公司',
  `是否全额计提坏账` varchar(50) COMMENT '是否全额计提坏账',
  `到期时间` date COMMENT '到期时间',
  `更新时间` datetime COMMENT '更新时间',
  PRIMARY KEY (`债权人`, `债务人`, `年份`, `月份`, `是否涉诉`, `期间`),
  KEY `idx_management_company` (`管理公司`),
  KEY `idx_period` (`期间`),
  KEY `idx_year_month` (`年份`, `月份`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='减值准备表';

-- =====================================================
-- 创建索引以提高查询性能
-- =====================================================

-- 新增表索引
ALTER TABLE `新增表` ADD INDEX `idx_debt_risk` (`债权风险类型`);
ALTER TABLE `新增表` ADD INDEX `idx_subject_name` (`科目名称`);

-- 处置表索引
ALTER TABLE `处置表` ADD INDEX `idx_debt_risk` (`债权风险类型`);
ALTER TABLE `处置表` ADD INDEX `idx_customer_category` (`客户情况分类`);

-- 诉讼表索引
ALTER TABLE `诉讼表` ADD INDEX `idx_subject_name` (`科目名称`);
ALTER TABLE `诉讼表` ADD INDEX `idx_debt_category` (`债权类别`);

-- 非诉讼表索引
ALTER TABLE `非诉讼表` ADD INDEX `idx_subject_name` (`科目名称`);
ALTER TABLE `非诉讼表` ADD INDEX `idx_debt_category` (`债权类别`);

-- 减值准备表索引
ALTER TABLE `减值准备表` ADD INDEX `idx_subject_name` (`科目名称`);
ALTER TABLE `减值准备表` ADD INDEX `idx_case_name` (`案件名称`);

-- =====================================================
-- 插入示例数据（可选）
-- =====================================================

-- 插入新增表示例数据
INSERT IGNORE INTO `新增表` (
  `债权人`, `债务人`, `期间`, `是否涉诉`, `年份`, `序号`, `管理公司`, 
  `科目名称`, `债权性质`, `新增金额`, `债权余额`, `更新时间`
) VALUES 
('示例债权人', '示例债务人', '2024年12月', '否', '2024', 1, '示例管理公司', 
 '应收账款', '商业债权', 100000.00, 100000.00, NOW());

-- 记录初始化完成
INSERT INTO `user_system`.`user_audit_logs` (
  `username`, `action`, `resource`, `result`, `created_time`
) VALUES (
  'system', 'DATABASE_INIT', 'financial_system_business_tables', 'SUCCESS', NOW()
);

-- =====================================================
-- 脚本执行完成
-- =====================================================
