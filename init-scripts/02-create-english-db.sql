-- 创建英文名称的数据库作为临时解决方案
CREATE DATABASE IF NOT EXISTS `overdue_debt_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `kingdee` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `user_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 复制表结构到英文数据库
USE `overdue_debt_db`;

-- 如果中文数据库存在，复制其结构
-- 否则创建基本表结构
CREATE TABLE IF NOT EXISTS `test_table` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 为user_system创建基本用户表
USE `user_system`;

CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入默认管理员
INSERT INTO `user` (`username`, `password`, `real_name`, `email`) 
VALUES ('admin', '$2a$10$5LXJkPH4X.xQJz5Y5xqAq.qXBsrtJqN4fQQJtN5VgQQN1cKEp0Bim', '系统管理员', '<EMAIL>')
ON DUPLICATE KEY UPDATE username=username;