-- 创建数据库并设置字符集
CREATE DATABASE IF NOT EXISTS `overdue_debt_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `kingdee` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `user_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 授权用户访问
GRANT ALL PRIVILEGES ON `overdue_debt_db`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `kingdee`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `user_system`.* TO 'root'@'%';
FLUSH PRIVILEGES;

-- 创建user_system基础表
USE `user_system`;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `is_first_login` tinyint DEFAULT '1' COMMENT '是否首次登录：1-是，0-否',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS `role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色代码',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `status` tinyint DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 插入默认管理员用户（密码：admin123）
INSERT INTO `user` (`username`, `password`, `real_name`, `email`, `status`) 
VALUES ('admin', '$2a$10$5LXJkPH4X.xQJz5Y5xqAq.qXBsrtJqN4fQQJtN5VgQQN1cKEp0Bim', '系统管理员', '<EMAIL>', 1)
ON DUPLICATE KEY UPDATE username=username;

-- 插入默认角色
INSERT INTO `role` (`role_name`, `role_code`, `description`) 
VALUES ('系统管理员', 'ADMIN', '系统管理员角色'),
       ('普通用户', 'USER', '普通用户角色')
ON DUPLICATE KEY UPDATE role_code=role_code;

-- 关联管理员角色
INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r 
WHERE u.username = 'admin' AND r.role_code = 'ADMIN'
ON DUPLICATE KEY UPDATE user_id=user_id;