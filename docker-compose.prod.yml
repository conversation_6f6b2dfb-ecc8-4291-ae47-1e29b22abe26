# FinancialSystem 生产环境 Docker Compose 配置
# 不包含构建服务，仅用于运行已构建的应用

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: financial-mysql
    pull_policy: never
    environment:
      MYSQL_ROOT_PASSWORD: Zlb&198838
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 后端运行服务
  backend:
    image: openjdk:21-jdk
    platform: linux/amd64
    container_name: financial-backend
    pull_policy: never
    ports:
      - "8080:8080"
    volumes:
      - ./api-gateway/target:/app
      - ./api-gateway/logs:/logs
    working_dir: /app
    command: >
      sh -c "
        echo '等待数据库完全启动...' &&
        sleep 30 &&
        echo '启动Spring Boot应用...' &&
        java -Dfile.encoding=UTF-8 -jar api-gateway-1.0-SNAPSHOT.jar --spring.profiles.active=production
      "
    environment:
      - SPRING_PROFILES_ACTIVE=production,docker
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
      - SPRING_DATASOURCE_PRIMARY_URL=***********************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_PRIMARY_USERNAME=root
      - SPRING_DATASOURCE_PRIMARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_SECONDARY_URL=***************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_SECONDARY_USERNAME=root
      - SPRING_DATASOURCE_SECONDARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_USER_SYSTEM_URL=*************************************************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USER_SYSTEM_USERNAME=root
      - SPRING_DATASOURCE_USER_SYSTEM_PASSWORD=Zlb&198838
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx Web服务器
  nginx:
    image: nginx:alpine
    platform: linux/amd64
    container_name: financial-nginx
    pull_policy: never
    ports:
      - "80:80"
    volumes:
      - ./FinancialSystem-web/build:/usr/share/nginx/html:ro
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      backend:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local

networks:
  default:
    name: financial-network
    driver: bridge