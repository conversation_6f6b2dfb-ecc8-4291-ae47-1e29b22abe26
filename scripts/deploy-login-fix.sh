#!/bin/bash

# =====================================================
# 本地修复提交和Linux部署脚本
# 修复登录接口403错误
# =====================================================

set -e

echo "=========================================="
echo "登录接口403错误修复 - 本地提交和部署"
echo "时间: $(date)"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Git状态
check_git_status() {
    log_info "检查Git状态..."
    
    if [ -n "$(git status --porcelain)" ]; then
        log_info "发现未提交的更改:"
        git status --short
    else
        log_info "工作目录干净"
    fi
}

# 提交本地更改
commit_changes() {
    log_info "提交本地更改..."
    
    # 添加修改的文件
    git add nginx.conf
    git add init-db.sql
    git add api-gateway/src/main/java/com/laoshu198838/config/SecurityConfig.java
    git add api-gateway/src/main/java/com/laoshu198838/controller/AuthController.java
    git add docs/operations/linux-compatibility-fixes.md
    git add scripts/fix-login-403-error.sh
    git add scripts/deploy-login-fix.sh
    
    # 提交更改
    git commit -m "fix: 修复登录接口403错误

- 修复nginx.conf中的CORS配置冲突，移除重复的CORS头设置
- 完善init-db.sql，添加user_system数据库表结构和默认用户数据
- 在SecurityConfig和AuthController中添加调试日志
- 更新linux-compatibility-fixes.md文档，记录修复步骤
- 添加自动化修复脚本

修复内容:
1. CORS配置冲突: Nginx和Spring Security的CORS配置冲突导致403
2. 数据库初始化不完整: 缺少用户表和默认数据
3. 调试信息不足: 添加详细的调试日志

测试账号: laoshu198838 / Zlb&198838"
    
    log_success "本地更改已提交"
}

# 推送到远程仓库
push_changes() {
    log_info "推送更改到远程仓库..."
    
    # 推送到origin
    git push origin main
    
    log_success "更改已推送到远程仓库"
}

# SSH到Linux服务器并执行修复
deploy_to_linux() {
    log_info "连接到Linux服务器并执行修复..."
    
    # Linux服务器信息
    SERVER="admin@**********"
    PROJECT_DIR="/opt/FinancialSystem"
    
    # 创建远程执行脚本
    cat > /tmp/remote_fix.sh << 'EOF'
#!/bin/bash
set -e

echo "=========================================="
echo "Linux服务器端修复执行"
echo "时间: $(date)"
echo "=========================================="

# 进入项目目录
cd /opt/FinancialSystem

# 拉取最新代码
echo "[INFO] 拉取最新代码..."
git pull origin main

# 给脚本执行权限
chmod +x scripts/fix-login-403-error.sh

# 执行修复脚本
echo "[INFO] 执行修复脚本..."
./scripts/fix-login-403-error.sh

echo "[SUCCESS] 修复完成！"
EOF

    # 复制脚本到服务器并执行
    scp /tmp/remote_fix.sh $SERVER:/tmp/
    ssh $SERVER "chmod +x /tmp/remote_fix.sh && /tmp/remote_fix.sh"
    
    # 清理临时文件
    rm /tmp/remote_fix.sh
    
    log_success "Linux服务器修复完成"
}

# 验证部署结果
verify_deployment() {
    log_info "验证部署结果..."
    
    # 测试前端访问
    if curl -f http://**********/ >/dev/null 2>&1; then
        log_success "前端服务可访问"
    else
        log_warning "前端服务不可访问"
    fi
    
    # 测试登录接口
    RESPONSE=$(curl -s -w "%{http_code}" -X POST http://**********/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"username":"laoshu198838","password":"Zlb&198838"}' \
        -o /tmp/verify_response.json 2>/dev/null || echo "000")
    
    HTTP_CODE="${RESPONSE: -3}"
    
    if [ "$HTTP_CODE" = "200" ]; then
        log_success "登录接口测试成功 (HTTP $HTTP_CODE)"
        if command -v jq >/dev/null 2>&1; then
            cat /tmp/verify_response.json | jq .
        else
            cat /tmp/verify_response.json
        fi
    else
        log_error "登录接口测试失败 (HTTP $HTTP_CODE)"
        cat /tmp/verify_response.json 2>/dev/null || echo "无响应内容"
    fi
    
    # 清理临时文件
    rm -f /tmp/verify_response.json
}

# 显示部署总结
show_summary() {
    echo "=========================================="
    log_success "部署总结"
    echo "=========================================="
    
    log_info "修复内容:"
    echo "  1. ✅ 修复CORS配置冲突"
    echo "  2. ✅ 完善数据库初始化"
    echo "  3. ✅ 添加调试日志"
    echo "  4. ✅ 更新文档记录"
    echo "  5. ✅ 创建自动化脚本"
    
    echo ""
    log_info "访问信息:"
    echo "  前端地址: http://**********"
    echo "  API地址:  http://**********/api"
    echo "  测试账号: laoshu198838"
    echo "  测试密码: Zlb&198838"
    
    echo ""
    log_info "后续操作:"
    echo "  1. 在浏览器中访问 http://**********"
    echo "  2. 使用测试账号登录验证功能"
    echo "  3. 如有问题，查看服务器日志: ssh admin@********** 'docker-compose logs -f'"
    
    echo "=========================================="
}

# 主函数
main() {
    log_info "开始执行修复和部署流程..."
    
    # 检查Git状态
    check_git_status
    
    # 提交更改
    commit_changes
    
    # 推送更改
    push_changes
    
    # 部署到Linux
    deploy_to_linux
    
    # 等待服务稳定
    log_info "等待服务稳定..."
    sleep 10
    
    # 验证部署
    verify_deployment
    
    # 显示总结
    show_summary
    
    log_success "修复和部署流程完成！"
}

# 执行主函数
main "$@"
