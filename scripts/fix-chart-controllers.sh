#!/bin/bash

# 修复Chart.js控制器注册问题的脚本
# 用于解决Chart.js v4需要注册LineController的兼容性问题

echo "🔧 开始修复Chart.js控制器注册问题..."
echo "📅 执行时间: $(date)"
echo

# 定义需要修复的文件列表
FILES=(
    "FinancialSystem-web/src/examples/Charts/LineCharts/DefaultLineChart/index.js"
    "FinancialSystem-web/src/examples/Charts/LineCharts/ReportsLineChart/index.js"
    "FinancialSystem-web/src/examples/Charts/LineCharts/GradientLineChart/index.js"
    "FinancialSystem-web/src/examples/Charts/LineCharts/ProgressLineChart/index.js"
)

# 统计变量
TOTAL_FILES=${#FILES[@]}
FIXED_FILES=0
SKIPPED_FILES=0
ERROR_FILES=0

echo "📋 需要检查的文件数量: $TOTAL_FILES"
echo

# 修复每个文件
for file in "${FILES[@]}"; do
    echo "🔍 检查文件: $file"
    
    if [ -f "$file" ]; then
        # 备份原文件
        backup_file="$file.backup-$(date +%Y%m%d-%H%M%S)"
        cp "$file" "$backup_file"
        echo "  💾 已创建备份: $(basename $backup_file)"
        
        # 检查是否已经包含LineController
        if ! grep -q "LineController" "$file"; then
            # 检查文件是否包含Chart.js导入
            if grep -q "from 'chart.js'" "$file"; then
                # 添加LineController到import语句
                sed -i '/LineElement,/a\  LineController,' "$file"
                echo "  ➕ 已添加LineController到import语句"
                
                # 添加LineController到register语句
                if grep -q "ChartJS.register" "$file"; then
                    sed -i '/LineElement,/a\  LineController,' "$file"
                    echo "  ➕ 已添加LineController到register语句"
                fi
                
                # 移除可能的重复LineController
                sed -i '/LineController,/{N;s/LineController,\n  LineController,/LineController,/;}' "$file"
                
                echo "  ✅ 文件修复完成"
                ((FIXED_FILES++))
            else
                echo "  ⚠️  文件不包含Chart.js导入，跳过修复"
                rm "$backup_file"  # 删除不必要的备份
                ((SKIPPED_FILES++))
            fi
        else
            echo "  ℹ️  文件已包含LineController，跳过修复"
            rm "$backup_file"  # 删除不必要的备份
            ((SKIPPED_FILES++))
        fi
    else
        echo "  ❌ 文件不存在: $file"
        ((ERROR_FILES++))
    fi
    echo
done

# 输出修复结果统计
echo "📊 修复结果统计:"
echo "  ✅ 成功修复: $FIXED_FILES 个文件"
echo "  ⏭️  跳过处理: $SKIPPED_FILES 个文件"
echo "  ❌ 错误文件: $ERROR_FILES 个文件"
echo "  📁 总计文件: $TOTAL_FILES 个文件"
echo

if [ $FIXED_FILES -gt 0 ]; then
    echo "🎉 Chart.js控制器修复完成！"
    echo "📝 建议接下来执行以下步骤:"
    echo "   1. cd FinancialSystem-web"
    echo "   2. npm run build"
    echo "   3. 测试图表功能是否正常"
    echo
    echo "🔄 如需回滚，可使用以下命令:"
    echo "   find FinancialSystem-web/src/examples/Charts/LineCharts/ -name '*.backup*' | \\"
    echo "   while read backup; do"
    echo "       original=\${backup%%.backup*}"
    echo "       cp \"\$backup\" \"\$original\""
    echo "   done"
else
    echo "ℹ️  没有文件需要修复，所有文件都已是最新状态。"
fi

echo
echo "🏁 脚本执行完成！"
