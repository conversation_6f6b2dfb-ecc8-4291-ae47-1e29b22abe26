#!/bin/bash

# 直接数据库同步方案
# 解决文件传输问题，使用直接管道传输

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

# 需要同步的数据库
DATABASES=("overdue_debt_db" "kingdee" "user_system")

echo -e "${PURPLE}🔄==================== 直接数据库同步方案 ====================${NC}"
echo -e "${YELLOW}UltraThink解决方案：通过管道直接传输数据库${NC}"
echo ""

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] INFO${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] SUCCESS${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR${NC} $1"
}

# 函数：直接同步数据库
sync_database_direct() {
    local db_name=$1
    
    log_info "🔄 开始直接同步数据库: $db_name"
    
    # 获取本地数据库大小
    local db_size=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' 
            FROM information_schema.tables 
            WHERE table_schema='$db_name';" -s -N 2>/dev/null || echo "0.0")
    
    log_info "数据库 $db_name 大小: ${db_size}MB"
    
    # 创建临时备份文件
    local temp_file="/tmp/${db_name}_direct_sync_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "📦 生成数据库备份..."
    mysqldump -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        --add-drop-database \
        --databases "$db_name" > "$temp_file"
    
    if [ ! -s "$temp_file" ]; then
        log_error "❌ 数据库 $db_name 备份生成失败"
        rm -f "$temp_file"
        return 1
    fi
    
    local file_size=$(du -h "$temp_file" | cut -f1)
    log_success "✅ 备份文件生成完成 (大小: $file_size)"
    
    log_info "📤 传输并恢复数据库到Linux..."
    
    # 使用cat通过SSH管道直接传输并执行
    if cat "$temp_file" | ssh "$LINUX_SERVER" "cat > /tmp/$(basename $temp_file) && docker cp /tmp/$(basename $temp_file) $MYSQL_CONTAINER:/tmp/ && docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' --default-character-set=utf8mb4 < /tmp/$(basename $temp_file) && rm -f /tmp/$(basename $temp_file) && docker exec $MYSQL_CONTAINER rm -f /tmp/$(basename $temp_file)"; then
        log_success "✅ 数据库 $db_name 同步成功"
        rm -f "$temp_file"
        return 0
    else
        log_error "❌ 数据库 $db_name 同步失败"
        rm -f "$temp_file"
        return 1
    fi
}

# 函数：验证关键数据
verify_key_data() {
    log_info "🔍 验证关键数据同步结果..."
    
    echo ""
    echo -e "${BLUE}📊 关键数据对比${NC}"
    echo "========================================"
    
    # 验证overdue_debt_db的2025年数据
    local_2025=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;" -s -N 2>/dev/null || echo "0")
    
    linux_2025=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e 'SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;' -s -N" 2>/dev/null || echo "0")
    
    echo "🗓️  2025年新增表记录: 本地($local_2025) vs Linux($linux_2025)"
    
    # 验证3月份数据
    local_march=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0);" -s -N 2>/dev/null || echo "0")
    
    linux_march=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e 'SELECT COUNT(*) FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0);' -s -N" 2>/dev/null || echo "0")
    
    echo "📈 3月份有效数据: 本地($local_march) vs Linux($linux_march)"
    
    # 验证用户系统数据
    local_users=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -D user_system --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM users;" -s -N 2>/dev/null || echo "0")
    
    linux_users=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D user_system --default-character-set=utf8mb4 \
        -e 'SELECT COUNT(*) FROM users;' -s -N" 2>/dev/null || echo "0")
    
    echo "👥 用户数据: 本地($local_users) vs Linux($linux_users)"
    
    echo "========================================"
    
    if [ "$local_2025" -eq "$linux_2025" ] && [ "$local_march" -eq "$linux_march" ] && [ "$local_users" -eq "$linux_users" ]; then
        log_success "🎉 关键数据验证通过，同步成功！"
        return 0
    else
        log_error "⚠️ 关键数据验证失败，存在不一致"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${PURPLE}🚀 开始直接数据库同步...${NC}"
    echo ""
    
    local sync_success=true
    local success_count=0
    
    # 逐个同步数据库
    for db_name in "${DATABASES[@]}"; do
        echo -e "${YELLOW}--- 同步数据库: $db_name ---${NC}"
        
        if sync_database_direct "$db_name"; then
            ((success_count++))
        else
            sync_success=false
            log_error "数据库 $db_name 同步失败"
        fi
        
        echo ""
    done
    
    echo -e "${BLUE}📊 同步统计${NC}"
    echo "• 总数据库数: ${#DATABASES[@]}"
    echo "• 成功同步: $success_count"
    echo "• 失败数量: $((${#DATABASES[@]} - success_count))"
    
    if [ "$sync_success" = true ]; then
        echo ""
        log_success "🎉 所有数据库同步完成！"
        
        # 验证关键数据
        echo ""
        if verify_key_data; then
            # 重启应用服务
            echo ""
            log_info "🔄 重启Linux应用服务..."
            ssh "$LINUX_SERVER" "docker restart financial-backend"
            
            log_info "⏳ 等待应用服务启动..."
            sleep 35
            
            echo ""
            echo -e "${GREEN}🎉 UltraThink数据库同步解决方案执行完成！${NC}"
            echo ""
            echo -e "${YELLOW}📋 验证步骤:${NC}"
            echo "1. 清除浏览器缓存"
            echo "2. 刷新Linux端数据更新观测平台页面"
            echo "3. 选择2025年3月查看数据"
            echo "4. 对比本地和Linux端显示是否一致"
            echo ""
            echo -e "${YELLOW}💡 如果仍有差异，可能需要:${NC}"
            echo "• 重新登录系统"
            echo "• 检查前端缓存"
            echo "• 验证数据库连接配置"
            
        else
            log_error "关键数据验证失败，需要进一步排查"
        fi
    else
        log_error "部分数据库同步失败，请检查错误信息"
        exit 1
    fi
    
    echo ""
    echo -e "${PURPLE}==================== 同步完成 ====================${NC}"
}

# 执行主函数
main "$@"