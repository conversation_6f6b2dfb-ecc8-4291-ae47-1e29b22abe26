#!/bin/bash

# ===================================================================
# MySQL全数据库双向同步自动化配置脚本
# ===================================================================
# 功能：实现本地MySQL与Linux Docker MySQL之间的全数据库双向同步
# 支持：自动发现新数据库、冲突处理、监控告警
# 作者：SuperClaude
# 版本：1.0
# ===================================================================

set -e

# 配置变量
LOCAL_MYSQL_HOST="localhost"
LINUX_MYSQL_HOST="**********"
MYSQL_PORT="3306"
MYSQL_ROOT_PASSWORD="Zlb&198838"
REPL_USER="repl_universal"
REPL_PASSWORD="Zlb&198838"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查MySQL连接
check_mysql_connection() {
    local host=$1
    local desc=$2
    
    log_step "检查${desc}MySQL连接..."
    
    if mysql -h"$host" -uroot -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1" &>/dev/null; then
        log_info "${desc}MySQL连接成功"
        return 0
    else
        log_error "${desc}MySQL连接失败"
        return 1
    fi
}

# 获取所有数据库列表（排除系统数据库）
get_databases() {
    local host=$1
    mysql -h"$host" -uroot -p"$MYSQL_ROOT_PASSWORD" -e "
        SELECT SCHEMA_NAME 
        FROM information_schema.SCHEMATA 
        WHERE SCHEMA_NAME NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
    " --skip-column-names
}

# 创建复制用户
create_replication_user() {
    local host=$1
    local desc=$2
    
    log_step "在${desc}创建复制用户..."
    
    mysql -h"$host" -uroot -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 删除旧的复制用户（如果存在）
DROP USER IF EXISTS '${REPL_USER}'@'%';

-- 创建新的复制用户
CREATE USER '${REPL_USER}'@'%' IDENTIFIED WITH mysql_native_password BY '${REPL_PASSWORD}';

-- 授予复制权限
GRANT REPLICATION SLAVE ON *.* TO '${REPL_USER}'@'%';
GRANT SELECT ON *.* TO '${REPL_USER}'@'%';
GRANT RELOAD ON *.* TO '${REPL_USER}'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 验证用户创建
SELECT User, Host, plugin FROM mysql.user WHERE User = '${REPL_USER}';
EOF

    log_info "${desc}复制用户创建完成"
}

# 配置MySQL服务器参数
configure_mysql_server() {
    local host=$1
    local server_id=$2
    local increment_offset=$3
    local desc=$4
    
    log_step "配置${desc}MySQL服务器参数..."
    
    mysql -h"$host" -uroot -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 设置服务器ID
SET GLOBAL server_id = ${server_id};

-- 启用GTID模式
SET GLOBAL gtid_mode = ON;
SET GLOBAL enforce_gtid_consistency = ON;

-- 配置AUTO_INCREMENT避免主键冲突
SET GLOBAL auto_increment_increment = 2;
SET GLOBAL auto_increment_offset = ${increment_offset};

-- 配置二进制日志
SET GLOBAL log_bin = ON;
SET GLOBAL binlog_format = 'ROW';
SET GLOBAL sync_binlog = 1;

-- 优化复制性能
SET GLOBAL slave_parallel_workers = 4;
SET GLOBAL slave_parallel_type = 'LOGICAL_CLOCK';
SET GLOBAL slave_preserve_commit_order = ON;

-- 显示当前配置
SELECT 
    @@server_id as server_id,
    @@gtid_mode as gtid_mode,
    @@auto_increment_increment as auto_increment_increment,
    @@auto_increment_offset as auto_increment_offset,
    @@log_bin as log_bin,
    @@binlog_format as binlog_format;
EOF

    log_info "${desc}MySQL服务器参数配置完成"
}

# 配置主从复制关系
setup_replication() {
    local slave_host=$1
    local master_host=$2
    local desc=$3
    
    log_step "配置${desc}的复制关系..."
    
    # 停止现有复制
    mysql -h"$slave_host" -uroot -p"$MYSQL_ROOT_PASSWORD" << EOF
STOP SLAVE;
RESET SLAVE ALL;
EOF

    # 配置新的复制关系
    mysql -h"$slave_host" -uroot -p"$MYSQL_ROOT_PASSWORD" << EOF
CHANGE MASTER TO
    MASTER_HOST='${master_host}',
    MASTER_USER='${REPL_USER}',
    MASTER_PASSWORD='${REPL_PASSWORD}',
    MASTER_PORT=${MYSQL_PORT},
    MASTER_AUTO_POSITION=1;

START SLAVE;
EOF

    log_info "${desc}复制关系配置完成"
}

# 验证复制状态
verify_replication() {
    local host=$1
    local desc=$2
    
    log_step "验证${desc}复制状态..."
    
    local result=$(mysql -h"$host" -uroot -p"$MYSQL_ROOT_PASSWORD" -e "
        SELECT 
            Slave_IO_Running,
            Slave_SQL_Running,
            Seconds_Behind_Master,
            Last_Error
        FROM performance_schema.replication_connection_status rcs
        JOIN performance_schema.replication_applier_status_by_worker rasw 
        ON rcs.channel_name = rasw.channel_name
        LIMIT 1;
    " --skip-column-names 2>/dev/null)
    
    if [[ -n "$result" ]]; then
        echo "$result" | while read io_running sql_running seconds_behind last_error; do
            log_info "${desc}IO线程: $io_running"
            log_info "${desc}SQL线程: $sql_running"
            log_info "${desc}延迟: ${seconds_behind}秒"
            
            if [[ -n "$last_error" ]]; then
                log_warn "${desc}错误: $last_error"
            fi
        done
    else
        log_warn "${desc}无法获取复制状态，可能未配置复制"
    fi
}

# 测试数据同步
test_sync() {
    local db_name="test_sync_db"
    local table_name="sync_test"
    local test_data="sync_test_$(date +%s)"
    
    log_step "测试数据同步功能..."
    
    # 在本地MySQL创建测试数据库和表
    mysql -h"$LOCAL_MYSQL_HOST" -uroot -p"$MYSQL_ROOT_PASSWORD" << EOF
CREATE DATABASE IF NOT EXISTS ${db_name};
USE ${db_name};

CREATE TABLE IF NOT EXISTS ${table_name} (
    id INT AUTO_INCREMENT PRIMARY KEY,
    test_data VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO ${table_name} (test_data) VALUES ('${test_data}');
EOF

    log_info "在本地MySQL插入测试数据: $test_data"
    
    # 等待同步
    sleep 5
    
    # 检查Linux MySQL是否同步
    local synced_data=$(mysql -h"$LINUX_MYSQL_HOST" -uroot -p"$MYSQL_ROOT_PASSWORD" \
        -e "SELECT test_data FROM ${db_name}.${table_name} WHERE test_data='${test_data}'" \
        --skip-column-names 2>/dev/null || echo "")
    
    if [[ "$synced_data" == "$test_data" ]]; then
        log_info "✅ 本地 → Linux 同步成功"
    else
        log_error "❌ 本地 → Linux 同步失败"
    fi
    
    # 测试反向同步
    local reverse_test_data="reverse_sync_test_$(date +%s)"
    
    mysql -h"$LINUX_MYSQL_HOST" -uroot -p"$MYSQL_ROOT_PASSWORD" << EOF
USE ${db_name};
INSERT INTO ${table_name} (test_data) VALUES ('${reverse_test_data}');
EOF

    log_info "在Linux MySQL插入测试数据: $reverse_test_data"
    
    # 等待同步
    sleep 5
    
    # 检查本地MySQL是否同步
    local reverse_synced_data=$(mysql -h"$LOCAL_MYSQL_HOST" -uroot -p"$MYSQL_ROOT_PASSWORD" \
        -e "SELECT test_data FROM ${db_name}.${table_name} WHERE test_data='${reverse_test_data}'" \
        --skip-column-names 2>/dev/null || echo "")
    
    if [[ "$reverse_synced_data" == "$reverse_test_data" ]]; then
        log_info "✅ Linux → 本地 同步成功"
    else
        log_error "❌ Linux → 本地 同步失败"
    fi
    
    # 清理测试数据
    mysql -h"$LOCAL_MYSQL_HOST" -uroot -p"$MYSQL_ROOT_PASSWORD" -e "DROP DATABASE ${db_name};" 2>/dev/null || true
}

# 生成数据库发现脚本
generate_database_discovery_script() {
    log_step "生成数据库自动发现脚本..."
    
    cat > "${SCRIPT_DIR}/database-auto-discovery.sh" << 'EOF'
#!/bin/bash

# 数据库自动发现和同步配置脚本
# 定期运行此脚本以发现新数据库并自动配置同步

LOCAL_HOST="localhost"
LINUX_HOST="**********"
MYSQL_PASSWORD="Zlb&198838"
REPL_USER="repl_universal"

# 获取数据库列表
get_databases() {
    local host=$1
    mysql -h"$host" -uroot -p"$MYSQL_PASSWORD" -e "
        SELECT SCHEMA_NAME 
        FROM information_schema.SCHEMATA 
        WHERE SCHEMA_NAME NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
    " --skip-column-names 2>/dev/null
}

# 检查数据库是否存在于目标服务器
database_exists() {
    local host=$1
    local db_name=$2
    local count=$(mysql -h"$host" -uroot -p"$MYSQL_PASSWORD" -e "
        SELECT COUNT(*) 
        FROM information_schema.SCHEMATA 
        WHERE SCHEMA_NAME='$db_name'
    " --skip-column-names 2>/dev/null)
    [[ "$count" -eq 1 ]]
}

# 创建数据库
create_database() {
    local host=$1
    local db_name=$2
    mysql -h"$host" -uroot -p"$MYSQL_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$db_name\`" 2>/dev/null
}

# 主逻辑
main() {
    echo "开始数据库自动发现..."
    
    # 获取本地数据库列表
    local_dbs=$(get_databases "$LOCAL_HOST")
    
    # 获取Linux数据库列表
    linux_dbs=$(get_databases "$LINUX_HOST")
    
    # 检查本地新增的数据库
    for db in $local_dbs; do
        if ! database_exists "$LINUX_HOST" "$db"; then
            echo "发现新数据库: $db (本地 → Linux)"
            create_database "$LINUX_HOST" "$db"
            echo "已在Linux服务器创建数据库: $db"
        fi
    done
    
    # 检查Linux新增的数据库
    for db in $linux_dbs; do
        if ! database_exists "$LOCAL_HOST" "$db"; then
            echo "发现新数据库: $db (Linux → 本地)"
            create_database "$LOCAL_HOST" "$db"
            echo "已在本地服务器创建数据库: $db"
        fi
    done
    
    echo "数据库自动发现完成"
}

main "$@"
EOF

    chmod +x "${SCRIPT_DIR}/database-auto-discovery.sh"
    log_info "数据库自动发现脚本已生成: ${SCRIPT_DIR}/database-auto-discovery.sh"
}

# 生成监控脚本
generate_monitoring_script() {
    log_step "生成同步监控脚本..."
    
    cat > "${SCRIPT_DIR}/sync-monitor.sh" << 'EOF'
#!/bin/bash

# MySQL双向同步监控脚本
# 监控复制状态、数据一致性、告警处理

LOCAL_HOST="localhost"
LINUX_HOST="**********"
MYSQL_PASSWORD="Zlb&198838"
LOG_FILE="/tmp/mysql_sync_monitor.log"

# 检查复制状态
check_replication_status() {
    local host=$1
    local desc=$2
    
    echo "[$desc] 检查复制状态..." >> "$LOG_FILE"
    
    local status=$(mysql -h"$host" -uroot -p"$MYSQL_PASSWORD" -e "
        SHOW SLAVE STATUS\G
    " 2>/dev/null | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_Error")
    
    echo "$status" >> "$LOG_FILE"
    
    # 检查关键指标
    local io_running=$(echo "$status" | grep "Slave_IO_Running" | awk '{print $2}')
    local sql_running=$(echo "$status" | grep "Slave_SQL_Running" | awk '{print $2}')
    local lag=$(echo "$status" | grep "Seconds_Behind_Master" | awk '{print $2}')
    
    # 告警条件
    if [[ "$io_running" != "Yes" ]] || [[ "$sql_running" != "Yes" ]]; then
        echo "ALERT: [$desc] 复制线程停止! IO:$io_running SQL:$sql_running" >> "$LOG_FILE"
        return 1
    fi
    
    if [[ "$lag" != "NULL" ]] && [[ "$lag" -gt 30 ]]; then
        echo "WARNING: [$desc] 复制延迟过高: ${lag}秒" >> "$LOG_FILE"
        return 2
    fi
    
    echo "OK: [$desc] 复制状态正常, 延迟: ${lag}秒" >> "$LOG_FILE"
    return 0
}

# 主监控循环
main() {
    echo "$(date): 开始MySQL双向同步监控" >> "$LOG_FILE"
    
    while true; do
        echo "$(date): 执行监控检查" >> "$LOG_FILE"
        
        # 检查本地MySQL复制状态
        check_replication_status "$LOCAL_HOST" "本地MySQL"
        local_status=$?
        
        # 检查Linux MySQL复制状态
        check_replication_status "$LINUX_HOST" "Linux MySQL"
        linux_status=$?
        
        # 如果有告警，可以在这里添加通知逻辑
        if [[ $local_status -ne 0 ]] || [[ $linux_status -ne 0 ]]; then
            echo "$(date): 发现同步问题，请检查日志" >> "$LOG_FILE"
            # 这里可以添加邮件/钉钉通知
        fi
        
        # 运行数据库自动发现
        bash "$(dirname "$0")/database-auto-discovery.sh" >> "$LOG_FILE" 2>&1
        
        # 等待下次检查（每5分钟）
        sleep 300
    done
}

# 支持一次性检查模式
if [[ "$1" == "check" ]]; then
    check_replication_status "$LOCAL_HOST" "本地MySQL"
    check_replication_status "$LINUX_HOST" "Linux MySQL"
    exit 0
fi

main "$@"
EOF

    chmod +x "${SCRIPT_DIR}/sync-monitor.sh"
    log_info "同步监控脚本已生成: ${SCRIPT_DIR}/sync-monitor.sh"
}

# 主函数
main() {
    echo "========================================"
    echo "MySQL全数据库双向同步配置脚本"
    echo "========================================"
    echo ""
    
    # 获取脚本目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 检查参数
    case "${1:-setup}" in
        "setup")
            log_info "开始全数据库双向同步配置..."
            
            # 检查连接
            check_mysql_connection "$LOCAL_MYSQL_HOST" "本地"
            check_mysql_connection "$LINUX_MYSQL_HOST" "Linux"
            
            # 创建复制用户
            create_replication_user "$LOCAL_MYSQL_HOST" "本地MySQL"
            create_replication_user "$LINUX_MYSQL_HOST" "Linux MySQL"
            
            # 配置服务器参数
            configure_mysql_server "$LOCAL_MYSQL_HOST" "1" "1" "本地"
            configure_mysql_server "$LINUX_MYSQL_HOST" "2" "2" "Linux"
            
            # 配置双向复制
            setup_replication "$LOCAL_MYSQL_HOST" "$LINUX_MYSQL_HOST" "本地作为Linux的从服务器"
            setup_replication "$LINUX_MYSQL_HOST" "$LOCAL_MYSQL_HOST" "Linux作为本地的从服务器"
            
            # 等待复制启动
            sleep 10
            
            # 验证复制状态
            verify_replication "$LOCAL_MYSQL_HOST" "本地MySQL"
            verify_replication "$LINUX_MYSQL_HOST" "Linux MySQL"
            
            # 测试同步
            test_sync
            
            # 生成辅助脚本
            generate_database_discovery_script
            generate_monitoring_script
            
            log_info "🎉 全数据库双向同步配置完成！"
            echo ""
            echo "后续操作："
            echo "1. 启动监控: bash ${SCRIPT_DIR}/sync-monitor.sh &"
            echo "2. 手动检查: bash ${SCRIPT_DIR}/sync-monitor.sh check"
            echo "3. 数据库发现: bash ${SCRIPT_DIR}/database-auto-discovery.sh"
            ;;
            
        "monitor")
            exec bash "${SCRIPT_DIR}/sync-monitor.sh"
            ;;
            
        "discover")
            exec bash "${SCRIPT_DIR}/database-auto-discovery.sh"
            ;;
            
        "status")
            bash "${SCRIPT_DIR}/sync-monitor.sh" check
            ;;
            
        "help"|"-h"|"--help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  setup     - 配置全数据库双向同步（默认）"
            echo "  monitor   - 启动监控服务"
            echo "  discover  - 运行数据库自动发现"
            echo "  status    - 检查当前同步状态"
            echo "  help      - 显示此帮助信息"
            ;;
            
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

main "$@"