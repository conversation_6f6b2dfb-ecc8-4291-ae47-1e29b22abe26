#!/bin/bash

# ===================================================================
# MySQL认证修复脚本
# 解决caching_sha2_password导致的复制失败问题
# 
# 功能：
# 1. 在Linux服务器创建使用mysql_native_password的复制用户
# 2. 在本地MySQL重新配置复制连接
# 3. 验证复制状态并报告结果
# 
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 配置变量
LINUX_SERVER="10.25.1.85"
LINUX_USER="admin"
MYSQL_ROOT_PASS="Zlb&198838"
REPL_USER="repl_native"
REPL_PASS="Zlb&198838"
LOCAL_MYSQL_USER="root"
LOCAL_MYSQL_PASS="Zlb&198838"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查必要工具..."
    
    if ! command -v ssh &> /dev/null; then
        log_error "SSH未安装，请先安装SSH客户端"
        exit 1
    fi
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装，请先安装MySQL客户端"
        exit 1
    fi
    
    log_success "所有必要工具已就绪"
}

# 创建Linux服务器上的复制用户
create_replication_user() {
    log_info "在Linux服务器创建复制用户..."
    
    # SQL命令
    local sql_commands="
-- 删除已存在的复制用户（如果存在）
DROP USER IF EXISTS '${REPL_USER}'@'%';

-- 创建使用mysql_native_password的复制用户
CREATE USER '${REPL_USER}'@'%' IDENTIFIED WITH mysql_native_password BY '${REPL_PASS}';

-- 授予复制权限
GRANT REPLICATION SLAVE ON *.* TO '${REPL_USER}'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 验证用户创建成功
SELECT User, Host, plugin FROM mysql.user WHERE User = '${REPL_USER}';
"
    
    # 执行SQL命令
    if ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -e \"${sql_commands}\"" 2>/dev/null; then
        log_success "复制用户创建成功"
    else
        log_error "创建复制用户失败"
        return 1
    fi
}

# 获取Linux服务器的Master状态
get_master_status() {
    log_info "获取Linux服务器的Master状态..."
    
    local master_status=$(ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -e 'SHOW MASTER STATUS\G'" 2>/dev/null)
    
    MASTER_LOG_FILE=$(echo "$master_status" | grep -i "File:" | awk '{print $2}')
    MASTER_LOG_POS=$(echo "$master_status" | grep -i "Position:" | awk '{print $2}')
    
    if [[ -z "$MASTER_LOG_FILE" || -z "$MASTER_LOG_POS" ]]; then
        log_error "无法获取Master状态"
        return 1
    fi
    
    log_success "Master状态: File=$MASTER_LOG_FILE, Position=$MASTER_LOG_POS"
}

# 配置本地MySQL复制
configure_local_replication() {
    log_info "配置本地MySQL复制..."
    
    # 停止当前的复制
    mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "STOP SLAVE;" 2>/dev/null || true
    
    # 重置复制状态
    mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "RESET SLAVE ALL;" 2>/dev/null || true
    
    # 配置新的复制
    local change_master_sql="
CHANGE MASTER TO 
    MASTER_HOST='${LINUX_SERVER}',
    MASTER_USER='${REPL_USER}',
    MASTER_PASSWORD='${REPL_PASS}',
    MASTER_PORT=3306,
    MASTER_LOG_FILE='${MASTER_LOG_FILE}',
    MASTER_LOG_POS=${MASTER_LOG_POS},
    MASTER_CONNECT_RETRY=10,
    MASTER_RETRY_COUNT=3;
"
    
    if mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "${change_master_sql}" 2>/dev/null; then
        log_success "复制配置成功"
    else
        log_error "复制配置失败"
        return 1
    fi
    
    # 启动复制
    if mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "START SLAVE;" 2>/dev/null; then
        log_success "复制启动成功"
    else
        log_error "复制启动失败"
        return 1
    fi
}

# 验证复制状态
verify_replication_status() {
    log_info "验证复制状态..."
    
    # 等待几秒让复制建立连接
    sleep 5
    
    # 获取复制状态
    local slave_status=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null)
    
    # 提取关键信息
    local io_running=$(echo "$slave_status" | grep "Slave_IO_Running:" | awk '{print $2}')
    local sql_running=$(echo "$slave_status" | grep "Slave_SQL_Running:" | awk '{print $2}')
    local last_error=$(echo "$slave_status" | grep "Last_Error:" | cut -d':' -f2-)
    local seconds_behind=$(echo "$slave_status" | grep "Seconds_Behind_Master:" | awk '{print $2}')
    
    # 显示状态
    echo -e "\n${BLUE}========== 复制状态报告 ==========${NC}"
    echo -e "IO线程状态: $([ "$io_running" = "Yes" ] && echo -e "${GREEN}$io_running${NC}" || echo -e "${RED}$io_running${NC}")"
    echo -e "SQL线程状态: $([ "$sql_running" = "Yes" ] && echo -e "${GREEN}$sql_running${NC}" || echo -e "${RED}$sql_running${NC}")"
    echo -e "复制延迟: ${YELLOW}$seconds_behind${NC} 秒"
    
    if [[ -n "$last_error" && "$last_error" != " " ]]; then
        echo -e "最后错误: ${RED}$last_error${NC}"
    fi
    
    echo -e "${BLUE}===================================${NC}\n"
    
    # 判断复制是否成功
    if [[ "$io_running" = "Yes" && "$sql_running" = "Yes" ]]; then
        log_success "复制状态正常！"
        return 0
    else
        log_error "复制状态异常，请检查错误信息"
        return 1
    fi
}

# 测试数据同步
test_data_sync() {
    log_info "测试数据同步..."
    
    # 创建测试表
    local test_table="replication_test_$(date +%s)"
    
    # 在Linux服务器创建测试数据
    ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -e \"
        USE overdue_debt_db;
        CREATE TABLE IF NOT EXISTS ${test_table} (
            id INT PRIMARY KEY AUTO_INCREMENT,
            test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            test_data VARCHAR(100)
        );
        INSERT INTO ${test_table} (test_data) VALUES ('Linux Server Test Data');
    \"" 2>/dev/null
    
    # 等待同步
    log_info "等待数据同步（5秒）..."
    sleep 5
    
    # 检查本地是否同步到数据
    local sync_result=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "
        USE overdue_debt_db;
        SELECT COUNT(*) as count FROM ${test_table};
    " 2>/dev/null | grep -v "count" | tail -1)
    
    if [[ "$sync_result" -gt 0 ]]; then
        log_success "数据同步测试成功！从Linux服务器同步到本地"
        
        # 清理测试表
        ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -e \"
            USE overdue_debt_db;
            DROP TABLE ${test_table};
        \"" 2>/dev/null
    else
        log_error "数据同步测试失败"
        return 1
    fi
}

# 生成配置建议
generate_recommendations() {
    log_info "生成配置建议..."
    
    cat > /tmp/mysql_replication_recommendations.txt << EOF
MySQL复制配置建议
==================

1. 在Linux服务器 (${LINUX_SERVER}) 的 my.cnf 添加：
   [mysqld]
   # 复制相关
   server_id = 1
   log_bin = mysql-bin
   binlog_format = ROW
   sync_binlog = 1
   
   # 认证
   default_authentication_plugin = mysql_native_password
   
   # 性能
   binlog_expire_logs_seconds = 604800

2. 在本地MySQL的 my.cnf 添加：
   [mysqld]
   # 复制相关
   server_id = 2
   log_bin = mysql-bin
   binlog_format = ROW
   relay_log = relay-bin
   
   # 认证
   default_authentication_plugin = mysql_native_password
   
   # 性能
   slave_parallel_workers = 4
   slave_preserve_commit_order = ON

3. 监控命令：
   # 查看复制状态
   mysql -e "SHOW SLAVE STATUS\G"
   
   # 查看复制延迟
   mysql -e "SHOW SLAVE STATUS\G" | grep "Seconds_Behind_Master"
   
   # 查看错误
   mysql -e "SHOW SLAVE STATUS\G" | grep -E "Last_.*Error"

4. 故障恢复：
   # 如果复制中断
   STOP SLAVE;
   SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1;
   START SLAVE;
   
   # 如果需要重新同步
   STOP SLAVE;
   RESET SLAVE;
   # 重新执行 CHANGE MASTER TO ...
   START SLAVE;

EOF
    
    log_success "配置建议已保存到: /tmp/mysql_replication_recommendations.txt"
}

# 主函数
main() {
    echo -e "${BLUE}===== MySQL认证修复脚本 =====${NC}"
    echo -e "${BLUE}目标: 解决caching_sha2_password导致的复制失败${NC}\n"
    
    # 检查必要工具
    check_requirements
    
    # 创建复制用户
    if ! create_replication_user; then
        log_error "创建复制用户失败，脚本终止"
        exit 1
    fi
    
    # 获取Master状态
    if ! get_master_status; then
        log_error "获取Master状态失败，脚本终止"
        exit 1
    fi
    
    # 配置本地复制
    if ! configure_local_replication; then
        log_error "配置本地复制失败，脚本终止"
        exit 1
    fi
    
    # 验证复制状态
    if ! verify_replication_status; then
        log_warning "复制状态异常，请检查错误信息"
    fi
    
    # 测试数据同步
    if test_data_sync; then
        log_success "数据同步测试通过"
    else
        log_warning "数据同步测试失败，但复制可能仍在工作"
    fi
    
    # 生成配置建议
    generate_recommendations
    
    echo -e "\n${GREEN}===== 脚本执行完成 =====${NC}"
    echo -e "${YELLOW}提示：${NC}"
    echo -e "1. 请定期检查复制状态: mysql -e 'SHOW SLAVE STATUS\\G'"
    echo -e "2. 如遇到问题，查看建议文件: cat /tmp/mysql_replication_recommendations.txt"
    echo -e "3. 建议设置监控脚本定期检查复制健康状态"
}

# 执行主函数
main "$@"