#!/bin/bash

# ===================================================================
# 自动告警系统安装和配置脚本
# 为MySQL复制监控设置告警系统
#
# 功能：
# 1. 安装Python依赖
# 2. 生成配置文件
# 3. 配置告警渠道
# 4. 启动监控服务
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ALERT_SCRIPT="$SCRIPT_DIR/alert-system.py"
CONFIG_FILE="$SCRIPT_DIR/alert_config.json"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3未安装，请先安装Python 3"
        exit 1
    fi
    
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3未安装，请先安装pip3"
        exit 1
    fi
    
    log_success "Python环境检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "安装Python依赖..."
    
    # 检查并安装mysql-connector-python
    if ! python3 -c "import mysql.connector" 2>/dev/null; then
        log_info "安装mysql-connector-python..."
        pip3 install mysql-connector-python
    fi
    
    # 检查并安装requests
    if ! python3 -c "import requests" 2>/dev/null; then
        log_info "安装requests..."
        pip3 install requests
    fi
    
    log_success "依赖安装完成"
}

# 生成配置文件
generate_config() {
    log_info "生成告警配置文件..."
    
    python3 "$ALERT_SCRIPT" --generate-config --config "$CONFIG_FILE"
    
    log_success "配置文件已生成: $CONFIG_FILE"
}

# 配置邮件告警
configure_email() {
    echo -e "\n${CYAN}===== 配置邮件告警 =====${NC}"
    
    read -p "是否启用邮件告警？(y/N): " enable_email
    if [[ ! "$enable_email" =~ ^[Yy]$ ]]; then
        return
    fi
    
    read -p "SMTP服务器 (默认: smtp.qq.com): " smtp_server
    smtp_server=${smtp_server:-smtp.qq.com}
    
    read -p "SMTP端口 (默认: 587): " smtp_port
    smtp_port=${smtp_port:-587}
    
    read -p "发送邮箱: " email_from
    read -s -p "邮箱密码/授权码: " email_password
    echo
    
    read -p "接收邮箱 (多个用逗号分隔): " email_to
    
    # 更新配置文件
    python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)

config['email_enabled'] = True
config['smtp_server'] = '$smtp_server'
config['smtp_port'] = $smtp_port
config['smtp_user'] = '$email_from'
config['smtp_password'] = '$email_password'
config['email_from'] = '$email_from'
config['email_to'] = [email.strip() for email in '$email_to'.split(',')]

with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, indent=2)
"
    
    log_success "邮件告警配置完成"
}

# 配置企业微信告警
configure_wechat() {
    echo -e "\n${CYAN}===== 配置企业微信告警 =====${NC}"
    
    read -p "是否启用企业微信告警？(y/N): " enable_wechat
    if [[ ! "$enable_wechat" =~ ^[Yy]$ ]]; then
        return
    fi
    
    echo "请在企业微信群中添加机器人，并获取Webhook URL"
    read -p "企业微信Webhook URL: " wechat_webhook
    
    # 更新配置文件
    python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)

config['wechat_enabled'] = True
config['wechat_webhook'] = '$wechat_webhook'

with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, indent=2)
"
    
    log_success "企业微信告警配置完成"
}

# 配置钉钉告警
configure_dingtalk() {
    echo -e "\n${CYAN}===== 配置钉钉告警 =====${NC}"
    
    read -p "是否启用钉钉告警？(y/N): " enable_dingtalk
    if [[ ! "$enable_dingtalk" =~ ^[Yy]$ ]]; then
        return
    fi
    
    echo "请在钉钉群中添加自定义机器人，并获取Webhook URL"
    read -p "钉钉Webhook URL: " dingtalk_webhook
    read -p "钉钉机器人密钥 (可选): " dingtalk_secret
    
    # 更新配置文件
    python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)

config['dingtalk_enabled'] = True
config['dingtalk_webhook'] = '$dingtalk_webhook'
config['dingtalk_secret'] = '$dingtalk_secret'

with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, indent=2)
"
    
    log_success "钉钉告警配置完成"
}

# 配置告警阈值
configure_thresholds() {
    echo -e "\n${CYAN}===== 配置告警阈值 =====${NC}"
    
    read -p "复制延迟警告阈值(秒，默认60): " lag_warning
    lag_warning=${lag_warning:-60}
    
    read -p "复制延迟严重阈值(秒，默认300): " lag_critical
    lag_critical=${lag_critical:-300}
    
    read -p "监控检查间隔(秒，默认30): " check_interval
    check_interval=${check_interval:-30}
    
    # 更新配置文件
    python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)

config['lag_warning_threshold'] = $lag_warning
config['lag_critical_threshold'] = $lag_critical
config['check_interval'] = $check_interval

with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, indent=2)
"
    
    log_success "告警阈值配置完成"
}

# 测试告警
test_alerts() {
    log_info "测试告警发送..."
    
    python3 "$ALERT_SCRIPT" --config "$CONFIG_FILE" --test-alert
    
    log_success "测试告警已发送，请检查各个渠道是否收到消息"
}

# 启动监控
start_monitoring() {
    echo -e "\n${CYAN}===== 启动监控 =====${NC}"
    
    read -p "是否现在启动监控？(y/N): " start_now
    if [[ ! "$start_now" =~ ^[Yy]$ ]]; then
        echo "可以稍后使用以下命令启动监控："
        echo "  python3 $ALERT_SCRIPT --config $CONFIG_FILE"
        echo "  # 或在后台运行:"
        echo "  nohup python3 $ALERT_SCRIPT --config $CONFIG_FILE > /tmp/alert_monitor.log 2>&1 &"
        return
    fi
    
    read -p "是否在后台运行？(y/N): " background
    if [[ "$background" =~ ^[Yy]$ ]]; then
        nohup python3 "$ALERT_SCRIPT" --config "$CONFIG_FILE" > /tmp/alert_monitor.log 2>&1 &
        local pid=$!
        log_success "监控已在后台启动 (PID: $pid)"
        echo "查看日志: tail -f /tmp/alert_monitor.log"
        echo "停止监控: kill $pid"
    else
        log_info "启动前台监控 (按Ctrl+C停止)..."
        python3 "$ALERT_SCRIPT" --config "$CONFIG_FILE"
    fi
}

# 显示配置摘要
show_config_summary() {
    log_info "配置摘要:"
    
    if [[ -f "$CONFIG_FILE" ]]; then
        python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)

print(f'MySQL: {config[\"mysql_host\"]}:{config[\"mysql_port\"]}')
print(f'延迟阈值: 警告 {config[\"lag_warning_threshold\"]}s, 严重 {config[\"lag_critical_threshold\"]}s')
print(f'检查间隔: {config[\"check_interval\"]}s')
print(f'邮件告警: {\"启用\" if config[\"email_enabled\"] else \"禁用\"}')
print(f'企业微信: {\"启用\" if config[\"wechat_enabled\"] else \"禁用\"}')
print(f'钉钉告警: {\"启用\" if config[\"dingtalk_enabled\"] else \"禁用\"}')
"
    fi
}

# 生成系统服务文件
generate_systemd_service() {
    log_info "生成systemd服务文件..."
    
    local service_file="/tmp/mysql-alert-monitor.service"
    
    cat > "$service_file" << EOF
[Unit]
Description=MySQL Replication Alert Monitor
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=$SCRIPT_DIR
ExecStart=/usr/bin/python3 $ALERT_SCRIPT --config $CONFIG_FILE
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    log_success "服务文件已生成: $service_file"
    echo "安装服务: sudo cp $service_file /etc/systemd/system/"
    echo "启用服务: sudo systemctl enable mysql-alert-monitor"
    echo "启动服务: sudo systemctl start mysql-alert-monitor"
}

# 主菜单
show_menu() {
    echo -e "\n${CYAN}===== MySQL告警系统配置工具 =====${NC}"
    echo "1. 完整配置 (推荐)"
    echo "2. 生成配置文件"
    echo "3. 配置邮件告警"
    echo "4. 配置企业微信告警"
    echo "5. 配置钉钉告警"
    echo "6. 配置告警阈值"
    echo "7. 测试告警"
    echo "8. 查看配置"
    echo "9. 启动监控"
    echo "10. 生成系统服务"
    echo "0. 退出"
    echo -n "请选择操作 [0-10]: "
}

# 完整配置流程
full_setup() {
    log_info "开始完整配置流程..."
    
    check_python
    install_dependencies
    generate_config
    configure_thresholds
    configure_email
    configure_wechat
    configure_dingtalk
    show_config_summary
    
    echo -e "\n${GREEN}配置完成！${NC}"
    test_alerts
    start_monitoring
}

# 主函数
main() {
    echo -e "${CYAN}===== MySQL复制告警系统 =====${NC}"
    echo -e "${BLUE}为FinancialSystem项目配置告警监控${NC}\n"
    
    # 解析命令行参数
    case "${1:-menu}" in
        "setup")
            full_setup
            ;;
        "config")
            generate_config
            ;;
        "test")
            test_alerts
            ;;
        "start")
            start_monitoring
            ;;
        "menu")
            while true; do
                show_menu
                read choice
                
                case $choice in
                    1)
                        full_setup
                        ;;
                    2)
                        check_python && install_dependencies && generate_config
                        ;;
                    3)
                        configure_email
                        ;;
                    4)
                        configure_wechat
                        ;;
                    5)
                        configure_dingtalk
                        ;;
                    6)
                        configure_thresholds
                        ;;
                    7)
                        test_alerts
                        ;;
                    8)
                        show_config_summary
                        ;;
                    9)
                        start_monitoring
                        ;;
                    10)
                        generate_systemd_service
                        ;;
                    0)
                        log_info "退出程序"
                        exit 0
                        ;;
                    *)
                        log_error "无效选择"
                        ;;
                esac
            done
            ;;
        *)
            log_error "无效参数: $1"
            echo "用法: $0 [setup|config|test|start]"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"