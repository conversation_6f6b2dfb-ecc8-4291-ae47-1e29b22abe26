#!/bin/bash

# ===================================================================
# MySQL复制监控脚本
# 实时监控MySQL主从复制状态并发送告警
#
# 功能：
# 1. 监控复制线程状态
# 2. 监控复制延迟
# 3. 记录性能指标
# 4. 发送告警通知
# 5. 生成监控报告
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 配置变量
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASS="Zlb&198838"
LOG_DIR="/tmp/mysql_replication_monitor"
LOG_FILE="$LOG_DIR/replication_monitor.log"
METRICS_FILE="$LOG_DIR/replication_metrics.csv"
ALERT_LOG="$LOG_DIR/alerts.log"

# 告警阈值
LAG_WARNING_THRESHOLD=60    # 延迟警告阈值（秒）
LAG_CRITICAL_THRESHOLD=300  # 延迟严重阈值（秒）
CHECK_INTERVAL=30           # 检查间隔（秒）

# 告警配置
ENABLE_EMAIL_ALERT=false
EMAIL_TO="<EMAIL>"
ENABLE_WEBHOOK_ALERT=false
WEBHOOK_URL=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${BLUE}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_success() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_warning() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

# 初始化指标文件
init_metrics_file() {
    if [[ ! -f "$METRICS_FILE" ]]; then
        echo "timestamp,io_running,sql_running,seconds_behind_master,exec_master_log_pos,relay_log_space" > "$METRICS_FILE"
    fi
}

# 获取复制状态
get_replication_status() {
    local status=$(mysql -h${MYSQL_HOST} -u${MYSQL_USER} -p"${MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null)
    
    if [[ -z "$status" ]]; then
        echo "NO_REPLICATION"
        return 1
    fi
    
    echo "$status"
}

# 解析复制状态
parse_replication_status() {
    local status="$1"
    
    # 提取关键指标
    IO_RUNNING=$(echo "$status" | grep "Slave_IO_Running:" | awk '{print $2}')
    SQL_RUNNING=$(echo "$status" | grep "Slave_SQL_Running:" | awk '{print $2}')
    SECONDS_BEHIND=$(echo "$status" | grep "Seconds_Behind_Master:" | awk '{print $2}')
    MASTER_HOST=$(echo "$status" | grep "Master_Host:" | awk '{print $2}')
    MASTER_LOG_FILE=$(echo "$status" | grep "Master_Log_File:" | awk '{print $2}')
    EXEC_MASTER_LOG_POS=$(echo "$status" | grep "Exec_Master_Log_Pos:" | awk '{print $2}')
    RELAY_LOG_SPACE=$(echo "$status" | grep "Relay_Log_Space:" | awk '{print $2}')
    LAST_IO_ERROR=$(echo "$status" | grep "Last_IO_Error:" | cut -d':' -f2-)
    LAST_SQL_ERROR=$(echo "$status" | grep "Last_SQL_Error:" | cut -d':' -f2-)
}

# 检查复制健康状态
check_replication_health() {
    local status_level="OK"
    local status_message=""
    
    # 检查复制线程
    if [[ "$IO_RUNNING" != "Yes" ]]; then
        status_level="CRITICAL"
        status_message="IO线程停止"
    elif [[ "$SQL_RUNNING" != "Yes" ]]; then
        status_level="CRITICAL"
        status_message="SQL线程停止"
    elif [[ "$SECONDS_BEHIND" == "NULL" ]]; then
        status_level="WARNING"
        status_message="复制延迟未知"
    elif [[ "$SECONDS_BEHIND" -gt "$LAG_CRITICAL_THRESHOLD" ]]; then
        status_level="CRITICAL"
        status_message="复制延迟严重: ${SECONDS_BEHIND}秒"
    elif [[ "$SECONDS_BEHIND" -gt "$LAG_WARNING_THRESHOLD" ]]; then
        status_level="WARNING"
        status_message="复制延迟警告: ${SECONDS_BEHIND}秒"
    else
        status_level="OK"
        status_message="复制正常，延迟${SECONDS_BEHIND}秒"
    fi
    
    # 检查错误
    if [[ -n "$LAST_IO_ERROR" && "$LAST_IO_ERROR" != " " ]]; then
        status_level="ERROR"
        status_message="$status_message | IO错误: $LAST_IO_ERROR"
    fi
    
    if [[ -n "$LAST_SQL_ERROR" && "$LAST_SQL_ERROR" != " " ]]; then
        status_level="ERROR"
        status_message="$status_message | SQL错误: $LAST_SQL_ERROR"
    fi
    
    echo "$status_level|$status_message"
}

# 记录指标
record_metrics() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "$timestamp,$IO_RUNNING,$SQL_RUNNING,$SECONDS_BEHIND,$EXEC_MASTER_LOG_POS,$RELAY_LOG_SPACE" >> "$METRICS_FILE"
}

# 发送告警
send_alert() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 记录告警日志
    echo "[$timestamp] [$level] $message" >> "$ALERT_LOG"
    
    # 发送邮件告警
    if [[ "$ENABLE_EMAIL_ALERT" == "true" && ("$level" == "CRITICAL" || "$level" == "ERROR") ]]; then
        echo -e "Subject: [MySQL复制告警] $level: $message\n\n告警时间: $timestamp\n告警级别: $level\n告警内容: $message\n\n主机: $MYSQL_HOST\n主服务器: $MASTER_HOST" | \
        sendmail "$EMAIL_TO" 2>/dev/null || log_error "邮件发送失败"
    fi
    
    # 发送Webhook告警
    if [[ "$ENABLE_WEBHOOK_ALERT" == "true" && -n "$WEBHOOK_URL" ]]; then
        curl -s -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{
                \"level\": \"$level\",
                \"message\": \"$message\",
                \"timestamp\": \"$timestamp\",
                \"host\": \"$MYSQL_HOST\",
                \"master\": \"$MASTER_HOST\"
            }" 2>/dev/null || log_error "Webhook发送失败"
    fi
}

# 显示监控仪表板
show_dashboard() {
    clear
    echo -e "${CYAN}===== MySQL复制监控仪表板 ===== $(date '+%Y-%m-%d %H:%M:%S')${NC}\n"
    
    echo -e "${BLUE}复制配置：${NC}"
    echo -e "  主服务器: ${MASTER_HOST}"
    echo -e "  Binlog文件: ${MASTER_LOG_FILE}"
    echo -e "  执行位置: ${EXEC_MASTER_LOG_POS}"
    
    echo -e "\n${BLUE}复制状态：${NC}"
    echo -e "  IO线程: $([ "$IO_RUNNING" = "Yes" ] && echo -e "${GREEN}运行中${NC}" || echo -e "${RED}已停止${NC}")"
    echo -e "  SQL线程: $([ "$SQL_RUNNING" = "Yes" ] && echo -e "${GREEN}运行中${NC}" || echo -e "${RED}已停止${NC}")"
    
    if [[ "$SECONDS_BEHIND" == "NULL" ]]; then
        echo -e "  复制延迟: ${YELLOW}未知${NC}"
    elif [[ "$SECONDS_BEHIND" -eq 0 ]]; then
        echo -e "  复制延迟: ${GREEN}无延迟${NC}"
    elif [[ "$SECONDS_BEHIND" -lt "$LAG_WARNING_THRESHOLD" ]]; then
        echo -e "  复制延迟: ${GREEN}${SECONDS_BEHIND}秒${NC}"
    elif [[ "$SECONDS_BEHIND" -lt "$LAG_CRITICAL_THRESHOLD" ]]; then
        echo -e "  复制延迟: ${YELLOW}${SECONDS_BEHIND}秒${NC}"
    else
        echo -e "  复制延迟: ${RED}${SECONDS_BEHIND}秒${NC}"
    fi
    
    echo -e "  中继日志空间: $(echo "scale=2; $RELAY_LOG_SPACE/1024/1024" | bc) MB"
    
    # 显示最近的指标趋势
    if [[ -f "$METRICS_FILE" ]]; then
        echo -e "\n${BLUE}最近10分钟延迟趋势：${NC}"
        tail -20 "$METRICS_FILE" | awk -F',' '{print $1 " -> " $4 "秒"}' | tail -5
    fi
    
    # 显示最近的告警
    if [[ -f "$ALERT_LOG" ]]; then
        echo -e "\n${BLUE}最近的告警：${NC}"
        tail -5 "$ALERT_LOG"
    fi
}

# 监控循环
monitor_loop() {
    local last_alert_time=0
    local alert_interval=300  # 告警间隔（秒）
    
    while true; do
        # 获取复制状态
        local status=$(get_replication_status)
        
        if [[ "$status" == "NO_REPLICATION" ]]; then
            log_error "未配置复制或无法连接到MySQL"
            sleep "$CHECK_INTERVAL"
            continue
        fi
        
        # 解析状态
        parse_replication_status "$status"
        
        # 检查健康状态
        local health_check=$(check_replication_health)
        local status_level=$(echo "$health_check" | cut -d'|' -f1)
        local status_message=$(echo "$health_check" | cut -d'|' -f2)
        
        # 记录指标
        record_metrics
        
        # 显示仪表板
        show_dashboard
        
        # 发送告警（限制告警频率）
        local current_time=$(date +%s)
        if [[ "$status_level" != "OK" ]]; then
            if [[ $((current_time - last_alert_time)) -gt $alert_interval ]]; then
                send_alert "$status_level" "$status_message"
                last_alert_time=$current_time
            fi
        fi
        
        # 等待下次检查
        sleep "$CHECK_INTERVAL"
    done
}

# 生成监控报告
generate_report() {
    log_info "生成监控报告..."
    
    local report_file="$LOG_DIR/replication_report_$(date +%Y%m%d_%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>MySQL复制监控报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .metric { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .ok { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #4CAF50; color: white; }
    </style>
</head>
<body>
    <h1>MySQL复制监控报告</h1>
    <p>生成时间: $(date)</p>
    
    <h2>当前状态</h2>
    <div class="metric">
        <strong>主服务器:</strong> $MASTER_HOST<br>
        <strong>IO线程:</strong> <span class="$([ "$IO_RUNNING" = "Yes" ] && echo "ok" || echo "error")">$IO_RUNNING</span><br>
        <strong>SQL线程:</strong> <span class="$([ "$SQL_RUNNING" = "Yes" ] && echo "ok" || echo "error")">$SQL_RUNNING</span><br>
        <strong>复制延迟:</strong> $SECONDS_BEHIND 秒
    </div>
    
    <h2>历史指标</h2>
    <table>
        <tr>
            <th>时间</th>
            <th>IO线程</th>
            <th>SQL线程</th>
            <th>延迟(秒)</th>
        </tr>
EOF
    
    # 添加最近的指标数据
    tail -20 "$METRICS_FILE" | while IFS=',' read -r timestamp io sql lag pos space; do
        echo "        <tr><td>$timestamp</td><td>$io</td><td>$sql</td><td>$lag</td></tr>" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF
    </table>
    
    <h2>告警历史</h2>
    <pre>
$(tail -20 "$ALERT_LOG" 2>/dev/null || echo "无告警记录")
    </pre>
</body>
</html>
EOF
    
    log_success "报告已生成: $report_file"
    
    # 在macOS上自动打开报告
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open "$report_file"
    fi
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  monitor    - 启动持续监控"
    echo "  check      - 执行单次检查"
    echo "  report     - 生成监控报告"
    echo "  dashboard  - 显示实时仪表板"
    echo "  help       - 显示此帮助信息"
    echo ""
    echo "配置:"
    echo "  编辑脚本修改以下配置:"
    echo "  - LAG_WARNING_THRESHOLD: 延迟警告阈值（默认60秒）"
    echo "  - LAG_CRITICAL_THRESHOLD: 延迟严重阈值（默认300秒）"
    echo "  - CHECK_INTERVAL: 检查间隔（默认30秒）"
    echo "  - ENABLE_EMAIL_ALERT: 启用邮件告警"
    echo "  - ENABLE_WEBHOOK_ALERT: 启用Webhook告警"
}

# 主函数
main() {
    # 初始化
    init_metrics_file
    
    # 解析命令行参数
    case "${1:-monitor}" in
        "monitor")
            log_info "启动MySQL复制监控..."
            monitor_loop
            ;;
        "check")
            # 执行单次检查
            local status=$(get_replication_status)
            if [[ "$status" == "NO_REPLICATION" ]]; then
                log_error "未配置复制或无法连接到MySQL"
                exit 1
            fi
            parse_replication_status "$status"
            show_dashboard
            ;;
        "report")
            # 获取当前状态用于报告
            local status=$(get_replication_status)
            if [[ "$status" != "NO_REPLICATION" ]]; then
                parse_replication_status "$status"
            fi
            generate_report
            ;;
        "dashboard")
            # 实时仪表板模式
            while true; do
                local status=$(get_replication_status)
                if [[ "$status" != "NO_REPLICATION" ]]; then
                    parse_replication_status "$status"
                    show_dashboard
                fi
                sleep 5
            done
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "无效的选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"