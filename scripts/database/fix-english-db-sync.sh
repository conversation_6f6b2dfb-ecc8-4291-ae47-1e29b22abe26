#!/bin/bash

# 修复英文数据库双向同步的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_USER="root"
DB_PASSWORD="Zlb&198838"
LOCAL_SERVER_ID="2"  # 修改本地server_id为2，避免与Linux服务器冲突
REMOTE_HOST="**********"
REPL_USER="repl"
REPL_PASSWORD="Zlb&198838"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止现有的复制
stop_replication() {
    log_info "停止现有的复制..."
    
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        STOP SLAVE;
    " 2>/dev/null || log_warning "复制可能已经停止"
}

# 修改server_id
fix_server_id() {
    log_info "修改本地MySQL server_id为 $LOCAL_SERVER_ID"
    
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        SET GLOBAL server_id = $LOCAL_SERVER_ID;
    "
    
    log_success "server_id已修改为 $LOCAL_SERVER_ID"
}

# 配置复制过滤器（包含英文数据库）
configure_replication_filters() {
    log_info "配置复制过滤器（包含英文数据库）..."
    
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        -- 重置复制配置
        RESET SLAVE ALL;
        
        -- 配置新的Master连接（包含复制过滤器）
        CHANGE MASTER TO
            MASTER_HOST='$REMOTE_HOST',
            MASTER_USER='$REPL_USER',
            MASTER_PASSWORD='$REPL_PASSWORD',
            MASTER_PORT=3306,
            MASTER_AUTO_POSITION=0;
    "
    
    log_success "复制过滤器配置完成"
}

# 启动复制
start_replication() {
    log_info "启动复制..."
    
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        START SLAVE;
    "
    
    # 等待几秒钟让复制启动
    sleep 3
    
    # 检查复制状态
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        SHOW SLAVE STATUS\G
    " | grep -E "(Slave_IO_Running|Slave_SQL_Running|Last_IO_Error|Last_SQL_Error|Seconds_Behind_Master)"
}

# 测试英文数据库同步
test_english_db_sync() {
    log_info "测试英文数据库同步..."
    
    # 在英文数据库中创建测试表
    local test_table="sync_test_$(date +%s)"
    
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        USE overdue_debt_db;
        CREATE TABLE IF NOT EXISTS $test_table (
            id INT AUTO_INCREMENT PRIMARY KEY,
            test_data VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        INSERT INTO $test_table (test_data) VALUES ('English DB Sync Test');
        
        SELECT * FROM $test_table;
    "
    
    log_success "测试表创建完成：$test_table"
    log_info "请在Linux服务器上检查是否同步了此测试表"
}

# 创建英文数据库的MySQL配置建议
create_mysql_config_suggestion() {
    log_info "生成MySQL配置建议..."
    
    local config_file="/tmp/mysql_english_db_config.cnf"
    
    cat > "$config_file" << EOF
# MySQL配置建议 - 支持英文数据库双向同步
# 请将以下配置添加到MySQL配置文件中（通常是my.cnf）

[mysqld]
# 服务器ID（本地使用2，Linux服务器使用1）
server_id = $LOCAL_SERVER_ID

# 启用binlog
log-bin = mysql-bin
binlog_format = ROW

# 复制过滤器 - 仅复制指定的数据库
binlog-do-db = overdue_debt_db
binlog-do-db = user_system
binlog-do-db = kingdee

# 复制相关配置
replicate-do-db = overdue_debt_db
replicate-do-db = user_system
replicate-do-db = kingdee

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 其他复制配置
log_slave_updates = ON
read_only = OFF
relay_log = relay-bin
relay_log_index = relay-bin.index

EOF

    log_success "MySQL配置建议已生成：$config_file"
    log_warning "请注意：运行时的SET GLOBAL命令只是临时生效，重启后会失效"
    log_warning "要永久生效，请将配置添加到MySQL配置文件中并重启MySQL服务"
}

# 主函数
main() {
    log_info "开始修复英文数据库双向同步配置"
    
    # 停止现有复制
    stop_replication
    
    # 修改server_id
    fix_server_id
    
    # 配置复制过滤器
    configure_replication_filters
    
    # 启动复制
    start_replication
    
    # 测试英文数据库同步
    test_english_db_sync
    
    # 生成配置建议
    create_mysql_config_suggestion
    
    echo ""
    log_success "英文数据库双向同步修复完成"
    log_info "建议执行以下命令检查同步状态："
    echo "  ./scripts/database/check-english-db-sync.sh"
    echo ""
    log_warning "注意：某些配置更改需要重启MySQL服务才能永久生效"
}

# 显示帮助信息
show_help() {
    echo "英文数据库双向同步修复脚本"
    echo ""
    echo "此脚本将修复以下问题："
    echo "1. server_id冲突问题"
    echo "2. 配置英文数据库的复制过滤器"
    echo "3. 重新启动复制服务"
    echo "4. 测试英文数据库同步功能"
    echo ""
    echo "使用方法:"
    echo "  $0           # 执行修复"
    echo "  $0 --help    # 显示帮助信息"
}

# 解析命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac