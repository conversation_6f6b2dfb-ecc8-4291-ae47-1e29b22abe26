-- ===================================================================
-- 用户数据初始化脚本
-- 解决登录401问题，创建初始用户和权限数据
--
-- 功能：
-- 1. 创建默认管理员和测试用户
-- 2. 创建公司组织结构
-- 3. 分配用户权限
-- 4. 创建角色定义
--
-- 注意：密码使用BCrypt加密，默认密码为 admin123
-- 作者：SuperClaude
-- 日期：2025-07-08
-- ===================================================================

-- 使用user_system数据库
USE user_system;

-- 开始事务
START TRANSACTION;

-- ===================================================================
-- 1. 创建角色表（如果不存在）
-- ===================================================================
CREATE TABLE IF NOT EXISTS `role` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `code` VARCHAR(50) NOT NULL COMMENT '角色代码',
    `description` VARCHAR(200) COMMENT '角色描述',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 插入默认角色
INSERT INTO `role` (`name`, `code`, `description`) VALUES 
('系统管理员', 'ADMIN', '拥有系统所有权限'),
('普通用户', 'USER', '拥有基本业务操作权限'),
('查看用户', 'VIEWER', '只能查看数据，不能修改'),
('数据导出用户', 'EXPORTER', '可以导出数据报表')
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `description` = VALUES(`description`);

-- ===================================================================
-- 2. 创建用户表（如果不存在）
-- ===================================================================
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(100) NOT NULL COMMENT '密码（BCrypt加密）',
    `real_name` VARCHAR(50) COMMENT '真实姓名',
    `email` VARCHAR(100) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '手机号',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `role_id` BIGINT COMMENT '角色ID',
    `last_login_time` TIMESTAMP NULL COMMENT '最后登录时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_role_id` (`role_id`),
    CONSTRAINT `fk_user_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入默认用户
-- 密码: admin123 的 BCrypt 加密值
-- 可以使用在线工具生成: https://bcrypt-generator.com/
-- 或使用命令: htpasswd -bnBC 10 "" admin123 | tr -d ':\n'
INSERT INTO `user` (`username`, `password`, `real_name`, `email`, `phone`, `status`, `role_id`) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '系统管理员', '<EMAIL>', '13800000001', 1, (SELECT id FROM role WHERE code = 'ADMIN')),
('test_user', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '测试用户', '<EMAIL>', '13800000002', 1, (SELECT id FROM role WHERE code = 'USER')),
('viewer', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '查看用户', '<EMAIL>', '13800000003', 1, (SELECT id FROM role WHERE code = 'VIEWER')),
('exporter', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi', '导出用户', '<EMAIL>', '13800000004', 1, (SELECT id FROM role WHERE code = 'EXPORTER'))
ON DUPLICATE KEY UPDATE 
    `password` = VALUES(`password`),
    `real_name` = VALUES(`real_name`),
    `email` = VALUES(`email`);

-- ===================================================================
-- 3. 创建公司表（如果不存在）
-- ===================================================================
CREATE TABLE IF NOT EXISTS `company` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '公司名称',
    `code` VARCHAR(50) NOT NULL COMMENT '公司代码',
    `parent_id` BIGINT COMMENT '上级公司ID',
    `level` INT DEFAULT 1 COMMENT '层级',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_company_code` (`code`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司组织表';

-- 插入公司组织结构
INSERT INTO `company` (`name`, `code`, `parent_id`, `level`, `sort_order`) VALUES 
('集团总部', 'HQ001', NULL, 1, 1),
('华东分公司', 'EAST001', 1, 2, 1),
('华南分公司', 'SOUTH001', 1, 2, 2),
('华北分公司', 'NORTH001', 1, 2, 3),
('华西分公司', 'WEST001', 1, 2, 4),
('上海办事处', 'SH001', 2, 3, 1),
('广州办事处', 'GZ001', 3, 3, 1),
('北京办事处', 'BJ001', 4, 3, 1)
ON DUPLICATE KEY UPDATE 
    `name` = VALUES(`name`),
    `parent_id` = VALUES(`parent_id`),
    `level` = VALUES(`level`);

-- ===================================================================
-- 4. 创建用户公司权限表（如果不存在）
-- ===================================================================
CREATE TABLE IF NOT EXISTS `user_company_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `company_id` BIGINT NOT NULL COMMENT '公司ID',
    `permission_level` VARCHAR(20) DEFAULT 'READ' COMMENT '权限级别：READ/WRITE/FULL',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_company` (`user_id`, `company_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_company_id` (`company_id`),
    CONSTRAINT `fk_permission_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`),
    CONSTRAINT `fk_permission_company` FOREIGN KEY (`company_id`) REFERENCES `company` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户公司权限表';

-- 分配用户权限
INSERT INTO `user_company_permission` (`user_id`, `company_id`, `permission_level`) VALUES 
-- 管理员拥有所有公司的完全权限
((SELECT id FROM user WHERE username = 'admin'), (SELECT id FROM company WHERE code = 'HQ001'), 'FULL'),
((SELECT id FROM user WHERE username = 'admin'), (SELECT id FROM company WHERE code = 'EAST001'), 'FULL'),
((SELECT id FROM user WHERE username = 'admin'), (SELECT id FROM company WHERE code = 'SOUTH001'), 'FULL'),
((SELECT id FROM user WHERE username = 'admin'), (SELECT id FROM company WHERE code = 'NORTH001'), 'FULL'),
((SELECT id FROM user WHERE username = 'admin'), (SELECT id FROM company WHERE code = 'WEST001'), 'FULL'),

-- 测试用户拥有华东分公司的写权限
((SELECT id FROM user WHERE username = 'test_user'), (SELECT id FROM company WHERE code = 'EAST001'), 'WRITE'),
((SELECT id FROM user WHERE username = 'test_user'), (SELECT id FROM company WHERE code = 'SH001'), 'WRITE'),

-- 查看用户拥有所有公司的读权限
((SELECT id FROM user WHERE username = 'viewer'), (SELECT id FROM company WHERE code = 'HQ001'), 'READ'),
((SELECT id FROM user WHERE username = 'viewer'), (SELECT id FROM company WHERE code = 'EAST001'), 'READ'),
((SELECT id FROM user WHERE username = 'viewer'), (SELECT id FROM company WHERE code = 'SOUTH001'), 'READ'),

-- 导出用户拥有总部的读权限
((SELECT id FROM user WHERE username = 'exporter'), (SELECT id FROM company WHERE code = 'HQ001'), 'READ')
ON DUPLICATE KEY UPDATE 
    `permission_level` = VALUES(`permission_level`);

-- ===================================================================
-- 5. 创建权限表（如果不存在）
-- ===================================================================
CREATE TABLE IF NOT EXISTS `permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '权限名称',
    `code` VARCHAR(100) NOT NULL COMMENT '权限代码',
    `type` VARCHAR(20) COMMENT '权限类型：MENU/BUTTON/API',
    `parent_id` BIGINT COMMENT '父权限ID',
    `path` VARCHAR(200) COMMENT '权限路径',
    `sort_order` INT DEFAULT 0 COMMENT '排序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`code`),
    KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 插入基础权限
INSERT INTO `permission` (`name`, `code`, `type`, `parent_id`, `path`, `sort_order`) VALUES 
-- 菜单权限
('系统管理', 'system', 'MENU', NULL, '/system', 1),
('用户管理', 'system:user', 'MENU', 1, '/system/user', 1),
('角色管理', 'system:role', 'MENU', 1, '/system/role', 2),
('权限管理', 'system:permission', 'MENU', 1, '/system/permission', 3),

('债权管理', 'debt', 'MENU', NULL, '/debt', 2),
('逾期债权', 'debt:overdue', 'MENU', 5, '/debt/overdue', 1),
('债权处置', 'debt:disposal', 'MENU', 5, '/debt/disposal', 2),
('债权统计', 'debt:statistics', 'MENU', 5, '/debt/statistics', 3),

-- API权限
('用户查询', 'api:user:list', 'API', NULL, '/api/users', 100),
('用户创建', 'api:user:create', 'API', NULL, '/api/users/create', 101),
('用户更新', 'api:user:update', 'API', NULL, '/api/users/update', 102),
('用户删除', 'api:user:delete', 'API', NULL, '/api/users/delete', 103),

('债权查询', 'api:debt:list', 'API', NULL, '/api/debts', 200),
('债权新增', 'api:debt:create', 'API', NULL, '/api/debts/add', 201),
('债权更新', 'api:debt:update', 'API', NULL, '/api/debts/update', 202),
('债权删除', 'api:debt:delete', 'API', NULL, '/api/debts/delete', 203),
('债权导出', 'api:debt:export', 'API', NULL, '/api/export', 204)
ON DUPLICATE KEY UPDATE 
    `name` = VALUES(`name`),
    `type` = VALUES(`type`),
    `path` = VALUES(`path`);

-- ===================================================================
-- 6. 创建角色权限关联表（如果不存在）
-- ===================================================================
CREATE TABLE IF NOT EXISTS `role_permission` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `role_id` BIGINT NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT NOT NULL COMMENT '权限ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_id` (`permission_id`),
    CONSTRAINT `fk_rp_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`),
    CONSTRAINT `fk_rp_permission` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 为管理员角色分配所有权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM role r, permission p 
WHERE r.code = 'ADMIN'
ON DUPLICATE KEY UPDATE `role_id` = VALUES(`role_id`);

-- 为普通用户分配债权管理权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM role r, permission p 
WHERE r.code = 'USER' 
AND (p.code LIKE 'debt:%' OR p.code LIKE 'api:debt:%')
ON DUPLICATE KEY UPDATE `role_id` = VALUES(`role_id`);

-- 为查看用户分配查询权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM role r, permission p 
WHERE r.code = 'VIEWER' 
AND (p.code LIKE '%:list' OR p.code LIKE '%statistics%' OR p.type = 'MENU')
ON DUPLICATE KEY UPDATE `role_id` = VALUES(`role_id`);

-- 为导出用户分配导出权限
INSERT INTO `role_permission` (`role_id`, `permission_id`)
SELECT r.id, p.id 
FROM role r, permission p 
WHERE r.code = 'EXPORTER' 
AND (p.code LIKE '%:list' OR p.code LIKE '%:export' OR p.type = 'MENU')
ON DUPLICATE KEY UPDATE `role_id` = VALUES(`role_id`);

-- ===================================================================
-- 7. 显示创建的用户信息
-- ===================================================================
SELECT '已创建的用户账号：' as '提示信息';
SELECT 
    u.username as '用户名',
    '********' as '密码',
    u.real_name as '真实姓名',
    r.name as '角色',
    u.email as '邮箱',
    CASE u.status 
        WHEN 1 THEN '启用'
        ELSE '禁用'
    END as '状态'
FROM user u
LEFT JOIN role r ON u.role_id = r.id
ORDER BY u.id;

SELECT '注意：所有用户的默认密码都是 admin123' as '重要提示';

-- 提交事务
COMMIT;

-- ===================================================================
-- 验证脚本
-- 可以单独运行以下查询验证数据是否正确创建
-- ===================================================================
/*
-- 验证用户数量
SELECT COUNT(*) as user_count FROM user;

-- 验证角色数量
SELECT COUNT(*) as role_count FROM role;

-- 验证公司数量
SELECT COUNT(*) as company_count FROM company;

-- 验证权限分配
SELECT 
    u.username,
    r.name as role_name,
    COUNT(DISTINCT rp.permission_id) as permission_count
FROM user u
JOIN role r ON u.role_id = r.id
LEFT JOIN role_permission rp ON r.id = rp.role_id
GROUP BY u.id, u.username, r.name;

-- 验证用户公司权限
SELECT 
    u.username,
    c.name as company_name,
    ucp.permission_level
FROM user_company_permission ucp
JOIN user u ON ucp.user_id = u.id
JOIN company c ON ucp.company_id = c.id
ORDER BY u.username, c.name;
*/