#!/bin/bash

# ===================================================================
# 数据一致性检查包装脚本
# 为Python数据一致性检查工具提供友好的shell接口
#
# 功能：
# 1. 自动安装Python依赖
# 2. 提供交互式菜单
# 3. 生成配置文件
# 4. 执行一致性检查
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/data-consistency-checker.py"
CONFIG_FILE="$SCRIPT_DIR/consistency_config.json"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    # 检查Python 3
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3未安装，请先安装Python 3"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3未安装，请先安装pip3"
        exit 1
    fi
    
    log_success "Python环境检查通过"
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装Python依赖..."
    
    # 检查mysql-connector-python
    if ! python3 -c "import mysql.connector" 2>/dev/null; then
        log_info "安装mysql-connector-python..."
        pip3 install mysql-connector-python
    fi
    
    log_success "依赖检查完成"
}

# 生成配置文件
generate_config() {
    log_info "生成配置文件..."
    
    cat > "$CONFIG_FILE" << EOF
{
    "linux_server": {
        "host": "**********",
        "port": 3306,
        "user": "root",
        "password": "Zlb&198838",
        "name": "overdue_debt_db",
        "alias": "Linux服务器"
    },
    "local_mysql": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "Zlb&198838",
        "name": "overdue_debt_db",
        "alias": "本地MySQL"
    },
    "docker_mysql": {
        "host": "localhost",
        "port": 3307,
        "user": "root",
        "password": "Zlb&198838",
        "name": "overdue_debt_db",
        "alias": "Docker MySQL"
    },
    "user_system_local": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "Zlb&198838",
        "name": "user_system",
        "alias": "本地用户系统"
    },
    "user_system_linux": {
        "host": "**********",
        "port": 3306,
        "user": "root",
        "password": "Zlb&198838",
        "name": "user_system",
        "alias": "Linux用户系统"
    }
}
EOF
    
    log_success "配置文件已生成: $CONFIG_FILE"
}

# 测试数据库连接
test_connections() {
    log_info "测试数据库连接..."
    
    python3 "$PYTHON_SCRIPT" --config "$CONFIG_FILE" --source linux_server --target local_mysql --tables __test__ 2>/dev/null || true
    
    log_success "连接测试完成"
}

# 快速检查
quick_check() {
    log_info "执行快速一致性检查..."
    
    python3 "$PYTHON_SCRIPT" --config "$CONFIG_FILE" --source linux_server --target local_mysql
    
    log_success "快速检查完成"
}

# 完整检查
full_check() {
    log_info "执行完整一致性检查..."
    
    python3 "$PYTHON_SCRIPT" --config "$CONFIG_FILE" --compare-all
    
    log_success "完整检查完成"
}

# 自定义检查
custom_check() {
    echo -e "\n${CYAN}===== 自定义检查 =====${NC}"
    
    # 显示可用数据库
    echo "可用数据库："
    echo "1. linux_server (Linux服务器)"
    echo "2. local_mysql (本地MySQL)"
    echo "3. docker_mysql (Docker MySQL)"
    echo "4. user_system_local (本地用户系统)"
    echo "5. user_system_linux (Linux用户系统)"
    
    # 选择源数据库
    echo -n "选择源数据库 [1-5]: "
    read source_choice
    
    # 选择目标数据库
    echo -n "选择目标数据库 [1-5]: "
    read target_choice
    
    # 映射选择
    declare -A db_map
    db_map[1]="linux_server"
    db_map[2]="local_mysql"
    db_map[3]="docker_mysql"
    db_map[4]="user_system_local"
    db_map[5]="user_system_linux"
    
    local source_db="${db_map[$source_choice]}"
    local target_db="${db_map[$target_choice]}"
    
    if [[ -z "$source_db" || -z "$target_db" ]]; then
        log_error "无效选择"
        return 1
    fi
    
    # 询问是否指定表
    echo -n "是否指定特定表检查？(y/N): "
    read specify_tables
    
    if [[ "$specify_tables" =~ ^[Yy]$ ]]; then
        echo -n "请输入表名（空格分隔）: "
        read -a tables
        python3 "$PYTHON_SCRIPT" --config "$CONFIG_FILE" --source "$source_db" --target "$target_db" --tables "${tables[@]}"
    else
        python3 "$PYTHON_SCRIPT" --config "$CONFIG_FILE" --source "$source_db" --target "$target_db"
    fi
}

# 查看报告
view_reports() {
    log_info "查看生成的报告..."
    
    local reports=($(ls /tmp/consistency_*.html 2>/dev/null | sort -r))
    
    if [[ ${#reports[@]} -eq 0 ]]; then
        log_warning "没有找到报告文件"
        return
    fi
    
    echo -e "\n${CYAN}可用报告：${NC}"
    for i in "${!reports[@]}"; do
        local filename=$(basename "${reports[$i]}")
        local timestamp=$(echo "$filename" | grep -o '[0-9]\{8\}_[0-9]\{6\}')
        echo "$((i+1)). $filename (生成时间: $timestamp)"
    done
    
    echo -n "选择要查看的报告 [1-${#reports[@]}]: "
    read report_choice
    
    if [[ "$report_choice" =~ ^[0-9]+$ ]] && [[ "$report_choice" -ge 1 ]] && [[ "$report_choice" -le "${#reports[@]}" ]]; then
        local selected_report="${reports[$((report_choice-1))]}"
        log_info "打开报告: $selected_report"
        
        # 在macOS上自动打开
        if [[ "$OSTYPE" == "darwin"* ]]; then
            open "$selected_report"
        else
            echo "请在浏览器中打开: $selected_report"
        fi
    else
        log_error "无效选择"
    fi
}

# 显示帮助
show_help() {
    echo -e "\n${CYAN}数据一致性检查工具${NC}"
    echo -e "${BLUE}用法：${NC}"
    echo "  $0 [选项]"
    echo ""
    echo -e "${BLUE}选项：${NC}"
    echo "  quick      - 快速检查（Linux -> 本地）"
    echo "  full       - 完整检查（所有组合）"
    echo "  custom     - 自定义检查"
    echo "  reports    - 查看报告"
    echo "  config     - 重新生成配置文件"
    echo "  test       - 测试数据库连接"
    echo "  help       - 显示此帮助"
    echo ""
    echo -e "${BLUE}示例：${NC}"
    echo "  $0 quick           # 快速检查"
    echo "  $0 full            # 完整检查"
    echo "  $0 custom          # 自定义检查"
    echo ""
    echo -e "${BLUE}报告位置：${NC}/tmp/consistency_*.html"
}

# 主菜单
show_menu() {
    echo -e "\n${CYAN}===== 数据一致性检查工具 =====${NC}"
    echo "1. 快速检查（Linux -> 本地）"
    echo "2. 完整检查（所有组合）"
    echo "3. 自定义检查"
    echo "4. 查看报告"
    echo "5. 测试数据库连接"
    echo "6. 重新生成配置文件"
    echo "0. 退出"
    echo -n "请选择操作 [0-6]: "
}

# 主函数
main() {
    # 检查环境
    check_python
    install_dependencies
    
    # 生成配置文件（如果不存在）
    if [[ ! -f "$CONFIG_FILE" ]]; then
        generate_config
    fi
    
    # 解析命令行参数
    case "${1:-menu}" in
        "quick")
            quick_check
            ;;
        "full")
            full_check
            ;;
        "custom")
            custom_check
            ;;
        "reports")
            view_reports
            ;;
        "config")
            generate_config
            ;;
        "test")
            test_connections
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        "menu")
            # 交互式菜单
            while true; do
                show_menu
                read choice
                
                case $choice in
                    1)
                        quick_check
                        ;;
                    2)
                        full_check
                        ;;
                    3)
                        custom_check
                        ;;
                    4)
                        view_reports
                        ;;
                    5)
                        test_connections
                        ;;
                    6)
                        generate_config
                        ;;
                    0)
                        log_info "退出程序"
                        exit 0
                        ;;
                    *)
                        log_error "无效选择"
                        ;;
                esac
            done
            ;;
        *)
            log_error "无效参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"