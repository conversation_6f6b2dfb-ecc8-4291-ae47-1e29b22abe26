#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库导出脚本
从本地MySQL导出逾期债权数据库和user_system数据库
"""

import mysql.connector
import os
import sys
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'Zlb&198838',
    'charset': 'utf8mb4'
}

# 要导出的数据库列表
DATABASES = ['逾期债权数据库', 'user_system']

def connect_to_mysql():
    """连接到MySQL服务器"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        print(f"✅ 成功连接到MySQL服务器")
        return connection
    except mysql.connector.Error as err:
        print(f"❌ 连接MySQL失败: {err}")
        return None

def get_table_list(connection, database_name):
    """获取数据库中的所有表"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"USE `{database_name}`")
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        cursor.close()
        return tables
    except mysql.connector.Error as err:
        print(f"❌ 获取表列表失败: {err}")
        return []

def export_table_structure(connection, database_name, table_name):
    """导出表结构"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"SHOW CREATE TABLE `{database_name}`.`{table_name}`")
        result = cursor.fetchone()
        cursor.close()
        if result:
            return result[1] + ";\n\n"
        return ""
    except mysql.connector.Error as err:
        print(f"❌ 导出表结构失败 {table_name}: {err}")
        return ""

def export_table_data(connection, database_name, table_name):
    """导出表数据"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"SELECT * FROM `{database_name}`.`{table_name}`")
        rows = cursor.fetchall()

        if not rows:
            cursor.close()
            return ""

        # 获取列信息
        cursor.execute(f"DESCRIBE `{database_name}`.`{table_name}`")
        columns = cursor.fetchall()
        cursor.close()

        # 生成INSERT语句
        column_names = [col[0] for col in columns]
        insert_sql = f"INSERT INTO `{table_name}` (`{'`, `'.join(column_names)}`) VALUES\n"

        values_list = []
        for row in rows:
            values = []
            for value in row:
                if value is None:
                    values.append('NULL')
                elif isinstance(value, str):
                    # 转义单引号和反斜杠，并截断过长的字符串
                    escaped_value = value.replace('\\', '\\\\').replace("'", "\\'")
                    # 限制字符串长度为200字符，避免数据过长问题
                    if len(escaped_value) > 200:
                        escaped_value = escaped_value[:200] + "..."
                    values.append(f"'{escaped_value}'")
                elif isinstance(value, (int, float)):
                    values.append(str(value))
                elif isinstance(value, datetime):
                    values.append(f"'{value.strftime('%Y-%m-%d %H:%M:%S')}'")
                else:
                    values.append(f"'{str(value)}'")
            values_list.append(f"({', '.join(values)})")

        insert_sql += ',\n'.join(values_list) + ";\n\n"
        return insert_sql

    except mysql.connector.Error as err:
        print(f"❌ 导出表数据失败 {table_name}: {err}")
        return ""

def export_database(connection, database_name):
    """导出整个数据库"""
    print(f"🔄 开始导出数据库: {database_name}")

    # 检查数据库是否存在
    try:
        cursor = connection.cursor()
        cursor.execute(f"USE `{database_name}`")
        cursor.close()
    except mysql.connector.Error as err:
        print(f"❌ 数据库不存在: {database_name}")
        return None

    # 获取表列表
    tables = get_table_list(connection, database_name)
    if not tables:
        print(f"⚠️ 数据库 {database_name} 中没有表")
        return ""

    print(f"📋 找到 {len(tables)} 个表: {', '.join(tables)}")

    # 生成SQL内容
    sql_content = f"-- 数据库导出: {database_name}\n"
    sql_content += f"-- 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    sql_content += f"-- 表数量: {len(tables)}\n\n"

    sql_content += f"-- 创建数据库\n"
    sql_content += f"CREATE DATABASE IF NOT EXISTS `{database_name}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n"
    sql_content += f"USE `{database_name}`;\n\n"

    sql_content += "-- 禁用外键检查\n"
    sql_content += "SET FOREIGN_KEY_CHECKS = 0;\n\n"

    # 导出每个表
    for table in tables:
        print(f"  📄 导出表: {table}")

        # 导出表结构
        sql_content += f"-- 表结构: {table}\n"
        sql_content += f"DROP TABLE IF EXISTS `{table}`;\n"
        structure = export_table_structure(connection, database_name, table)
        sql_content += structure

        # 导出表数据
        sql_content += f"-- 表数据: {table}\n"
        data = export_table_data(connection, database_name, table)
        if data:
            sql_content += data
        else:
            sql_content += f"-- 表 {table} 无数据\n\n"

    sql_content += "-- 启用外键检查\n"
    sql_content += "SET FOREIGN_KEY_CHECKS = 1;\n\n"

    print(f"✅ 数据库 {database_name} 导出完成")
    return sql_content

def main():
    """主函数"""
    print("🚀 开始导出数据库...")

    # 创建输出目录
    output_dir = "/tmp/database_export"
    os.makedirs(output_dir, exist_ok=True)

    # 连接数据库
    connection = connect_to_mysql()
    if not connection:
        sys.exit(1)

    try:
        # 导出每个数据库
        for database_name in DATABASES:
            sql_content = export_database(connection, database_name)
            if sql_content:
                # 保存到文件
                filename = f"{database_name.replace('/', '_')}_export.sql"
                filepath = os.path.join(output_dir, filename)

                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(sql_content)

                print(f"💾 已保存到: {filepath}")
                print(f"📊 文件大小: {os.path.getsize(filepath)} 字节")
            else:
                print(f"❌ 数据库 {database_name} 导出失败")

    finally:
        connection.close()
        print("🔒 数据库连接已关闭")

    print(f"\n🎉 导出完成！文件保存在: {output_dir}")

if __name__ == "__main__":
    main()
