#!/bin/bash

# FinancialSystem 三层数据库同步验证脚本
# 架构: Linux服务器 ⇄ 本地MySQL ⇄ Docker MySQL
# 作者: 高级数据库架构师

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
LINUX_IP="**********"
LOCAL_IP=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | grep -v '**********' | awk '{print $2}' | head -1)
MYSQL_CONTAINER="financial-mysql"
TEST_DOCKER_PORT="3307"
ENGLISH_DB="overdue_debt_db"
CHINESE_DB="overdue_debt_db"
USER_DB="user_system"

# 日志函数
log_header() {
    echo -e "\n${PURPLE}==== $1 ====${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查并解决主要同步障碍
resolve_sync_barriers() {
    log_header "解决同步障碍"
    
    # 障碍1: 认证问题
    log_info "解决MySQL复制用户认证问题..."
    
    # 修复本地复制用户
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 删除可能存在的有问题的用户
DROP USER IF EXISTS 'repl'@'%';
DROP USER IF EXISTS 'repl'@'$LINUX_IP';

-- 创建正确的复制用户
CREATE USER IF NOT EXISTS 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
CREATE USER IF NOT EXISTS 'repl_user'@'$LINUX_IP' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
CREATE USER IF NOT EXISTS 'repl_user'@'$LOCAL_IP' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';

-- 授权复制权限
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'$LINUX_IP';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'$LOCAL_IP';

-- 额外权限用于监控
GRANT SELECT ON *.* TO 'repl_user'@'%';
GRANT SELECT ON *.* TO 'repl_user'@'$LINUX_IP';
GRANT SELECT ON *.* TO 'repl_user'@'$LOCAL_IP';

FLUSH PRIVILEGES;

-- 验证用户创建
SELECT User, Host, plugin FROM mysql.user WHERE User = 'repl_user';
EOF
    
    if [ $? -eq 0 ]; then
        log_success "本地MySQL复制用户认证问题已解决"
    else
        log_error "本地MySQL复制用户认证修复失败"
        return 1
    fi
    
    # 修复Linux服务器复制用户
    log_info "修复Linux服务器复制用户..."
    ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' << 'EOF'
-- 删除可能存在的有问题的用户
DROP USER IF EXISTS 'repl'@'%';
DROP USER IF EXISTS 'repl'@'$LOCAL_IP';

-- 创建正确的复制用户
CREATE USER IF NOT EXISTS 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
CREATE USER IF NOT EXISTS 'repl_user'@'$LOCAL_IP' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';

-- 授权复制权限
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'$LOCAL_IP';

-- 额外权限用于监控
GRANT SELECT ON *.* TO 'repl_user'@'%';
GRANT SELECT ON *.* TO 'repl_user'@'$LOCAL_IP';

FLUSH PRIVILEGES;

-- 验证用户创建
SELECT User, Host, plugin FROM mysql.user WHERE User = 'repl_user';
EOF"
    
    if [ $? -eq 0 ]; then
        log_success "Linux服务器MySQL复制用户认证问题已解决"
    else
        log_error "Linux服务器MySQL复制用户认证修复失败"
        return 1
    fi
    
    # 障碍2: 复制配置问题
    log_info "重置并重新配置双向复制..."
    
    # 停止本地复制
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
STOP SLAVE;
RESET SLAVE ALL;
RESET MASTER;
EOF
    
    # 停止Linux服务器复制
    ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' << 'EOF'
STOP SLAVE;
RESET SLAVE ALL;
RESET MASTER;
EOF"
    
    log_success "复制配置已重置"
    
    # 障碍3: 数据不一致问题
    log_info "检查并解决数据不一致问题..."
    
    # 获取本地和远程的英文数据库表数量
    local local_tables=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$ENGLISH_DB' AND table_type='BASE TABLE';" --skip-column-names --batch 2>/dev/null)
    local remote_tables=$(ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SELECT COUNT(*) FROM information_schema.tables WHERE table_schema=\"$ENGLISH_DB\" AND table_type=\"BASE TABLE\";' --skip-column-names --batch" 2>/dev/null)
    
    log_info "本地英文数据库表数量: $local_tables"
    log_info "远程英文数据库表数量: $remote_tables"
    
    if [ "$local_tables" != "$remote_tables" ]; then
        log_warning "检测到数据不一致，将同步本地数据到远程"
        
        # 导出本地英文数据库
        mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" \
            --single-transaction \
            --routines \
            --triggers \
            --events \
            --hex-blob \
            --default-character-set=utf8mb4 \
            "$ENGLISH_DB" > /tmp/local_english_db_sync.sql
        
        # 导入到Linux服务器
        cat /tmp/local_english_db_sync.sql | ssh $LINUX_SERVER "docker exec -i $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' '$ENGLISH_DB'"
        
        # 清理临时文件
        rm -f /tmp/local_english_db_sync.sql
        
        log_success "数据不一致问题已解决"
    else
        log_success "数据一致性检查通过"
    fi
}

# 重新建立双向复制
establish_bidirectional_replication() {
    log_header "重新建立双向复制"
    
    # 获取主服务器状态
    log_info "获取主服务器状态..."
    
    local local_status=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW MASTER STATUS;" --skip-column-names --batch 2>/dev/null)
    local local_file=$(echo "$local_status" | awk '{print $1}')
    local local_pos=$(echo "$local_status" | awk '{print $2}')
    
    local remote_status=$(ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW MASTER STATUS;' --skip-column-names --batch" 2>/dev/null)
    local remote_file=$(echo "$remote_status" | awk '{print $1}')
    local remote_pos=$(echo "$remote_status" | awk '{print $2}')
    
    log_info "本地主服务器: $local_file:$local_pos"
    log_info "远程主服务器: $remote_file:$remote_pos"
    
    # 配置本地从远程复制
    log_info "配置本地MySQL从Linux服务器复制..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
CHANGE MASTER TO
    MASTER_HOST='$LINUX_IP',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='$MYSQL_ROOT_PASSWORD',
    MASTER_PORT=3306,
    MASTER_LOG_FILE='$remote_file',
    MASTER_LOG_POS=$remote_pos,
    MASTER_CONNECT_RETRY=60,
    MASTER_RETRY_COUNT=86400;

START SLAVE;
EOF
    
    # 配置Linux服务器从本地复制
    log_info "配置Linux服务器MySQL从本地复制..."
    ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' << 'EOF'
CHANGE MASTER TO
    MASTER_HOST='$LOCAL_IP',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='$MYSQL_ROOT_PASSWORD',
    MASTER_PORT=3306,
    MASTER_LOG_FILE='$local_file',
    MASTER_LOG_POS=$local_pos,
    MASTER_CONNECT_RETRY=60,
    MASTER_RETRY_COUNT=86400;

START SLAVE;
EOF"
    
    log_success "双向复制已重新建立"
}

# 创建本地Docker测试环境
create_docker_test_environment() {
    log_header "创建本地Docker测试环境"
    
    # 创建Docker Compose文件
    cat > /tmp/docker-mysql-test.yml << EOF
version: '3.8'

services:
  test-mysql:
    image: mysql:8.0
    container_name: test-mysql-sync
    environment:
      MYSQL_ROOT_PASSWORD: $MYSQL_ROOT_PASSWORD
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "$TEST_DOCKER_PORT:3306"
    volumes:
      - test_mysql_data:/var/lib/mysql
    command: >
      --server-id=99
      --log-bin=mysql-bin
      --binlog-format=ROW
      --binlog-do-db=$ENGLISH_DB
      --binlog-do-db=$USER_DB
      --default-authentication-plugin=mysql_native_password
      --skip-slave-start
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

volumes:
  test_mysql_data:
EOF
    
    log_info "Docker测试环境配置文件已创建: /tmp/docker-mysql-test.yml"
    
    # 检查Docker是否可用
    if docker ps >/dev/null 2>&1; then
        log_info "启动Docker测试环境..."
        docker-compose -f /tmp/docker-mysql-test.yml up -d
        
        # 等待MySQL启动
        log_info "等待Docker MySQL启动..."
        sleep 30
        
        # 检查连接
        if docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT VERSION();" >/dev/null 2>&1; then
            log_success "Docker MySQL测试环境启动成功"
            
            # 创建测试数据库
            docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
CREATE DATABASE IF NOT EXISTS $ENGLISH_DB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS $USER_DB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建复制用户
CREATE USER IF NOT EXISTS 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
GRANT SELECT ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;
EOF
            
            log_success "Docker测试数据库已创建"
        else
            log_error "Docker MySQL启动失败"
        fi
    else
        log_warning "Docker不可用，跳过Docker测试环境创建"
        log_info "手动启动命令: docker-compose -f /tmp/docker-mysql-test.yml up -d"
    fi
}

# 验证三层同步
validate_three_layer_sync() {
    log_header "三层同步验证"
    
    local test_table="three_layer_sync_test_$(date +%s)"
    
    # 第一层：本地MySQL
    log_info "第一层测试: 本地MySQL -> Linux服务器"
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
USE $ENGLISH_DB;
CREATE TABLE IF NOT EXISTS $test_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    layer VARCHAR(50),
    test_data VARCHAR(200),
    sync_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO $test_table (layer, test_data) VALUES 
('local', '本地MySQL测试数据 - $(date)');
EOF
    
    # 等待同步
    sleep 5
    
    # 检查Linux服务器是否收到数据
    local remote_count=$(ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'USE $ENGLISH_DB; SELECT COUNT(*) FROM $test_table WHERE layer=\"local\";' --skip-column-names --batch" 2>/dev/null)
    
    if [ "$remote_count" = "1" ]; then
        log_success "✅ 第一层同步正常: 本地 -> Linux服务器"
    else
        log_error "❌ 第一层同步失败: 本地 -> Linux服务器"
    fi
    
    # 第二层：Linux服务器 -> 本地
    log_info "第二层测试: Linux服务器 -> 本地MySQL"
    
    ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' << 'EOF'
USE $ENGLISH_DB;
INSERT INTO $test_table (layer, test_data) VALUES 
('linux', 'Linux服务器测试数据 - $(date)');
EOF"
    
    # 等待同步
    sleep 5
    
    # 检查本地是否收到数据
    local local_count=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "USE $ENGLISH_DB; SELECT COUNT(*) FROM $test_table WHERE layer='linux';" --skip-column-names --batch 2>/dev/null)
    
    if [ "$local_count" = "1" ]; then
        log_success "✅ 第二层同步正常: Linux服务器 -> 本地"
    else
        log_error "❌ 第二层同步失败: Linux服务器 -> 本地"
    fi
    
    # 第三层：Docker测试（如果可用）
    if docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT VERSION();" >/dev/null 2>&1; then
        log_info "第三层测试: 本地MySQL <-> Docker MySQL"
        
        # 同步数据到Docker
        mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --single-transaction $ENGLISH_DB $test_table | \
        docker exec -i test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" $ENGLISH_DB
        
        # 在Docker中添加测试数据
        docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
USE $ENGLISH_DB;
INSERT INTO $test_table (layer, test_data) VALUES 
('docker', 'Docker MySQL测试数据 - $(date)');
EOF
        
        # 检查Docker中的数据
        local docker_count=$(docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "USE $ENGLISH_DB; SELECT COUNT(*) FROM $test_table;" --skip-column-names --batch 2>/dev/null)
        
        if [ "$docker_count" -ge "3" ]; then
            log_success "✅ 第三层Docker环境正常"
        else
            log_warning "⚠️ 第三层Docker环境数据不完整"
        fi
    else
        log_warning "第三层Docker环境不可用，跳过测试"
    fi
    
    # 显示测试结果摘要
    log_info "测试结果摘要:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE $ENGLISH_DB;
    SELECT layer, test_data, sync_timestamp FROM $test_table ORDER BY sync_timestamp;
    " 2>/dev/null
    
    # 清理测试表
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "DROP TABLE IF EXISTS $ENGLISH_DB.$test_table;" 2>/dev/null
    ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'DROP TABLE IF EXISTS $ENGLISH_DB.$test_table;'" 2>/dev/null
    if docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT VERSION();" >/dev/null 2>&1; then
        docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "DROP TABLE IF EXISTS $ENGLISH_DB.$test_table;" 2>/dev/null
    fi
}

# 生成同步状态报告
generate_sync_status_report() {
    log_header "生成同步状态报告"
    
    local report_file="/tmp/three-layer-sync-report-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=== FinancialSystem 三层数据库同步状态报告 ==="
        echo "生成时间: $(date)"
        echo "架构: Linux服务器 ⇄ 本地MySQL ⇄ Docker MySQL"
        echo ""
        
        echo "=== 环境信息 ==="
        echo "本地IP: $LOCAL_IP"
        echo "Linux服务器IP: $LINUX_IP"
        echo "Docker测试端口: $TEST_DOCKER_PORT"
        echo "英文数据库: $ENGLISH_DB"
        echo ""
        
        echo "=== 复制状态 ==="
        echo "本地MySQL从服务器状态:"
        mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_Error)" || echo "未配置"
        
        echo ""
        echo "Linux服务器MySQL从服务器状态:"
        ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW SLAVE STATUS\G'" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_Error)" || echo "未配置"
        
        echo ""
        echo "=== 数据库状态 ==="
        echo "本地英文数据库表数量:"
        mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='$ENGLISH_DB' AND table_type='BASE TABLE';" 2>/dev/null
        
        echo "Linux服务器英文数据库表数量:"
        ssh $LINUX_SERVER "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema=\"$ENGLISH_DB\" AND table_type=\"BASE TABLE\";'" 2>/dev/null
        
        if docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT VERSION();" >/dev/null 2>&1; then
            echo "Docker测试环境英文数据库表数量:"
            docker exec test-mysql-sync mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema='$ENGLISH_DB' AND table_type='BASE TABLE';" 2>/dev/null
        else
            echo "Docker测试环境: 不可用"
        fi
        
        echo ""
        echo "=== 网络连接 ==="
        echo "Linux服务器连接: $(ping -c 1 $LINUX_IP >/dev/null 2>&1 && echo '正常' || echo '失败')"
        echo "SSH连接: $(ssh -o ConnectTimeout=5 -o BatchMode=yes $LINUX_SERVER "echo 'OK'" 2>/dev/null && echo '正常' || echo '失败')"
        
    } > "$report_file"
    
    log_success "同步状态报告已生成: $report_file"
    
    # 显示报告内容
    cat "$report_file"
}

# 主函数
main() {
    log_header "FinancialSystem 三层数据库同步验证"
    
    echo -e "${PURPLE}🚀 开始三层数据库同步验证和实施...${NC}\n"
    
    # 步骤1: 解决同步障碍
    if ! resolve_sync_barriers; then
        log_error "同步障碍解决失败，请检查日志"
        exit 1
    fi
    
    # 步骤2: 重新建立双向复制
    establish_bidirectional_replication
    
    # 步骤3: 创建Docker测试环境
    create_docker_test_environment
    
    # 等待复制稳定
    log_info "等待复制链路稳定..."
    sleep 10
    
    # 步骤4: 验证三层同步
    validate_three_layer_sync
    
    # 步骤5: 生成状态报告
    generate_sync_status_report
    
    echo ""
    log_success "🎉 三层数据库同步验证完成!"
    log_info "📊 查看生成的报告了解详细状态"
    log_info "🔄 建议定期运行验证以确保同步正常"
}

# 显示帮助信息
show_help() {
    cat << EOF
FinancialSystem 三层数据库同步验证工具

功能:
- 解决MySQL复制同步障碍
- 重建双向复制链路
- 创建Docker测试环境
- 验证三层同步架构
- 生成详细状态报告

用法:
  $0                    # 执行完整验证
  $0 --help             # 显示帮助信息
  $0 --repair-only      # 仅修复同步问题
  $0 --test-only        # 仅测试同步状态

架构:
  Linux服务器(**********) ⇄ 本地MySQL ⇄ Docker MySQL(:3307)

EOF
}

# 仅修复模式
repair_only() {
    log_header "仅修复同步问题"
    resolve_sync_barriers
    establish_bidirectional_replication
    log_success "修复完成"
}

# 仅测试模式
test_only() {
    log_header "仅测试同步状态"
    validate_three_layer_sync
    generate_sync_status_report
}

# 参数解析
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --repair-only)
        repair_only
        exit 0
        ;;
    --test-only)
        test_only
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac