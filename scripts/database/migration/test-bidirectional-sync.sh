#!/bin/bash

# MySQL双向同步测试脚本
# 测试本地和Linux服务器之间的数据同步

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"
LOCAL_IP="***********"

# 修复本地MySQL复制用户认证方式
fix_local_replication_user() {
    log_info "修复本地MySQL复制用户认证方式..."
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 修改复制用户使用mysql_native_password认证
ALTER USER 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY 'Zlb&198838';
FLUSH PRIVILEGES;

-- 显示用户信息
SELECT User, Host, plugin FROM mysql.user WHERE User = 'repl_user';
EOF
    
    log_success "本地复制用户认证方式已修复"
}

# 修复Linux服务器MySQL复制用户认证方式
fix_linux_replication_user() {
    log_info "修复Linux服务器MySQL复制用户认证方式..."
    
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' << 'EOF'
-- 修改复制用户使用mysql_native_password认证
ALTER USER 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY 'Zlb&198838';
FLUSH PRIVILEGES;

-- 显示用户信息
SELECT User, Host, plugin FROM mysql.user WHERE User = 'repl_user';
EOF"
    
    log_success "Linux服务器复制用户认证方式已修复"
}

# 配置本地MySQL从Linux服务器复制
setup_local_slave() {
    log_info "配置本地MySQL从Linux服务器复制..."
    
    # 获取Linux服务器的master状态
    local master_info=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW MASTER STATUS;'" | tail -n +2)
    local master_file=$(echo "$master_info" | awk '{print $1}')
    local master_pos=$(echo "$master_info" | awk '{print $2}')
    
    log_info "Linux服务器Master状态: File=$master_file, Position=$master_pos"
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 停止现有复制
STOP SLAVE;
RESET SLAVE ALL;

-- 配置从Linux服务器复制
CHANGE MASTER TO
    MASTER_HOST='**********',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838',
    MASTER_PORT=3306,
    MASTER_LOG_FILE='$master_file',
    MASTER_LOG_POS=$master_pos;

-- 启动复制
START SLAVE;

-- 显示状态
SHOW SLAVE STATUS\G
EOF
    
    log_success "本地MySQL从Linux服务器复制配置完成"
}

# 配置Linux服务器从本地复制
setup_linux_slave() {
    log_info "配置Linux服务器从本地MySQL复制..."
    
    # 获取本地master状态
    local master_info=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW MASTER STATUS;" | tail -n +2)
    local master_file=$(echo "$master_info" | awk '{print $1}')
    local master_pos=$(echo "$master_info" | awk '{print $2}')
    
    log_info "本地Master状态: File=$master_file, Position=$master_pos"
    
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' << 'EOF'
-- 停止现有复制
STOP SLAVE;
RESET SLAVE ALL;

-- 配置从本地服务器复制
CHANGE MASTER TO
    MASTER_HOST='$LOCAL_IP',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838',
    MASTER_PORT=3306,
    MASTER_LOG_FILE='$master_file',
    MASTER_LOG_POS=$master_pos;

-- 启动复制
START SLAVE;

-- 显示状态
SHOW SLAVE STATUS\G
EOF"
    
    log_success "Linux服务器从本地MySQL复制配置完成"
}

# 测试双向同步
test_sync() {
    log_info "测试双向同步功能..."
    
    local test_table="sync_test_$(date +%s)"
    
    # 测试1: 本地到Linux服务器
    log_info "测试1: 本地 -> Linux服务器"
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
USE \`逾期债权数据库\`;
CREATE TABLE $test_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message VARCHAR(255),
    source VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO $test_table (message, source) VALUES ('Test from local', 'local');
SELECT * FROM $test_table;
EOF
    
    # 等待同步
    sleep 3
    
    # 检查Linux服务器
    log_info "检查Linux服务器是否收到数据..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    USE \`逾期债权数据库\`;
    SELECT * FROM $test_table WHERE source = \"local\";
    '" && log_success "本地到Linux服务器同步成功" || log_error "本地到Linux服务器同步失败"
    
    # 测试2: Linux服务器到本地
    log_info "测试2: Linux服务器 -> 本地"
    
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    USE \`逾期债权数据库\`;
    INSERT INTO $test_table (message, source) VALUES (\"Test from linux\", \"linux\");
    SELECT * FROM $test_table WHERE source = \"linux\";
    '"
    
    # 等待同步
    sleep 3
    
    # 检查本地
    log_info "检查本地是否收到数据..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    SELECT * FROM $test_table WHERE source = 'linux';
    " && log_success "Linux服务器到本地同步成功" || log_error "Linux服务器到本地同步失败"
    
    # 显示完整测试结果
    log_info "完整测试结果:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    SELECT * FROM $test_table ORDER BY created_at;
    "
    
    # 清理测试表
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    DROP TABLE $test_table;
    "
    
    log_info "测试完成，已清理测试数据"
}

# 显示同步状态
show_sync_status() {
    log_info "显示双向同步状态..."
    
    echo "=== 本地MySQL状态 ==="
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 'Master Status:' as Info;
    SHOW MASTER STATUS;
    " 2>/dev/null
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SHOW SLAVE STATUS\G
    " 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)"
    
    echo ""
    echo "=== Linux服务器MySQL状态 ==="
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    SELECT \"Master Status:\" as Info;
    SHOW MASTER STATUS;
    '" 2>/dev/null
    
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    SHOW SLAVE STATUS\G
    '" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)"
}

# 主函数
main() {
    log_info "🚀 开始MySQL双向同步配置和测试..."
    
    # 修复认证方式
    fix_local_replication_user
    fix_linux_replication_user
    
    # 配置双向复制
    setup_linux_slave
    setup_local_slave
    
    # 显示状态
    show_sync_status
    
    # 测试同步
    echo ""
    read -p "是否要测试双向同步功能？(y/n): " test_choice
    if [ "$test_choice" = "y" ] || [ "$test_choice" = "Y" ]; then
        test_sync
    fi
    
    log_success "🎉 MySQL双向同步配置完成！"
    log_info "现在您的本地和Linux服务器数据库将保持双向同步"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
