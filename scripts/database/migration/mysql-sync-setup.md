# MySQL双向同步配置指南

## 🎯 方案概述

使用MySQL原生的双主复制（Master-Master Replication）实现本地和Linux服务器之间的数据库双向同步。

## 📋 配置步骤

### 第一步：本地MySQL配置

1. **修改本地MySQL配置文件** (`/etc/mysql/my.cnf` 或 `/usr/local/etc/my.cnf`)

```ini
[mysqld]
# 服务器ID（本地）
server-id = 1

# 启用二进制日志
log-bin = mysql-bin
binlog-format = ROW

# 指定要同步的数据库
binlog-do-db = 逾期债权数据库
binlog-do-db = user_system

# 自增长字段配置（避免冲突）
auto-increment-increment = 2
auto-increment-offset = 1

# 启用relay log
relay-log = relay-bin
relay-log-index = relay-bin.index

# 启用GTID（推荐）
gtid-mode = ON
enforce-gtid-consistency = ON
```

2. **重启本地MySQL服务**

```bash
# macOS
brew services restart mysql

# 或者
sudo /usr/local/mysql/support-files/mysql.server restart
```

3. **创建复制用户**

```sql
-- 连接到本地MySQL
mysql -u root -p

-- 创建复制用户
CREATE USER 'repl_user'@'%' IDENTIFIED BY 'Zlb&198838_repl';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
GRANT SELECT ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;

-- 查看主服务器状态
SHOW MASTER STATUS;
```

### 第二步：Linux服务器MySQL配置

1. **修改Docker容器MySQL配置**

创建自定义MySQL配置文件：

```bash
# 在Linux服务器上创建配置文件
cat > /home/<USER>/下载/FinancialSystem-Production-Deploy/mysql-master.cnf << 'EOF'
[mysqld]
# 服务器ID（Linux服务器）
server-id = 2

# 启用二进制日志
log-bin = mysql-bin
binlog-format = ROW

# 指定要同步的数据库
binlog-do-db = 逾期债权数据库
binlog-do-db = user_system

# 自增长字段配置（避免冲突）
auto-increment-increment = 2
auto-increment-offset = 2

# 启用relay log
relay-log = relay-bin
relay-log-index = relay-bin.index

# 启用GTID
gtid-mode = ON
enforce-gtid-consistency = ON

# 网络配置
bind-address = 0.0.0.0
EOF
```

2. **更新docker-compose.yml**

```yaml
services:
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: financial-mysql
    environment:
      MYSQL_ROOT_PASSWORD: Zlb&198838
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./mysql-master.cnf:/etc/mysql/conf.d/mysql-master.cnf:ro
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
```

3. **重启MySQL容器**

```bash
docker-compose down
docker-compose up -d mysql
```

4. **配置复制用户**

```sql
-- 连接到Linux MySQL
docker exec -it financial-mysql mysql -u root -p

-- 创建复制用户
CREATE USER 'repl_user'@'%' IDENTIFIED BY 'Zlb&198838_repl';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
GRANT SELECT ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;

-- 查看主服务器状态
SHOW MASTER STATUS;
```

### 第三步：配置双向复制

1. **在本地MySQL上配置Linux服务器为从服务器**

```sql
-- 连接到本地MySQL
mysql -u root -p

-- 配置从服务器（指向Linux服务器）
CHANGE MASTER TO
    MASTER_HOST='**********',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838_repl',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=1;

-- 启动从服务器
START SLAVE;

-- 检查状态
SHOW SLAVE STATUS\G
```

2. **在Linux MySQL上配置本地为从服务器**

```sql
-- 连接到Linux MySQL
docker exec -it financial-mysql mysql -u root -p

-- 获取本地IP（需要替换为实际IP）
-- 配置从服务器（指向本地）
CHANGE MASTER TO
    MASTER_HOST='你的本地IP',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838_repl',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=1;

-- 启动从服务器
START SLAVE;

-- 检查状态
SHOW SLAVE STATUS\G
```

## 🔧 监控和维护

### 同步状态检查脚本

```bash
#!/bin/bash
# 检查同步状态

echo "=== 本地MySQL同步状态 ==="
mysql -u root -p -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master)"

echo "=== Linux MySQL同步状态 ==="
ssh admin@********** "docker exec financial-mysql mysql -u root -pZlb&198838 -e 'SHOW SLAVE STATUS\G'" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master)"
```

## ⚠️ 注意事项

1. **网络要求**：确保两台服务器之间网络稳定，端口3306互通
2. **防火墙配置**：开放MySQL端口3306
3. **数据一致性**：初始同步前确保两边数据一致
4. **冲突处理**：使用不同的auto-increment-offset避免主键冲突
5. **监控**：定期检查同步状态，及时处理同步延迟或错误

## 🚨 故障排除

### 常见问题

1. **同步停止**：检查网络连接和错误日志
2. **主键冲突**：调整auto-increment配置
3. **权限问题**：确认复制用户权限正确
4. **GTID问题**：确保两边GTID配置一致

### 重置同步

```sql
-- 停止同步
STOP SLAVE;

-- 重置同步
RESET SLAVE ALL;

-- 重新配置
CHANGE MASTER TO ...;

-- 启动同步
START SLAVE;
```

---

## 🔄 方案二：使用专业同步工具

### 1. SymmetricDS（推荐用于复杂场景）

SymmetricDS是专业的数据库同步工具，支持双向同步：

```bash
# 下载SymmetricDS
wget https://github.com/JumpMind/symmetric-ds/releases/download/symmetric-ds-3.14.7/symmetric-ds-3.14.7.zip
unzip symmetric-ds-3.14.7.zip
cd symmetric-ds-3.14.7
```

### 2. MySQL Shell + MySQL Router

使用MySQL官方工具实现同步：

```bash
# 安装MySQL Shell
brew install mysql-shell  # macOS

# 配置InnoDB Cluster
mysqlsh --uri root@localhost:3306
```

### 3. Percona XtraDB Cluster

高可用MySQL集群解决方案：

```bash
# 安装Percona XtraDB Cluster
# 提供自动故障转移和数据同步
```

---

## 🔄 方案三：应用层同步（简单可控）

### 创建同步服务

```java
@Service
public class DatabaseSyncService {

    @Autowired
    private PrimaryDataSource primaryDataSource;

    @Autowired
    private RemoteDataSource remoteDataSource;

    @Scheduled(fixedRate = 30000) // 每30秒同步一次
    public void syncData() {
        // 实现数据同步逻辑
        syncOverdueDebtData();
        syncUserSystemData();
    }

    private void syncOverdueDebtData() {
        // 同步逾期债权数据
    }

    private void syncUserSystemData() {
        // 同步用户系统数据
    }
}
```

---

## 🔄 方案四：使用云服务（最简单）

### 1. 阿里云DTS（数据传输服务）

```bash
# 配置数据传输服务
# 支持实时同步、双向同步
# 提供监控和报警
```

### 2. 腾讯云DTS

```bash
# 类似阿里云DTS
# 支持MySQL双向同步
```

---

## 📊 方案对比

| 方案 | 复杂度 | 可靠性 | 成本 | 推荐度 |
|------|--------|--------|------|--------|
| MySQL主从复制 | 中等 | 高 | 免费 | ⭐⭐⭐⭐⭐ |
| SymmetricDS | 高 | 高 | 免费 | ⭐⭐⭐⭐ |
| 应用层同步 | 低 | 中等 | 免费 | ⭐⭐⭐ |
| 云服务DTS | 低 | 高 | 付费 | ⭐⭐⭐⭐ |

---

## 🚀 快速开始（推荐方案）

基于您的需求，我推荐使用**MySQL主从复制**方案，原因：

1. ✅ **原生支持**：MySQL内置功能，稳定可靠
2. ✅ **零成本**：无需额外软件或服务费用
3. ✅ **实时同步**：延迟通常在毫秒级别
4. ✅ **易于监控**：可通过SQL命令查看同步状态
5. ✅ **故障恢复**：支持断点续传和自动重连

### 立即开始配置

1. **第一步**：配置本地MySQL（5分钟）
2. **第二步**：配置Linux MySQL（5分钟）
3. **第三步**：建立双向复制（5分钟）
4. **第四步**：测试和监控（5分钟）

总计约20分钟即可完成配置！

```bash
#!/bin/bash
# 检查同步状态

echo "=== 本地MySQL同步状态 ==="
mysql -u root -p -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master)"

echo "=== Linux MySQL同步状态 ==="
ssh admin@********** "docker exec financial-mysql mysql -u root -pZlb&198838 -e 'SHOW SLAVE STATUS\G'" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master)"
```

## ⚠️ 注意事项

1. **网络要求**：确保两台服务器之间网络稳定，端口3306互通
2. **防火墙配置**：开放MySQL端口3306
3. **数据一致性**：初始同步前确保两边数据一致
4. **冲突处理**：使用不同的auto-increment-offset避免主键冲突
5. **监控**：定期检查同步状态，及时处理同步延迟或错误

## 🚨 故障排除

### 常见问题

1. **同步停止**：检查网络连接和错误日志
2. **主键冲突**：调整auto-increment配置
3. **权限问题**：确认复制用户权限正确
4. **GTID问题**：确保两边GTID配置一致

### 重置同步

```sql
-- 停止同步
STOP SLAVE;

-- 重置同步
RESET SLAVE ALL;

-- 重新配置
CHANGE MASTER TO ...;

-- 启动同步
START SLAVE;
```
