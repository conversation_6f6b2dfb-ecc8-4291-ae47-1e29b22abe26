#!/bin/bash

# MySQL双向同步状态检查脚本
# 检查本地和Linux服务器之间的MySQL双向同步状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@10.25.1.85"
MYSQL_CONTAINER="financial-mysql"

# 检查本地MySQL状态
check_local_mysql_status() {
    log_info "检查本地MySQL状态..."
    
    echo "=== 本地MySQL主服务器状态 ==="
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 'Master Status:' as Status;
    SHOW MASTER STATUS;
    
    SELECT 'Binary Logs:' as Status;
    SHOW BINARY LOGS;
    " 2>/dev/null || log_error "本地MySQL连接失败"
    
    echo ""
    echo "=== 本地MySQL从服务器状态 ==="
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SHOW SLAVE STATUS\G
    " 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)" || log_warning "本地MySQL无从服务器配置"
}

# 检查Linux服务器MySQL状态
check_linux_mysql_status() {
    log_info "检查Linux服务器MySQL状态..."
    
    echo "=== Linux服务器MySQL主服务器状态 ==="
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    SELECT \"Master Status:\" as Status;
    SHOW MASTER STATUS;
    
    SELECT \"Binary Logs:\" as Status;
    SHOW BINARY LOGS;
    '" 2>/dev/null || log_error "Linux服务器MySQL连接失败"
    
    echo ""
    echo "=== Linux服务器MySQL从服务器状态 ==="
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    SHOW SLAVE STATUS\G
    '" 2>/dev/null | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)" || log_warning "Linux服务器MySQL无从服务器配置"
}

# 测试双向同步
test_bidirectional_sync() {
    log_info "测试双向同步功能..."
    
    local test_table="sync_test_$(date +%s)"
    local test_data="Test data $(date)"
    
    echo "=== 测试从本地到Linux服务器的同步 ==="
    
    # 在本地创建测试表和数据
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
USE \`逾期债权数据库\`;
CREATE TABLE IF NOT EXISTS $test_table (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
INSERT INTO $test_table (message) VALUES ('$test_data - from local');
SELECT * FROM $test_table WHERE message LIKE '%from local%';
EOF
    
    # 等待同步
    sleep 5
    
    # 检查Linux服务器是否同步
    echo "检查Linux服务器是否收到数据..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    USE \`逾期债权数据库\`;
    SELECT * FROM $test_table WHERE message LIKE \"%from local%\";
    '" 2>/dev/null && log_success "本地到Linux服务器同步正常" || log_error "本地到Linux服务器同步失败"
    
    echo ""
    echo "=== 测试从Linux服务器到本地的同步 ==="
    
    # 在Linux服务器创建测试数据
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    USE \`逾期债权数据库\`;
    INSERT INTO $test_table (message) VALUES (\"$test_data - from linux\");
    SELECT * FROM $test_table WHERE message LIKE \"%from linux%\";
    '" 2>/dev/null
    
    # 等待同步
    sleep 5
    
    # 检查本地是否同步
    echo "检查本地是否收到数据..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    SELECT * FROM $test_table WHERE message LIKE '%from linux%';
    " 2>/dev/null && log_success "Linux服务器到本地同步正常" || log_error "Linux服务器到本地同步失败"
    
    # 清理测试表
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    DROP TABLE IF EXISTS $test_table;
    " 2>/dev/null
    
    log_info "测试完成，已清理测试数据"
}

# 显示数据库对比
compare_databases() {
    log_info "对比两端数据库状态..."
    
    echo "=== 本地数据库表统计 ==="
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 
        table_schema as '数据库',
        COUNT(*) as '表数量'
    FROM information_schema.tables 
    WHERE table_schema IN ('逾期债权数据库', 'user_system')
    GROUP BY table_schema;
    
    SELECT 
        table_schema as '数据库',
        table_name as '表名',
        table_rows as '行数'
    FROM information_schema.tables 
    WHERE table_schema IN ('逾期债权数据库', 'user_system')
    ORDER BY table_schema, table_name;
    " 2>/dev/null
    
    echo ""
    echo "=== Linux服务器数据库表统计 ==="
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    SELECT 
        table_schema as \"数据库\",
        COUNT(*) as \"表数量\"
    FROM information_schema.tables 
    WHERE table_schema IN (\"逾期债权数据库\", \"user_system\")
    GROUP BY table_schema;
    
    SELECT 
        table_schema as \"数据库\",
        table_name as \"表名\",
        table_rows as \"行数\"
    FROM information_schema.tables 
    WHERE table_schema IN (\"逾期债权数据库\", \"user_system\")
    ORDER BY table_schema, table_name;
    '" 2>/dev/null
}

# 显示网络连接状态
check_network_connectivity() {
    log_info "检查网络连接状态..."
    
    # 检查到Linux服务器的连接
    if ping -c 3 10.25.1.85 >/dev/null 2>&1; then
        log_success "到Linux服务器的网络连接正常"
    else
        log_error "无法连接到Linux服务器"
    fi
    
    # 检查MySQL端口
    if nc -z 10.25.1.85 3306 2>/dev/null; then
        log_success "Linux服务器MySQL端口可访问"
    else
        log_error "Linux服务器MySQL端口不可访问"
    fi
    
    # 检查SSH连接
    if ssh -o ConnectTimeout=5 "$LINUX_SERVER" "echo 'SSH连接正常'" >/dev/null 2>&1; then
        log_success "SSH连接正常"
    else
        log_error "SSH连接失败"
    fi
}

# 主函数
main() {
    log_info "🔍 开始检查MySQL双向同步状态..."
    
    # 检查网络连接
    check_network_connectivity
    
    echo ""
    # 检查本地MySQL状态
    check_local_mysql_status
    
    echo ""
    # 检查Linux服务器MySQL状态
    check_linux_mysql_status
    
    echo ""
    # 对比数据库
    compare_databases
    
    echo ""
    # 测试双向同步
    read -p "是否要测试双向同步功能？(y/n): " test_sync
    if [ "$test_sync" = "y" ] || [ "$test_sync" = "Y" ]; then
        test_bidirectional_sync
    fi
    
    log_success "🎉 MySQL双向同步状态检查完成！"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
