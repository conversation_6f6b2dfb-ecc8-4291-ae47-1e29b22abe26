#!/bin/bash

# MySQL双向复制 - Linux服务器端配置脚本
# 配置Linux服务器MySQL作为主服务器，同时从本地接收复制

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
MYSQL_CONTAINER="financial-mysql"
MYSQL_ROOT_PASSWORD="Zlb&198838"
REPL_USER="repl_user"
REPL_PASSWORD="Zlb&198838"

# 检查Docker容器状态
check_mysql_container() {
    log_info "检查MySQL容器状态..."
    
    if ! docker ps | grep -q "$MYSQL_CONTAINER"; then
        log_error "MySQL容器未运行，请先启动容器"
        exit 1
    fi
    
    log_success "MySQL容器运行正常"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if docker exec "$MYSQL_CONTAINER" mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "MySQL连接正常"
        return 0
    else
        log_error "MySQL连接失败，请检查密码和容器状态"
        return 1
    fi
}

# 检查当前复制状态
check_current_replication() {
    log_info "检查当前复制状态..."
    
    docker exec "$MYSQL_CONTAINER" mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 'Master Status:' as Info;
    SHOW MASTER STATUS;
    SELECT 'Slave Status:' as Info;
    SHOW SLAVE STATUS\G
    " 2>/dev/null || true
}

# 创建复制用户
create_replication_user() {
    log_info "创建复制用户..."
    
    docker exec "$MYSQL_CONTAINER" mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 创建复制用户
CREATE USER IF NOT EXISTS '$REPL_USER'@'%' IDENTIFIED BY '$REPL_PASSWORD';
GRANT REPLICATION SLAVE ON *.* TO '$REPL_USER'@'%';
GRANT REPLICATION CLIENT ON *.* TO '$REPL_USER'@'%';
FLUSH PRIVILEGES;

-- 显示用户
SELECT User, Host FROM mysql.user WHERE User = '$REPL_USER';
EOF
    
    log_success "复制用户创建完成"
}

# 配置从本地服务器的复制
setup_slave_from_local() {
    local local_ip="$1"
    
    if [ -z "$local_ip" ]; then
        log_error "请提供本地IP地址"
        echo "用法: $0 <本地IP地址>"
        exit 1
    fi
    
    log_info "配置从本地服务器($local_ip)的复制..."
    
    docker exec "$MYSQL_CONTAINER" mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 停止现有的复制（如果有）
STOP SLAVE;
RESET SLAVE ALL;

-- 配置从本地服务器复制
CHANGE MASTER TO
    MASTER_HOST='$local_ip',
    MASTER_USER='$REPL_USER',
    MASTER_PASSWORD='$REPL_PASSWORD',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=0;

-- 启动复制
START SLAVE;

-- 显示状态
SHOW SLAVE STATUS\G
EOF
    
    log_success "从本地服务器的复制配置完成"
}

# 显示主服务器状态
show_master_status() {
    log_info "显示主服务器状态..."
    
    docker exec "$MYSQL_CONTAINER" mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 'Master Status Information:' as Info;
    SHOW MASTER STATUS;
    
    SELECT 'Binary Log Files:' as Info;
    SHOW BINARY LOGS;
    "
}

# 测试网络连接
test_network_connection() {
    local local_ip="$1"
    
    log_info "测试到本地服务器的网络连接..."
    
    if ping -c 3 "$local_ip" >/dev/null 2>&1; then
        log_success "网络连接正常"
    else
        log_warning "无法ping通本地服务器，请检查网络连接"
    fi
    
    # 测试MySQL端口
    if nc -z "$local_ip" 3306 2>/dev/null; then
        log_success "MySQL端口3306可访问"
    else
        log_warning "MySQL端口3306不可访问，请检查防火墙设置"
    fi
}

# 显示数据库状态
show_database_status() {
    log_info "显示数据库状态..."
    
    docker exec "$MYSQL_CONTAINER" mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 'Database List:' as Info;
    SHOW DATABASES;
    
    SELECT 'Overdue Debt Database Tables:' as Info;
    USE \`逾期债权数据库\`;
    SHOW TABLES;
    
    SELECT 'User System Database Tables:' as Info;
    USE user_system;
    SHOW TABLES;
    "
}

# 显示配置信息
show_configuration_info() {
    local local_ip="$1"
    
    log_info "配置信息摘要:"
    echo "=================================="
    echo "Linux MySQL服务器: **********:3306 (容器: $MYSQL_CONTAINER)"
    echo "本地服务器: $local_ip:3306"
    echo "复制用户: $REPL_USER"
    echo "复制密码: $REPL_PASSWORD"
    echo "=================================="
    echo ""
    echo "验证步骤："
    echo "1. 在本地数据库中插入测试数据"
    echo "2. 检查Linux服务器是否同步"
    echo "3. 在Linux服务器中插入测试数据"
    echo "4. 检查本地是否同步"
}

# 主函数
main() {
    local local_ip="$1"
    
    log_info "🚀 开始配置Linux服务器MySQL双向复制..."
    
    if [ -z "$local_ip" ]; then
        log_error "请提供本地IP地址"
        echo "用法: $0 <本地IP地址>"
        exit 1
    fi
    
    # 检查Docker容器
    check_mysql_container
    
    # 检查MySQL连接
    if ! check_mysql_connection; then
        exit 1
    fi
    
    # 检查当前状态
    check_current_replication
    
    # 显示数据库状态
    show_database_status
    
    # 测试网络连接
    test_network_connection "$local_ip"
    
    # 创建复制用户
    create_replication_user
    
    # 配置从本地服务器的复制
    setup_slave_from_local "$local_ip"
    
    # 显示主服务器状态
    show_master_status
    
    # 显示配置信息
    show_configuration_info "$local_ip"
    
    log_success "🎉 Linux服务器MySQL双向复制配置完成！"
    log_info "现在可以测试双向同步功能"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
