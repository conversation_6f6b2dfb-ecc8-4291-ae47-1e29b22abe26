# MySQL双向同步配置指南

## 🎯 方案概述

使用MySQL原生的双主复制（Master-Master Replication）实现本地和Linux服务器之间的数据库双向同步。

## 📋 配置步骤

### 第一步：本地MySQL配置

#### 1. 修改本地MySQL配置文件
```bash
# 找到MySQL配置文件位置
mysql --help | grep "Default options" -A 1

# 编辑配置文件 (通常是 /etc/mysql/my.cnf 或 /usr/local/etc/my.cnf)
sudo nano /usr/local/etc/my.cnf
```

#### 2. 添加复制配置
```ini
[mysqld]
# 服务器ID（本地）
server-id = 1

# 启用二进制日志
log-bin = mysql-bin
log-bin-index = mysql-bin.index

# 指定要同步的数据库
binlog-do-db = 逾期债权数据库
binlog-do-db = user_system

# 复制过滤
replicate-do-db = 逾期债权数据库
replicate-do-db = user_system

# 自动递增设置（避免冲突）
auto-increment-increment = 2
auto-increment-offset = 1

# 启用GTID（推荐）
gtid-mode = ON
enforce-gtid-consistency = ON

# 网络配置
bind-address = 0.0.0.0
```

#### 3. 重启本地MySQL
```bash
sudo brew services restart mysql
# 或
sudo systemctl restart mysql
```

### 第二步：Linux服务器MySQL配置

#### 1. 修改Docker容器MySQL配置
创建自定义MySQL配置文件：

```bash
# 在Linux服务器上创建配置文件
cat > /home/<USER>/下载/FinancialSystem-Production-Deploy/mysql-master.cnf << 'EOF'
[mysqld]
# 服务器ID（Linux服务器）
server-id = 2

# 启用二进制日志
log-bin = mysql-bin
log-bin-index = mysql-bin.index

# 指定要同步的数据库
binlog-do-db = 逾期债权数据库
binlog-do-db = user_system

# 复制过滤
replicate-do-db = 逾期债权数据库
replicate-do-db = user_system

# 自动递增设置（避免冲突）
auto-increment-increment = 2
auto-increment-offset = 2

# 启用GTID
gtid-mode = ON
enforce-gtid-consistency = ON

# 网络配置
bind-address = 0.0.0.0
EOF
```

#### 2. 更新docker-compose.yml
```yaml
services:
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: financial-mysql
    environment:
      MYSQL_ROOT_PASSWORD: Zlb&198838
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-master.cnf:/etc/mysql/conf.d/master.cnf:ro
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
```

### 第三步：创建复制用户

#### 1. 在本地MySQL创建复制用户
```sql
-- 连接到本地MySQL
mysql -u root -p

-- 创建复制用户
CREATE USER 'repl_user'@'%' IDENTIFIED BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;

-- 查看主服务器状态
SHOW MASTER STATUS;
```

#### 2. 在Linux服务器MySQL创建复制用户
```sql
-- 连接到Linux服务器MySQL
docker exec -it financial-mysql mysql -u root -p

-- 创建复制用户
CREATE USER 'repl_user'@'%' IDENTIFIED BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;

-- 查看主服务器状态
SHOW MASTER STATUS;
```

### 第四步：配置双向复制

#### 1. 在本地MySQL配置从Linux服务器复制
```sql
-- 在本地MySQL执行
CHANGE MASTER TO
    MASTER_HOST='**********',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=1;

-- 启动复制
START SLAVE;

-- 检查状态
SHOW SLAVE STATUS\G
```

#### 2. 在Linux服务器MySQL配置从本地复制
```sql
-- 在Linux服务器MySQL执行（需要替换YOUR_LOCAL_IP）
CHANGE MASTER TO
    MASTER_HOST='YOUR_LOCAL_IP',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=1;

-- 启动复制
START SLAVE;

-- 检查状态
SHOW SLAVE STATUS\G
```

## 🔧 自动化配置脚本

### 本地配置脚本
```bash
#!/bin/bash
# setup-local-replication.sh

echo "🔧 配置本地MySQL双向复制..."

# 获取本地IP
LOCAL_IP=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1)
echo "本地IP: $LOCAL_IP"

# 配置MySQL
mysql -u root -p"Zlb&198838" << EOF
-- 创建复制用户
CREATE USER IF NOT EXISTS 'repl_user'@'%' IDENTIFIED BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;

-- 重置复制配置
STOP SLAVE;
RESET SLAVE ALL;

-- 配置从Linux服务器复制
CHANGE MASTER TO
    MASTER_HOST='**********',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=1;

-- 启动复制
START SLAVE;

-- 显示状态
SHOW MASTER STATUS;
SHOW SLAVE STATUS\G
EOF

echo "✅ 本地MySQL配置完成"
```

### Linux服务器配置脚本
```bash
#!/bin/bash
# setup-linux-replication.sh

echo "🔧 配置Linux服务器MySQL双向复制..."

LOCAL_IP="$1"
if [ -z "$LOCAL_IP" ]; then
    echo "❌ 请提供本地IP地址"
    echo "用法: $0 <本地IP地址>"
    exit 1
fi

# 配置MySQL
docker exec financial-mysql mysql -u root -p"Zlb&198838" << EOF
-- 创建复制用户
CREATE USER IF NOT EXISTS 'repl_user'@'%' IDENTIFIED BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;

-- 重置复制配置
STOP SLAVE;
RESET SLAVE ALL;

-- 配置从本地复制
CHANGE MASTER TO
    MASTER_HOST='$LOCAL_IP',
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Zlb&198838',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=1;

-- 启动复制
START SLAVE;

-- 显示状态
SHOW MASTER STATUS;
SHOW SLAVE STATUS\G
EOF

echo "✅ Linux服务器MySQL配置完成"
```

## 📊 监控和维护

### 检查同步状态脚本
```bash
#!/bin/bash
# check-sync-status.sh

echo "📊 检查MySQL双向同步状态..."

echo "=== 本地MySQL状态 ==="
mysql -u root -p"Zlb&198838" -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Last_Error)"

echo "=== Linux服务器MySQL状态 ==="
ssh admin@********** "docker exec financial-mysql mysql -u root -p'Zlb&198838' -e 'SHOW SLAVE STATUS\G'" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Last_Error)"
```

## ⚠️ 注意事项

1. **防火墙配置**：确保3306端口在两端都开放
2. **网络稳定性**：确保两端网络连接稳定
3. **冲突处理**：使用不同的auto-increment设置避免主键冲突
4. **监控**：定期检查复制状态
5. **备份**：即使有同步，也要定期备份数据

## 🚨 故障排除

### 常见问题
1. **连接失败**：检查网络和防火墙
2. **权限问题**：确认复制用户权限正确
3. **GTID问题**：确保两端GTID配置一致
4. **数据冲突**：检查auto-increment配置
