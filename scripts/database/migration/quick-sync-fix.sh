#!/bin/bash
# 快速修复MySQL双向同步问题
# 作者: laoshu198838
# 日期: 2025-06-25
# 功能: 快速修复当前的双向同步问题，不重建数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

log_success() {
    echo -e "${GREEN}[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

# 配置变量
MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"
LOCAL_IP=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1)

# 重置复制配置
reset_replication() {
    log_info "重置MySQL复制配置..."
    
    # 停止并重置本地MySQL复制
    log_info "重置本地MySQL复制..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    STOP SLAVE;
    RESET SLAVE ALL;
    RESET MASTER;
    " 2>/dev/null || log_warning "本地MySQL复制重置失败"
    
    # 停止并重置Linux服务器MySQL复制
    log_info "重置Linux服务器MySQL复制..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    STOP SLAVE;
    RESET SLAVE ALL;
    RESET MASTER;
    '" 2>/dev/null || log_warning "Linux服务器MySQL复制重置失败"
    
    log_success "复制配置重置完成"
}

# 修复复制用户认证
fix_replication_users() {
    log_info "修复复制用户认证..."
    
    # 在本地MySQL修复复制用户
    log_info "修复本地MySQL复制用户..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    DROP USER IF EXISTS 'repl_user'@'%';
    CREATE USER 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
    GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
    FLUSH PRIVILEGES;
    "
    
    # 在Linux服务器MySQL修复复制用户
    log_info "修复Linux服务器MySQL复制用户..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    DROP USER IF EXISTS \"repl_user\"@\"%\";
    CREATE USER \"repl_user\"@\"%\" IDENTIFIED WITH mysql_native_password BY \"$MYSQL_ROOT_PASSWORD\";
    GRANT REPLICATION SLAVE ON *.* TO \"repl_user\"@\"%\";
    FLUSH PRIVILEGES;
    '"
    
    log_success "复制用户修复完成"
}

# 重新配置双向复制
setup_replication() {
    log_info "重新配置双向复制..."
    
    # 获取本地MySQL主服务器状态
    log_info "获取本地MySQL主服务器状态..."
    local_master_status=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW MASTER STATUS;" --skip-column-names)
    local_log_file=$(echo "$local_master_status" | awk '{print $1}')
    local_log_pos=$(echo "$local_master_status" | awk '{print $2}')
    
    # 获取Linux服务器MySQL主服务器状态
    log_info "获取Linux服务器MySQL主服务器状态..."
    linux_master_status=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW MASTER STATUS;' --skip-column-names")
    linux_log_file=$(echo "$linux_master_status" | awk '{print $1}')
    linux_log_pos=$(echo "$linux_master_status" | awk '{print $2}')
    
    log_info "本地主服务器状态: $local_log_file:$local_log_pos"
    log_info "Linux主服务器状态: $linux_log_file:$linux_log_pos"
    
    # 在本地MySQL配置从Linux服务器复制
    log_info "配置本地MySQL从Linux服务器复制..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    CHANGE MASTER TO
        MASTER_HOST='**********',
        MASTER_USER='repl_user',
        MASTER_PASSWORD='$MYSQL_ROOT_PASSWORD',
        MASTER_PORT=3306,
        MASTER_LOG_FILE='$linux_log_file',
        MASTER_LOG_POS=$linux_log_pos;
    START SLAVE;
    "
    
    # 在Linux服务器MySQL配置从本地复制
    log_info "配置Linux服务器MySQL从本地复制..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    CHANGE MASTER TO
        MASTER_HOST=\"$LOCAL_IP\",
        MASTER_USER=\"repl_user\",
        MASTER_PASSWORD=\"$MYSQL_ROOT_PASSWORD\",
        MASTER_PORT=3306,
        MASTER_LOG_FILE=\"$local_log_file\",
        MASTER_LOG_POS=$local_log_pos;
    START SLAVE;
    '"
    
    log_success "双向复制配置完成"
}

# 检查复制状态
check_replication_status() {
    log_info "检查复制状态..."
    
    echo ""
    echo "=== 本地MySQL从服务器状态 ==="
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)"
    
    echo ""
    echo "=== Linux服务器MySQL从服务器状态 ==="
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW SLAVE STATUS\G'" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)"
}

# 快速测试同步
quick_sync_test() {
    log_info "快速测试双向同步..."
    
    local test_table="quick_sync_test_$(date +%s)"
    
    # 在本地创建测试数据
    log_info "在本地创建测试数据..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`user_system\`;
    CREATE TABLE IF NOT EXISTS $test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        message VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    INSERT INTO $test_table (message) VALUES ('Quick test from local');
    "
    
    # 等待同步
    log_info "等待5秒进行同步..."
    sleep 5
    
    # 检查Linux服务器
    log_info "检查Linux服务器同步结果..."
    local sync_result=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    USE \`user_system\`;
    SELECT COUNT(*) FROM $test_table;
    ' --skip-column-names" 2>/dev/null || echo "0")
    
    if [ "$sync_result" = "1" ]; then
        log_success "✅ 快速同步测试成功"
    else
        log_warning "⚠️ 快速同步测试失败，可能需要更多时间"
    fi
    
    # 清理测试表
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`user_system\`;
    DROP TABLE IF EXISTS $test_table;
    " 2>/dev/null
    
    log_success "快速测试完成"
}

# 主函数
main() {
    log_info "🔧 开始快速修复MySQL双向同步..."
    
    echo ""
    log_warning "⚠️  此操作将："
    echo "   1. 重置现有的MySQL复制配置"
    echo "   2. 修复复制用户认证问题"
    echo "   3. 重新配置双向复制"
    echo "   4. 检查复制状态"
    echo ""
    echo "注意：此操作不会删除或修改数据，只修复同步配置"
    echo ""
    
    read -p "确认继续执行？(y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行修复步骤
    reset_replication
    fix_replication_users
    setup_replication
    
    # 等待复制启动
    log_info "等待复制启动..."
    sleep 5
    
    # 检查状态
    check_replication_status
    
    echo ""
    read -p "是否要进行快速同步测试？(y/n): " test_choice
    if [ "$test_choice" = "y" ] || [ "$test_choice" = "Y" ]; then
        quick_sync_test
    fi
    
    echo ""
    log_success "🎉 快速修复完成！"
    log_info "如果复制状态显示正常，双向同步应该已经恢复"
    log_info "如果仍有问题，请运行完整的数据库同步修复脚本"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
