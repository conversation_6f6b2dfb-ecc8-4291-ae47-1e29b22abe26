#!/bin/bash
# 数据库一致性修复和双向同步重建脚本
# 作者: laoshu198838
# 日期: 2025-06-25
# 功能: 修复本地和Linux服务器数据库不一致问题，重建双向同步

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

log_success() {
    echo -e "${GREEN}[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

log_error() {
    echo -e "${RED}[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1${NC}"
}

# 配置变量
MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"
LOCAL_IP=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1)
BACKUP_DIR="./database-backups/$(date +%Y%m%d_%H%M%S)"

# 创建备份目录
create_backup_directory() {
    log_info "创建备份目录: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    log_success "备份目录创建完成"
}

# 备份本地数据库
backup_local_databases() {
    log_info "开始备份本地数据库..."

    # 备份逾期债权数据库
    log_info "备份本地逾期债权数据库..."
    mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        "逾期债权数据库" > "$BACKUP_DIR/local_overdue_debt_db.sql"

    # 备份user_system数据库
    log_info "备份本地user_system数据库..."
    mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        "user_system" > "$BACKUP_DIR/local_user_system.sql"

    log_success "本地数据库备份完成"
}

# 备份Linux服务器数据库
backup_linux_databases() {
    log_info "开始备份Linux服务器数据库..."

    # 检查Linux服务器上的数据库
    log_info "检查Linux服务器数据库..."
    linux_dbs=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW DATABASES;' --skip-column-names" 2>/dev/null)

    # 备份逾期债权相关数据库
    if echo "$linux_dbs" | grep -q "逾期债权数据库"; then
        log_info "备份Linux服务器逾期债权数据库..."
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysqldump -u root -p'$MYSQL_ROOT_PASSWORD' \
            --single-transaction \
            --routines \
            --triggers \
            --events \
            --hex-blob \
            --default-character-set=utf8mb4 \
            '逾期债权数据库'" > "$BACKUP_DIR/linux_overdue_debt_db.sql" 2>/dev/null || log_warning "逾期债权数据库备份失败"
    fi

    if echo "$linux_dbs" | grep -q "overdue_debt_db"; then
        log_info "备份Linux服务器overdue_debt_db数据库..."
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysqldump -u root -p'$MYSQL_ROOT_PASSWORD' \
            --single-transaction \
            --routines \
            --triggers \
            --events \
            --hex-blob \
            --default-character-set=utf8mb4 \
            'overdue_debt_db'" > "$BACKUP_DIR/linux_overdue_debt_db_alt.sql" 2>/dev/null || log_warning "overdue_debt_db备份失败"
    fi

    # 备份user_system数据库
    log_info "备份Linux服务器user_system数据库..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysqldump -u root -p'$MYSQL_ROOT_PASSWORD' \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        'user_system'" > "$BACKUP_DIR/linux_user_system.sql" 2>/dev/null || log_warning "user_system备份失败"

    log_success "Linux服务器数据库备份完成"
}

# 停止现有的复制
stop_existing_replication() {
    log_info "停止现有的MySQL复制..."

    # 停止本地MySQL复制
    log_info "停止本地MySQL复制..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    STOP SLAVE;
    RESET SLAVE ALL;
    " 2>/dev/null || log_warning "本地MySQL复制停止失败或未配置"

    # 停止Linux服务器MySQL复制
    log_info "停止Linux服务器MySQL复制..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    STOP SLAVE;
    RESET SLAVE ALL;
    '" 2>/dev/null || log_warning "Linux服务器MySQL复制停止失败或未配置"

    log_success "现有复制已停止"
}

# 清理Linux服务器数据库
clean_linux_databases() {
    log_info "清理Linux服务器数据库..."

    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    -- 删除并重建逾期债权数据库
    DROP DATABASE IF EXISTS \`逾期债权数据库\`;
    DROP DATABASE IF EXISTS \`overdue_debt_db\`;
    CREATE DATABASE \`逾期债权数据库\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

    -- 删除并重建user_system数据库
    DROP DATABASE IF EXISTS \`user_system\`;
    CREATE DATABASE \`user_system\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    '"

    log_success "Linux服务器数据库清理完成"
}

# 同步数据到Linux服务器
sync_data_to_linux() {
    log_info "开始同步本地数据到Linux服务器..."

    # 同步逾期债权数据库
    log_info "同步逾期债权数据库到Linux服务器..."
    cat "$BACKUP_DIR/local_overdue_debt_db.sql" | ssh "$LINUX_SERVER" "docker exec -i $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' '逾期债权数据库'"

    # 同步user_system数据库
    log_info "同步user_system数据库到Linux服务器..."
    cat "$BACKUP_DIR/local_user_system.sql" | ssh "$LINUX_SERVER" "docker exec -i $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' 'user_system'"

    log_success "数据同步到Linux服务器完成"
}

# 配置MySQL复制用户
setup_replication_users() {
    log_info "配置MySQL复制用户..."

    # 在本地MySQL创建复制用户
    log_info "在本地MySQL创建复制用户..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    CREATE USER IF NOT EXISTS 'repl_user'@'%' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
    GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
    FLUSH PRIVILEGES;
    "

    # 在Linux服务器MySQL创建复制用户
    log_info "在Linux服务器MySQL创建复制用户..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    CREATE USER IF NOT EXISTS \"repl_user\"@\"%\" IDENTIFIED WITH mysql_native_password BY \"$MYSQL_ROOT_PASSWORD\";
    GRANT REPLICATION SLAVE ON *.* TO \"repl_user\"@\"%\";
    FLUSH PRIVILEGES;
    '"

    log_success "复制用户配置完成"
}

# 重新配置双向复制
setup_bidirectional_replication() {
    log_info "重新配置双向复制..."

    # 获取本地MySQL主服务器状态
    log_info "获取本地MySQL主服务器状态..."
    local_master_status=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW MASTER STATUS;" --skip-column-names)
    local_log_file=$(echo "$local_master_status" | awk '{print $1}')
    local_log_pos=$(echo "$local_master_status" | awk '{print $2}')

    # 获取Linux服务器MySQL主服务器状态
    log_info "获取Linux服务器MySQL主服务器状态..."
    linux_master_status=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW MASTER STATUS;' --skip-column-names")
    linux_log_file=$(echo "$linux_master_status" | awk '{print $1}')
    linux_log_pos=$(echo "$linux_master_status" | awk '{print $2}')

    log_info "本地主服务器状态: $local_log_file:$local_log_pos"
    log_info "Linux主服务器状态: $linux_log_file:$linux_log_pos"

    # 在本地MySQL配置从Linux服务器复制
    log_info "配置本地MySQL从Linux服务器复制..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    CHANGE MASTER TO
        MASTER_HOST='**********',
        MASTER_USER='repl_user',
        MASTER_PASSWORD='$MYSQL_ROOT_PASSWORD',
        MASTER_PORT=3306,
        MASTER_LOG_FILE='$linux_log_file',
        MASTER_LOG_POS=$linux_log_pos;
    START SLAVE;
    "

    # 在Linux服务器MySQL配置从本地复制
    log_info "配置Linux服务器MySQL从本地复制..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    CHANGE MASTER TO
        MASTER_HOST=\"$LOCAL_IP\",
        MASTER_USER=\"repl_user\",
        MASTER_PASSWORD=\"$MYSQL_ROOT_PASSWORD\",
        MASTER_PORT=3306,
        MASTER_LOG_FILE=\"$local_log_file\",
        MASTER_LOG_POS=$local_log_pos;
    START SLAVE;
    '"

    log_success "双向复制配置完成"
}

# 验证复制状态
verify_replication_status() {
    log_info "验证复制状态..."

    echo ""
    echo "=== 本地MySQL从服务器状态 ==="
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)"

    echo ""
    echo "=== Linux服务器MySQL从服务器状态 ==="
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e 'SHOW SLAVE STATUS\G'" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Master_Host|Last_Error|Seconds_Behind_Master)"
}

# 测试双向同步
test_bidirectional_sync() {
    log_info "测试双向同步功能..."

    local test_table="sync_test_$(date +%s)"

    # 在本地创建测试数据
    log_info "在本地创建测试数据..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    CREATE TABLE IF NOT EXISTS $test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        message VARCHAR(255),
        source VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    INSERT INTO $test_table (message, source) VALUES ('Test from local at $(date)', 'local');
    "

    # 等待同步
    log_info "等待数据同步到Linux服务器..."
    sleep 10

    # 检查Linux服务器是否收到数据
    log_info "检查Linux服务器同步结果..."
    local linux_result=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    USE \`逾期债权数据库\`;
    SELECT COUNT(*) FROM $test_table WHERE source=\"local\";
    ' --skip-column-names" 2>/dev/null)

    if [ "$linux_result" = "1" ]; then
        log_success "✅ 本地 → Linux服务器 同步正常"
    else
        log_error "❌ 本地 → Linux服务器 同步失败"
    fi

    # 在Linux服务器创建测试数据
    log_info "在Linux服务器创建测试数据..."
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u root -p'$MYSQL_ROOT_PASSWORD' -e '
    USE \`逾期债权数据库\`;
    INSERT INTO $test_table (message, source) VALUES (\"Test from linux at \$(date)\", \"linux\");
    '"

    # 等待同步
    log_info "等待数据同步到本地..."
    sleep 10

    # 检查本地是否收到数据
    log_info "检查本地同步结果..."
    local local_result=$(mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    SELECT COUNT(*) FROM $test_table WHERE source='linux';
    " --skip-column-names 2>/dev/null)

    if [ "$local_result" = "1" ]; then
        log_success "✅ Linux服务器 → 本地 同步正常"
    else
        log_error "❌ Linux服务器 → 本地 同步失败"
    fi

    # 显示测试结果
    log_info "测试表数据统计:"
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    SELECT source, COUNT(*) as count FROM $test_table GROUP BY source;
    "

    # 清理测试表
    log_info "清理测试表..."
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    USE \`逾期债权数据库\`;
    DROP TABLE IF EXISTS $test_table;
    "

    log_success "双向同步测试完成"
}

# 主函数
main() {
    log_info "🚀 开始数据库一致性修复和双向同步重建..."

    echo ""
    log_warning "⚠️  此操作将："
    echo "   1. 备份本地和Linux服务器的数据库"
    echo "   2. 停止现有的MySQL复制"
    echo "   3. 清理Linux服务器数据库"
    echo "   4. 将本地数据同步到Linux服务器"
    echo "   5. 重新配置双向复制"
    echo ""

    read -p "确认继续执行？(y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "操作已取消"
        exit 0
    fi

    # 执行修复步骤
    create_backup_directory
    backup_local_databases
    backup_linux_databases
    stop_existing_replication
    clean_linux_databases
    sync_data_to_linux
    setup_replication_users
    setup_bidirectional_replication

    # 验证结果
    echo ""
    verify_replication_status

    echo ""
    read -p "是否要测试双向同步功能？(y/n): " test_choice
    if [ "$test_choice" = "y" ] || [ "$test_choice" = "Y" ]; then
        test_bidirectional_sync
    fi

    echo ""
    log_success "🎉 数据库一致性修复和双向同步重建完成！"
    log_info "备份文件保存在: $BACKUP_DIR"
    log_info "现在您的本地和Linux服务器数据库应该完全一致并保持双向同步"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
