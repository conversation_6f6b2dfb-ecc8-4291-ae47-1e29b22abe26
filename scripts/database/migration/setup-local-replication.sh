#!/bin/bash

# MySQL双向复制 - 本地端配置脚本
# 配置本地MySQL作为主服务器，同时从Linux服务器接收复制

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
MYSQL_ROOT_PASSWORD="Zlb&198838"
LINUX_SERVER_IP="**********"
REPL_USER="repl_user"
REPL_PASSWORD="Zlb&198838"

# 获取本地IP地址
get_local_ip() {
    log_info "获取本地IP地址..."
    
    # 尝试多种方法获取本地IP
    local ip=""
    
    # 方法1: 使用route命令 (macOS)
    if command -v route >/dev/null 2>&1; then
        local interface=$(route get default 2>/dev/null | grep interface | awk '{print $2}' || echo "")
        if [ ! -z "$interface" ]; then
            ip=$(ifconfig "$interface" 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1 || echo "")
        fi
    fi
    
    # 方法2: 使用ifconfig直接查找
    if [ -z "$ip" ] && command -v ifconfig >/dev/null 2>&1; then
        ip=$(ifconfig 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | grep -E '192\.168\.|10\.|172\.' | awk '{print $2}' | head -1 || echo "")
    fi
    
    # 方法3: 手动输入
    if [ -z "$ip" ]; then
        echo "无法自动获取本地IP地址，请手动输入："
        read -p "请输入本地IP地址: " ip
    fi
    
    if [ -z "$ip" ]; then
        log_error "无法获取本地IP地址"
        exit 1
    fi
    
    log_success "本地IP地址: $ip"
    echo "$ip"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "MySQL连接正常"
        return 0
    else
        log_error "MySQL连接失败，请检查密码和服务状态"
        return 1
    fi
}

# 检查当前复制状态
check_current_replication() {
    log_info "检查当前复制状态..."
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 'Master Status:' as Info;
    SHOW MASTER STATUS;
    SELECT 'Slave Status:' as Info;
    SHOW SLAVE STATUS\G
    " 2>/dev/null || true
}

# 创建复制用户
create_replication_user() {
    log_info "创建复制用户..."
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 创建复制用户
CREATE USER IF NOT EXISTS '$REPL_USER'@'%' IDENTIFIED BY '$REPL_PASSWORD';
GRANT REPLICATION SLAVE ON *.* TO '$REPL_USER'@'%';
GRANT REPLICATION CLIENT ON *.* TO '$REPL_USER'@'%';
FLUSH PRIVILEGES;

-- 显示用户
SELECT User, Host FROM mysql.user WHERE User = '$REPL_USER';
EOF
    
    log_success "复制用户创建完成"
}

# 配置从Linux服务器的复制
setup_slave_from_linux() {
    log_info "配置从Linux服务器的复制..."
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 停止现有的复制（如果有）
STOP SLAVE;
RESET SLAVE ALL;

-- 配置从Linux服务器复制
CHANGE MASTER TO
    MASTER_HOST='$LINUX_SERVER_IP',
    MASTER_USER='$REPL_USER',
    MASTER_PASSWORD='$REPL_PASSWORD',
    MASTER_PORT=3306,
    MASTER_AUTO_POSITION=0;

-- 启动复制
START SLAVE;

-- 显示状态
SHOW SLAVE STATUS\G
EOF
    
    log_success "从Linux服务器的复制配置完成"
}

# 显示主服务器状态
show_master_status() {
    log_info "显示主服务器状态..."
    
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" -e "
    SELECT 'Master Status Information:' as Info;
    SHOW MASTER STATUS;
    
    SELECT 'Binary Log Files:' as Info;
    SHOW BINARY LOGS;
    "
}

# 测试网络连接
test_network_connection() {
    log_info "测试到Linux服务器的网络连接..."
    
    if ping -c 3 "$LINUX_SERVER_IP" >/dev/null 2>&1; then
        log_success "网络连接正常"
    else
        log_warning "无法ping通Linux服务器，请检查网络连接"
    fi
    
    # 测试MySQL端口
    if nc -z "$LINUX_SERVER_IP" 3306 2>/dev/null; then
        log_success "MySQL端口3306可访问"
    else
        log_warning "MySQL端口3306不可访问，请检查防火墙设置"
    fi
}

# 显示配置信息
show_configuration_info() {
    local local_ip="$1"
    
    log_info "配置信息摘要:"
    echo "=================================="
    echo "本地MySQL服务器: $local_ip:3306"
    echo "Linux服务器: $LINUX_SERVER_IP:3306"
    echo "复制用户: $REPL_USER"
    echo "复制密码: $REPL_PASSWORD"
    echo "=================================="
    echo ""
    echo "下一步操作："
    echo "1. 在Linux服务器上运行对应的配置脚本"
    echo "2. 配置Linux服务器从本地复制"
    echo "3. 验证双向同步是否正常工作"
}

# 主函数
main() {
    log_info "🚀 开始配置本地MySQL双向复制..."
    
    # 获取本地IP
    local_ip=$(get_local_ip)
    
    # 检查MySQL连接
    if ! check_mysql_connection; then
        exit 1
    fi
    
    # 检查当前状态
    check_current_replication
    
    # 测试网络连接
    test_network_connection
    
    # 创建复制用户
    create_replication_user
    
    # 配置从Linux服务器的复制
    setup_slave_from_linux
    
    # 显示主服务器状态
    show_master_status
    
    # 显示配置信息
    show_configuration_info "$local_ip"
    
    log_success "🎉 本地MySQL双向复制配置完成！"
    log_info "请在Linux服务器上运行对应的配置脚本"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
