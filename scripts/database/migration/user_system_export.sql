-- 数据库导出: user_system
-- 导出时间: 2025-06-24 11:34:45
-- 表数量: 5

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `user_system` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `user_system`;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 表结构: roles
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `role_id` int NOT NULL,
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 表数据: roles
INSERT INTO `roles` (`role_id`, `role_name`) VALUES
(1, '<PERSON>MI<PERSON>'),
(2, 'USER'),
(3, 'VIEWER');

-- 表结构: system_log
DROP TABLE IF EXISTS `system_log`;
CREATE TABLE `system_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `level` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=329 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 表数据: system_log
INSERT INTO `system_log` (`id`, `message`, `level`, `created_time`, `source`) VALUES
(1, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:21:00', 'NonLitigationUpdateService'),
(2, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:22:00', 'NonLitigationUpdateService'),
(3, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:23:00', 'NonLitigationUpdateService'),
(4, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:24:00', 'NonLitigationUpdateService'),
(5, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:26:00', 'NonLitigationUpdateService'),
(6, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:27:00', 'NonLitigationUpdateService'),
(7, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:28:00', 'NonLitigationUpdateService'),
(8, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:29:00', 'NonLitigationUpdateService'),
(9, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:30:00', 'NonLitigationUpdateService'),
(10, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:31:00', 'NonLitigationUpdateService'),
(11, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:32:00', 'NonLitigationUpdateService'),
(12, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:33:00', 'NonLitigationUpdateService'),
(13, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:34:00', 'NonLitigationUpdateService'),
(14, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:35:00', 'NonLitigationUpdateService'),
(15, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:36:00', 'NonLitigationUpdateService'),
(16, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:37:00', 'NonLitigationUpdateService'),
(17, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:38:00', 'NonLitigationUpdateService'),
(18, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:39:00', 'NonLitigationUpdateService'),
(19, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:40:00', 'NonLitigationUpdateService'),
(20, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:41:00', 'NonLitigationUpdateService'),
(21, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:42:00', 'NonLitigationUpdateService'),
(22, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:43:00', 'NonLitigationUpdateService'),
(23, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:44:00', 'NonLitigationUpdateService'),
(24, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:45:00', 'NonLitigationUpdateService'),
(25, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:46:00', 'NonLitigationUpdateService'),
(26, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:47:00', 'NonLitigationUpdateService'),
(27, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:48:00', 'NonLitigationUpdateService'),
(28, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:49:00', 'NonLitigationUpdateService'),
(29, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:50:00', 'NonLitigationUpdateService'),
(30, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:51:00', 'NonLitigationUpdateService'),
(31, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:52:00', 'NonLitigationUpdateService'),
(32, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT COUNT(*) FROM non_litigation_claim]', 'ERROR', '2025-04-04 12:53:00', 'NonLitigationUpdateService'),
(33, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT ID, 债务人, 年份, 创建时间 FROM 非诉讼表 ORDER BY 创建时间 DESC LIMIT 5]', 'ERROR', '2025-04-04 12:55:00', 'NonLitigationUpdateService'),
(34, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT ID, 债务人, 年份, 创建时间 FROM 非诉讼表 ORDER BY 创建时间 DESC LIMIT 5]', 'ERROR', '2025-04-04 12:56:00', 'NonLitigationUpdateService'),
(35, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT ID, 债务人, 年份, 创建时间 FROM 非诉讼表 ORDER BY 创建时间 DESC LIMIT 5]', 'ERROR', '2025-04-04 12:57:00', 'NonLitigationUpdateService'),
(36, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT ID, 债务人, 年份, 创建时间 FROM 非诉讼表 ORDER BY 创建时间 DESC LIMIT 5]', 'ERROR', '2025-04-04 12:58:00', 'NonLitigationUpdateService'),
(37, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT ID, 债务人, 年份, 创建时间 FROM 非诉讼表 ORDER BY 创建时间 DESC LIMIT 5]', 'ERROR', '2025-04-04 12:59:00', 'NonLitigationUpdateService'),
(38, '非诉讼表月份数据更新任务执行失败: StatementCallback; bad SQL grammar [SELECT ID, 债务人, 年份, 创建时间 FROM 非诉讼表 ORDER BY 创建时间 DESC LIMIT 5]', 'ERROR', '2025-04-04 13:00:00', 'NonLitigationUpdateService'),
(39, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:01:00', 'NonLitigationUpdateService'),
(40, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:02:00', 'NonLitigationUpdateService'),
(41, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:03:00', 'NonLitigationUpdateService'),
(42, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:04:00', 'NonLitigationUpdateService'),
(43, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:05:00', 'NonLitigationUpdateService'),
(44, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:06:00', 'NonLitigationUpdateService'),
(45, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:09:00', 'NonLitigationUpdateService'),
(46, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:10:00', 'NonLitigationUpdateService'),
(47, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:11:00', 'NonLitigationUpdateService'),
(48, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:14:00', 'NonLitigationUpdateService'),
(49, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:15:00', 'NonLitigationUpdateService'),
(50, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:17:00', 'NonLitigationUpdateService'),
(51, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:18:00', 'NonLitigationUpdateService'),
(52, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:19:00', 'NonLitigationUpdateService'),
(53, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:20:00', 'NonLitigationUpdateService'),
(54, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:21:00', 'NonLitigationUpdateService'),
(55, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:22:00', 'NonLitigationUpdateService'),
(56, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:23:00', 'NonLitigationUpdateService'),
(57, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:24:00', 'NonLitigationUpdateService'),
(58, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:24:59', 'NonLitigationUpdateService'),
(59, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:26:00', 'NonLitigationUpdateService'),
(60, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:27:00', 'NonLitigationUpdateService'),
(61, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:28:00', 'NonLitigationUpdateService'),
(62, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:29:00', 'NonLitigationUpdateService'),
(63, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:30:00', 'NonLitigationUpdateService'),
(64, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:31:00', 'NonLitigationUpdateService'),
(65, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:32:00', 'NonLitigationUpdateService'),
(66, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:33:00', 'NonLitigationUpdateService'),
(67, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:34:00', 'NonLitigationUpdateService'),
(68, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:35:00', 'NonLitigationUpdateService'),
(69, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:36:00', 'NonLitigationUpdateService'),
(70, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:37:00', 'NonLitigationUpdateService'),
(71, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:38:00', 'NonLitigationUpdateService'),
(72, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:39:00', 'NonLitigationUpdateService'),
(73, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:40:00', 'NonLitigationUpdateService'),
(74, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:41:00', 'NonLitigationUpdateService'),
(75, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:42:00', 'NonLitigationUpdateService'),
(76, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:43:00', 'NonLitigationUpdateService'),
(77, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:44:00', 'NonLitigationUpdateService'),
(78, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:45:00', 'NonLitigationUpdateService'),
(79, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:46:00', 'NonLitigationUpdateService'),
(80, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:47:00', 'NonLitigationUpdateService'),
(81, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:48:00', 'NonLitigationUpdateService'),
(82, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:49:00', 'NonLitigationUpdateService'),
(83, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:50:00', 'NonLitigationUpdateService'),
(84, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:51:00', 'NonLitigationUpdateService'),
(85, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:52:00', 'NonLitigationUpdateService'),
(86, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:53:00', 'NonLitigationUpdateService'),
(87, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:54:00', 'NonLitigationUpdateService'),
(88, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:55:00', 'NonLitigationUpdateService'),
(89, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:56:00', 'NonLitigationUpdateService'),
(90, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:57:00', 'NonLitigationUpdateService'),
(91, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:58:00', 'NonLitigationUpdateService'),
(92, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 13:59:00', 'NonLitigationUpdateService'),
(93, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:00:00', 'NonLitigationUpdateService'),
(94, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:01:00', 'NonLitigationUpdateService'),
(95, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:02:00', 'NonLitigationUpdateService'),
(96, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:03:00', 'NonLitigationUpdateService'),
(97, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:04:00', 'NonLitigationUpdateService'),
(98, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:05:00', 'NonLitigationUpdateService'),
(99, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:06:00', 'NonLitigationUpdateService'),
(100, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:07:00', 'NonLitigationUpdateService'),
(101, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:08:00', 'NonLitigationUpdateService'),
(102, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:09:00', 'NonLitigationUpdateService'),
(103, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:10:00', 'NonLitigationUpdateService'),
(104, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:10:59', 'NonLitigationUpdateService'),
(105, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:12:00', 'NonLitigationUpdateService'),
(106, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:13:00', 'NonLitigationUpdateService'),
(107, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:14:00', 'NonLitigationUpdateService'),
(108, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:15:00', 'NonLitigationUpdateService'),
(109, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:16:00', 'NonLitigationUpdateService'),
(110, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:17:00', 'NonLitigationUpdateService'),
(111, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:18:00', 'NonLitigationUpdateService'),
(112, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:19:00', 'NonLitigationUpdateService'),
(113, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:20:00', 'NonLitigationUpdateService'),
(114, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:21:00', 'NonLitigationUpdateService'),
(115, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:22:00', 'NonLitigationUpdateService'),
(116, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:23:00', 'NonLitigationUpdateService'),
(117, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:24:00', 'NonLitigationUpdateService'),
(118, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:25:00', 'NonLitigationUpdateService'),
(119, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:25:59', 'NonLitigationUpdateService'),
(120, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:27:00', 'NonLitigationUpdateService'),
(121, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:28:00', 'NonLitigationUpdateService'),
(122, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:29:00', 'NonLitigationUpdateService'),
(123, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:30:00', 'NonLitigationUpdateService'),
(124, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:31:00', 'NonLitigationUpdateService'),
(125, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:32:00', 'NonLitigationUpdateService'),
(126, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:33:00', 'NonLitigationUpdateService'),
(127, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:34:00', 'NonLitigationUpdateService'),
(128, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:35:00', 'NonLitigationUpdateService'),
(129, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:36:00', 'NonLitigationUpdateService'),
(130, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:37:00', 'NonLitigationUpdateService'),
(131, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:38:00', 'NonLitigationUpdateService'),
(132, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:39:00', 'NonLitigationUpdateService'),
(133, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:40:00', 'NonLitigationUpdateService'),
(134, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:41:00', 'NonLitigationUpdateService'),
(135, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:42:00', 'NonLitigationUpdateService'),
(136, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:43:00', 'NonLitigationUpdateService'),
(137, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:44:00', 'NonLitigationUpdateService'),
(138, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:45:00', 'NonLitigationUpdateService'),
(139, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:46:00', 'NonLitigationUpdateService'),
(140, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:47:00', 'NonLitigationUpdateService'),
(141, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:48:00', 'NonLitigationUpdateService'),
(142, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:49:00', 'NonLitigationUpdateService'),
(143, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:50:00', 'NonLitigationUpdateService'),
(144, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:51:00', 'NonLitigationUpdateService'),
(145, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:52:00', 'NonLitigationUpdateService'),
(146, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:53:00', 'NonLitigationUpdateService'),
(147, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:54:00', 'NonLitigationUpdateService'),
(148, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:55:00', 'NonLitigationUpdateService'),
(149, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:56:00', 'NonLitigationUpdateService'),
(150, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:57:00', 'NonLitigationUpdateService'),
(151, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:58:00', 'NonLitigationUpdateService'),
(152, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 14:59:00', 'NonLitigationUpdateService'),
(153, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:00:00', 'NonLitigationUpdateService'),
(154, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:01:00', 'NonLitigationUpdateService'),
(155, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:02:00', 'NonLitigationUpdateService'),
(156, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:03:00', 'NonLitigationUpdateService'),
(157, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:04:00', 'NonLitigationUpdateService'),
(158, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:05:00', 'NonLitigationUpdateService'),
(159, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:06:00', 'NonLitigationUpdateService'),
(160, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:07:00', 'NonLitigationUpdateService'),
(161, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:08:00', 'NonLitigationUpdateService'),
(162, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:09:00', 'NonLitigationUpdateService'),
(163, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:10:00', 'NonLitigationUpdateService'),
(164, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:11:00', 'NonLitigationUpdateService'),
(165, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:12:00', 'NonLitigationUpdateService'),
(166, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:13:00', 'NonLitigationUpdateService'),
(167, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:14:00', 'NonLitigationUpdateService'),
(168, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:15:00', 'NonLitigationUpdateService'),
(169, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:16:00', 'NonLitigationUpdateService'),
(170, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:17:00', 'NonLitigationUpdateService'),
(171, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:18:00', 'NonLitigationUpdateService'),
(172, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:19:00', 'NonLitigationUpdateService'),
(173, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:20:00', 'NonLitigationUpdateService'),
(174, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:21:00', 'NonLitigationUpdateService'),
(175, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:22:00', 'NonLitigationUpdateService'),
(176, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:23:00', 'NonLitigationUpdateService'),
(177, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:24:00', 'NonLitigationUpdateService'),
(178, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:25:00', 'NonLitigationUpdateService'),
(179, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:26:00', 'NonLitigationUpdateService'),
(180, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:27:00', 'NonLitigationUpdateService'),
(181, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:28:00', 'NonLitigationUpdateService'),
(182, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:29:00', 'NonLitigationUpdateService'),
(183, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:30:00', 'NonLitigationUpdateService'),
(184, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:31:00', 'NonLitigationUpdateService'),
(185, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:32:00', 'NonLitigationUpdateService'),
(186, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:33:00', 'NonLitigationUpdateService'),
(187, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:34:00', 'NonLitigationUpdateService'),
(188, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:35:00', 'NonLitigationUpdateService'),
(189, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:36:00', 'NonLitigationUpdateService'),
(190, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:37:00', 'NonLitigationUpdateService'),
(191, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:38:00', 'NonLitigationUpdateService'),
(192, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:39:00', 'NonLitigationUpdateService'),
(193, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:40:00', 'NonLitigationUpdateService'),
(194, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:41:00', 'NonLitigationUpdateService'),
(195, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:42:00', 'NonLitigationUpdateService'),
(196, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:43:00', 'NonLitigationUpdateService'),
(197, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:44:00', 'NonLitigationUpdateService'),
(198, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:45:00', 'NonLitigationUpdateService'),
(199, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:46:00', 'NonLitigationUpdateService'),
(200, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:47:00', 'NonLitigationUpdateService'),
(201, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:48:00', 'NonLitigationUpdateService'),
(202, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:49:00', 'NonLitigationUpdateService'),
(203, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:50:00', 'NonLitigationUpdateService'),
(204, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:51:00', 'NonLitigationUpdateService'),
(205, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:52:00', 'NonLitigationUpdateService'),
(206, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:53:00', 'NonLitigationUpdateService'),
(207, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:54:00', 'NonLitigationUpdateService'),
(208, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:55:00', 'NonLitigationUpdateService'),
(209, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:56:00', 'NonLitigationUpdateService'),
(210, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:57:00', 'NonLitigationUpdateService'),
(211, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:58:00', 'NonLitigationUpdateService'),
(212, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 15:59:00', 'NonLitigationUpdateService'),
(213, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:00:00', 'NonLitigationUpdateService'),
(214, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:01:00', 'NonLitigationUpdateService'),
(215, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:02:00', 'NonLitigationUpdateService'),
(216, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:03:00', 'NonLitigationUpdateService'),
(217, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:04:00', 'NonLitigationUpdateService'),
(218, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:05:00', 'NonLitigationUpdateService'),
(219, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:06:00', 'NonLitigationUpdateService'),
(220, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:07:00', 'NonLitigationUpdateService'),
(221, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:08:00', 'NonLitigationUpdateService'),
(222, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:09:00', 'NonLitigationUpdateService'),
(223, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:10:00', 'NonLitigationUpdateService'),
(224, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:11:00', 'NonLitigationUpdateService'),
(225, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:12:00', 'NonLitigationUpdateService'),
(226, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:13:00', 'NonLitigationUpdateService'),
(227, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:14:00', 'NonLitigationUpdateService'),
(228, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:15:00', 'NonLitigationUpdateService'),
(229, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:16:00', 'NonLitigationUpdateService'),
(230, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:17:00', 'NonLitigationUpdateService'),
(231, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:18:00', 'NonLitigationUpdateService'),
(232, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:19:00', 'NonLitigationUpdateService'),
(233, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:20:00', 'NonLitigationUpdateService'),
(234, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:21:00', 'NonLitigationUpdateService'),
(235, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:22:00', 'NonLitigationUpdateService'),
(236, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:23:00', 'NonLitigationUpdateService'),
(237, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:24:00', 'NonLitigationUpdateService'),
(238, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:25:00', 'NonLitigationUpdateService'),
(239, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:26:00', 'NonLitigationUpdateService'),
(240, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:27:00', 'NonLitigationUpdateService'),
(241, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:28:00', 'NonLitigationUpdateService'),
(242, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:29:00', 'NonLitigationUpdateService'),
(243, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:30:00', 'NonLitigationUpdateService'),
(244, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:31:00', 'NonLitigationUpdateService'),
(245, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:32:00', 'NonLitigationUpdateService'),
(246, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:33:00', 'NonLitigationUpdateService'),
(247, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:34:00', 'NonLitigationUpdateService'),
(248, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:35:00', 'NonLitigationUpdateService'),
(249, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:36:00', 'NonLitigationUpdateService'),
(250, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:37:00', 'NonLitigationUpdateService'),
(251, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:38:00', 'NonLitigationUpdateService'),
(252, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:39:00', 'NonLitigationUpdateService'),
(253, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:40:00', 'NonLitigationUpdateService'),
(254, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:41:00', 'NonLitigationUpdateService'),
(255, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:42:00', 'NonLitigationUpdateService'),
(256, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:43:00', 'NonLitigationUpdateService'),
(257, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:44:00', 'NonLitigationUpdateService'),
(258, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:45:00', 'NonLitigationUpdateService'),
(259, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:46:00', 'NonLitigationUpdateService'),
(260, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:47:00', 'NonLitigationUpdateService'),
(261, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:48:00', 'NonLitigationUpdateService'),
(262, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:49:00', 'NonLitigationUpdateService'),
(263, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:50:00', 'NonLitigationUpdateService'),
(264, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:51:00', 'NonLitigationUpdateService'),
(265, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:52:00', 'NonLitigationUpdateService'),
(266, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:53:00', 'NonLitigationUpdateService'),
(267, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:54:00', 'NonLitigationUpdateService'),
(268, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:55:00', 'NonLitigationUpdateService'),
(269, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:56:00', 'NonLitigationUpdateService'),
(270, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:57:00', 'NonLitigationUpdateService'),
(271, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:58:00', 'NonLitigationUpdateService'),
(272, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 16:59:00', 'NonLitigationUpdateService'),
(273, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:00:00', 'NonLitigationUpdateService'),
(274, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:01:00', 'NonLitigationUpdateService'),
(275, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:02:00', 'NonLitigationUpdateService'),
(276, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:03:00', 'NonLitigationUpdateService'),
(277, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:04:00', 'NonLitigationUpdateService'),
(278, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:05:00', 'NonLitigationUpdateService'),
(279, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:06:00', 'NonLitigationUpdateService'),
(280, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:07:00', 'NonLitigationUpdateService'),
(281, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:08:00', 'NonLitigationUpdateService'),
(282, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:09:00', 'NonLitigationUpdateService'),
(283, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:10:00', 'NonLitigationUpdateService'),
(284, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:11:00', 'NonLitigationUpdateService'),
(285, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:12:00', 'NonLitigationUpdateService'),
(286, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:13:00', 'NonLitigationUpdateService'),
(287, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:13:59', 'NonLitigationUpdateService'),
(288, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:15:00', 'NonLitigationUpdateService'),
(289, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:16:00', 'NonLitigationUpdateService'),
(290, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:17:00', 'NonLitigationUpdateService'),
(291, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:18:00', 'NonLitigationUpdateService'),
(292, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:19:00', 'NonLitigationUpdateService'),
(293, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:20:00', 'NonLitigationUpdateService'),
(294, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:21:00', 'NonLitigationUpdateService'),
(295, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:22:00', 'NonLitigationUpdateService'),
(296, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:23:00', 'NonLitigationUpdateService'),
(297, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:24:00', 'NonLitigationUpdateService'),
(298, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:25:00', 'NonLitigationUpdateService'),
(299, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:26:00', 'NonLitigationUpdateService'),
(300, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:27:00', 'NonLitigationUpdateService'),
(301, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:28:00', 'NonLitigationUpdateService'),
(302, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:29:00', 'NonLitigationUpdateService'),
(303, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:30:00', 'NonLitigationUpdateService'),
(304, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:31:00', 'NonLitigationUpdateService'),
(305, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:32:00', 'NonLitigationUpdateService'),
(306, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:33:00', 'NonLitigationUpdateService'),
(307, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:34:00', 'NonLitigationUpdateService'),
(308, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:35:00', 'NonLitigationUpdateService'),
(309, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:36:00', 'NonLitigationUpdateService'),
(310, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:37:00', 'NonLitigationUpdateService'),
(311, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:38:00', 'NonLitigationUpdateService'),
(312, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:39:00', 'NonLitigationUpdateService'),
(313, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:40:00', 'NonLitigationUpdateService'),
(314, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:41:00', 'NonLitigationUpdateService'),
(315, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:42:00', 'NonLitigationUpdateService'),
(316, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:43:00', 'NonLitigationUpdateService'),
(317, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:44:00', 'NonLitigationUpdateService'),
(318, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:45:00', 'NonLitigationUpdateService'),
(319, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:46:00', 'NonLitigationUpdateService'),
(320, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:47:00', 'NonLitigationUpdateService'),
(321, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:48:00', 'NonLitigationUpdateService'),
(322, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 17:49:00', 'NonLitigationUpdateService'),
(323, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 18:22:01', 'NonLitigationUpdateService'),
(324, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 18:23:00', 'NonLitigationUpdateService'),
(325, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 18:24:00', 'NonLitigationUpdateService'),
(326, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 18:25:00', 'NonLitigationUpdateService'),
(327, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 18:28:00', 'NonLitigationUpdateService'),
(328, '非诉讼表月份数据更新任务定时执行', 'INFO', '2025-04-04 18:29:00', 'NonLitigationUpdateService');

-- 表结构: user_audit_logs
DROP TABLE IF EXISTS `user_audit_logs`;
CREATE TABLE `user_audit_logs` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户名',
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `resource` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作资源',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `result` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'SUCCESS' COMMENT '操作结果：SUCCESS、FAILED',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`),
  KEY `idx_action` (`action`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_audit_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作审计日志表';

-- 表数据: user_audit_logs
-- 表 user_audit_logs 无数据

-- 表结构: user_sessions
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
  `session_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_access_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后访问时间',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT '用户代理信息',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否活跃',
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_access` (`last_access_time`),
  CONSTRAINT `fk_session_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 表数据: user_sessions
-- 表 user_sessions 无数据

-- 表结构: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(190) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role_id` int DEFAULT NULL,
  `companyname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_username` (`username`),
  KEY `users_ibfk_1` (`role_id`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=174 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 表数据: users
INSERT INTO `users` (`id`, `username`, `password`, `role_id`, `companyname`, `department`, `name`, `status`, `created_at`, `updated_at`) VALUES
(1, 'laoshu198838', '$2a$10$7THXRE0rp/EtC3lmxyydV.RMZq8mMhL4lOP2PveMB1YOfRwk/Cpj.', 1, '所有公司', '所有部门', '周先生', 'ACTIVE', '2025-06-16 17:31:20', '2025-06-21 16:38:45'),
(9, 'laoshu1988380', '$2a$10$2yTB8t7MS722odXAmJlaFeqewPfn00otdoHXM8V93eDV7DOsEeAsi', 2, '万润科技', '资产财务部', '周利兵', 'DELETED', '2025-06-16 17:31:20', '2025-06-21 22:52:30'),
(10, '周利兵', '$2a$10$Szn.gP1I9whr7U1MGzC8ZuxEfYlolCj7yY/zjy5F3zo06Q/wcX.TC', 2, '万润科技', '资产财务部', '范德萨', 'ACTIVE', '2025-06-16 17:31:20', '2025-06-21 22:55:18'),
(15, '陈宗华', '$2a$10$YasuHqOO50B.fRswRj2eBecmxg/ibZnlkOKJKUiB5a8ymuvQtKUGK', 2, '中筑天佑', '资产财务部', '陈宗华', 'DELETED', '2025-06-16 17:31:20', '2025-06-20 09:04:28'),
(18, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P8jskrvi.6hwMK', 1, '所有公司', '所有部门', '系统管理员', 'ACTIVE', '2025-06-16 17:31:20', '2025-06-22 18:52:34');

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

