#!/bin/bash

# MySQL三数据库双向同步实施脚本
# 基于MySQL Master-Master复制架构
# 支持：overdue_debt_db, kingdee, user_system

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

# 复制用户配置
REPL_USER="repl_user"
REPL_PASSWORD="Repl#2025@Sync"

# 需要同步的数据库
DATABASES=("overdue_debt_db" "kingdee" "user_system")

echo -e "${PURPLE}🔄==================== MySQL双向同步配置 ====================${NC}"
echo -e "${CYAN}UltraThink解决方案：Master-Master复制架构${NC}"
echo ""

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] INFO${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] SUCCESS${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] WARNING${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR${NC} $1"
}

# 函数：检查MySQL服务状态
check_mysql_status() {
    local server_type=$1
    
    if [ "$server_type" = "local" ]; then
        if mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SELECT 1;" > /dev/null 2>&1; then
            log_success "本地MySQL服务正常"
            return 0
        else
            log_error "本地MySQL服务不可用"
            return 1
        fi
    else
        if ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SELECT 1;'" > /dev/null 2>&1; then
            log_success "Linux MySQL服务正常"
            return 0
        else
            log_error "Linux MySQL服务不可用"
            return 1
        fi
    fi
}

# 函数：创建复制用户
create_replication_user() {
    local server_type=$1
    
    log_info "在${server_type}服务器创建复制用户..."
    
    local create_user_sql="
    CREATE USER IF NOT EXISTS '${REPL_USER}'@'%' IDENTIFIED BY '${REPL_PASSWORD}';
    GRANT REPLICATION SLAVE ON *.* TO '${REPL_USER}'@'%';
    GRANT SELECT ON *.* TO '${REPL_USER}'@'%';
    FLUSH PRIVILEGES;
    "
    
    if [ "$server_type" = "local" ]; then
        mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "$create_user_sql"
    else
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e \"$create_user_sql\""
    fi
    
    log_success "${server_type}服务器复制用户创建完成"
}

# 函数：配置MySQL参数
configure_mysql_replication() {
    local server_type=$1
    local server_id=$2
    
    log_info "配置${server_type}服务器MySQL复制参数..."
    
    # 检查当前配置
    if [ "$server_type" = "local" ]; then
        local current_server_id=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SHOW VARIABLES LIKE 'server_id';" -s -N | awk '{print $2}')
        local log_bin_status=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SHOW VARIABLES LIKE 'log_bin';" -s -N | awk '{print $2}')
        
        echo "  当前server_id: $current_server_id"
        echo "  当前log_bin: $log_bin_status"
        
        if [ "$current_server_id" != "$server_id" ] || [ "$log_bin_status" != "ON" ]; then
            log_warning "本地MySQL需要重新配置，请确保my.cnf包含以下配置："
            cat << EOF

[mysqld]
server-id = $server_id
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON
binlog-do-db = overdue_debt_db
binlog-do-db = kingdee
binlog-do-db = user_system
auto-increment-increment = 2
auto-increment-offset = $((server_id % 2))

EOF
            log_warning "配置完成后请重启MySQL服务"
            return 1
        fi
    else
        # 为Linux Docker容器创建配置
        ssh "$LINUX_SERVER" << EOF
cat > /tmp/mysql-replication.cnf << 'MYCNF'
[mysqld]
server-id = $server_id
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON
binlog-do-db = overdue_debt_db
binlog-do-db = kingdee
binlog-do-db = user_system
auto-increment-increment = 2
auto-increment-offset = $((server_id % 2))
max-connections = 1000
MYCNF

# 复制配置到容器并重启
docker cp /tmp/mysql-replication.cnf $MYSQL_CONTAINER:/etc/mysql/conf.d/
docker restart $MYSQL_CONTAINER

echo "等待MySQL容器重启..."
sleep 20

# 验证容器是否正常启动
if docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SELECT 1;' > /dev/null 2>&1; then
    echo "MySQL容器重启成功"
else
    echo "MySQL容器重启失败"
    exit 1
fi
EOF
    fi
    
    log_success "${server_type}服务器MySQL配置完成"
}

# 函数：获取主服务器状态
get_master_status() {
    local server_type=$1
    
    if [ "$server_type" = "local" ]; then
        mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SHOW MASTER STATUS\G"
    else
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SHOW MASTER STATUS\G'"
    fi
}

# 函数：配置从服务器
configure_slave() {
    local slave_type=$1
    local master_host=$2
    local master_port=$3
    
    log_info "配置${slave_type}服务器作为从服务器..."
    
    # 获取主服务器的位置信息
    if [ "$slave_type" = "local" ]; then
        # 本地作为从服务器，Linux作为主服务器
        local master_status=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SHOW MASTER STATUS;' -s -N")
        local log_file=$(echo "$master_status" | awk '{print $1}')
        local log_pos=$(echo "$master_status" | awk '{print $2}')
        
        log_info "主服务器状态: File=$log_file, Position=$log_pos"
        
        # 配置从服务器
        mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl << EOF
STOP SLAVE;
RESET SLAVE ALL;
CHANGE MASTER TO
    MASTER_HOST='$master_host',
    MASTER_PORT=$master_port,
    MASTER_USER='$REPL_USER',
    MASTER_PASSWORD='$REPL_PASSWORD',
    MASTER_LOG_FILE='$log_file',
    MASTER_LOG_POS=$log_pos;
START SLAVE;
EOF
    else
        # Linux作为从服务器，本地作为主服务器
        local master_status=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SHOW MASTER STATUS;" -s -N)
        local log_file=$(echo "$master_status" | awk '{print $1}')
        local log_pos=$(echo "$master_status" | awk '{print $2}')
        
        log_info "主服务器状态: File=$log_file, Position=$log_pos"
        
        # 配置从服务器
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' << 'EOF'
STOP SLAVE;
RESET SLAVE ALL;
CHANGE MASTER TO
    MASTER_HOST='$master_host',
    MASTER_PORT=$master_port,
    MASTER_USER='$REPL_USER',
    MASTER_PASSWORD='$REPL_PASSWORD',
    MASTER_LOG_FILE='$log_file',
    MASTER_LOG_POS=$log_pos;
START SLAVE;
EOF"
    fi
    
    log_success "${slave_type}服务器从配置完成"
}

# 函数：检查复制状态
check_replication_status() {
    log_info "检查复制状态..."
    
    echo ""
    echo -e "${CYAN}=== 本地MySQL从状态 ===${NC}"
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SHOW SLAVE STATUS\G" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Last_Error|Seconds_Behind_Master)"
    
    echo ""
    echo -e "${CYAN}=== Linux MySQL从状态 ===${NC}"
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SHOW SLAVE STATUS\G'" | grep -E "(Slave_IO_Running|Slave_SQL_Running|Last_Error|Seconds_Behind_Master)"
}

# 函数：测试双向同步
test_bidirectional_sync() {
    log_info "测试双向同步功能..."
    
    # 在本地创建测试记录
    local test_time=$(date '+%Y-%m-%d %H:%M:%S')
    
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -D overdue_debt_db -e "
    INSERT INTO 新增表 (序号, 管理公司, 债权人, 债务人, 年份, 到期时间, 科目名称, 债权性质, 是否涉诉, 期间, 新增金额, 备注, 更新时间) 
    VALUES (99999, 'SYNC_TEST_LOCAL', '测试债权人', '测试债务人', 2025, '2025-12-31', '测试科目', '测试性质', '否', '测试期间', 100.00, '本地同步测试', '$test_time');"
    
    log_info "本地测试记录已创建，等待同步..."
    sleep 5
    
    # 检查Linux端是否收到
    local linux_count=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -D overdue_debt_db -e \"SELECT COUNT(*) FROM 新增表 WHERE 序号=99999 AND 管理公司='SYNC_TEST_LOCAL';\" -s -N" 2>/dev/null || echo "0")
    
    if [ "$linux_count" -gt 0 ]; then
        log_success "✅ 本地→Linux同步测试通过"
    else
        log_error "❌ 本地→Linux同步测试失败"
    fi
    
    # 在Linux创建测试记录
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -D overdue_debt_db -e \"
    INSERT INTO 新增表 (序号, 管理公司, 债权人, 债务人, 年份, 到期时间, 科目名称, 债权性质, 是否涉诉, 期间, 新增金额, 备注, 更新时间) 
    VALUES (99998, 'SYNC_TEST_LINUX', '测试债权人2', '测试债务人2', 2025, '2025-12-31', '测试科目2', '测试性质2', '否', '测试期间2', 200.00, 'Linux同步测试', '$test_time');\""
    
    log_info "Linux测试记录已创建，等待同步..."
    sleep 5
    
    # 检查本地是否收到
    local local_count=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -D overdue_debt_db -e "SELECT COUNT(*) FROM 新增表 WHERE 序号=99998 AND 管理公司='SYNC_TEST_LINUX';" -s -N 2>/dev/null || echo "0")
    
    if [ "$local_count" -gt 0 ]; then
        log_success "✅ Linux→本地同步测试通过"
    else
        log_error "❌ Linux→本地同步测试失败"
    fi
    
    # 清理测试数据
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -D overdue_debt_db -e "DELETE FROM 新增表 WHERE 序号 IN (99999, 99998);"
    
    log_info "测试数据已清理"
}

# 主函数
main() {
    echo -e "${PURPLE}🚀 开始MySQL双向同步配置...${NC}"
    echo ""
    
    # 1. 检查服务状态
    echo -e "${CYAN}=== 阶段1: 检查服务状态 ===${NC}"
    check_mysql_status "local" || exit 1
    check_mysql_status "linux" || exit 1
    
    # 2. 创建复制用户
    echo ""
    echo -e "${CYAN}=== 阶段2: 创建复制用户 ===${NC}"
    create_replication_user "local"
    create_replication_user "linux"
    
    # 3. 配置MySQL复制参数
    echo ""
    echo -e "${CYAN}=== 阶段3: 配置MySQL复制参数 ===${NC}"
    configure_mysql_replication "linux" 2
    
    log_info "检查本地MySQL配置..."
    configure_mysql_replication "local" 1
    
    # 4. 获取Linux服务器IP
    local linux_ip=$(echo "$LINUX_SERVER" | cut -d'@' -f2)
    
    # 5. 配置双向复制
    echo ""
    echo -e "${CYAN}=== 阶段4: 配置双向复制 ===${NC}"
    configure_slave "local" "$linux_ip" 3306
    configure_slave "linux" "host.docker.internal" 3306
    
    # 6. 检查复制状态
    echo ""
    echo -e "${CYAN}=== 阶段5: 检查复制状态 ===${NC}"
    sleep 10
    check_replication_status
    
    # 7. 测试双向同步
    echo ""
    echo -e "${CYAN}=== 阶段6: 测试双向同步 ===${NC}"
    test_bidirectional_sync
    
    echo ""
    echo -e "${GREEN}🎉 MySQL双向同步配置完成！${NC}"
    echo ""
    echo -e "${YELLOW}📋 配置摘要:${NC}"
    echo "• 已配置Master-Master双向复制"
    echo "• 同步数据库: ${DATABASES[*]}"
    echo "• 本地服务器ID: 1"
    echo "• Linux服务器ID: 2"
    echo "• 复制用户: $REPL_USER"
    echo ""
    echo -e "${YELLOW}🔍 监控命令:${NC}"
    echo "# 检查本地复制状态"
    echo "mysql -h$LOCAL_DB_HOST -u$LOCAL_DB_USER -p'$LOCAL_DB_PASSWORD' --skip-ssl -e 'SHOW SLAVE STATUS\G'"
    echo ""
    echo "# 检查Linux复制状态"
    echo "ssh $LINUX_SERVER \"docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SHOW SLAVE STATUS\G'\""
    
    echo ""
    echo -e "${PURPLE}==================== 配置完成 ====================${NC}"
}

# 执行主函数
main "$@"