#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MySQL全数据库双向同步高级监控系统
====================================
功能：
- 实时监控复制状态
- 数据一致性检查
- 性能指标收集
- 智能告警
- 自动恢复机制

作者：SuperClaude
版本：1.0
"""

import mysql.connector
import time
import json
import logging
import smtplib
import threading
from datetime import datetime
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from typing import Dict, List, Optional, Tuple

class MySQLSyncMonitor:
    """MySQL双向同步监控器"""
    
    def __init__(self, config_file: str = None):
        """初始化监控器
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self._load_config(config_file)
        self.logger = self._setup_logging()
        self.alert_history = []
        self.performance_metrics = {}
        
    def _load_config(self, config_file: str) -> Dict:
        """加载配置"""
        default_config = {
            "mysql_servers": {
                "local": {
                    "host": "localhost",
                    "port": 3306,
                    "user": "root",
                    "password": "Zlb&198838",
                    "server_id": 1,
                    "description": "本地MySQL"
                },
                "linux": {
                    "host": "**********",
                    "port": 3306,
                    "user": "root",
                    "password": "Zlb&198838",
                    "server_id": 2,
                    "description": "Linux MySQL"
                }
            },
            "monitoring": {
                "check_interval": 30,  # 检查间隔（秒）
                "lag_warning_threshold": 10,  # 延迟警告阈值（秒）
                "lag_critical_threshold": 30,  # 延迟严重阈值（秒）
                "enable_alerts": True,
                "enable_auto_recovery": True
            },
            "databases": [
                "overdue_debt_db",
                "user_system", 
                "kingdee"
            ],
            "alerts": {
                "email": {
                    "enabled": False,
                    "smtp_server": "smtp.qq.com",
                    "smtp_port": 587,
                    "username": "",
                    "password": "",
                    "recipients": []
                },
                "webhook": {
                    "enabled": False,
                    "url": ""
                }
            }
        }
        
        if config_file:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
        
        return default_config
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('mysql_sync_monitor')
        logger.setLevel(logging.INFO)
        
        # 文件处理器
        file_handler = logging.FileHandler('/tmp/mysql_sync_monitor.log', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def get_mysql_connection(self, server_key: str) -> Optional[mysql.connector.MySQLConnection]:
        """获取MySQL连接"""
        try:
            server_config = self.config["mysql_servers"][server_key]
            connection = mysql.connector.connect(
                host=server_config["host"],
                port=server_config["port"],
                user=server_config["user"],
                password=server_config["password"],
                charset='utf8mb4',
                autocommit=True
            )
            return connection
        except Exception as e:
            self.logger.error(f"连接{server_key}失败: {e}")
            return None
    
    def check_replication_status(self, server_key: str) -> Dict:
        """检查复制状态"""
        connection = self.get_mysql_connection(server_key)
        if not connection:
            return {
                "status": "error",
                "error": "无法连接MySQL服务器"
            }
        
        try:
            cursor = connection.cursor(dictionary=True)
            cursor.execute("SHOW SLAVE STATUS")
            slave_status = cursor.fetchone()
            
            if not slave_status:
                return {
                    "status": "no_replication", 
                    "message": "未配置主从复制"
                }
            
            # 解析关键指标
            io_running = slave_status.get('Slave_IO_Running', 'No')
            sql_running = slave_status.get('Slave_SQL_Running', 'No')
            seconds_behind = slave_status.get('Seconds_Behind_Master')
            last_error = slave_status.get('Last_Error', '')
            
            # 判断状态
            if io_running == 'Yes' and sql_running == 'Yes':
                if seconds_behind is None:
                    status = "healthy"
                elif seconds_behind <= self.config["monitoring"]["lag_warning_threshold"]:
                    status = "healthy"
                elif seconds_behind <= self.config["monitoring"]["lag_critical_threshold"]:
                    status = "warning"
                else:
                    status = "critical"
            else:
                status = "error"
            
            return {
                "status": status,
                "io_running": io_running,
                "sql_running": sql_running,
                "seconds_behind": seconds_behind,
                "last_error": last_error,
                "master_host": slave_status.get('Master_Host'),
                "master_port": slave_status.get('Master_Port'),
                "raw_status": slave_status
            }
            
        except Exception as e:
            self.logger.error(f"检查{server_key}复制状态失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
        finally:
            connection.close()
    
    def check_database_consistency(self, db_name: str) -> Dict:
        """检查数据库一致性"""
        local_conn = self.get_mysql_connection("local")
        linux_conn = self.get_mysql_connection("linux")
        
        if not local_conn or not linux_conn:
            return {
                "status": "error",
                "error": "无法连接MySQL服务器"
            }
        
        try:
            # 获取表列表
            local_cursor = local_conn.cursor()
            linux_cursor = linux_conn.cursor()
            
            # 检查数据库是否存在
            local_cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            local_db_exists = local_cursor.fetchone() is not None
            
            linux_cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            linux_db_exists = linux_cursor.fetchone() is not None
            
            if not local_db_exists or not linux_db_exists:
                return {
                    "status": "inconsistent",
                    "error": f"数据库{db_name}在某个服务器上不存在",
                    "local_exists": local_db_exists,
                    "linux_exists": linux_db_exists
                }
            
            # 获取表列表
            local_cursor.execute(f"SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{db_name}'")
            local_tables = {row[0] for row in local_cursor.fetchall()}
            
            linux_cursor.execute(f"SELECT TABLE_NAME FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{db_name}'")
            linux_tables = {row[0] for row in linux_cursor.fetchall()}
            
            # 比较表结构
            missing_in_linux = local_tables - linux_tables
            missing_in_local = linux_tables - local_tables
            common_tables = local_tables & linux_tables
            
            inconsistencies = []
            if missing_in_linux:
                inconsistencies.append(f"Linux缺少表: {', '.join(missing_in_linux)}")
            if missing_in_local:
                inconsistencies.append(f"本地缺少表: {', '.join(missing_in_local)}")
            
            # 检查共同表的记录数
            table_counts = {}
            for table in common_tables:
                try:
                    local_cursor.execute(f"SELECT COUNT(*) FROM `{db_name}`.`{table}`")
                    local_count = local_cursor.fetchone()[0]
                    
                    linux_cursor.execute(f"SELECT COUNT(*) FROM `{db_name}`.`{table}`")
                    linux_count = linux_cursor.fetchone()[0]
                    
                    table_counts[table] = {
                        "local": local_count,
                        "linux": linux_count,
                        "diff": abs(local_count - linux_count)
                    }
                    
                    if local_count != linux_count:
                        inconsistencies.append(f"表{table}记录数不一致: 本地{local_count}, Linux{linux_count}")
                        
                except Exception as e:
                    inconsistencies.append(f"表{table}检查失败: {e}")
            
            status = "consistent" if not inconsistencies else "inconsistent"
            
            return {
                "status": status,
                "database": db_name,
                "inconsistencies": inconsistencies,
                "table_counts": table_counts,
                "local_tables": len(local_tables),
                "linux_tables": len(linux_tables)
            }
            
        except Exception as e:
            self.logger.error(f"检查数据库{db_name}一致性失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
        finally:
            local_conn.close()
            linux_conn.close()
    
    def discover_new_databases(self) -> List[str]:
        """发现新数据库"""
        local_conn = self.get_mysql_connection("local")
        linux_conn = self.get_mysql_connection("linux")
        
        if not local_conn or not linux_conn:
            return []
        
        try:
            # 获取所有数据库
            local_cursor = local_conn.cursor()
            linux_cursor = linux_conn.cursor()
            
            local_cursor.execute("""
                SELECT SCHEMA_NAME 
                FROM information_schema.SCHEMATA 
                WHERE SCHEMA_NAME NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
            """)
            local_dbs = {row[0] for row in local_cursor.fetchall()}
            
            linux_cursor.execute("""
                SELECT SCHEMA_NAME 
                FROM information_schema.SCHEMATA 
                WHERE SCHEMA_NAME NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys')
            """)
            linux_dbs = {row[0] for row in linux_cursor.fetchall()}
            
            # 找出所有数据库
            all_dbs = local_dbs | linux_dbs
            current_monitored = set(self.config["databases"])
            
            # 发现新数据库
            new_dbs = all_dbs - current_monitored
            
            if new_dbs:
                self.logger.info(f"发现新数据库: {', '.join(new_dbs)}")
                # 更新配置
                self.config["databases"].extend(list(new_dbs))
            
            return list(new_dbs)
            
        except Exception as e:
            self.logger.error(f"发现新数据库失败: {e}")
            return []
        finally:
            local_conn.close()
            linux_conn.close()
    
    def send_alert(self, alert_type: str, message: str, details: Dict = None):
        """发送告警"""
        if not self.config["monitoring"]["enable_alerts"]:
            return
        
        alert = {
            "timestamp": datetime.now().isoformat(),
            "type": alert_type,
            "message": message,
            "details": details or {}
        }
        
        self.alert_history.append(alert)
        self.logger.warning(f"ALERT [{alert_type}]: {message}")
        
        # 邮件告警
        if self.config["alerts"]["email"]["enabled"]:
            self._send_email_alert(alert)
        
        # Webhook告警
        if self.config["alerts"]["webhook"]["enabled"]:
            self._send_webhook_alert(alert)
    
    def _send_email_alert(self, alert: Dict):
        """发送邮件告警"""
        try:
            email_config = self.config["alerts"]["email"]
            
            msg = MimeMultipart()
            msg['From'] = email_config["username"]
            msg['To'] = ", ".join(email_config["recipients"])
            msg['Subject'] = f"MySQL同步告警 - {alert['type']}"
            
            body = f"""
            时间: {alert['timestamp']}
            类型: {alert['type']}
            消息: {alert['message']}
            
            详细信息:
            {json.dumps(alert['details'], indent=2, ensure_ascii=False)}
            """
            
            msg.attach(MimeText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(email_config["smtp_server"], email_config["smtp_port"])
            server.starttls()
            server.login(email_config["username"], email_config["password"])
            server.send_message(msg)
            server.quit()
            
            self.logger.info("邮件告警发送成功")
            
        except Exception as e:
            self.logger.error(f"邮件告警发送失败: {e}")
    
    def _send_webhook_alert(self, alert: Dict):
        """发送Webhook告警"""
        try:
            import requests
            
            webhook_url = self.config["alerts"]["webhook"]["url"]
            response = requests.post(webhook_url, json=alert, timeout=10)
            
            if response.status_code == 200:
                self.logger.info("Webhook告警发送成功")
            else:
                self.logger.error(f"Webhook告警发送失败: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Webhook告警发送失败: {e}")
    
    def auto_recovery(self, server_key: str, status: Dict):
        """自动恢复"""
        if not self.config["monitoring"]["enable_auto_recovery"]:
            return
        
        if status["status"] == "error" and status.get("io_running") == "No":
            self.logger.info(f"尝试自动恢复{server_key}的复制...")
            
            connection = self.get_mysql_connection(server_key)
            if connection:
                try:
                    cursor = connection.cursor()
                    cursor.execute("START SLAVE")
                    
                    self.logger.info(f"{server_key}复制已重新启动")
                    self.send_alert("AUTO_RECOVERY", f"{server_key}复制已自动重新启动")
                    
                except Exception as e:
                    self.logger.error(f"{server_key}自动恢复失败: {e}")
                    self.send_alert("AUTO_RECOVERY_FAILED", f"{server_key}自动恢复失败: {e}")
                finally:
                    connection.close()
    
    def generate_report(self) -> Dict:
        """生成监控报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "replication_status": {},
            "database_consistency": {},
            "performance_metrics": self.performance_metrics,
            "recent_alerts": self.alert_history[-10:],  # 最近10条告警
            "summary": {
                "total_databases": len(self.config["databases"]),
                "healthy_replications": 0,
                "total_alerts": len(self.alert_history)
            }
        }
        
        # 检查复制状态
        for server_key in self.config["mysql_servers"]:
            status = self.check_replication_status(server_key)
            report["replication_status"][server_key] = status
            
            if status.get("status") == "healthy":
                report["summary"]["healthy_replications"] += 1
        
        # 检查数据库一致性
        for db_name in self.config["databases"]:
            consistency = self.check_database_consistency(db_name)
            report["database_consistency"][db_name] = consistency
        
        return report
    
    def run_monitoring(self):
        """运行监控"""
        self.logger.info("开始MySQL双向同步监控...")
        
        while True:
            try:
                # 发现新数据库
                new_dbs = self.discover_new_databases()
                if new_dbs:
                    self.send_alert("NEW_DATABASE", f"发现新数据库: {', '.join(new_dbs)}")
                
                # 检查复制状态
                for server_key, server_config in self.config["mysql_servers"].items():
                    status = self.check_replication_status(server_key)
                    desc = server_config["description"]
                    
                    if status["status"] == "error":
                        self.send_alert("REPLICATION_ERROR", f"{desc}复制错误", status)
                        self.auto_recovery(server_key, status)
                    elif status["status"] == "critical":
                        self.send_alert("REPLICATION_CRITICAL", f"{desc}复制延迟严重: {status.get('seconds_behind')}秒", status)
                    elif status["status"] == "warning":
                        self.send_alert("REPLICATION_WARNING", f"{desc}复制延迟警告: {status.get('seconds_behind')}秒", status)
                
                # 记录性能指标
                self.performance_metrics[datetime.now().isoformat()] = {
                    "check_time": time.time(),
                    "monitored_databases": len(self.config["databases"])
                }
                
                # 清理旧的性能指标（保留最近24小时）
                cutoff_time = time.time() - 24 * 3600
                self.performance_metrics = {
                    k: v for k, v in self.performance_metrics.items()
                    if v["check_time"] > cutoff_time
                }
                
                # 等待下次检查
                time.sleep(self.config["monitoring"]["check_interval"])
                
            except KeyboardInterrupt:
                self.logger.info("监控停止")
                break
            except Exception as e:
                self.logger.error(f"监控异常: {e}")
                time.sleep(60)  # 异常后等待1分钟再继续

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MySQL双向同步监控系统')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--report', '-r', action='store_true', help='生成监控报告')
    parser.add_argument('--check', action='store_true', help='执行一次检查')
    
    args = parser.parse_args()
    
    monitor = MySQLSyncMonitor(args.config)
    
    if args.report:
        report = monitor.generate_report()
        print(json.dumps(report, indent=2, ensure_ascii=False))
    elif args.check:
        # 执行一次检查
        for server_key in monitor.config["mysql_servers"]:
            status = monitor.check_replication_status(server_key)
            print(f"{server_key}: {status}")
    else:
        # 运行持续监控
        monitor.run_monitoring()

if __name__ == "__main__":
    main()