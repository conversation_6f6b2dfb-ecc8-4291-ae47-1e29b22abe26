#!/bin/bash

# ===================================================================
# MySQL配置优化文件生成脚本
# 根据不同环境和角色生成优化的MySQL配置文件
#
# 功能：
# 1. 生成主服务器配置（Linux服务器）
# 2. 生成从服务器配置（本地MySQL）
# 3. 生成Docker容器配置
# 4. 性能优化配置
# 5. 复制优化配置
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 配置目录
CONFIG_DIR="/Volumes/ExternalSSD-1T/08.program/FinancialSystem/config/mysql"
BACKUP_DIR="/tmp/mysql_config_backup_$(date +%Y%m%d_%H%M%S)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 创建配置目录
create_config_dir() {
    log_info "创建配置目录..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$BACKUP_DIR"
    
    log_success "配置目录已创建: $CONFIG_DIR"
}

# 生成Linux主服务器配置
generate_master_config() {
    log_info "生成Linux主服务器配置..."
    
    cat > "$CONFIG_DIR/master-server.cnf" << 'EOF'
# ===================================================================
# MySQL 8.0 主服务器配置 (Linux生产环境)
# 服务器: **********
# 角色: Master (主服务器)
# 用途: 生产环境主数据库
# ===================================================================

[mysqld]
# 基本设置
server-id = 1
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 数据目录
datadir = /var/lib/mysql
tmpdir = /tmp

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 认证设置（兼容旧版本）
default_authentication_plugin = mysql_native_password

# SQL模式（兼容性设置）
sql_mode = "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"

# 时区设置
default-time-zone = '+08:00'

# ===================================================================
# 二进制日志和复制设置
# ===================================================================

# 二进制日志
log-bin = mysql-bin
binlog-format = ROW
max_binlog_size = 1G
binlog_expire_logs_seconds = 604800  # 7天
sync_binlog = 1

# GTID设置（推荐用于复制）
gtid_mode = ON
enforce_gtid_consistency = ON
log_slave_updates = ON

# 复制过滤（仅复制指定数据库）
binlog-do-db = overdue_debt_db
binlog-do-db = user_system
binlog-do-db = kingdee

# 复制安全设置
master_info_repository = TABLE
relay_log_info_repository = TABLE
binlog_rows_query_log_events = ON

# ===================================================================
# 性能优化设置
# ===================================================================

# 连接设置
max_connections = 500
max_connect_errors = 100
connection_timeout = 60
interactive_timeout = 28800
wait_timeout = 28800

# 缓冲区设置
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 8
innodb_log_buffer_size = 64M

# InnoDB设置
innodb_file_per_table = ON
innodb_flush_method = O_DIRECT
innodb_log_file_size = 512M
innodb_log_files_in_group = 2
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 120

# MyISAM设置
key_buffer_size = 256M
myisam_sort_buffer_size = 128M

# 查询缓存（MySQL 8.0已废弃，这里保留注释）
# query_cache_type = 1
# query_cache_size = 256M

# 排序和临时表
sort_buffer_size = 4M
join_buffer_size = 4M
tmp_table_size = 256M
max_heap_table_size = 256M

# 线程设置
thread_cache_size = 50
thread_stack = 512K

# 表设置
table_open_cache = 2000
table_definition_cache = 1000

# ===================================================================
# 日志设置
# ===================================================================

# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = ON
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = ON
log_slow_admin_statements = ON

# 一般查询日志（生产环境建议关闭）
general_log = OFF
general_log_file = /var/log/mysql/general.log

# ===================================================================
# 安全设置
# ===================================================================

# 跳过域名解析
skip-name-resolve = ON

# 本地文件加载
local_infile = OFF

# 符号链接
symbolic-links = OFF

# ===================================================================
# 监控和状态
# ===================================================================

# 性能模式
performance_schema = ON
performance_schema_max_table_instances = 10000

# 信息模式优化
information_schema_stats_expiry = 86400

[mysql]
# 客户端默认字符集
default-character-set = utf8mb4

[client]
# 客户端设置
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock

[mysqldump]
# 备份设置
quick
quote-names
max_allowed_packet = 1G
default-character-set = utf8mb4

[mysql_safe]
# 安全启动设置
log-error = /var/log/mysql/error.log
pid-file = /var/run/mysqld/mysqld.pid
EOF

    log_success "Linux主服务器配置已生成"
}

# 生成本地从服务器配置
generate_slave_config() {
    log_info "生成本地从服务器配置..."
    
    cat > "$CONFIG_DIR/slave-server.cnf" << 'EOF'
# ===================================================================
# MySQL 8.0 从服务器配置 (本地开发环境)
# 角色: Slave (从服务器)
# 用途: 本地开发和测试
# ===================================================================

[mysqld]
# 基本设置
server-id = 2
port = 3306
bind-address = 127.0.0.1

# 数据目录（根据实际环境调整）
datadir = /usr/local/var/mysql
tmpdir = /tmp

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 认证设置
default_authentication_plugin = mysql_native_password

# SQL模式
sql_mode = "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"

# 时区设置
default-time-zone = '+08:00'

# ===================================================================
# 复制设置
# ===================================================================

# 二进制日志（从服务器也需要，用于链式复制）
log-bin = mysql-bin
binlog-format = ROW
max_binlog_size = 512M
binlog_expire_logs_seconds = 259200  # 3天
sync_binlog = 0  # 从服务器可以设置为0提高性能

# GTID设置
gtid_mode = ON
enforce_gtid_consistency = ON
log_slave_updates = ON

# 中继日志设置
relay-log = relay-bin
relay_log_recovery = ON
relay_log_purge = ON

# 从服务器设置
slave_parallel_workers = 4
slave_parallel_type = LOGICAL_CLOCK
slave_preserve_commit_order = ON
slave_pending_jobs_size_max = 128M

# 复制过滤
replicate-do-db = overdue_debt_db
replicate-do-db = user_system
replicate-do-db = kingdee

# 复制错误处理
slave_skip_errors = 1062,1032
slave_net_timeout = 60

# 复制信息存储
master_info_repository = TABLE
relay_log_info_repository = TABLE

# ===================================================================
# 性能优化设置（开发环境适中配置）
# ===================================================================

# 连接设置
max_connections = 200
max_connect_errors = 50
connection_timeout = 30
interactive_timeout = 7200
wait_timeout = 7200

# 缓冲区设置（较小配置适合开发环境）
innodb_buffer_pool_size = 1G
innodb_buffer_pool_instances = 4
innodb_log_buffer_size = 32M

# InnoDB设置
innodb_file_per_table = ON
innodb_flush_method = O_DIRECT
innodb_log_file_size = 256M
innodb_log_files_in_group = 2
innodb_flush_log_at_trx_commit = 2  # 开发环境可以设置为2
innodb_lock_wait_timeout = 60

# MyISAM设置
key_buffer_size = 128M
myisam_sort_buffer_size = 64M

# 排序和临时表
sort_buffer_size = 2M
join_buffer_size = 2M
tmp_table_size = 128M
max_heap_table_size = 128M

# 线程设置
thread_cache_size = 25
thread_stack = 256K

# 表设置
table_open_cache = 1000
table_definition_cache = 500

# ===================================================================
# 日志设置
# ===================================================================

# 错误日志
log-error = /usr/local/var/mysql/error.log

# 慢查询日志
slow_query_log = ON
slow_query_log_file = /usr/local/var/mysql/slow.log
long_query_time = 1
log_queries_not_using_indexes = OFF  # 开发环境关闭以减少日志

# 一般查询日志
general_log = OFF

# ===================================================================
# 开发环境特殊设置
# ===================================================================

# 跳过域名解析
skip-name-resolve = ON

# 开发环境可以启用本地文件加载
local_infile = ON

# 符号链接
symbolic-links = OFF

# 性能模式（开发环境可以关闭以节省资源）
performance_schema = OFF

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306

[mysqldump]
quick
quote-names
max_allowed_packet = 512M
default-character-set = utf8mb4
EOF

    log_success "本地从服务器配置已生成"
}

# 生成Docker容器配置
generate_docker_config() {
    log_info "生成Docker容器配置..."
    
    cat > "$CONFIG_DIR/docker-mysql.cnf" << 'EOF'
# ===================================================================
# MySQL 8.0 Docker容器配置
# 角色: Test Slave (测试从服务器)
# 用途: 容器化测试环境
# ===================================================================

[mysqld]
# 基本设置
server-id = 3
port = 3306
bind-address = 0.0.0.0

# 数据目录
datadir = /var/lib/mysql
tmpdir = /tmp

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 认证设置
default_authentication_plugin = mysql_native_password

# SQL模式
sql_mode = "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"

# 时区设置
default-time-zone = '+08:00'

# ===================================================================
# 复制设置
# ===================================================================

# 二进制日志
log-bin = mysql-bin
binlog-format = ROW
max_binlog_size = 256M
binlog_expire_logs_seconds = 86400  # 1天（容器环境短期保留）
sync_binlog = 0

# GTID设置
gtid_mode = ON
enforce_gtid_consistency = ON
log_slave_updates = ON

# 中继日志设置
relay-log = relay-bin
relay_log_recovery = ON
relay_log_purge = ON

# 从服务器设置
slave_parallel_workers = 2
slave_parallel_type = LOGICAL_CLOCK
slave_preserve_commit_order = ON

# 复制过滤
replicate-do-db = overdue_debt_db
replicate-do-db = user_system
replicate-do-db = kingdee

# 复制错误处理
slave_skip_errors = 1062,1032
slave_net_timeout = 30

# 复制信息存储
master_info_repository = TABLE
relay_log_info_repository = TABLE

# ===================================================================
# 容器环境优化设置
# ===================================================================

# 连接设置（容器环境较小配置）
max_connections = 100
max_connect_errors = 20
connection_timeout = 15
interactive_timeout = 3600
wait_timeout = 3600

# 缓冲区设置（容器环境内存限制）
innodb_buffer_pool_size = 512M
innodb_buffer_pool_instances = 2
innodb_log_buffer_size = 16M

# InnoDB设置
innodb_file_per_table = ON
innodb_flush_method = O_DIRECT
innodb_log_file_size = 128M
innodb_log_files_in_group = 2
innodb_flush_log_at_trx_commit = 2
innodb_lock_wait_timeout = 30

# MyISAM设置
key_buffer_size = 64M
myisam_sort_buffer_size = 32M

# 排序和临时表
sort_buffer_size = 1M
join_buffer_size = 1M
tmp_table_size = 64M
max_heap_table_size = 64M

# 线程设置
thread_cache_size = 10
thread_stack = 256K

# 表设置
table_open_cache = 500
table_definition_cache = 200

# ===================================================================
# 日志设置（容器环境简化）
# ===================================================================

# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志（容器环境可选）
slow_query_log = OFF
long_query_time = 5

# 一般查询日志
general_log = OFF

# ===================================================================
# 容器特殊设置
# ===================================================================

# 跳过域名解析
skip-name-resolve = ON

# 容器环境安全设置
local_infile = OFF
symbolic-links = OFF

# 性能模式（容器环境关闭以节省资源）
performance_schema = OFF

# 容器启动优化
skip-grant-tables = OFF
skip-networking = OFF

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306

[mysqldump]
quick
quote-names
max_allowed_packet = 256M
default-character-set = utf8mb4
EOF

    log_success "Docker容器配置已生成"
}

# 生成高性能配置
generate_high_performance_config() {
    log_info "生成高性能配置..."
    
    cat > "$CONFIG_DIR/high-performance.cnf" << 'EOF'
# ===================================================================
# MySQL 8.0 高性能配置
# 适用于高负载生产环境
# 注意：需要根据实际硬件规格调整参数
# ===================================================================

[mysqld]
# 基本设置
server-id = 1
port = 3306
bind-address = 0.0.0.0

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 认证设置
default_authentication_plugin = mysql_native_password

# ===================================================================
# 连接优化
# ===================================================================
max_connections = 1000
max_connect_errors = 1000
connection_timeout = 10
interactive_timeout = 28800
wait_timeout = 28800
back_log = 500

# 连接池优化
thread_cache_size = 100
thread_handling = pool-of-threads
thread_pool_size = 32

# ===================================================================
# InnoDB高性能配置
# ===================================================================

# 缓冲池（建议设置为物理内存的70-80%）
innodb_buffer_pool_size = 8G
innodb_buffer_pool_instances = 16
innodb_buffer_pool_chunk_size = 128M

# 日志设置
innodb_log_buffer_size = 128M
innodb_log_file_size = 2G
innodb_log_files_in_group = 2
innodb_flush_log_at_trx_commit = 2

# 刷新设置
innodb_flush_method = O_DIRECT
innodb_flush_neighbors = 0
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000

# 读写优化
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_thread_concurrency = 0

# 锁优化
innodb_lock_wait_timeout = 120
innodb_deadlock_detect = OFF
innodb_print_all_deadlocks = ON

# 页面和表空间
innodb_page_size = 16K
innodb_file_per_table = ON
innodb_open_files = 4000

# ===================================================================
# 查询优化
# ===================================================================

# 缓冲区大小
sort_buffer_size = 8M
join_buffer_size = 8M
read_buffer_size = 2M
read_rnd_buffer_size = 4M

# 临时表
tmp_table_size = 512M
max_heap_table_size = 512M
internal_tmp_disk_storage_engine = InnoDB

# 表缓存
table_open_cache = 4000
table_definition_cache = 2000
table_open_cache_instances = 16

# ===================================================================
# 复制高性能配置
# ===================================================================

# 二进制日志
log-bin = mysql-bin
binlog-format = ROW
max_binlog_size = 1G
binlog_expire_logs_seconds = 259200
sync_binlog = 0
binlog_cache_size = 2M
max_binlog_cache_size = 1G

# GTID
gtid_mode = ON
enforce_gtid_consistency = ON
log_slave_updates = ON

# 从服务器优化
slave_parallel_workers = 16
slave_parallel_type = LOGICAL_CLOCK
slave_preserve_commit_order = ON
slave_pending_jobs_size_max = 1G
slave_checkpoint_period = 300

# ===================================================================
# 网络优化
# ===================================================================
max_allowed_packet = 1G
net_buffer_length = 32K
net_read_timeout = 30
net_write_timeout = 60

# ===================================================================
# 监控和调试
# ===================================================================
slow_query_log = ON
long_query_time = 0.5
log_queries_not_using_indexes = OFF
log_slow_admin_statements = ON
log_slow_slave_statements = ON

# 性能模式
performance_schema = ON
performance_schema_max_table_instances = 20000

# ===================================================================
# 安全优化
# ===================================================================
skip-name-resolve = ON
local_infile = OFF
symbolic-links = OFF

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4

[mysqldump]
quick
quote-names
max_allowed_packet = 1G
EOF

    log_success "高性能配置已生成"
}

# 生成配置应用脚本
generate_apply_script() {
    log_info "生成配置应用脚本..."
    
    cat > "$CONFIG_DIR/apply-config.sh" << 'EOF'
#!/bin/bash

# MySQL配置应用脚本
# 用于将生成的配置文件应用到不同环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 备份现有配置
backup_config() {
    local config_path="$1"
    local backup_path="$2"
    
    if [[ -f "$config_path" ]]; then
        cp "$config_path" "$backup_path"
        log_info "已备份现有配置: $backup_path"
    fi
}

# 应用本地配置
apply_local_config() {
    log_info "应用本地MySQL配置..."
    
    local mysql_config_path=""
    
    # 检测MySQL配置文件位置
    if [[ -f "/etc/mysql/my.cnf" ]]; then
        mysql_config_path="/etc/mysql/my.cnf"
    elif [[ -f "/etc/my.cnf" ]]; then
        mysql_config_path="/etc/my.cnf"
    elif [[ -f "/usr/local/etc/my.cnf" ]]; then
        mysql_config_path="/usr/local/etc/my.cnf"
    else
        log_error "未找到MySQL配置文件"
        return 1
    fi
    
    # 备份现有配置
    backup_config "$mysql_config_path" "${mysql_config_path}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 复制新配置
    sudo cp "$(dirname "$0")/slave-server.cnf" "$mysql_config_path"
    
    log_success "本地配置已应用"
    log_warning "请重启MySQL服务: sudo service mysql restart"
}

# 应用Linux服务器配置
apply_linux_config() {
    log_info "应用Linux服务器配置..."
    
    local server="**********"
    local user="admin"
    
    # 上传配置文件
    scp "$(dirname "$0")/master-server.cnf" "$user@$server:/tmp/my.cnf.new"
    
    # 在远程服务器上应用配置
    ssh "$user@$server" "
        sudo cp /etc/mysql/my.cnf /etc/mysql/my.cnf.backup.\$(date +%Y%m%d_%H%M%S)
        sudo cp /tmp/my.cnf.new /etc/mysql/my.cnf
        sudo chown root:root /etc/mysql/my.cnf
        sudo chmod 644 /etc/mysql/my.cnf
        rm /tmp/my.cnf.new
    "
    
    log_success "Linux服务器配置已应用"
    log_warning "请在服务器上重启MySQL: sudo systemctl restart mysql"
}

# 显示帮助
show_help() {
    echo "MySQL配置应用脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  local     - 应用本地MySQL配置"
    echo "  linux     - 应用Linux服务器配置"
    echo "  help      - 显示此帮助"
    echo ""
    echo "注意:"
    echo "  - 应用配置前会自动备份现有配置"
    echo "  - 应用后需要重启MySQL服务"
    echo "  - Linux配置需要SSH访问权限"
}

# 主函数
case "${1:-help}" in
    "local")
        apply_local_config
        ;;
    "linux")
        apply_linux_config
        ;;
    "help"|*)
        show_help
        ;;
esac
EOF

    chmod +x "$CONFIG_DIR/apply-config.sh"
    log_success "配置应用脚本已生成"
}

# 生成配置说明文档
generate_config_documentation() {
    log_info "生成配置说明文档..."
    
    cat > "$CONFIG_DIR/README.md" << 'EOF'
# MySQL配置文件说明

本目录包含针对不同环境优化的MySQL配置文件。

## 配置文件列表

### 1. master-server.cnf
- **用途**: Linux生产环境主服务器
- **服务器**: **********
- **角色**: Master (主服务器)
- **特点**: 
  - 优化的复制配置
  - 生产级性能设置
  - 完整的日志记录
  - 安全性增强

### 2. slave-server.cnf
- **用途**: 本地开发环境从服务器
- **角色**: Slave (从服务器)
- **特点**:
  - 复制优化配置
  - 适中的资源使用
  - 开发友好设置
  - 并行复制支持

### 3. docker-mysql.cnf
- **用途**: Docker容器环境
- **角色**: Test Slave (测试从服务器)
- **特点**:
  - 容器环境优化
  - 较小的内存占用
  - 简化的日志配置
  - 快速启动设置

### 4. high-performance.cnf
- **用途**: 高性能生产环境
- **特点**:
  - 最大性能优化
  - 大内存配置
  - 高并发支持
  - 适用于大型系统

## 关键配置说明

### 复制相关
```ini
# 主服务器设置
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
gtid_mode = ON

# 从服务器设置
server-id = 2
relay-log = relay-bin
slave_parallel_workers = 4
```

### 性能优化
```ini
# 内存配置
innodb_buffer_pool_size = 2G
innodb_log_buffer_size = 64M

# 连接配置
max_connections = 500
thread_cache_size = 50

# 查询优化
sort_buffer_size = 4M
join_buffer_size = 4M
```

### 安全设置
```ini
# 认证设置
default_authentication_plugin = mysql_native_password

# 网络安全
skip-name-resolve = ON
bind-address = 0.0.0.0

# 文件安全
local_infile = OFF
symbolic-links = OFF
```

## 使用方法

### 1. 自动应用配置
```bash
# 应用本地配置
./apply-config.sh local

# 应用Linux服务器配置
./apply-config.sh linux
```

### 2. 手动应用配置
```bash
# 备份现有配置
sudo cp /etc/mysql/my.cnf /etc/mysql/my.cnf.backup

# 复制新配置
sudo cp master-server.cnf /etc/mysql/my.cnf

# 重启MySQL
sudo systemctl restart mysql
```

### 3. Docker使用
```yaml
# docker-compose.yml
services:
  mysql:
    image: mysql:8.0
    volumes:
      - ./config/mysql/docker-mysql.cnf:/etc/mysql/conf.d/custom.cnf
```

## 配置验证

### 检查配置是否生效
```sql
-- 查看当前配置
SHOW VARIABLES LIKE 'server_id';
SHOW VARIABLES LIKE 'log_bin';
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';

-- 查看复制状态
SHOW MASTER STATUS;
SHOW SLAVE STATUS\G
```

### 性能监控
```sql
-- 连接状态
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';

-- InnoDB状态
SHOW ENGINE INNODB STATUS;

-- 复制延迟
SHOW SLAVE STATUS\G
```

## 故障排查

### 常见问题

1. **配置文件位置错误**
   - 检查 `/etc/mysql/my.cnf`
   - 检查 `/etc/my.cnf`
   - 检查 `/usr/local/etc/my.cnf`

2. **权限问题**
   - 确保配置文件权限为 644
   - 确保所有者为 mysql:mysql

3. **内存不足**
   - 调整 `innodb_buffer_pool_size`
   - 减少 `max_connections`

4. **复制问题**
   - 检查 `server-id` 唯一性
   - 验证网络连接
   - 查看错误日志

### 日志位置
- 错误日志: `/var/log/mysql/error.log`
- 慢查询日志: `/var/log/mysql/slow.log`
- 二进制日志: `/var/lib/mysql/mysql-bin.*`

## 注意事项

1. **备份重要性**: 应用新配置前务必备份现有配置
2. **重启要求**: 大部分配置更改需要重启MySQL
3. **资源匹配**: 确保配置与硬件资源匹配
4. **监控必要**: 应用后监控性能和稳定性
5. **测试验证**: 在生产环境前先在测试环境验证

## 更新记录

- 2025-07-08: 初始版本，支持主从复制和多环境配置
EOF

    log_success "配置说明文档已生成"
}

# 主菜单
show_menu() {
    echo -e "\n${CYAN}===== MySQL配置生成工具 =====${NC}"
    echo "1. 生成Linux主服务器配置"
    echo "2. 生成本地从服务器配置"
    echo "3. 生成Docker容器配置"
    echo "4. 生成高性能配置"
    echo "5. 生成所有配置文件"
    echo "6. 生成配置应用脚本"
    echo "7. 查看配置说明"
    echo "0. 退出"
    echo -n "请选择操作 [0-7]: "
}

# 主函数
main() {
    echo -e "${CYAN}===== MySQL配置优化工具 =====${NC}"
    echo -e "${BLUE}为FinancialSystem项目生成优化的MySQL配置${NC}\n"
    
    # 创建配置目录
    create_config_dir
    
    # 解析命令行参数
    case "${1:-menu}" in
        "master")
            generate_master_config
            ;;
        "slave")
            generate_slave_config
            ;;
        "docker")
            generate_docker_config
            ;;
        "performance")
            generate_high_performance_config
            ;;
        "all")
            generate_master_config
            generate_slave_config
            generate_docker_config
            generate_high_performance_config
            generate_apply_script
            generate_config_documentation
            ;;
        "menu")
            while true; do
                show_menu
                read choice
                
                case $choice in
                    1)
                        generate_master_config
                        ;;
                    2)
                        generate_slave_config
                        ;;
                    3)
                        generate_docker_config
                        ;;
                    4)
                        generate_high_performance_config
                        ;;
                    5)
                        generate_master_config
                        generate_slave_config
                        generate_docker_config
                        generate_high_performance_config
                        generate_apply_script
                        generate_config_documentation
                        log_success "所有配置文件已生成在: $CONFIG_DIR"
                        ;;
                    6)
                        generate_apply_script
                        ;;
                    7)
                        generate_config_documentation
                        if [[ "$OSTYPE" == "darwin"* ]]; then
                            open "$CONFIG_DIR/README.md"
                        else
                            echo "请查看: $CONFIG_DIR/README.md"
                        fi
                        ;;
                    0)
                        log_info "退出程序"
                        exit 0
                        ;;
                    *)
                        log_error "无效选择"
                        ;;
                esac
            done
            ;;
        *)
            log_error "无效参数: $1"
            echo "用法: $0 [master|slave|docker|performance|all]"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"