#!/bin/bash

# 测试英文数据库连接脚本

echo "🔍 测试英文数据库连接..."

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="Zlb&198838"
DB_NAME="overdue_debt_db"

# 测试连接
echo "📊 连接数据库: $DB_NAME"
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
USE $DB_NAME;
SELECT '=== 数据库连接成功 ===' as status;
SELECT '=== 表统计信息 ===' as info;
SELECT 
    TABLE_NAME as '表名',
    TABLE_ROWS as '数据行数'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_ROWS DESC;
"

if [ $? -eq 0 ]; then
    echo "✅ 英文数据库连接测试成功！"
else
    echo "❌ 英文数据库连接失败！"
fi