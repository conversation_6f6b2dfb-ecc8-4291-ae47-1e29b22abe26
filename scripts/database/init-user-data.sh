#!/bin/bash

# ===================================================================
# 用户数据初始化执行脚本
# 执行SQL脚本初始化用户数据，解决登录401问题
#
# 功能：
# 1. 执行用户数据初始化SQL
# 2. 验证数据创建成功
# 3. 测试用户登录
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 配置变量
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASS="Zlb&198838"
SQL_FILE="$(dirname "$0")/init-user-data.sql"
API_HOST="http://localhost:8080"

# 远程服务器配置（可选）
REMOTE_SERVER="**********"
REMOTE_USER="admin"
REMOTE_API="http://${REMOTE_SERVER}:8080"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查SQL文件
check_sql_file() {
    if [[ ! -f "$SQL_FILE" ]]; then
        log_error "SQL文件不存在: $SQL_FILE"
        exit 1
    fi
    log_success "找到SQL文件: $SQL_FILE"
}

# 执行本地初始化
init_local_data() {
    log_info "在本地MySQL执行用户数据初始化..."
    
    if mysql -h${MYSQL_HOST} -u${MYSQL_USER} -p"${MYSQL_PASS}" < "$SQL_FILE" 2>/dev/null; then
        log_success "本地用户数据初始化成功"
    else
        log_error "本地用户数据初始化失败"
        return 1
    fi
    
    # 验证数据
    log_info "验证本地数据创建..."
    local user_count=$(mysql -h${MYSQL_HOST} -u${MYSQL_USER} -p"${MYSQL_PASS}" -Ne "SELECT COUNT(*) FROM user_system.user" 2>/dev/null)
    local role_count=$(mysql -h${MYSQL_HOST} -u${MYSQL_USER} -p"${MYSQL_PASS}" -Ne "SELECT COUNT(*) FROM user_system.role" 2>/dev/null)
    local company_count=$(mysql -h${MYSQL_HOST} -u${MYSQL_USER} -p"${MYSQL_PASS}" -Ne "SELECT COUNT(*) FROM user_system.company" 2>/dev/null)
    
    echo -e "${BLUE}本地数据统计：${NC}"
    echo -e "  用户数量: ${GREEN}${user_count}${NC}"
    echo -e "  角色数量: ${GREEN}${role_count}${NC}"
    echo -e "  公司数量: ${GREEN}${company_count}${NC}"
}

# 执行远程初始化
init_remote_data() {
    log_info "在远程服务器执行用户数据初始化..."
    
    # 复制SQL文件到远程服务器
    if scp "$SQL_FILE" ${REMOTE_USER}@${REMOTE_SERVER}:/tmp/init-user-data.sql; then
        log_success "SQL文件已复制到远程服务器"
    else
        log_error "无法复制SQL文件到远程服务器"
        return 1
    fi
    
    # 在远程服务器执行SQL
    if ssh ${REMOTE_USER}@${REMOTE_SERVER} "mysql -uroot -p'${MYSQL_PASS}' < /tmp/init-user-data.sql" 2>/dev/null; then
        log_success "远程用户数据初始化成功"
    else
        log_error "远程用户数据初始化失败"
        return 1
    fi
    
    # 清理临时文件
    ssh ${REMOTE_USER}@${REMOTE_SERVER} "rm -f /tmp/init-user-data.sql"
    
    # 验证远程数据
    log_info "验证远程数据创建..."
    local user_count=$(ssh ${REMOTE_USER}@${REMOTE_SERVER} "mysql -uroot -p'${MYSQL_PASS}' -Ne 'SELECT COUNT(*) FROM user_system.user'" 2>/dev/null)
    local role_count=$(ssh ${REMOTE_USER}@${REMOTE_SERVER} "mysql -uroot -p'${MYSQL_PASS}' -Ne 'SELECT COUNT(*) FROM user_system.role'" 2>/dev/null)
    
    echo -e "${BLUE}远程数据统计：${NC}"
    echo -e "  用户数量: ${GREEN}${user_count}${NC}"
    echo -e "  角色数量: ${GREEN}${role_count}${NC}"
}

# 测试登录功能
test_login() {
    local api_url=$1
    local environment=$2
    
    log_info "测试${environment}登录功能..."
    
    # 测试账户列表
    local users=("admin" "test_user" "viewer" "exporter")
    local password="admin123"
    
    for username in "${users[@]}"; do
        echo -n "  测试用户 ${username}: "
        
        # 发送登录请求
        local response=$(curl -s -X POST "${api_url}/api/auth/login" \
            -H "Content-Type: application/json" \
            -d "{\"username\":\"${username}\",\"password\":\"${password}\"}" \
            2>/dev/null || echo "连接失败")
        
        # 检查响应
        if [[ "$response" == *"token"* ]]; then
            echo -e "${GREEN}登录成功${NC}"
        elif [[ "$response" == *"401"* ]] || [[ "$response" == *"Invalid"* ]]; then
            echo -e "${RED}登录失败 - 认证错误${NC}"
        elif [[ "$response" == "连接失败" ]] || [[ -z "$response" ]]; then
            echo -e "${YELLOW}无法连接到API${NC}"
        else
            echo -e "${RED}登录失败 - ${response}${NC}"
        fi
    done
}

# 生成测试命令
generate_test_commands() {
    log_info "生成测试命令..."
    
    cat > /tmp/user_login_test_commands.txt << EOF
用户登录测试命令
================

1. 本地测试登录（命令行）:
   curl -X POST http://localhost:8080/api/auth/login \\
     -H "Content-Type: application/json" \\
     -d '{"username":"admin","password":"admin123"}'

2. 远程测试登录:
   curl -X POST http://${REMOTE_SERVER}:8080/api/auth/login \\
     -H "Content-Type: application/json" \\
     -d '{"username":"admin","password":"admin123"}'

3. 使用httpie测试（更友好）:
   http POST localhost:8080/api/auth/login username=admin password=admin123

4. 验证Token（将YOUR_TOKEN替换为实际token）:
   curl -H "Authorization: Bearer YOUR_TOKEN" \\
     http://localhost:8080/api/protected

5. 查看用户列表:
   mysql -uroot -p'${MYSQL_PASS}' -e "SELECT username, real_name FROM user_system.user"

6. 重置用户密码（如需要）:
   mysql -uroot -p'${MYSQL_PASS}' -e "
     UPDATE user_system.user 
     SET password = '\$2a\$10\$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVKIUi' 
     WHERE username = 'admin'"

测试账户信息：
--------------
用户名: admin     密码: admin123  角色: 系统管理员
用户名: test_user 密码: admin123  角色: 普通用户
用户名: viewer    密码: admin123  角色: 查看用户
用户名: exporter  密码: admin123  角色: 导出用户

EOF
    
    log_success "测试命令已保存到: /tmp/user_login_test_commands.txt"
}

# 主菜单
show_menu() {
    echo -e "\n${BLUE}===== 用户数据初始化工具 =====${NC}"
    echo "1. 初始化本地数据库"
    echo "2. 初始化远程数据库"
    echo "3. 初始化本地和远程数据库"
    echo "4. 测试本地登录"
    echo "5. 测试远程登录"
    echo "6. 显示测试命令"
    echo "0. 退出"
    echo -n "请选择操作 [0-6]: "
}

# 主函数
main() {
    # 检查SQL文件
    check_sql_file
    
    # 如果提供了参数，直接执行
    if [[ $# -gt 0 ]]; then
        case "$1" in
            "local")
                init_local_data
                test_login "$API_HOST" "本地"
                ;;
            "remote")
                init_remote_data
                test_login "$REMOTE_API" "远程"
                ;;
            "all")
                init_local_data
                init_remote_data
                test_login "$API_HOST" "本地"
                test_login "$REMOTE_API" "远程"
                ;;
            *)
                log_error "无效参数: $1"
                echo "用法: $0 [local|remote|all]"
                exit 1
                ;;
        esac
        generate_test_commands
        exit 0
    fi
    
    # 交互式菜单
    while true; do
        show_menu
        read choice
        
        case $choice in
            1)
                init_local_data
                ;;
            2)
                init_remote_data
                ;;
            3)
                init_local_data
                init_remote_data
                ;;
            4)
                test_login "$API_HOST" "本地"
                ;;
            5)
                test_login "$REMOTE_API" "远程"
                ;;
            6)
                generate_test_commands
                cat /tmp/user_login_test_commands.txt
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选择"
                ;;
        esac
    done
}

# 执行主函数
main "$@"