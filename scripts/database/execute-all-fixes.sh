#!/bin/bash

# ===================================================================
# 一键执行所有修复脚本
# 解决Linux部署中的MySQL认证和数据同步问题
#
# 功能：
# 1. 按顺序执行所有修复脚本
# 2. 验证每步执行结果
# 3. 提供回滚选项
# 4. 生成执行报告
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_FILE="/tmp/financial_system_fix_$(date +%Y%m%d_%H%M%S).log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${BLUE}$msg${NC}" | tee -a "$LOG_FILE"
}

log_success() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS] $1"
    echo -e "${GREEN}$msg${NC}" | tee -a "$LOG_FILE"
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING] $1"
    echo -e "${YELLOW}$msg${NC}" | tee -a "$LOG_FILE"
}

# 显示执行计划
show_execution_plan() {
    echo -e "\n${CYAN}===== 执行计划 =====${NC}"
    echo -e "${BLUE}将按以下顺序执行修复：${NC}"
    echo "1. 修复MySQL认证问题 (fix-mysql-authentication.sh)"
    echo "2. 初始化用户数据 (init-user-data.sh)"
    echo "3. 验证数据同步 (verify-data-sync.sh)"
    echo "4. 配置Docker环境 (docker-env-setup.sh) - 可选"
    echo "5. 启动复制监控 (monitor-replication.sh)"
    echo ""
    echo -e "${YELLOW}注意：${NC}"
    echo "- 执行过程需要SSH访问Linux服务器(**********)"
    echo "- 需要MySQL root权限"
    echo "- 整个过程预计需要10-15分钟"
    echo ""
    read -p "是否继续执行？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "用户取消执行"
        exit 0
    fi
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查必要的脚本文件
    local scripts=(
        "fix-mysql-authentication.sh"
        "init-user-data.sh"
        "verify-data-sync.sh"
        "docker-env-setup.sh"
        "monitor-replication.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ ! -f "$SCRIPT_DIR/$script" ]]; then
            log_error "缺少脚本文件: $script"
            exit 1
        fi
        if [[ ! -x "$SCRIPT_DIR/$script" ]]; then
            chmod +x "$SCRIPT_DIR/$script"
            log_info "已设置执行权限: $script"
        fi
    done
    
    # 检查MySQL连接
    if ! mysql -uroot -p'Zlb&198838' -e "SELECT 1" &>/dev/null; then
        log_error "无法连接到本地MySQL"
        exit 1
    fi
    
    # 检查SSH连接
    if ! ssh -o ConnectTimeout=5 admin@********** "echo test" &>/dev/null; then
        log_warning "无法SSH连接到Linux服务器，某些功能可能受限"
    fi
    
    log_success "前置条件检查通过"
}

# 执行认证修复
execute_auth_fix() {
    log_info "===== 步骤1: 修复MySQL认证问题 ====="
    
    if "$SCRIPT_DIR/fix-mysql-authentication.sh"; then
        log_success "认证修复完成"
        return 0
    else
        log_error "认证修复失败"
        return 1
    fi
}

# 执行用户初始化
execute_user_init() {
    log_info "===== 步骤2: 初始化用户数据 ====="
    
    if "$SCRIPT_DIR/init-user-data.sh" all; then
        log_success "用户数据初始化完成"
        return 0
    else
        log_error "用户数据初始化失败"
        return 1
    fi
}

# 验证数据同步
execute_sync_verify() {
    log_info "===== 步骤3: 验证数据同步 ====="
    
    if "$SCRIPT_DIR/verify-data-sync.sh" full; then
        log_success "数据同步验证完成"
        return 0
    else
        log_warning "数据同步验证存在问题，但不影响继续"
        return 0
    fi
}

# 配置Docker环境（可选）
execute_docker_setup() {
    log_info "===== 步骤4: 配置Docker环境（可选）====="
    
    read -p "是否配置Docker环境？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if "$SCRIPT_DIR/docker-env-setup.sh" setup; then
            log_success "Docker环境配置完成"
        else
            log_warning "Docker环境配置失败，但不影响主要功能"
        fi
    else
        log_info "跳过Docker环境配置"
    fi
}

# 启动监控
execute_monitoring() {
    log_info "===== 步骤5: 启动复制监控 ====="
    
    read -p "是否启动持续监控？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 在后台启动监控
        nohup "$SCRIPT_DIR/monitor-replication.sh" monitor > /tmp/replication_monitor.out 2>&1 &
        local monitor_pid=$!
        log_success "监控已在后台启动 (PID: $monitor_pid)"
        echo "查看监控输出: tail -f /tmp/replication_monitor.out"
    else
        log_info "跳过监控启动"
        # 执行一次性检查
        "$SCRIPT_DIR/monitor-replication.sh" check
    fi
}

# 生成执行报告
generate_report() {
    log_info "生成执行报告..."
    
    local report_file="/tmp/financial_system_fix_report_$(date +%Y%m%d_%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>FinancialSystem修复执行报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #4CAF50; color: white; }
        .section { margin: 20px 0; padding: 15px; background: #f9f9f9; border-left: 4px solid #4CAF50; }
    </style>
</head>
<body>
    <h1>FinancialSystem修复执行报告</h1>
    <p>生成时间: $(date '+%Y-%m-%d %H:%M:%S')</p>
    
    <div class="section">
        <h2>执行摘要</h2>
        <table>
            <tr><th>步骤</th><th>状态</th><th>说明</th></tr>
            <tr>
                <td>MySQL认证修复</td>
                <td class="$(grep -q "认证修复完成" "$LOG_FILE" && echo "success" || echo "error")">
                    $(grep -q "认证修复完成" "$LOG_FILE" && echo "成功" || echo "失败")
                </td>
                <td>解决caching_sha2_password认证问题</td>
            </tr>
            <tr>
                <td>用户数据初始化</td>
                <td class="$(grep -q "用户数据初始化完成" "$LOG_FILE" && echo "success" || echo "error")">
                    $(grep -q "用户数据初始化完成" "$LOG_FILE" && echo "成功" || echo "失败")
                </td>
                <td>创建默认用户和权限</td>
            </tr>
            <tr>
                <td>数据同步验证</td>
                <td class="$(grep -q "数据同步验证完成" "$LOG_FILE" && echo "success" || echo "warning")">
                    $(grep -q "数据同步验证完成" "$LOG_FILE" && echo "成功" || echo "警告")
                </td>
                <td>验证主从复制状态</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>当前复制状态</h2>
        <pre>
$(mysql -uroot -p'Zlb&198838' -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Master_Host" || echo "无法获取复制状态")
        </pre>
    </div>
    
    <div class="section">
        <h2>后续操作建议</h2>
        <ol>
            <li>定期检查复制状态: <code>./scripts/database/monitor-replication.sh check</code></li>
            <li>查看监控日志: <code>tail -f /tmp/mysql_replication_monitor/replication_monitor.log</code></li>
            <li>测试登录功能: <code>curl -X POST http://localhost:8080/api/auth/login -H "Content-Type: application/json" -d '{"username":"admin","password":"admin123"}'</code></li>
            <li>部署到生产环境: <code>git push production main</code></li>
        </ol>
    </div>
    
    <div class="section">
        <h2>执行日志</h2>
        <pre>
$(tail -50 "$LOG_FILE")
        </pre>
    </div>
    
    <div class="section">
        <h2>快速命令参考</h2>
        <pre>
# 检查复制状态
mysql -e "SHOW SLAVE STATUS\G" | grep -E "Running|Behind"

# 测试登录
curl -X POST http://localhost:8080/api/auth/login -H "Content-Type: application/json" -d '{"username":"admin","password":"admin123"}'

# 查看用户
mysql -e "SELECT username, real_name FROM user_system.user"

# 停止监控
pkill -f "monitor-replication.sh"
        </pre>
    </div>
</body>
</html>
EOF
    
    log_success "报告已生成: $report_file"
    
    # 在macOS上自动打开报告
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open "$report_file"
    fi
}

# 主函数
main() {
    echo -e "${CYAN}===== FinancialSystem Linux部署问题修复工具 =====${NC}"
    echo -e "${BLUE}版本: 1.0${NC}"
    echo -e "${BLUE}作者: SuperClaude${NC}"
    echo -e "${BLUE}日期: $(date '+%Y-%m-%d')${NC}\n"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 显示执行计划
    show_execution_plan
    
    # 检查前置条件
    check_prerequisites
    
    # 执行修复步骤
    local success=true
    
    if ! execute_auth_fix; then
        success=false
        log_error "认证修复失败，后续步骤可能受影响"
    fi
    
    if ! execute_user_init; then
        success=false
        log_error "用户初始化失败，登录功能将不可用"
    fi
    
    execute_sync_verify
    execute_docker_setup
    execute_monitoring
    
    # 生成报告
    generate_report
    
    # 计算执行时间
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo -e "\n${CYAN}===== 执行完成 =====${NC}"
    echo -e "总耗时: ${YELLOW}${duration}秒${NC}"
    echo -e "日志文件: ${BLUE}${LOG_FILE}${NC}"
    
    if $success; then
        log_success "所有关键步骤执行成功！"
        echo -e "\n${GREEN}✅ 系统已准备就绪，可以进行部署${NC}"
    else
        log_warning "部分步骤执行失败，请查看日志了解详情"
        echo -e "\n${YELLOW}⚠️  请解决上述问题后再进行部署${NC}"
    fi
    
    echo -e "\n${BLUE}快速验证命令：${NC}"
    echo "1. 检查复制: mysql -e 'SHOW SLAVE STATUS\\G' | grep Running"
    echo "2. 测试登录: curl -X POST http://localhost:8080/api/auth/login -H 'Content-Type: application/json' -d '{\"username\":\"admin\",\"password\":\"admin123\"}'"
}

# 执行主函数
main "$@"