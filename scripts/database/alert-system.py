#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MySQL复制自动告警系统
监控复制状态并发送多渠道告警通知

功能：
1. 实时监控MySQL复制状态
2. 多种告警渠道（邮件、企业微信、钉钉、Slack）
3. 智能告警防重复
4. 可配置的告警阈值
5. 告警升级机制
6. 监控指标收集和报告

作者：SuperClaude
日期：2025-07-08
"""

import os
import sys
import json
import time
import smtplib
import requests
import hashlib
import threading
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

try:
    import mysql.connector
except ImportError:
    print("错误：需要安装mysql-connector-python")
    print("执行：pip install mysql-connector-python")
    sys.exit(1)

@dataclass
class AlertConfig:
    """告警配置"""
    # MySQL连接配置
    mysql_host: str = "localhost"
    mysql_port: int = 3306
    mysql_user: str = "root"
    mysql_password: str = "Zlb&198838"
    
    # 告警阈值
    lag_warning_threshold: int = 60    # 延迟警告阈值（秒）
    lag_critical_threshold: int = 300  # 延迟严重阈值（秒）
    connection_timeout: int = 30       # 连接超时（秒）
    
    # 告警频率控制
    alert_interval: int = 300          # 重复告警间隔（秒）
    escalation_time: int = 1800        # 告警升级时间（秒）
    
    # 邮件配置
    email_enabled: bool = False
    smtp_server: str = "smtp.qq.com"
    smtp_port: int = 587
    smtp_user: str = ""
    smtp_password: str = ""
    email_from: str = ""
    email_to: List[str] = None
    
    # 企业微信配置
    wechat_enabled: bool = False
    wechat_webhook: str = ""
    
    # 钉钉配置
    dingtalk_enabled: bool = False
    dingtalk_webhook: str = ""
    dingtalk_secret: str = ""
    
    # Slack配置
    slack_enabled: bool = False
    slack_webhook: str = ""
    
    # 监控间隔
    check_interval: int = 30  # 检查间隔（秒）
    
    def __post_init__(self):
        if self.email_to is None:
            self.email_to = []

@dataclass
class ReplicationStatus:
    """复制状态"""
    timestamp: datetime
    io_running: bool
    sql_running: bool
    seconds_behind_master: Optional[int]
    master_host: str
    master_log_file: str
    master_log_pos: int
    last_io_error: str
    last_sql_error: str
    connection_status: str

@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    level: str  # INFO, WARNING, CRITICAL, RESOLVED
    title: str
    message: str
    timestamp: datetime
    status: Optional[ReplicationStatus] = None

class AlertManager:
    """告警管理器"""
    
    def __init__(self, config: AlertConfig):
        self.config = config
        self.last_alerts = {}  # alert_id -> timestamp
        self.alert_history = deque(maxlen=1000)
        self.escalated_alerts = set()
        
    def should_send_alert(self, alert: Alert) -> bool:
        """检查是否应该发送告警"""
        now = datetime.now()
        
        # 检查重复告警间隔
        if alert.alert_id in self.last_alerts:
            last_time = self.last_alerts[alert.alert_id]
            if (now - last_time).seconds < self.config.alert_interval:
                return False
        
        return True
    
    def send_alert(self, alert: Alert):
        """发送告警"""
        if not self.should_send_alert(alert):
            return
        
        self.last_alerts[alert.alert_id] = alert.timestamp
        self.alert_history.append(alert)
        
        # 并行发送到所有渠道
        threads = []
        
        if self.config.email_enabled:
            t = threading.Thread(target=self._send_email, args=(alert,))
            threads.append(t)
        
        if self.config.wechat_enabled:
            t = threading.Thread(target=self._send_wechat, args=(alert,))
            threads.append(t)
        
        if self.config.dingtalk_enabled:
            t = threading.Thread(target=self._send_dingtalk, args=(alert,))
            threads.append(t)
        
        if self.config.slack_enabled:
            t = threading.Thread(target=self._send_slack, args=(alert,))
            threads.append(t)
        
        # 启动所有线程
        for t in threads:
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join(timeout=10)
        
        print(f"📢 告警已发送: {alert.level} - {alert.title}")
    
    def _send_email(self, alert: Alert):
        """发送邮件告警"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.config.email_from
            msg['To'] = ', '.join(self.config.email_to)
            msg['Subject'] = f"[FinancialSystem Alert] {alert.level}: {alert.title}"
            
            # 构建邮件内容
            body = f"""
FinancialSystem MySQL复制告警

告警级别: {alert.level}
告警时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
告警标题: {alert.title}

详细信息:
{alert.message}
"""
            
            if alert.status:
                body += f"""
当前复制状态:
- IO线程: {'运行' if alert.status.io_running else '停止'}
- SQL线程: {'运行' if alert.status.sql_running else '停止'}
- 复制延迟: {alert.status.seconds_behind_master or '未知'} 秒
- 主服务器: {alert.status.master_host}
"""
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.config.smtp_server, self.config.smtp_port) as server:
                server.starttls()
                server.login(self.config.smtp_user, self.config.smtp_password)
                server.send_message(msg)
                
        except Exception as e:
            print(f"邮件发送失败: {e}")
    
    def _send_wechat(self, alert: Alert):
        """发送企业微信告警"""
        try:
            # 构建企业微信消息
            color_map = {
                'INFO': '#3498db',
                'WARNING': '#f39c12',
                'CRITICAL': '#e74c3c',
                'RESOLVED': '#27ae60'
            }
            
            content = f"""
## FinancialSystem 告警通知

**告警级别**: <font color="{color_map.get(alert.level, '#000000')}">{alert.level}</font>
**告警时间**: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
**告警标题**: {alert.title}

**详细信息**:
{alert.message}
"""
            
            if alert.status:
                content += f"""
**当前状态**:
- IO线程: {'✅运行' if alert.status.io_running else '❌停止'}
- SQL线程: {'✅运行' if alert.status.sql_running else '❌停止'}
- 复制延迟: {alert.status.seconds_behind_master or '未知'} 秒
"""
            
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }
            
            response = requests.post(self.config.wechat_webhook, json=data, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            print(f"企业微信发送失败: {e}")
    
    def _send_dingtalk(self, alert: Alert):
        """发送钉钉告警"""
        try:
            # 构建钉钉消息
            title = f"FinancialSystem {alert.level}告警"
            text = f"""
### {alert.title}

**告警级别**: {alert.level}
**告警时间**: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

**详细信息**:
{alert.message}
"""
            
            if alert.status:
                text += f"""
**当前状态**:
- IO线程: {'运行' if alert.status.io_running else '停止'}
- SQL线程: {'运行' if alert.status.sql_running else '停止'}
- 复制延迟: {alert.status.seconds_behind_master or '未知'} 秒
"""
            
            data = {
                "msgtype": "markdown",
                "markdown": {
                    "title": title,
                    "text": text
                }
            }
            
            # 如果配置了签名，添加签名
            if self.config.dingtalk_secret:
                import hmac
                import base64
                import urllib.parse
                
                timestamp = str(round(time.time() * 1000))
                secret_enc = self.config.dingtalk_secret.encode('utf-8')
                string_to_sign = f'{timestamp}\n{self.config.dingtalk_secret}'
                string_to_sign_enc = string_to_sign.encode('utf-8')
                hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
                
                url = f"{self.config.dingtalk_webhook}&timestamp={timestamp}&sign={sign}"
            else:
                url = self.config.dingtalk_webhook
            
            response = requests.post(url, json=data, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            print(f"钉钉发送失败: {e}")
    
    def _send_slack(self, alert: Alert):
        """发送Slack告警"""
        try:
            # 构建Slack消息
            color_map = {
                'INFO': '#36a64f',
                'WARNING': '#ff9500',
                'CRITICAL': '#ff0000',
                'RESOLVED': '#36a64f'
            }
            
            attachment = {
                "color": color_map.get(alert.level, '#000000'),
                "title": f"FinancialSystem {alert.level} Alert",
                "fields": [
                    {
                        "title": "Alert Title",
                        "value": alert.title,
                        "short": False
                    },
                    {
                        "title": "Level",
                        "value": alert.level,
                        "short": True
                    },
                    {
                        "title": "Time",
                        "value": alert.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        "short": True
                    },
                    {
                        "title": "Details",
                        "value": alert.message,
                        "short": False
                    }
                ]
            }
            
            if alert.status:
                attachment["fields"].extend([
                    {
                        "title": "IO Thread",
                        "value": "Running" if alert.status.io_running else "Stopped",
                        "short": True
                    },
                    {
                        "title": "SQL Thread",
                        "value": "Running" if alert.status.sql_running else "Stopped",
                        "short": True
                    },
                    {
                        "title": "Replication Lag",
                        "value": f"{alert.status.seconds_behind_master or 'Unknown'} seconds",
                        "short": True
                    }
                ])
            
            data = {
                "text": f"FinancialSystem Alert: {alert.title}",
                "attachments": [attachment]
            }
            
            response = requests.post(self.config.slack_webhook, json=data, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            print(f"Slack发送失败: {e}")

class ReplicationMonitor:
    """复制监控器"""
    
    def __init__(self, config: AlertConfig):
        self.config = config
        self.alert_manager = AlertManager(config)
        self.last_status = None
        self.connection = None
        self.consecutive_failures = 0
    
    def connect_mysql(self) -> bool:
        """连接MySQL"""
        try:
            if self.connection and self.connection.is_connected():
                return True
            
            self.connection = mysql.connector.connect(
                host=self.config.mysql_host,
                port=self.config.mysql_port,
                user=self.config.mysql_user,
                password=self.config.mysql_password,
                connection_timeout=self.config.connection_timeout
            )
            return True
        except Exception as e:
            print(f"MySQL连接失败: {e}")
            return False
    
    def get_replication_status(self) -> Optional[ReplicationStatus]:
        """获取复制状态"""
        if not self.connect_mysql():
            return None
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute("SHOW SLAVE STATUS")
            result = cursor.fetchone()
            
            if not result:
                return ReplicationStatus(
                    timestamp=datetime.now(),
                    io_running=False,
                    sql_running=False,
                    seconds_behind_master=None,
                    master_host="",
                    master_log_file="",
                    master_log_pos=0,
                    last_io_error="No replication configured",
                    last_sql_error="",
                    connection_status="Connected"
                )
            
            return ReplicationStatus(
                timestamp=datetime.now(),
                io_running=result.get('Slave_IO_Running') == 'Yes',
                sql_running=result.get('Slave_SQL_Running') == 'Yes',
                seconds_behind_master=result.get('Seconds_Behind_Master'),
                master_host=result.get('Master_Host', ''),
                master_log_file=result.get('Master_Log_File', ''),
                master_log_pos=result.get('Read_Master_Log_Pos', 0),
                last_io_error=result.get('Last_IO_Error', ''),
                last_sql_error=result.get('Last_SQL_Error', ''),
                connection_status="Connected"
            )
            
        except Exception as e:
            print(f"获取复制状态失败: {e}")
            return None
        finally:
            cursor.close()
    
    def analyze_status(self, status: ReplicationStatus) -> List[Alert]:
        """分析状态并生成告警"""
        alerts = []
        now = datetime.now()
        
        # 检查IO线程
        if not status.io_running:
            alert = Alert(
                alert_id="io_thread_stopped",
                level="CRITICAL",
                title="复制IO线程停止",
                message=f"IO线程已停止运行。错误信息: {status.last_io_error}",
                timestamp=now,
                status=status
            )
            alerts.append(alert)
        
        # 检查SQL线程
        if not status.sql_running:
            alert = Alert(
                alert_id="sql_thread_stopped",
                level="CRITICAL",
                title="复制SQL线程停止",
                message=f"SQL线程已停止运行。错误信息: {status.last_sql_error}",
                timestamp=now,
                status=status
            )
            alerts.append(alert)
        
        # 检查复制延迟
        if status.seconds_behind_master is not None:
            if status.seconds_behind_master > self.config.lag_critical_threshold:
                alert = Alert(
                    alert_id="replication_lag_critical",
                    level="CRITICAL",
                    title="复制延迟严重",
                    message=f"复制延迟已达到 {status.seconds_behind_master} 秒，超过严重阈值 {self.config.lag_critical_threshold} 秒",
                    timestamp=now,
                    status=status
                )
                alerts.append(alert)
            elif status.seconds_behind_master > self.config.lag_warning_threshold:
                alert = Alert(
                    alert_id="replication_lag_warning",
                    level="WARNING",
                    title="复制延迟警告",
                    message=f"复制延迟已达到 {status.seconds_behind_master} 秒，超过警告阈值 {self.config.lag_warning_threshold} 秒",
                    timestamp=now,
                    status=status
                )
                alerts.append(alert)
        
        # 检查是否从错误状态恢复
        if self.last_status:
            if (not self.last_status.io_running and status.io_running) or \
               (not self.last_status.sql_running and status.sql_running):
                alert = Alert(
                    alert_id="replication_recovered",
                    level="RESOLVED",
                    title="复制状态已恢复",
                    message="复制线程已恢复正常运行",
                    timestamp=now,
                    status=status
                )
                alerts.append(alert)
        
        return alerts
    
    def start_monitoring(self):
        """开始监控"""
        print(f"🚀 开始MySQL复制监控...")
        print(f"监控间隔: {self.config.check_interval} 秒")
        print(f"延迟阈值: 警告 {self.config.lag_warning_threshold}s, 严重 {self.config.lag_critical_threshold}s")
        
        while True:
            try:
                # 获取复制状态
                status = self.get_replication_status()
                
                if status is None:
                    # 连接失败
                    self.consecutive_failures += 1
                    if self.consecutive_failures >= 3:  # 连续失败3次发送告警
                        alert = Alert(
                            alert_id="mysql_connection_failed",
                            level="CRITICAL",
                            title="MySQL连接失败",
                            message=f"连续 {self.consecutive_failures} 次无法连接到MySQL服务器",
                            timestamp=datetime.now()
                        )
                        self.alert_manager.send_alert(alert)
                else:
                    # 连接成功，重置失败计数
                    if self.consecutive_failures > 0:
                        self.consecutive_failures = 0
                        alert = Alert(
                            alert_id="mysql_connection_recovered",
                            level="RESOLVED",
                            title="MySQL连接已恢复",
                            message="MySQL服务器连接已恢复正常",
                            timestamp=datetime.now(),
                            status=status
                        )
                        self.alert_manager.send_alert(alert)
                    
                    # 分析状态并发送告警
                    alerts = self.analyze_status(status)
                    for alert in alerts:
                        self.alert_manager.send_alert(alert)
                    
                    # 更新最后状态
                    self.last_status = status
                    
                    # 显示当前状态
                    io_status = "✅" if status.io_running else "❌"
                    sql_status = "✅" if status.sql_running else "❌"
                    lag = status.seconds_behind_master or "未知"
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] IO:{io_status} SQL:{sql_status} 延迟:{lag}s")
                
            except KeyboardInterrupt:
                print("\n💤 监控已停止")
                break
            except Exception as e:
                print(f"监控异常: {e}")
            
            # 等待下次检查
            time.sleep(self.config.check_interval)

def load_config(config_file: str) -> AlertConfig:
    """加载配置文件"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
                return AlertConfig(**config_dict)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
    
    # 返回默认配置
    return AlertConfig()

def generate_sample_config(config_file: str):
    """生成示例配置文件"""
    sample_config = AlertConfig(
        # MySQL配置
        mysql_host="localhost",
        mysql_port=3306,
        mysql_user="root",
        mysql_password="Zlb&198838",
        
        # 告警阈值
        lag_warning_threshold=60,
        lag_critical_threshold=300,
        
        # 邮件配置
        email_enabled=False,
        smtp_server="smtp.qq.com",
        smtp_port=587,
        smtp_user="<EMAIL>",
        smtp_password="your_app_password",
        email_from="<EMAIL>",
        email_to=["<EMAIL>"],
        
        # 企业微信配置
        wechat_enabled=False,
        wechat_webhook="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY",
        
        # 钉钉配置
        dingtalk_enabled=False,
        dingtalk_webhook="https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN",
        dingtalk_secret="",
        
        # Slack配置
        slack_enabled=False,
        slack_webhook="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
        
        # 监控间隔
        check_interval=30
    )
    
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(asdict(sample_config), f, indent=2, ensure_ascii=False)
    
    print(f"示例配置文件已生成: {config_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MySQL复制自动告警系统')
    parser.add_argument('--config', default='alert_config.json', help='配置文件路径')
    parser.add_argument('--generate-config', action='store_true', help='生成示例配置文件')
    parser.add_argument('--test-alert', action='store_true', help='测试告警发送')
    
    args = parser.parse_args()
    
    if args.generate_config:
        generate_sample_config(args.config)
        return
    
    # 加载配置
    config = load_config(args.config)
    
    if args.test_alert:
        # 测试告警
        alert_manager = AlertManager(config)
        test_alert = Alert(
            alert_id="test_alert",
            level="INFO",
            title="告警系统测试",
            message="这是一个测试告警，用于验证告警系统是否正常工作。",
            timestamp=datetime.now()
        )
        alert_manager.send_alert(test_alert)
        print("测试告警已发送")
        return
    
    # 启动监控
    monitor = ReplicationMonitor(config)
    monitor.start_monitoring()

if __name__ == "__main__":
    main()