#!/bin/bash

# ===================================================================
# 数据同步验证脚本
# 验证MySQL主从复制状态和数据一致性
#
# 功能：
# 1. 检查复制线程状态
# 2. 测试实时数据同步
# 3. 验证数据一致性
# 4. 生成同步报告
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 配置变量
LINUX_SERVER="10.25.1.85"
LINUX_USER="admin"
MYSQL_ROOT_PASS="Zlb&198838"
LOCAL_MYSQL_USER="root"
LOCAL_MYSQL_PASS="Zlb&198838"
DATABASES=("overdue_debt_db" "user_system" "kingdee")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查复制状态
check_replication_status() {
    log_info "检查本地MySQL复制状态..."
    
    local slave_status=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null)
    
    if [[ -z "$slave_status" ]]; then
        log_error "未配置复制或无法获取复制状态"
        return 1
    fi
    
    # 提取关键信息
    local master_host=$(echo "$slave_status" | grep "Master_Host:" | awk '{print $2}')
    local io_running=$(echo "$slave_status" | grep "Slave_IO_Running:" | awk '{print $2}')
    local sql_running=$(echo "$slave_status" | grep "Slave_SQL_Running:" | awk '{print $2}')
    local seconds_behind=$(echo "$slave_status" | grep "Seconds_Behind_Master:" | awk '{print $2}')
    local last_io_error=$(echo "$slave_status" | grep "Last_IO_Error:" | cut -d':' -f2-)
    local last_sql_error=$(echo "$slave_status" | grep "Last_SQL_Error:" | cut -d':' -f2-)
    
    # 显示复制状态
    echo -e "\n${CYAN}========== 复制状态详情 ==========${NC}"
    echo -e "主服务器: ${BLUE}${master_host}${NC}"
    echo -e "IO线程: $([ "$io_running" = "Yes" ] && echo -e "${GREEN}运行中${NC}" || echo -e "${RED}已停止${NC}")"
    echo -e "SQL线程: $([ "$sql_running" = "Yes" ] && echo -e "${GREEN}运行中${NC}" || echo -e "${RED}已停止${NC}")"
    
    if [[ "$seconds_behind" == "NULL" ]]; then
        echo -e "复制延迟: ${RED}未知${NC}"
    elif [[ "$seconds_behind" -eq 0 ]]; then
        echo -e "复制延迟: ${GREEN}无延迟${NC}"
    elif [[ "$seconds_behind" -lt 10 ]]; then
        echo -e "复制延迟: ${YELLOW}${seconds_behind}秒${NC}"
    else
        echo -e "复制延迟: ${RED}${seconds_behind}秒${NC}"
    fi
    
    # 显示错误信息
    if [[ -n "$last_io_error" && "$last_io_error" != " " ]]; then
        echo -e "IO错误: ${RED}${last_io_error}${NC}"
    fi
    
    if [[ -n "$last_sql_error" && "$last_sql_error" != " " ]]; then
        echo -e "SQL错误: ${RED}${last_sql_error}${NC}"
    fi
    
    echo -e "${CYAN}===================================${NC}\n"
    
    # 返回状态
    if [[ "$io_running" = "Yes" && "$sql_running" = "Yes" ]]; then
        return 0
    else
        return 1
    fi
}

# 测试实时同步
test_realtime_sync() {
    log_info "测试实时数据同步..."
    
    local test_table="sync_test_$(date +%s)"
    local test_db="overdue_debt_db"
    
    # 在主服务器创建测试表
    log_info "在主服务器创建测试数据..."
    ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -e \"
        USE ${test_db};
        CREATE TABLE ${test_table} (
            id INT PRIMARY KEY AUTO_INCREMENT,
            test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            test_data VARCHAR(100),
            source VARCHAR(20)
        );
        INSERT INTO ${test_table} (test_data, source) VALUES 
            ('Test from Linux Master', 'linux'),
            ('Sync Test Data', 'linux'),
            ('Timestamp: $(date)', 'linux');
    \"" 2>/dev/null
    
    # 等待同步
    log_info "等待数据同步..."
    local wait_time=5
    for i in $(seq 1 $wait_time); do
        echo -ne "\r  等待中... $i/$wait_time 秒"
        sleep 1
    done
    echo ""
    
    # 检查本地是否同步
    local row_count=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -Ne "
        SELECT COUNT(*) FROM ${test_db}.${test_table}
    " 2>/dev/null || echo "0")
    
    if [[ "$row_count" -gt 0 ]]; then
        log_success "数据同步成功！同步了 ${row_count} 条记录"
        
        # 显示同步的数据
        echo -e "\n${CYAN}同步的数据：${NC}"
        mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "
            SELECT * FROM ${test_db}.${test_table}
        " 2>/dev/null
        
        # 测试反向同步（如果配置了双向同步）
        log_info "测试本地插入数据..."
        mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "
            INSERT INTO ${test_db}.${test_table} (test_data, source) 
            VALUES ('Test from Local', 'local')
        " 2>/dev/null
        
        # 清理测试表
        ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -e \"
            DROP TABLE ${test_db}.${test_table}
        \"" 2>/dev/null
        
        return 0
    else
        log_error "数据同步失败！未能同步测试数据"
        
        # 清理测试表
        ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -e \"
            DROP TABLE IF EXISTS ${test_db}.${test_table}
        \"" 2>/dev/null
        
        return 1
    fi
}

# 验证数据一致性
verify_data_consistency() {
    log_info "验证数据一致性..."
    
    local inconsistent_count=0
    
    for db in "${DATABASES[@]}"; do
        echo -e "\n${CYAN}检查数据库: ${db}${NC}"
        
        # 获取主服务器表列表
        local master_tables=$(ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -Ne 'SHOW TABLES FROM ${db}'" 2>/dev/null | sort)
        
        # 获取本地表列表
        local local_tables=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -Ne "SHOW TABLES FROM ${db}" 2>/dev/null | sort)
        
        # 比较表数量
        local master_count=$(echo "$master_tables" | wc -l | tr -d ' ')
        local local_count=$(echo "$local_tables" | wc -l | tr -d ' ')
        
        if [[ "$master_count" -eq "$local_count" ]]; then
            echo -e "  表数量: ${GREEN}一致${NC} (${master_count}个表)"
        else
            echo -e "  表数量: ${RED}不一致${NC} (主: ${master_count}, 本地: ${local_count})"
            ((inconsistent_count++))
        fi
        
        # 检查每个表的行数
        while IFS= read -r table; do
            if [[ -z "$table" ]]; then continue; fi
            
            # 获取主服务器行数
            local master_rows=$(ssh ${LINUX_USER}@${LINUX_SERVER} "mysql -uroot -p'${MYSQL_ROOT_PASS}' -Ne 'SELECT COUNT(*) FROM ${db}.${table}'" 2>/dev/null || echo "错误")
            
            # 获取本地行数
            local local_rows=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -Ne "SELECT COUNT(*) FROM ${db}.${table}" 2>/dev/null || echo "错误")
            
            if [[ "$master_rows" == "$local_rows" ]]; then
                echo -e "  ${table}: ${GREEN}✓${NC} (${local_rows}行)"
            else
                echo -e "  ${table}: ${RED}✗${NC} (主: ${master_rows}, 本地: ${local_rows})"
                ((inconsistent_count++))
            fi
        done <<< "$local_tables"
    done
    
    echo -e "\n${CYAN}========== 一致性检查结果 ==========${NC}"
    if [[ $inconsistent_count -eq 0 ]]; then
        echo -e "${GREEN}所有数据完全一致！${NC}"
        return 0
    else
        echo -e "${RED}发现 ${inconsistent_count} 个不一致项${NC}"
        return 1
    fi
}

# 生成同步报告
generate_sync_report() {
    log_info "生成同步状态报告..."
    
    local report_file="/tmp/mysql_sync_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "MySQL数据同步状态报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "1. 复制配置信息"
        echo "----------------"
        mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "Master_Host|Master_User|Master_Port|Master_Log_File|Read_Master_Log_Pos"
        echo ""
        
        echo "2. 复制线程状态"
        echo "----------------"
        mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master"
        echo ""
        
        echo "3. 错误信息"
        echo "----------------"
        mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep -E "Last_.*Error" | grep -v "Last_.*Error: $"
        echo ""
        
        echo "4. 数据库表统计"
        echo "----------------"
        for db in "${DATABASES[@]}"; do
            local table_count=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -Ne "SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA='${db}'" 2>/dev/null)
            echo "${db}: ${table_count} 个表"
        done
        echo ""
        
        echo "5. 性能指标"
        echo "----------------"
        mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW GLOBAL STATUS LIKE 'Slave%'" 2>/dev/null
        echo ""
        
        echo "6. 建议操作"
        echo "----------------"
        local io_running=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep "Slave_IO_Running:" | awk '{print $2}')
        local sql_running=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null | grep "Slave_SQL_Running:" | awk '{print $2}')
        
        if [[ "$io_running" != "Yes" || "$sql_running" != "Yes" ]]; then
            echo "- 复制线程未正常运行，请检查错误日志"
            echo "- 尝试执行: STOP SLAVE; START SLAVE;"
            echo "- 检查网络连接和认证配置"
        else
            echo "- 复制状态正常"
            echo "- 建议定期监控复制延迟"
            echo "- 设置告警阈值为60秒"
        fi
        
    } > "$report_file"
    
    log_success "报告已生成: $report_file"
    
    # 显示报告摘要
    echo -e "\n${CYAN}报告摘要：${NC}"
    grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master" "$report_file" | head -3
}

# 持续监控模式
continuous_monitor() {
    log_info "进入持续监控模式（按Ctrl+C退出）..."
    
    while true; do
        clear
        echo -e "${CYAN}===== MySQL复制监控 ===== $(date)${NC}\n"
        
        # 获取复制状态
        local slave_status=$(mysql -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW SLAVE STATUS\G" 2>/dev/null)
        
        # 显示关键指标
        echo "$slave_status" | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_.*Error" | grep -v "Last_.*Error: $"
        
        # 显示处理的事件数
        local exec_pos=$(echo "$slave_status" | grep "Exec_Master_Log_Pos:" | awk '{print $2}')
        echo -e "\n执行位置: ${BLUE}${exec_pos}${NC}"
        
        # 等待5秒
        sleep 5
    done
}

# 主菜单
show_menu() {
    echo -e "\n${BLUE}===== 数据同步验证工具 =====${NC}"
    echo "1. 检查复制状态"
    echo "2. 测试实时同步"
    echo "3. 验证数据一致性"
    echo "4. 生成同步报告"
    echo "5. 持续监控模式"
    echo "6. 完整检查（1-4全部）"
    echo "0. 退出"
    echo -n "请选择操作 [0-6]: "
}

# 主函数
main() {
    # 如果提供了参数，直接执行
    if [[ $# -gt 0 ]]; then
        case "$1" in
            "status")
                check_replication_status
                ;;
            "sync")
                test_realtime_sync
                ;;
            "consistency")
                verify_data_consistency
                ;;
            "report")
                generate_sync_report
                ;;
            "monitor")
                continuous_monitor
                ;;
            "full")
                check_replication_status
                test_realtime_sync
                verify_data_consistency
                generate_sync_report
                ;;
            *)
                echo "用法: $0 [status|sync|consistency|report|monitor|full]"
                exit 1
                ;;
        esac
        exit 0
    fi
    
    # 交互式菜单
    while true; do
        show_menu
        read choice
        
        case $choice in
            1)
                check_replication_status
                ;;
            2)
                test_realtime_sync
                ;;
            3)
                verify_data_consistency
                ;;
            4)
                generate_sync_report
                ;;
            5)
                continuous_monitor
                ;;
            6)
                check_replication_status
                if [[ $? -eq 0 ]]; then
                    test_realtime_sync
                    verify_data_consistency
                fi
                generate_sync_report
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选择"
                ;;
        esac
    done
}

# 执行主函数
main "$@"