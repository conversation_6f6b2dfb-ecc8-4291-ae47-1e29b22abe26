#!/bin/bash

# 中文数据库到英文数据库的迁移脚本
# 功能：将"overdue_debt_db"的数据完整迁移到"overdue_debt_db"数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认数据库配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_USER="${DB_USER:-root}"
DB_PASSWORD="${DB_PASSWORD:-Zlb&198838}"
SOURCE_DB="overdue_debt_db"
TARGET_DB="overdue_debt_db"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查数据库连接
check_db_connection() {
    log_info "检查数据库连接..."
    
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "无法连接到数据库服务器"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 检查源数据库是否存在
check_source_db() {
    log_info "检查源数据库是否存在..."
    
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE \`$SOURCE_DB\`;" >/dev/null 2>&1; then
        log_error "源数据库 '$SOURCE_DB' 不存在"
        exit 1
    fi
    
    log_success "源数据库存在"
}

# 创建目标数据库
create_target_db() {
    log_info "创建目标数据库..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" << EOF
CREATE DATABASE IF NOT EXISTS \`$TARGET_DB\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EOF
    
    log_success "目标数据库创建成功"
}

# 获取源数据库的所有表
get_source_tables() {
    log_info "获取源数据库的表结构..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT TABLE_NAME FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$SOURCE_DB' AND TABLE_TYPE = 'BASE TABLE';
    " --skip-column-names --batch
}

# 复制表结构和数据
copy_table_structure_and_data() {
    local table_name="$1"
    
    log_info "复制表: $table_name"
    
    # 1. 复制表结构
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        CREATE TABLE IF NOT EXISTS \`$TARGET_DB\`.\`$table_name\` LIKE \`$SOURCE_DB\`.\`$table_name\`;
    "
    
    # 2. 复制数据
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        INSERT IGNORE INTO \`$TARGET_DB\`.\`$table_name\` SELECT * FROM \`$SOURCE_DB\`.\`$table_name\`;
    "
    
    # 3. 获取记录数量
    local source_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) FROM \`$SOURCE_DB\`.\`$table_name\`;
    " --skip-column-names --batch)
    
    local target_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) FROM \`$TARGET_DB\`.\`$table_name\`;
    " --skip-column-names --batch)
    
    log_success "表 $table_name 复制完成 (源: $source_count 条, 目标: $target_count 条)"
}

# 设置权限
set_permissions() {
    log_info "设置数据库权限..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" << EOF
GRANT ALL PRIVILEGES ON \`$TARGET_DB\`.* TO '$DB_USER'@'%';
FLUSH PRIVILEGES;
EOF
    
    log_success "权限设置完成"
}

# 验证迁移结果
verify_migration() {
    log_info "验证迁移结果..."
    
    # 获取源数据库表数量
    local source_tables_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$SOURCE_DB' AND TABLE_TYPE = 'BASE TABLE';
    " --skip-column-names --batch)
    
    # 获取目标数据库表数量
    local target_tables_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT COUNT(*) FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$TARGET_DB' AND TABLE_TYPE = 'BASE TABLE';
    " --skip-column-names --batch)
    
    log_info "源数据库表数量: $source_tables_count"
    log_info "目标数据库表数量: $target_tables_count"
    
    if [ "$source_tables_count" -eq "$target_tables_count" ]; then
        log_success "表结构迁移验证通过"
    else
        log_error "表结构迁移验证失败"
        exit 1
    fi
}

# 生成迁移报告
generate_migration_report() {
    log_info "生成迁移报告..."
    
    local report_file="/tmp/migration_report_$(date +%Y%m%d_%H%M%S).txt"
    
    echo "=== 数据库迁移报告 ===" > "$report_file"
    echo "迁移时间: $(date)" >> "$report_file"
    echo "源数据库: $SOURCE_DB" >> "$report_file"
    echo "目标数据库: $TARGET_DB" >> "$report_file"
    echo "" >> "$report_file"
    
    # 获取所有表的统计信息
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "
        SELECT 
            '$SOURCE_DB' AS database_name,
            TABLE_NAME,
            TABLE_ROWS,
            DATA_LENGTH,
            INDEX_LENGTH
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$SOURCE_DB' AND TABLE_TYPE = 'BASE TABLE'
        UNION ALL
        SELECT 
            '$TARGET_DB' AS database_name,
            TABLE_NAME,
            TABLE_ROWS,
            DATA_LENGTH,
            INDEX_LENGTH
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$TARGET_DB' AND TABLE_TYPE = 'BASE TABLE'
        ORDER BY database_name, TABLE_NAME;
    " >> "$report_file"
    
    log_success "迁移报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始数据库迁移: $SOURCE_DB -> $TARGET_DB"
    
    # 检查数据库连接
    check_db_connection
    
    # 检查源数据库
    check_source_db
    
    # 创建目标数据库
    create_target_db
    
    # 获取所有表并复制
    local tables=$(get_source_tables)
    
    if [ -z "$tables" ]; then
        log_warning "源数据库中没有找到表"
        exit 0
    fi
    
    # 复制每个表
    while IFS= read -r table; do
        if [ -n "$table" ]; then
            copy_table_structure_and_data "$table"
        fi
    done <<< "$tables"
    
    # 设置权限
    set_permissions
    
    # 验证迁移
    verify_migration
    
    # 生成报告
    generate_migration_report
    
    log_success "数据库迁移完成！"
    log_info "现在可以安全地将应用程序配置从 '$SOURCE_DB' 切换到 '$TARGET_DB'"
}

# 显示帮助信息
show_help() {
    echo "数据库迁移脚本 - 将中文数据库迁移到英文数据库"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -H, --host HOST         数据库主机 (默认: localhost)"
    echo "  -P, --port PORT         数据库端口 (默认: 3306)"
    echo "  -u, --user USER         数据库用户 (默认: root)"
    echo "  -p, --password PASSWORD 数据库密码"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST                 数据库主机"
    echo "  DB_PORT                 数据库端口"
    echo "  DB_USER                 数据库用户"
    echo "  DB_PASSWORD             数据库密码"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 -H localhost -P 3306 -u root -p 'password'"
    echo "  DB_HOST=localhost DB_PASSWORD='password' $0"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -H|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -P|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -p|--password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main