#!/bin/bash

# ===================================================================
# Docker环境启动和测试脚本
# 配置和测试三层同步架构中的Docker MySQL
#
# 功能：
# 1. 启动Docker Desktop（macOS）
# 2. 启动MySQL容器
# 3. 配置Docker MySQL复制
# 4. 测试三层同步架构
#
# 作者：SuperClaude
# 日期：2025-07-08
# ===================================================================

set -e

# 配置变量
PROJECT_DIR="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
DOCKER_MYSQL_CONTAINER="financial-mysql"
DOCKER_MYSQL_PORT="3307"  # 避免与本地MySQL冲突
LOCAL_MYSQL_PORT="3306"
MYSQL_ROOT_PASS="Zlb&198838"
LOCAL_MYSQL_USER="root"
LOCAL_MYSQL_PASS="Zlb&198838"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查Docker状态
check_docker_status() {
    log_info "检查Docker状态..."
    
    if ! docker info &> /dev/null; then
        log_warning "Docker未运行，尝试启动Docker Desktop..."
        
        # macOS启动Docker Desktop
        if [[ "$OSTYPE" == "darwin"* ]]; then
            open -a Docker
            
            # 等待Docker启动
            local max_wait=60
            local count=0
            while ! docker info &> /dev/null && [ $count -lt $max_wait ]; do
                echo -ne "\r  等待Docker启动... $count/$max_wait 秒"
                sleep 1
                ((count++))
            done
            echo ""
            
            if docker info &> /dev/null; then
                log_success "Docker已启动"
            else
                log_error "Docker启动失败，请手动启动Docker Desktop"
                return 1
            fi
        else
            log_error "请手动启动Docker服务"
            return 1
        fi
    else
        log_success "Docker正在运行"
    fi
}

# 启动MySQL容器
start_mysql_container() {
    log_info "启动MySQL容器..."
    
    cd "$PROJECT_DIR"
    
    # 检查容器是否已存在
    if docker ps -a | grep -q "$DOCKER_MYSQL_CONTAINER"; then
        if docker ps | grep -q "$DOCKER_MYSQL_CONTAINER"; then
            log_success "MySQL容器已在运行"
        else
            log_info "启动已存在的MySQL容器..."
            docker start "$DOCKER_MYSQL_CONTAINER"
            sleep 5
        fi
    else
        log_info "创建并启动MySQL容器..."
        
        # 使用docker-compose启动
        if [[ -f "docker-compose.local.yml" ]]; then
            docker-compose -f docker-compose.local.yml up -d mysql
        else
            # 直接使用docker run
            docker run -d \
                --name "$DOCKER_MYSQL_CONTAINER" \
                -e MYSQL_ROOT_PASSWORD="$MYSQL_ROOT_PASS" \
                -p "${DOCKER_MYSQL_PORT}:3306" \
                -v mysql_data:/var/lib/mysql \
                mysql:8.0 \
                --default-authentication-plugin=mysql_native_password \
                --server-id=3 \
                --log-bin=mysql-bin \
                --binlog-format=ROW \
                --binlog-do-db=overdue_debt_db \
                --binlog-do-db=user_system \
                --binlog-do-db=kingdee
        fi
        
        # 等待MySQL启动
        log_info "等待MySQL容器启动..."
        local max_wait=30
        local count=0
        while ! docker exec "$DOCKER_MYSQL_CONTAINER" mysqladmin ping -h localhost --silent 2>/dev/null && [ $count -lt $max_wait ]; do
            echo -ne "\r  等待MySQL就绪... $count/$max_wait 秒"
            sleep 1
            ((count++))
        done
        echo ""
        
        if docker exec "$DOCKER_MYSQL_CONTAINER" mysqladmin ping -h localhost --silent 2>/dev/null; then
            log_success "MySQL容器已就绪"
        else
            log_error "MySQL容器启动失败"
            return 1
        fi
    fi
}

# 初始化Docker MySQL数据库
init_docker_databases() {
    log_info "初始化Docker MySQL数据库..."
    
    # 创建必要的数据库
    docker exec "$DOCKER_MYSQL_CONTAINER" mysql -uroot -p"$MYSQL_ROOT_PASS" -e "
        CREATE DATABASE IF NOT EXISTS overdue_debt_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        CREATE DATABASE IF NOT EXISTS user_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        CREATE DATABASE IF NOT EXISTS kingdee CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        SHOW DATABASES;
    " 2>/dev/null
    
    log_success "数据库初始化完成"
}

# 配置Docker MySQL作为从服务器
configure_docker_replication() {
    log_info "配置Docker MySQL复制（从本地MySQL复制）..."
    
    # 获取本地MySQL的binlog位置
    local master_status=$(mysql -h localhost -P${LOCAL_MYSQL_PORT} -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "SHOW MASTER STATUS\G" 2>/dev/null)
    local master_file=$(echo "$master_status" | grep "File:" | awk '{print $2}')
    local master_pos=$(echo "$master_status" | grep "Position:" | awk '{print $2}')
    
    if [[ -z "$master_file" || -z "$master_pos" ]]; then
        log_error "无法获取本地MySQL的Master状态"
        return 1
    fi
    
    log_info "本地MySQL状态: File=$master_file, Position=$master_pos"
    
    # 在Docker MySQL中配置复制
    docker exec "$DOCKER_MYSQL_CONTAINER" mysql -uroot -p"$MYSQL_ROOT_PASS" -e "
        STOP SLAVE;
        RESET SLAVE ALL;
        
        CHANGE MASTER TO
            MASTER_HOST='host.docker.internal',
            MASTER_PORT=${LOCAL_MYSQL_PORT},
            MASTER_USER='repl',
            MASTER_PASSWORD='${MYSQL_ROOT_PASS}',
            MASTER_LOG_FILE='${master_file}',
            MASTER_LOG_POS=${master_pos};
        
        START SLAVE;
    " 2>/dev/null
    
    # 检查复制状态
    sleep 3
    local slave_status=$(docker exec "$DOCKER_MYSQL_CONTAINER" mysql -uroot -p"$MYSQL_ROOT_PASS" -e "SHOW SLAVE STATUS\G" 2>/dev/null)
    local io_running=$(echo "$slave_status" | grep "Slave_IO_Running:" | awk '{print $2}')
    local sql_running=$(echo "$slave_status" | grep "Slave_SQL_Running:" | awk '{print $2}')
    
    if [[ "$io_running" = "Yes" && "$sql_running" = "Yes" ]]; then
        log_success "Docker MySQL复制配置成功"
    else
        log_error "Docker MySQL复制配置失败"
        echo "$slave_status" | grep -E "Last_.*Error"
        return 1
    fi
}

# 测试三层同步
test_three_layer_sync() {
    log_info "测试三层同步架构..."
    
    local test_table="three_layer_test_$(date +%s)"
    local test_db="overdue_debt_db"
    
    echo -e "\n${CYAN}===== 三层同步测试 =====${NC}"
    echo -e "架构: Linux(**********) -> 本地MySQL -> Docker MySQL"
    
    # 1. 在本地MySQL创建测试数据
    log_info "步骤1: 在本地MySQL创建测试数据..."
    mysql -h localhost -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "
        USE ${test_db};
        CREATE TABLE IF NOT EXISTS ${test_table} (
            id INT PRIMARY KEY AUTO_INCREMENT,
            test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            source VARCHAR(20),
            data VARCHAR(100)
        );
        INSERT INTO ${test_table} (source, data) VALUES 
            ('local', 'Test from Local MySQL'),
            ('local', 'Three Layer Sync Test');
    " 2>/dev/null
    
    # 2. 等待同步到Docker
    log_info "步骤2: 等待数据同步到Docker MySQL..."
    sleep 5
    
    # 3. 检查Docker MySQL是否收到数据
    local docker_count=$(docker exec "$DOCKER_MYSQL_CONTAINER" mysql -uroot -p"$MYSQL_ROOT_PASS" -Ne "
        SELECT COUNT(*) FROM ${test_db}.${test_table}
    " 2>/dev/null || echo "0")
    
    if [[ "$docker_count" -gt 0 ]]; then
        log_success "Docker MySQL成功接收数据: ${docker_count} 条记录"
        
        # 显示数据
        echo -e "\n${CYAN}Docker MySQL中的数据：${NC}"
        docker exec "$DOCKER_MYSQL_CONTAINER" mysql -uroot -p"$MYSQL_ROOT_PASS" -e "
            SELECT * FROM ${test_db}.${test_table}
        " 2>/dev/null
    else
        log_error "Docker MySQL未能同步数据"
    fi
    
    # 4. 测试从Linux到Docker的完整链路（如果可访问Linux服务器）
    if command -v ssh &> /dev/null; then
        log_info "步骤3: 测试完整链路（Linux -> 本地 -> Docker）..."
        
        # 清理测试表
        mysql -h localhost -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "
            DROP TABLE IF EXISTS ${test_db}.${test_table}
        " 2>/dev/null
    fi
    
    echo -e "${CYAN}===================================${NC}\n"
}

# 显示同步状态
show_sync_status() {
    log_info "显示三层同步状态..."
    
    echo -e "\n${CYAN}===== 本地MySQL复制状态 =====${NC}"
    mysql -h localhost -u${LOCAL_MYSQL_USER} -p"${LOCAL_MYSQL_PASS}" -e "
        SHOW SLAVE STATUS\G
    " 2>/dev/null | grep -E "Master_Host|Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master"
    
    echo -e "\n${CYAN}===== Docker MySQL复制状态 =====${NC}"
    docker exec "$DOCKER_MYSQL_CONTAINER" mysql -uroot -p"$MYSQL_ROOT_PASS" -e "
        SHOW SLAVE STATUS\G
    " 2>/dev/null | grep -E "Master_Host|Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master"
}

# 生成配置文件
generate_config_files() {
    log_info "生成Docker MySQL配置文件..."
    
    # 创建配置目录
    mkdir -p "$PROJECT_DIR/config/mysql"
    
    # 生成Docker MySQL配置
    cat > "$PROJECT_DIR/config/mysql/docker-mysql.cnf" << EOF
[mysqld]
# 基本配置
server-id = 3
log-bin = mysql-bin
binlog-format = ROW
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 复制配置
relay-log = relay-bin
relay-log-recovery = ON
slave-skip-errors = 1062,1032
slave-net-timeout = 60

# 性能优化
innodb_buffer_pool_size = 512M
innodb_log_file_size = 128M
max_connections = 200

# 认证
default-authentication-plugin = mysql_native_password

[client]
default-character-set = utf8mb4
EOF
    
    # 生成docker-compose覆盖文件
    cat > "$PROJECT_DIR/docker-compose.three-layer.yml" << EOF
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ${DOCKER_MYSQL_CONTAINER}
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASS}
    ports:
      - "${DOCKER_MYSQL_PORT}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./config/mysql/docker-mysql.cnf:/etc/mysql/conf.d/custom.cnf
      - ./init-scripts:/docker-entrypoint-initdb.d
    command: >
      --default-authentication-plugin=mysql_native_password
      --server-id=3
      --log-bin=mysql-bin
      --binlog-format=ROW
      --binlog-do-db=overdue_debt_db
      --binlog-do-db=user_system
      --binlog-do-db=kingdee
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  mysql_data:
    driver: local
EOF
    
    log_success "配置文件已生成"
}

# 清理环境
cleanup_environment() {
    log_warning "清理Docker环境..."
    
    # 停止容器
    docker stop "$DOCKER_MYSQL_CONTAINER" 2>/dev/null || true
    
    # 删除容器
    docker rm "$DOCKER_MYSQL_CONTAINER" 2>/dev/null || true
    
    # 删除数据卷（谨慎操作）
    read -p "是否删除数据卷？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume rm mysql_data 2>/dev/null || true
        log_success "环境清理完成"
    fi
}

# 主菜单
show_menu() {
    echo -e "\n${BLUE}===== Docker环境管理工具 =====${NC}"
    echo "1. 检查Docker状态"
    echo "2. 启动MySQL容器"
    echo "3. 配置Docker复制"
    echo "4. 测试三层同步"
    echo "5. 显示同步状态"
    echo "6. 生成配置文件"
    echo "7. 完整设置（1-5全部）"
    echo "8. 清理环境"
    echo "0. 退出"
    echo -n "请选择操作 [0-8]: "
}

# 主函数
main() {
    cd "$PROJECT_DIR"
    
    # 如果提供了参数，直接执行
    if [[ $# -gt 0 ]]; then
        case "$1" in
            "start")
                check_docker_status && start_mysql_container
                ;;
            "config")
                configure_docker_replication
                ;;
            "test")
                test_three_layer_sync
                ;;
            "status")
                show_sync_status
                ;;
            "setup")
                check_docker_status && \
                start_mysql_container && \
                init_docker_databases && \
                configure_docker_replication && \
                test_three_layer_sync && \
                show_sync_status
                ;;
            "clean")
                cleanup_environment
                ;;
            *)
                echo "用法: $0 [start|config|test|status|setup|clean]"
                exit 1
                ;;
        esac
        exit 0
    fi
    
    # 交互式菜单
    while true; do
        show_menu
        read choice
        
        case $choice in
            1)
                check_docker_status
                ;;
            2)
                start_mysql_container && init_docker_databases
                ;;
            3)
                configure_docker_replication
                ;;
            4)
                test_three_layer_sync
                ;;
            5)
                show_sync_status
                ;;
            6)
                generate_config_files
                ;;
            7)
                check_docker_status && \
                start_mysql_container && \
                init_docker_databases && \
                configure_docker_replication && \
                test_three_layer_sync && \
                show_sync_status
                ;;
            8)
                cleanup_environment
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "无效选择"
                ;;
        esac
    done
}

# 执行主函数
main "$@"