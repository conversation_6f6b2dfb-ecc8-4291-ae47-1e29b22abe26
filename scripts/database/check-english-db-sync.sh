#!/bin/bash

# 检查英文数据库双向同步状态的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_USER="root"
DB_PASSWORD="Zlb&198838"
ENGLISH_DB="overdue_debt_db"
CHINESE_DB="overdue_debt_db"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查数据库是否存在
check_database_exists() {
    local db_name="$1"
    local exists=$(mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$db_name';" --skip-column-names --batch 2>/dev/null | wc -l)
    echo "$exists"
}

# 检查复制状态
check_replication_status() {
    log_info "检查MySQL复制状态..."
    
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        SELECT '=== Master状态 ===' as info;
        SHOW MASTER STATUS;
        
        SELECT '=== Slave状态 ===' as info;
        SHOW SLAVE STATUS\G
    " 2>/dev/null || log_warning "无法获取复制状态，可能未配置复制"
}

# 检查binlog配置
check_binlog_config() {
    log_info "检查binlog配置..."
    
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        SELECT '=== binlog状态 ===' as info;
        SHOW VARIABLES LIKE 'log_bin%';
        
        SELECT '=== binlog文件 ===' as info;
        SHOW BINARY LOGS;
        
        SELECT '=== server_id ===' as info;
        SHOW VARIABLES LIKE 'server_id';
    " 2>/dev/null
}

# 检查数据库同步状态
check_database_sync() {
    local source_db="$1"
    local target_db="$2"
    
    log_info "检查数据库同步状态: $source_db -> $target_db"
    
    # 检查表数量
    local source_tables=$(mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = '$source_db' AND TABLE_TYPE = 'BASE TABLE';
    " --skip-column-names --batch 2>/dev/null)
    
    local target_tables=$(mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = '$target_db' AND TABLE_TYPE = 'BASE TABLE';
    " --skip-column-names --batch 2>/dev/null)
    
    echo "源数据库 $source_db 表数量: $source_tables"
    echo "目标数据库 $target_db 表数量: $target_tables"
    
    if [ "$source_tables" -eq "$target_tables" ]; then
        log_success "表数量一致"
    else
        log_warning "表数量不一致"
    fi
    
    # 检查数据行数
    mysql -u"$DB_USER" -p"$DB_PASSWORD" --skip-ssl -e "
        SELECT 
            '$source_db' as database_name,
            TABLE_NAME,
            TABLE_ROWS
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$source_db' AND TABLE_TYPE = 'BASE TABLE'
        UNION ALL
        SELECT 
            '$target_db' as database_name,
            TABLE_NAME,
            TABLE_ROWS
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$target_db' AND TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME, database_name;
    " 2>/dev/null
}

# 主函数
main() {
    log_info "开始检查英文数据库双向同步状态"
    
    # 检查数据库是否存在
    local chinese_exists=$(check_database_exists "$CHINESE_DB")
    local english_exists=$(check_database_exists "$ENGLISH_DB")
    
    echo ""
    log_info "=== 数据库存在性检查 ==="
    if [ "$chinese_exists" -gt 0 ]; then
        log_success "中文数据库 '$CHINESE_DB' 存在"
    else
        log_error "中文数据库 '$CHINESE_DB' 不存在"
    fi
    
    if [ "$english_exists" -gt 0 ]; then
        log_success "英文数据库 '$ENGLISH_DB' 存在"
    else
        log_error "英文数据库 '$ENGLISH_DB' 不存在"
        exit 1
    fi
    
    echo ""
    log_info "=== MySQL复制配置检查 ==="
    check_replication_status
    
    echo ""
    log_info "=== binlog配置检查 ==="
    check_binlog_config
    
    echo ""
    log_info "=== 数据库同步状态检查 ==="
    if [ "$chinese_exists" -gt 0 ] && [ "$english_exists" -gt 0 ]; then
        check_database_sync "$CHINESE_DB" "$ENGLISH_DB"
    fi
    
    echo ""
    log_info "=== Docker容器状态检查 ==="
    if command -v docker >/dev/null 2>&1; then
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -i mysql; then
            log_success "发现运行中的MySQL Docker容器"
        else
            log_warning "未发现运行中的MySQL Docker容器"
        fi
    else
        log_warning "Docker命令不可用"
    fi
    
    echo ""
    log_info "检查完成"
}

# 执行主函数
main "$@"