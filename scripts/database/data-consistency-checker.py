#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据一致性检查工具
深度分析和验证多数据源间的数据一致性

功能：
1. 表结构一致性检查
2. 数据行数统计对比
3. 数据内容校验和对比
4. 字段级数据类型验证
5. 外键约束一致性检查
6. 索引结构对比
7. 生成详细HTML报告

作者：SuperClaude
日期：2025-07-08
"""

import sys
import os
import json
import hashlib
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict

try:
    import mysql.connector
    from mysql.connector import Error
except ImportError:
    print("错误：需要安装mysql-connector-python")
    print("执行：pip install mysql-connector-python")
    sys.exit(1)

# 配置类
@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str
    port: int
    user: str
    password: str
    name: str
    alias: str

@dataclass
class TableInfo:
    """表信息"""
    name: str
    row_count: int
    checksum: Optional[str]
    columns: List[Dict[str, Any]]
    indexes: List[Dict[str, Any]]
    foreign_keys: List[Dict[str, Any]]
    engine: str
    charset: str
    collation: str

@dataclass
class ConsistencyResult:
    """一致性检查结果"""
    table_name: str
    status: str  # MATCH, MISMATCH, ERROR, MISSING
    source_count: int
    target_count: int
    source_checksum: Optional[str]
    target_checksum: Optional[str]
    differences: List[str]
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL

class DataConsistencyChecker:
    """数据一致性检查器"""
    
    def __init__(self, config_file: str = None):
        self.databases = {}
        self.connections = {}
        self.results = []
        self.report_data = {}
        
        # 默认配置
        self.default_configs = {
            'linux_server': DatabaseConfig(
                host='**********',
                port=3306,
                user='root',
                password='Zlb&198838',
                name='overdue_debt_db',
                alias='Linux服务器'
            ),
            'local_mysql': DatabaseConfig(
                host='localhost',
                port=3306,
                user='root',
                password='Zlb&198838',
                name='overdue_debt_db',
                alias='本地MySQL'
            ),
            'docker_mysql': DatabaseConfig(
                host='localhost',
                port=3307,
                user='root',
                password='Zlb&198838',
                name='overdue_debt_db',
                alias='Docker MySQL'
            )
        }
        
        # 如果提供了配置文件，加载它
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
        else:
            self.databases = self.default_configs
    
    def load_config(self, config_file: str):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                for key, db_config in config.items():
                    self.databases[key] = DatabaseConfig(**db_config)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            print("使用默认配置")
            self.databases = self.default_configs
    
    def connect_database(self, db_key: str) -> Optional[mysql.connector.MySQLConnection]:
        """连接数据库"""
        if db_key in self.connections:
            return self.connections[db_key]
        
        try:
            config = self.databases[db_key]
            connection = mysql.connector.connect(
                host=config.host,
                port=config.port,
                user=config.user,
                password=config.password,
                database=config.name,
                charset='utf8mb4',
                autocommit=True
            )
            self.connections[db_key] = connection
            print(f"✅ 连接成功: {config.alias} ({config.host}:{config.port})")
            return connection
        except Error as e:
            print(f"❌ 连接失败: {self.databases[db_key].alias} - {e}")
            return None
    
    def get_table_info(self, db_key: str, table_name: str) -> Optional[TableInfo]:
        """获取表信息"""
        conn = self.connect_database(db_key)
        if not conn:
            return None
        
        try:
            cursor = conn.cursor(dictionary=True)
            
            # 获取行数
            cursor.execute(f"SELECT COUNT(*) as count FROM `{table_name}`")
            row_count = cursor.fetchone()['count']
            
            # 获取校验和
            try:
                cursor.execute(f"CHECKSUM TABLE `{table_name}`")
                checksum_result = cursor.fetchone()
                checksum = str(checksum_result['Checksum']) if checksum_result['Checksum'] else None
            except:
                checksum = None
            
            # 获取列信息
            cursor.execute(f"DESCRIBE `{table_name}`")
            columns = cursor.fetchall()
            
            # 获取索引信息
            cursor.execute(f"SHOW INDEX FROM `{table_name}`")
            indexes = cursor.fetchall()
            
            # 获取外键信息
            cursor.execute(f"""
                SELECT 
                    CONSTRAINT_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = '{table_name}' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            """)
            foreign_keys = cursor.fetchall()
            
            # 获取表状态信息
            cursor.execute(f"SHOW TABLE STATUS LIKE '{table_name}'")
            table_status = cursor.fetchone()
            
            return TableInfo(
                name=table_name,
                row_count=row_count,
                checksum=checksum,
                columns=columns,
                indexes=indexes,
                foreign_keys=foreign_keys,
                engine=table_status.get('Engine', 'Unknown'),
                charset=table_status.get('Collation', 'Unknown').split('_')[0] if table_status.get('Collation') else 'Unknown',
                collation=table_status.get('Collation', 'Unknown')
            )
            
        except Error as e:
            print(f"❌ 获取表信息失败 {table_name}: {e}")
            return None
        finally:
            cursor.close()
    
    def get_all_tables(self, db_key: str) -> List[str]:
        """获取所有表名"""
        conn = self.connect_database(db_key)
        if not conn:
            return []
        
        try:
            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            return sorted(tables)
        except Error as e:
            print(f"❌ 获取表列表失败: {e}")
            return []
        finally:
            cursor.close()
    
    def compare_table_structure(self, source_table: TableInfo, target_table: TableInfo) -> List[str]:
        """比较表结构"""
        differences = []
        
        # 比较列数量
        if len(source_table.columns) != len(target_table.columns):
            differences.append(f"列数量不同: {len(source_table.columns)} vs {len(target_table.columns)}")
        
        # 比较列定义
        source_cols = {col['Field']: col for col in source_table.columns}
        target_cols = {col['Field']: col for col in target_table.columns}
        
        for col_name, col_info in source_cols.items():
            if col_name not in target_cols:
                differences.append(f"目标表缺少列: {col_name}")
            else:
                target_col = target_cols[col_name]
                if col_info['Type'] != target_col['Type']:
                    differences.append(f"列类型不同 {col_name}: {col_info['Type']} vs {target_col['Type']}")
                if col_info['Null'] != target_col['Null']:
                    differences.append(f"空值约束不同 {col_name}: {col_info['Null']} vs {target_col['Null']}")
                if col_info['Default'] != target_col['Default']:
                    differences.append(f"默认值不同 {col_name}: {col_info['Default']} vs {target_col['Default']}")
        
        for col_name in target_cols:
            if col_name not in source_cols:
                differences.append(f"源表缺少列: {col_name}")
        
        # 比较引擎和字符集
        if source_table.engine != target_table.engine:
            differences.append(f"存储引擎不同: {source_table.engine} vs {target_table.engine}")
        
        if source_table.charset != target_table.charset:
            differences.append(f"字符集不同: {source_table.charset} vs {target_table.charset}")
        
        return differences
    
    def check_table_consistency(self, table_name: str, source_db: str, target_db: str) -> ConsistencyResult:
        """检查单个表的一致性"""
        print(f"🔍 检查表: {table_name}")
        
        source_table = self.get_table_info(source_db, table_name)
        target_table = self.get_table_info(target_db, table_name)
        
        # 确定严重程度
        severity = "LOW"
        status = "MATCH"
        differences = []
        
        if not source_table:
            status = "ERROR"
            severity = "CRITICAL"
            differences.append(f"无法获取源表信息")
            return ConsistencyResult(
                table_name=table_name,
                status=status,
                source_count=0,
                target_count=0,
                source_checksum=None,
                target_checksum=None,
                differences=differences,
                severity=severity
            )
        
        if not target_table:
            status = "MISSING"
            severity = "CRITICAL"
            differences.append("目标表不存在")
            return ConsistencyResult(
                table_name=table_name,
                status=status,
                source_count=source_table.row_count,
                target_count=0,
                source_checksum=source_table.checksum,
                target_checksum=None,
                differences=differences,
                severity=severity
            )
        
        # 比较行数
        row_diff = abs(source_table.row_count - target_table.row_count)
        if row_diff > 0:
            status = "MISMATCH"
            differences.append(f"行数不同: {source_table.row_count} vs {target_table.row_count}")
            if row_diff > 100:
                severity = "HIGH"
            elif row_diff > 10:
                severity = "MEDIUM"
        
        # 比较校验和
        if source_table.checksum and target_table.checksum:
            if source_table.checksum != target_table.checksum:
                status = "MISMATCH"
                differences.append(f"数据校验和不同")
                severity = max(severity, "MEDIUM")
        
        # 比较表结构
        structure_diffs = self.compare_table_structure(source_table, target_table)
        if structure_diffs:
            status = "MISMATCH"
            differences.extend(structure_diffs)
            severity = "HIGH"  # 结构差异通常很严重
        
        return ConsistencyResult(
            table_name=table_name,
            status=status,
            source_count=source_table.row_count,
            target_count=target_table.row_count,
            source_checksum=source_table.checksum,
            target_checksum=target_table.checksum,
            differences=differences,
            severity=severity
        )
    
    def run_consistency_check(self, source_db: str, target_db: str, tables: List[str] = None) -> List[ConsistencyResult]:
        """运行一致性检查"""
        print(f"\n🚀 开始一致性检查")
        print(f"源数据库: {self.databases[source_db].alias}")
        print(f"目标数据库: {self.databases[target_db].alias}")
        print("=" * 50)
        
        # 获取要检查的表
        if not tables:
            tables = self.get_all_tables(source_db)
            if not tables:
                print("❌ 无法获取表列表")
                return []
        
        results = []
        for table in tables:
            result = self.check_table_consistency(table, source_db, target_db)
            results.append(result)
            
            # 显示检查结果
            status_icon = {
                'MATCH': '✅',
                'MISMATCH': '⚠️',
                'MISSING': '❌',
                'ERROR': '💥'
            }
            print(f"  {status_icon.get(result.status, '❓')} {table}: {result.status}")
            if result.differences:
                for diff in result.differences[:3]:  # 只显示前3个差异
                    print(f"    - {diff}")
        
        return results
    
    def generate_html_report(self, results: List[ConsistencyResult], output_file: str):
        """生成HTML报告"""
        total_tables = len(results)
        match_count = sum(1 for r in results if r.status == 'MATCH')
        mismatch_count = sum(1 for r in results if r.status == 'MISMATCH')
        missing_count = sum(1 for r in results if r.status == 'MISSING')
        error_count = sum(1 for r in results if r.status == 'ERROR')
        
        # 统计严重程度
        severity_counts = defaultdict(int)
        for result in results:
            severity_counts[result.severity] += 1
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据一致性检查报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }}
        .summary-card.match {{ background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }}
        .summary-card.mismatch {{ background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }}
        .summary-card.missing {{ background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }}
        .summary-card.error {{ background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%); }}
        
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 2em;
        }}
        .summary-card p {{
            margin: 0;
            opacity: 0.9;
        }}
        
        .filters {{
            margin: 20px 0;
            text-align: center;
        }}
        .filter-btn {{
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }}
        .filter-btn:hover {{
            background: #1976D2;
        }}
        .filter-btn.active {{
            background: #1565C0;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        
        .status-match {{ color: #4CAF50; font-weight: bold; }}
        .status-mismatch {{ color: #ff9800; font-weight: bold; }}
        .status-missing {{ color: #f44336; font-weight: bold; }}
        .status-error {{ color: #9c27b0; font-weight: bold; }}
        
        .severity-low {{ background-color: #e8f5e8; }}
        .severity-medium {{ background-color: #fff3e0; }}
        .severity-high {{ background-color: #ffebee; }}
        .severity-critical {{ background-color: #fce4ec; }}
        
        .differences {{
            max-width: 300px;
            max-height: 100px;
            overflow-y: auto;
            font-size: 0.9em;
        }}
        .differences ul {{
            margin: 0;
            padding-left: 20px;
        }}
        
        .metadata {{
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }}
        
        @media (max-width: 768px) {{
            .summary {{
                grid-template-columns: 1fr;
            }}
            table {{
                font-size: 0.9em;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 数据一致性检查报告</h1>
        
        <div class="summary">
            <div class="summary-card match">
                <h3>{match_count}</h3>
                <p>完全匹配</p>
            </div>
            <div class="summary-card mismatch">
                <h3>{mismatch_count}</h3>
                <p>存在差异</p>
            </div>
            <div class="summary-card missing">
                <h3>{missing_count}</h3>
                <p>表缺失</p>
            </div>
            <div class="summary-card error">
                <h3>{error_count}</h3>
                <p>检查错误</p>
            </div>
        </div>
        
        <div class="filters">
            <button class="filter-btn active" onclick="filterTable('all')">全部</button>
            <button class="filter-btn" onclick="filterTable('MATCH')">匹配</button>
            <button class="filter-btn" onclick="filterTable('MISMATCH')">差异</button>
            <button class="filter-btn" onclick="filterTable('MISSING')">缺失</button>
            <button class="filter-btn" onclick="filterTable('ERROR')">错误</button>
        </div>
        
        <table id="resultsTable">
            <thead>
                <tr>
                    <th>表名</th>
                    <th>状态</th>
                    <th>严重程度</th>
                    <th>源表行数</th>
                    <th>目标表行数</th>
                    <th>行数差异</th>
                    <th>差异详情</th>
                </tr>
            </thead>
            <tbody>
"""
        
        for result in results:
            row_diff = abs(result.source_count - result.target_count)
            differences_html = ""
            if result.differences:
                differences_html = "<ul>" + "".join(f"<li>{diff}</li>" for diff in result.differences[:5]) + "</ul>"
                if len(result.differences) > 5:
                    differences_html += f"<small>... 还有 {len(result.differences) - 5} 个差异</small>"
            
            html_content += f"""
                <tr class="table-row severity-{result.severity.lower()}" data-status="{result.status}">
                    <td><strong>{result.table_name}</strong></td>
                    <td class="status-{result.status.lower()}">{result.status}</td>
                    <td>{result.severity}</td>
                    <td>{result.source_count:,}</td>
                    <td>{result.target_count:,}</td>
                    <td>{row_diff:,}</td>
                    <td class="differences">{differences_html}</td>
                </tr>
            """
        
        html_content += f"""
            </tbody>
        </table>
        
        <div class="metadata">
            <h3>📋 检查元数据</h3>
            <p><strong>生成时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>总表数:</strong> {total_tables}</p>
            <p><strong>一致性比例:</strong> {(match_count/total_tables*100):.1f}%</p>
            <p><strong>严重程度分布:</strong> 
                严重: {severity_counts['CRITICAL']}, 
                高: {severity_counts['HIGH']}, 
                中: {severity_counts['MEDIUM']}, 
                低: {severity_counts['LOW']}
            </p>
        </div>
    </div>
    
    <script>
        function filterTable(status) {{
            const rows = document.querySelectorAll('.table-row');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 过滤行
            rows.forEach(row => {{
                if (status === 'all' || row.dataset.status === status) {{
                    row.style.display = '';
                }} else {{
                    row.style.display = 'none';
                }}
            }});
        }}
    </script>
</body>
</html>
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n📄 HTML报告已生成: {output_file}")
    
    def close_connections(self):
        """关闭所有数据库连接"""
        for conn in self.connections.values():
            if conn.is_connected():
                conn.close()
    
    def run_full_check(self, comparisons: List[Tuple[str, str]]):
        """运行完整检查"""
        all_results = []
        
        for source_db, target_db in comparisons:
            print(f"\n🔄 比较 {self.databases[source_db].alias} -> {self.databases[target_db].alias}")
            results = self.run_consistency_check(source_db, target_db)
            all_results.extend(results)
            
            # 生成单独的报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"/tmp/consistency_report_{source_db}_to_{target_db}_{timestamp}.html"
            self.generate_html_report(results, report_file)
        
        # 生成汇总报告
        if len(comparisons) > 1:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_report = f"/tmp/consistency_summary_{timestamp}.html"
            self.generate_html_report(all_results, summary_report)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据一致性检查工具')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--source', default='linux_server', help='源数据库')
    parser.add_argument('--target', default='local_mysql', help='目标数据库')
    parser.add_argument('--tables', nargs='*', help='指定要检查的表')
    parser.add_argument('--compare-all', action='store_true', help='比较所有数据库组合')
    
    args = parser.parse_args()
    
    # 创建检查器
    checker = DataConsistencyChecker(args.config)
    
    try:
        if args.compare_all:
            # 比较所有组合
            db_keys = list(checker.databases.keys())
            comparisons = [
                ('linux_server', 'local_mysql'),
                ('local_mysql', 'docker_mysql'),
                ('linux_server', 'docker_mysql')
            ]
            # 只保留存在的数据库
            valid_comparisons = [
                (src, tgt) for src, tgt in comparisons 
                if src in db_keys and tgt in db_keys
            ]
            checker.run_full_check(valid_comparisons)
        else:
            # 单个比较
            results = checker.run_consistency_check(args.source, args.target, args.tables)
            
            # 生成报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"/tmp/consistency_report_{timestamp}.html"
            checker.generate_html_report(results, report_file)
            
            # 打印摘要
            print(f"\n📊 检查摘要:")
            print(f"总表数: {len(results)}")
            print(f"匹配: {sum(1 for r in results if r.status == 'MATCH')}")
            print(f"差异: {sum(1 for r in results if r.status == 'MISMATCH')}")
            print(f"缺失: {sum(1 for r in results if r.status == 'MISSING')}")
            print(f"错误: {sum(1 for r in results if r.status == 'ERROR')}")
    
    finally:
        checker.close_connections()


if __name__ == "__main__":
    main()