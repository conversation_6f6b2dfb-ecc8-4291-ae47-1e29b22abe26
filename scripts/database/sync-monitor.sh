#!/bin/bash

# MySQL双向同步监控脚本
# 监控复制状态、数据一致性、性能指标

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

# 需要监控的数据库
DATABASES=("overdue_debt_db" "kingdee" "user_system")

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] INFO${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] SUCCESS${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] WARNING${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR${NC} $1"
}

# 函数：检查复制状态
check_replication_status() {
    local server_type=$1
    local status_ok=true
    
    echo -e "${CYAN}=== ${server_type}服务器复制状态 ===${NC}"
    
    if [ "$server_type" = "本地" ]; then
        local slave_status=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SHOW SLAVE STATUS\G" 2>/dev/null)
    else
        local slave_status=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SHOW SLAVE STATUS\G'" 2>/dev/null)
    fi
    
    if [ -z "$slave_status" ]; then
        log_error "无法获取${server_type}服务器复制状态"
        return 1
    fi
    
    # 解析关键指标
    local io_running=$(echo "$slave_status" | grep "Slave_IO_Running:" | awk '{print $2}')
    local sql_running=$(echo "$slave_status" | grep "Slave_SQL_Running:" | awk '{print $2}')
    local last_error=$(echo "$slave_status" | grep "Last_Error:" | cut -d':' -f2- | xargs)
    local seconds_behind=$(echo "$slave_status" | grep "Seconds_Behind_Master:" | awk '{print $2}')
    
    echo "  IO线程状态: $io_running"
    echo "  SQL线程状态: $sql_running"
    echo "  延迟时间: ${seconds_behind}秒"
    
    if [ "$io_running" != "Yes" ] || [ "$sql_running" != "Yes" ]; then
        log_error "复制线程异常"
        if [ -n "$last_error" ] && [ "$last_error" != "" ]; then
            echo "  错误信息: $last_error"
        fi
        status_ok=false
    else
        if [ "$seconds_behind" = "NULL" ] || [ "$seconds_behind" -gt 30 ]; then
            log_warning "复制延迟较高: ${seconds_behind}秒"
        else
            log_success "复制状态正常"
        fi
    fi
    
    return $([ "$status_ok" = true ] && echo 0 || echo 1)
}

# 函数：检查数据一致性
check_data_consistency() {
    echo ""
    echo -e "${CYAN}=== 数据一致性检查 ===${NC}"
    
    local all_consistent=true
    
    for db_name in "${DATABASES[@]}"; do
        echo ""
        echo -e "${BLUE}数据库: $db_name${NC}"
        
        # 获取表数量
        local local_table_count=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
            -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$db_name';" -s -N 2>/dev/null || echo "0")
        
        local linux_table_count=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            -e \"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$db_name';\" -s -N" 2>/dev/null || echo "0")
        
        echo "  表数量: 本地($local_table_count) vs Linux($linux_table_count)"
        
        if [ "$local_table_count" -eq "$linux_table_count" ]; then
            echo -e "  ${GREEN}✅ 表结构一致${NC}"
        else
            echo -e "  ${RED}❌ 表结构不一致${NC}"
            all_consistent=false
        fi
        
        # 特殊检查overdue_debt_db的数据
        if [ "$db_name" = "overdue_debt_db" ] && [ "$local_table_count" -gt 0 ]; then
            local local_data_count=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
                -D"$db_name" --default-character-set=utf8mb4 \
                -e "SELECT COUNT(*) FROM 新增表;" -s -N 2>/dev/null || echo "0")
            
            local linux_data_count=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
                -D'$db_name' --default-character-set=utf8mb4 \
                -e 'SELECT COUNT(*) FROM 新增表;' -s -N" 2>/dev/null || echo "0")
            
            echo "  新增表记录: 本地($local_data_count) vs Linux($linux_data_count)"
            
            if [ "$local_data_count" -eq "$linux_data_count" ]; then
                echo -e "  ${GREEN}✅ 数据记录一致${NC}"
            else
                echo -e "  ${RED}❌ 数据记录不一致${NC}"
                all_consistent=false
            fi
        fi
    done
    
    echo ""
    if [ "$all_consistent" = true ]; then
        log_success "🎉 所有数据库一致性检查通过"
    else
        log_error "⚠️ 数据一致性检查发现问题"
    fi
    
    return $([ "$all_consistent" = true ] && echo 0 || echo 1)
}

# 函数：检查复制性能
check_replication_performance() {
    echo ""
    echo -e "${CYAN}=== 复制性能分析 ===${NC}"
    
    # 检查本地服务器性能
    echo ""
    echo -e "${BLUE}本地MySQL性能指标:${NC}"
    mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "
    SELECT 
        'Connections' as Metric, Variable_value as Value FROM performance_schema.global_status WHERE Variable_name = 'Connections'
    UNION ALL
    SELECT 
        'Queries' as Metric, Variable_value as Value FROM performance_schema.global_status WHERE Variable_name = 'Queries'
    UNION ALL
    SELECT 
        'Bytes_sent' as Metric, ROUND(Variable_value/1024/1024, 2) as 'Value(MB)' FROM performance_schema.global_status WHERE Variable_name = 'Bytes_sent'
    UNION ALL
    SELECT 
        'Bytes_received' as Metric, ROUND(Variable_value/1024/1024, 2) as 'Value(MB)' FROM performance_schema.global_status WHERE Variable_name = 'Bytes_received';
    " 2>/dev/null || log_warning "无法获取本地性能指标"
    
    # 检查Linux服务器性能
    echo ""
    echo -e "${BLUE}Linux MySQL性能指标:${NC}"
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e \"
    SELECT 
        'Connections' as Metric, Variable_value as Value FROM performance_schema.global_status WHERE Variable_name = 'Connections'
    UNION ALL
    SELECT 
        'Queries' as Metric, Variable_value as Value FROM performance_schema.global_status WHERE Variable_name = 'Queries'
    UNION ALL
    SELECT 
        'Bytes_sent' as Metric, ROUND(Variable_value/1024/1024, 2) as 'Value(MB)' FROM performance_schema.global_status WHERE Variable_name = 'Bytes_sent'
    UNION ALL
    SELECT 
        'Bytes_received' as Metric, ROUND(Variable_value/1024/1024, 2) as 'Value(MB)' FROM performance_schema.global_status WHERE Variable_name = 'Bytes_received';
    \"" 2>/dev/null || log_warning "无法获取Linux性能指标"
}

# 函数：生成监控报告
generate_report() {
    local report_file="/tmp/mysql_sync_report_$(date +%Y%m%d_%H%M%S).txt"
    
    echo "生成详细监控报告到: $report_file"
    
    {
        echo "MySQL双向同步监控报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        # 复制状态报告
        echo "复制状态检查:"
        check_replication_status "本地" 2>&1
        echo ""
        check_replication_status "Linux" 2>&1
        echo ""
        
        # 数据一致性报告
        echo "数据一致性检查:"
        check_data_consistency 2>&1
        echo ""
        
        # 性能分析报告
        echo "性能分析:"
        check_replication_performance 2>&1
        
    } > "$report_file"
    
    log_success "监控报告已生成: $report_file"
}

# 函数：修复复制问题
repair_replication() {
    local server_type=$1
    
    log_info "尝试修复${server_type}服务器复制问题..."
    
    if [ "$server_type" = "本地" ]; then
        mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl << 'EOF'
STOP SLAVE;
SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1;
START SLAVE;
EOF
    else
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' << 'EOF'
STOP SLAVE;
SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1;
START SLAVE;
EOF"
    fi
    
    sleep 5
    
    if check_replication_status "$server_type" > /dev/null 2>&1; then
        log_success "${server_type}服务器复制问题已修复"
    else
        log_error "${server_type}服务器复制问题修复失败"
    fi
}

# 函数：实时监控模式
real_time_monitor() {
    log_info "进入实时监控模式（按Ctrl+C退出）..."
    
    while true; do
        clear
        echo -e "${PURPLE}📊 MySQL双向同步实时监控 - $(date)${NC}"
        echo ""
        
        # 检查复制状态
        local local_status=true
        local linux_status=true
        
        check_replication_status "本地" || local_status=false
        echo ""
        check_replication_status "Linux" || linux_status=false
        
        # 显示整体状态
        echo ""
        echo -e "${CYAN}=== 整体状态 ===${NC}"
        if [ "$local_status" = true ] && [ "$linux_status" = true ]; then
            echo -e "${GREEN}✅ 双向同步正常运行${NC}"
        else
            echo -e "${RED}❌ 双向同步存在问题${NC}"
            
            if [ "$local_status" = false ]; then
                echo "  - 本地复制异常"
            fi
            if [ "$linux_status" = false ]; then
                echo "  - Linux复制异常"
            fi
        fi
        
        echo ""
        echo "下次检查: $(date -d '+30 seconds')"
        sleep 30
    done
}

# 主函数
main() {
    local command=${1:-"status"}
    
    case "$command" in
        "status"|"check")
            echo -e "${PURPLE}🔍 MySQL双向同步状态检查${NC}"
            echo ""
            
            check_replication_status "本地"
            echo ""
            check_replication_status "Linux"
            check_data_consistency
            ;;
            
        "performance"|"perf")
            echo -e "${PURPLE}📊 MySQL复制性能分析${NC}"
            check_replication_performance
            ;;
            
        "report")
            echo -e "${PURPLE}📋 生成监控报告${NC}"
            generate_report
            ;;
            
        "repair")
            local server=${2:-"both"}
            echo -e "${PURPLE}🔧 修复复制问题${NC}"
            
            if [ "$server" = "local" ] || [ "$server" = "both" ]; then
                repair_replication "本地"
            fi
            
            if [ "$server" = "linux" ] || [ "$server" = "both" ]; then
                repair_replication "Linux"
            fi
            ;;
            
        "monitor"|"watch")
            real_time_monitor
            ;;
            
        "help"|"--help"|"-h")
            echo -e "${CYAN}MySQL双向同步监控脚本使用说明${NC}"
            echo ""
            echo "用法: $0 [命令] [选项]"
            echo ""
            echo "命令:"
            echo "  status    检查复制状态和数据一致性（默认）"
            echo "  perf      分析复制性能"
            echo "  report    生成详细监控报告"
            echo "  repair    修复复制问题 [local|linux|both]"
            echo "  monitor   实时监控模式"
            echo "  help      显示此帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                    # 检查状态"
            echo "  $0 status             # 检查状态"
            echo "  $0 perf               # 性能分析"
            echo "  $0 repair local       # 修复本地复制"
            echo "  $0 monitor            # 实时监控"
            ;;
            
        *)
            log_error "未知命令: $command"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 捕获Ctrl+C信号
trap 'echo -e "\n${YELLOW}监控已停止${NC}"; exit 0' INT

# 执行主函数
main "$@"