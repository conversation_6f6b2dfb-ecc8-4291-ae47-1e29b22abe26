#!/bin/bash

# 测试Docker和本地MySQL之间英文数据库同步的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
DOCKER_DB_USER="root"
DOCKER_DB_PASSWORD="Zlb&198838"
DOCKER_DB_PORT="3306"
ENGLISH_DB="overdue_debt_db"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker容器状态
check_docker_status() {
    log_info "检查Docker容器状态..."
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker命令不可用"
        return 1
    fi
    
    if ! docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "financial-mysql"; then
        log_warning "financial-mysql容器未运行"
        log_info "尝试启动Docker MySQL容器..."
        docker-compose up -d mysql
        sleep 30  # 等待MySQL启动
    else
        log_success "financial-mysql容器正在运行"
    fi
}

# 检查本地MySQL连接
check_local_mysql() {
    log_info "检查本地MySQL连接..."
    
    if mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "本地MySQL连接正常"
        return 0
    else
        log_error "本地MySQL连接失败"
        return 1
    fi
}

# 检查Docker MySQL连接
check_docker_mysql() {
    log_info "检查Docker MySQL连接..."
    
    if docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "Docker MySQL连接正常"
        return 0
    else
        log_error "Docker MySQL连接失败"
        return 1
    fi
}

# 检查英文数据库是否存在
check_english_db_exists() {
    local location="$1"
    
    if [ "$location" = "local" ]; then
        local exists=$(mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$ENGLISH_DB';" --skip-column-names --batch 2>/dev/null | wc -l)
    else
        local exists=$(docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$ENGLISH_DB';" --skip-column-names --batch 2>/dev/null | wc -l)
    fi
    
    if [ "$exists" -gt 0 ]; then
        log_success "$location 英文数据库 '$ENGLISH_DB' 存在"
        return 0
    else
        log_error "$location 英文数据库 '$ENGLISH_DB' 不存在"
        return 1
    fi
}

# 比较数据库表结构
compare_db_structure() {
    log_info "比较本地和Docker中英文数据库的表结构..."
    
    # 获取本地数据库表信息
    local local_tables=$(mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "
        SELECT TABLE_NAME, TABLE_ROWS 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = '$ENGLISH_DB' AND TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME;
    " --skip-column-names --batch 2>/dev/null)
    
    # 获取Docker数据库表信息
    local docker_tables=$(docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "
        SELECT TABLE_NAME, TABLE_ROWS 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = '$ENGLISH_DB' AND TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME;
    " --skip-column-names --batch 2>/dev/null)
    
    echo "=== 本地MySQL表信息 ==="
    echo "$local_tables"
    echo ""
    echo "=== Docker MySQL表信息 ==="
    echo "$docker_tables"
    echo ""
    
    # 比较表数量
    local local_count=$(echo "$local_tables" | wc -l)
    local docker_count=$(echo "$docker_tables" | wc -l)
    
    if [ "$local_count" -eq "$docker_count" ]; then
        log_success "表数量一致：$local_count 个表"
    else
        log_warning "表数量不一致：本地 $local_count 个，Docker $docker_count 个"
    fi
}

# 测试数据同步
test_data_sync() {
    log_info "测试数据同步功能..."
    
    local test_table="docker_sync_test_$(date +%s)"
    
    # 在本地MySQL中创建测试表
    log_info "在本地MySQL中创建测试表..."
    mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "
        USE $ENGLISH_DB;
        CREATE TABLE IF NOT EXISTS $test_table (
            id INT AUTO_INCREMENT PRIMARY KEY,
            source VARCHAR(50),
            test_data VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        INSERT INTO $test_table (source, test_data) VALUES ('local', 'Docker同步测试 - 来自本地MySQL');
    " 2>/dev/null
    
    log_success "本地测试表创建完成：$test_table"
    
    # 等待几秒钟（如果有复制配置）
    sleep 5
    
    # 检查Docker中是否存在该表
    log_info "检查Docker MySQL中是否同步了测试表..."
    if docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "USE $ENGLISH_DB; DESC $test_table;" >/dev/null 2>&1; then
        log_success "Docker中存在同步的测试表"
        
        # 显示同步的数据
        docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "
            USE $ENGLISH_DB; 
            SELECT * FROM $test_table;
        " 2>/dev/null
    else
        log_warning "Docker中未发现同步的测试表（可能未配置实时同步）"
        
        # 手动在Docker中创建测试数据
        log_info "在Docker MySQL中创建测试数据..."
        docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "
            USE $ENGLISH_DB;
            CREATE TABLE IF NOT EXISTS $test_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                source VARCHAR(50),
                test_data VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            INSERT INTO $test_table (source, test_data) VALUES ('docker', 'Docker同步测试 - 来自Docker MySQL');
        " 2>/dev/null
        
        log_info "Docker测试数据创建完成"
    fi
    
    # 清理测试表
    log_info "清理测试表..."
    mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "DROP TABLE IF EXISTS $ENGLISH_DB.$test_table;" 2>/dev/null
    docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "DROP TABLE IF EXISTS $ENGLISH_DB.$test_table;" 2>/dev/null || true
}

# 检查MySQL复制配置
check_replication_config() {
    log_info "检查MySQL复制配置..."
    
    echo "=== 本地MySQL复制配置 ==="
    mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "
        SHOW VARIABLES LIKE 'server_id';
        SHOW VARIABLES LIKE 'log_bin%';
        SHOW MASTER STATUS;
    " 2>/dev/null
    
    echo ""
    echo "=== Docker MySQL复制配置 ==="
    docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "
        SHOW VARIABLES LIKE 'server_id';
        SHOW VARIABLES LIKE 'log_bin%';
        SHOW MASTER STATUS;
    " 2>/dev/null
}

# 生成同步状态报告
generate_sync_report() {
    log_info "生成同步状态报告..."
    
    local report_file="/tmp/docker_local_mysql_sync_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=== Docker和本地MySQL英文数据库同步状态报告 ==="
        echo "报告生成时间: $(date)"
        echo "英文数据库名: $ENGLISH_DB"
        echo ""
        
        echo "=== 环境状态 ==="
        echo "Docker容器状态:"
        docker ps --filter name=financial-mysql --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Docker不可用"
        echo ""
        
        echo "=== 数据库连接状态 ==="
        if mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SELECT 'Local MySQL OK' as status;" 2>/dev/null; then
            echo "本地MySQL: ✅ 连接正常"
        else
            echo "本地MySQL: ❌ 连接失败"
        fi
        
        if docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "SELECT 'Docker MySQL OK' as status;" 2>/dev/null; then
            echo "Docker MySQL: ✅ 连接正常"
        else
            echo "Docker MySQL: ❌ 连接失败"
        fi
        echo ""
        
        echo "=== 英文数据库状态 ==="
        echo "本地MySQL中的 $ENGLISH_DB:"
        mysql -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "
            SELECT COUNT(*) as '表数量' FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = '$ENGLISH_DB' AND TABLE_TYPE = 'BASE TABLE';
        " 2>/dev/null || echo "❌ 获取失败"
        
        echo "Docker MySQL中的 $ENGLISH_DB:"
        docker exec financial-mysql mysql -u"$DOCKER_DB_USER" -p"$DOCKER_DB_PASSWORD" -e "
            SELECT COUNT(*) as '表数量' FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = '$ENGLISH_DB' AND TABLE_TYPE = 'BASE TABLE';
        " 2>/dev/null || echo "❌ 获取失败"
        
    } > "$report_file"
    
    log_success "同步状态报告已生成: $report_file"
    
    # 显示报告内容
    cat "$report_file"
}

# 主函数
main() {
    log_info "开始测试Docker和本地MySQL之间的英文数据库同步"
    
    # 检查Docker状态
    if ! check_docker_status; then
        log_error "Docker环境检查失败"
        exit 1
    fi
    
    # 检查本地MySQL
    if ! check_local_mysql; then
        log_error "本地MySQL检查失败"
        exit 1
    fi
    
    # 检查Docker MySQL
    if ! check_docker_mysql; then
        log_error "Docker MySQL检查失败"
        exit 1
    fi
    
    # 检查英文数据库是否存在
    check_english_db_exists "local"
    check_english_db_exists "docker"
    
    # 比较数据库结构
    compare_db_structure
    
    # 测试数据同步
    test_data_sync
    
    # 检查复制配置
    check_replication_config
    
    # 生成报告
    generate_sync_report
    
    echo ""
    log_success "Docker和本地MySQL英文数据库同步测试完成"
}

# 显示帮助信息
show_help() {
    echo "Docker和本地MySQL英文数据库同步测试脚本"
    echo ""
    echo "此脚本将测试以下功能："
    echo "1. Docker和本地MySQL的连接状态"
    echo "2. 英文数据库的存在性"
    echo "3. 数据库表结构比较"
    echo "4. 数据同步功能测试"
    echo "5. MySQL复制配置检查"
    echo ""
    echo "使用方法:"
    echo "  $0           # 执行测试"
    echo "  $0 --help    # 显示帮助信息"
}

# 解析命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac