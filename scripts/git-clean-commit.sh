#!/bin/bash
# Git安全提交脚本 - 自动化提交流程

# 颜色定义
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${RED}错误：请提供提交信息${NC}"
    echo "使用方法: ./git-clean-commit.sh \"提交信息\""
    echo "示例: ./git-clean-commit.sh \"修复登录问题\""
    exit 1
fi

COMMIT_MSG="$1"

echo -e "${BLUE}📝 Git安全提交流程${NC}"
echo "================================"
echo -e "提交信息: ${GREEN}$COMMIT_MSG${NC}"
echo ""

# ========== 1. 检查工作区状态 ==========
echo -e "${YELLOW}🔍 步骤1: 检查工作区状态...${NC}"

# 检查是否有更改
if [ -z "$(git status --porcelain)" ]; then
    echo -e "${YELLOW}⚠️  没有检测到更改，无需提交${NC}"
    git status
    exit 0
fi

# 显示将要提交的更改
echo "即将提交的更改："
git status --short
echo ""

# ========== 2. 运行pre-commit检查 ==========
echo -e "${YELLOW}🔧 步骤2: 运行代码质量检查...${NC}"

# 检查是否有pre-commit hook
if [ -x ".git/hooks/pre-commit" ]; then
    echo "执行pre-commit检查..."
    if .git/hooks/pre-commit; then
        echo -e "${GREEN}✓ 代码质量检查通过${NC}"
    else
        echo -e "${RED}❌ 代码质量检查失败${NC}"
        echo "请修复上述问题后重新提交"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  未找到pre-commit hook，跳过质量检查${NC}"
fi
echo ""

# ========== 3. 暂存文件 ==========
echo -e "${YELLOW}📦 步骤3: 暂存文件...${NC}"

# 智能添加文件
git add .

# 显示暂存的文件
STAGED_FILES=$(git diff --cached --name-status | wc -l | tr -d ' ')
if [ "$STAGED_FILES" -gt 0 ]; then
    echo "已暂存 $STAGED_FILES 个文件："
    git diff --cached --name-status | head -10
    if [ "$STAGED_FILES" -gt 10 ]; then
        echo "... 还有 $((STAGED_FILES - 10)) 个文件"
    fi
else
    echo -e "${RED}❌ 没有文件被暂存${NC}"
    exit 1
fi
echo ""

# ========== 4. 生成提交信息 ==========
echo -e "${YELLOW}✍️  步骤4: 生成提交信息...${NC}"

# 检查提交信息格式
if [[ ! "$COMMIT_MSG" =~ ^(feat|fix|docs|style|refactor|test|chore|perf|ci|build): ]]; then
    # 智能推断提交类型
    if echo "$COMMIT_MSG" | grep -qi "修复\|fix\|bug\|错误"; then
        COMMIT_TYPE="fix"
    elif echo "$COMMIT_MSG" | grep -qi "添加\|新增\|实现\|feat\|feature\|功能"; then
        COMMIT_TYPE="feat"  
    elif echo "$COMMIT_MSG" | grep -qi "优化\|改进\|重构\|refactor"; then
        COMMIT_TYPE="refactor"
    elif echo "$COMMIT_MSG" | grep -qi "文档\|docs"; then
        COMMIT_TYPE="docs"
    elif echo "$COMMIT_MSG" | grep -qi "测试\|test"; then
        COMMIT_TYPE="test"
    else
        COMMIT_TYPE="chore"
    fi
    
    FORMATTED_MSG="$COMMIT_TYPE: $COMMIT_MSG"
else
    FORMATTED_MSG="$COMMIT_MSG"
fi

# 添加共同作者信息
FULL_COMMIT_MSG="$FORMATTED_MSG

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"

echo "最终提交信息："
echo "$FULL_COMMIT_MSG"
echo ""

# ========== 5. 执行提交 ==========
echo -e "${YELLOW}🚀 步骤5: 执行提交...${NC}"

if git commit -m "$FULL_COMMIT_MSG"; then
    echo -e "${GREEN}✅ 提交成功！${NC}"
    
    # 显示提交信息
    echo ""
    echo -e "${BLUE}📊 提交详情：${NC}"
    git log --oneline -1
    
    # 显示当前状态
    echo ""
    echo -e "${GREEN}当前状态：${NC}"
    git status --short
    
    # 提示推送
    CURRENT_BRANCH=$(git branch --show-current)
    if git ls-remote --heads origin "$CURRENT_BRANCH" | grep -q "$CURRENT_BRANCH"; then
        echo ""
        echo -e "${BLUE}💡 提示：如需推送到远程仓库，使用：${NC}"
        echo "git push origin $CURRENT_BRANCH"
    else
        echo ""
        echo -e "${BLUE}💡 提示：如需推送新分支到远程，使用：${NC}"
        echo "git push -u origin $CURRENT_BRANCH"
    fi
    
else
    echo -e "${RED}❌ 提交失败${NC}"
    exit 1
fi

exit 0