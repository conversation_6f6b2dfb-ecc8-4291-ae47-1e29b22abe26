#!/bin/bash
# Git安全回退脚本 - 确保工作区绝对干净

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${RED}错误：未提供commit ID${NC}"
    echo "使用方法: ./git-clean-reset.sh <commit-id>"
    echo "示例: ./git-clean-reset.sh 7613068"
    exit 1
fi

COMMIT_ID=$1
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"

echo -e "${YELLOW}=== Git安全回退脚本 ===${NC}"
echo -e "目标提交: ${GREEN}$COMMIT_ID${NC}"
echo ""

# 步骤1：检查当前状态
echo -e "${YELLOW}🔍 步骤1: 检查当前状态...${NC}"
git status --short
echo ""

# 步骤2：备份当前更改
echo -e "${YELLOW}💾 步骤2: 备份当前更改...${NC}"
if [ -n "$(git status --porcelain)" ]; then
    git stash push -m "$BACKUP_NAME" --include-untracked
    echo -e "${GREEN}✓ 已创建备份: $BACKUP_NAME${NC}"
else
    echo -e "${GREEN}✓ 工作区已经是干净的${NC}"
fi
echo ""

# 步骤3：清理未跟踪文件
echo -e "${YELLOW}🧹 步骤3: 清理未跟踪文件...${NC}"
UNTRACKED_FILES=$(git ls-files --others --exclude-standard)
if [ -n "$UNTRACKED_FILES" ]; then
    echo "发现未跟踪文件："
    echo "$UNTRACKED_FILES"
    git clean -fd
    echo -e "${GREEN}✓ 已清理未跟踪文件${NC}"
else
    echo -e "${GREEN}✓ 没有未跟踪文件需要清理${NC}"
fi
echo ""

# 步骤4：执行硬重置
echo -e "${YELLOW}⚡ 步骤4: 执行硬重置到: $COMMIT_ID${NC}"
git reset --hard $COMMIT_ID
echo ""

# 步骤5：验证结果
echo -e "${YELLOW}✅ 步骤5: 验证结果${NC}"
echo -e "${GREEN}当前状态：${NC}"
git status --short

echo -e "${GREEN}当前提交：${NC}"
git log --oneline -1

echo ""
echo -e "${GREEN}✨ 回退完成！工作区已完全干净。${NC}"

# 显示stash列表提醒
STASH_COUNT=$(git stash list | wc -l)
if [ $STASH_COUNT -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}📌 提醒：你有 $STASH_COUNT 个stash记录${NC}"
    echo "使用 'git stash list' 查看"
    echo "使用 'git stash pop' 恢复最近的更改"
fi