#!/bin/bash
# 简化版快照初始化脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== FinancialSystem 快照初始化 ===${NC}"

# 创建必要的目录
echo -e "${YELLOW}📁 创建目录结构...${NC}"
mkdir -p test-data/snapshots
mkdir -p test-data/baseline/v1.0
mkdir -p test-data/scenarios

# 检查数据库连接
echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_NAME=${DB_NAME:-financial_system}
DB_USER=${DB_USER:-root}

# 创建示例快照数据
echo -e "${YELLOW}📸 创建初始快照...${NC}"

# 快照1: 债权统计基准
cat > test-data/snapshots/debt_statistics_baseline.json << 'EOF'
{
  "id": "debt_statistics_baseline",
  "timestamp": "2024-07-22T08:55:00",
  "data": [],
  "metadata": {
    "version": "1.0.0",
    "description": "基准债权统计快照",
    "recordCount": 0,
    "createdBy": "init_script",
    "queryParams": {
      "year": 2024,
      "includeProcessed": true
    }
  }
}
EOF

# 快照2: 月度汇总基准
cat > test-data/snapshots/monthly_summary_baseline.json << 'EOF'
{
  "id": "monthly_summary_baseline", 
  "timestamp": "2024-07-22T08:55:00",
  "data": {
    "year": 2024,
    "monthlySummary": {
      "january": 0,
      "february": 0,
      "march": 0,
      "april": 0,
      "may": 0,
      "june": 0,
      "july": 0,
      "august": 0,
      "september": 0,
      "october": 0,
      "november": 0,
      "december": 0
    },
    "totalAmount": 0
  },
  "metadata": {
    "version": "1.0.0",
    "description": "基准月度汇总快照",
    "createdBy": "init_script"
  }
}
EOF

# 快照3: 一致性检查基准
cat > test-data/snapshots/consistency_check_baseline.json << 'EOF'
{
  "id": "consistency_check_baseline",
  "timestamp": "2024-07-22T08:55:00", 
  "data": {
    "tableCounts": {
      "overdue_debt_add": 0,
      "overdue_debt_decrease": 0,
      "impairment_reserve": 0,
      "litigation_claim": 0,
      "non_litigation_claim": 0
    },
    "totalAmounts": {
      "debt_amount": 0,
      "disposal_amount": 0,
      "impairment_amount": 0
    },
    "consistencyScore": 100
  },
  "metadata": {
    "version": "1.0.0",
    "description": "基准一致性检查快照",
    "createdBy": "init_script"
  }
}
EOF

# 快照4: 跨表验证基准
cat > test-data/snapshots/cross_table_validation_baseline.json << 'EOF'
{
  "id": "cross_table_validation_baseline",
  "timestamp": "2024-07-22T08:55:00",
  "data": [],
  "metadata": {
    "version": "1.0.0", 
    "description": "基准跨表验证快照",
    "recordCount": 0,
    "createdBy": "init_script",
    "validationRules": [
      "debt_amount_consistency",
      "disposal_amount_validation", 
      "impairment_calculation_check",
      "litigation_status_consistency"
    ]
  }
}
EOF

# 创建快照元数据索引
cat > test-data/snapshots/index.json << 'EOF'
{
  "snapshots": [
    {
      "id": "debt_statistics_baseline",
      "file": "debt_statistics_baseline.json",
      "type": "query_result",
      "description": "债权统计查询基准快照"
    },
    {
      "id": "monthly_summary_baseline", 
      "file": "monthly_summary_baseline.json",
      "type": "summary_data",
      "description": "月度汇总基准快照"
    },
    {
      "id": "consistency_check_baseline",
      "file": "consistency_check_baseline.json", 
      "type": "validation_result",
      "description": "一致性检查基准快照"
    },
    {
      "id": "cross_table_validation_baseline",
      "file": "cross_table_validation_baseline.json",
      "type": "validation_result", 
      "description": "跨表验证基准快照"
    }
  ],
  "metadata": {
    "version": "1.0.0",
    "created": "2024-07-22T08:55:00",
    "total_snapshots": 4,
    "init_method": "script"
  }
}
EOF

# 创建基准数据说明
cat > test-data/baseline/v1.0/README.md << 'EOF'
# 基准数据集 v1.0

## 概述
这是FinancialSystem测试框架的初始基准数据集。

## 数据文件
- `schema.sql` - 数据库表结构
- `data.sql` - 基准测试数据
- `indexes.sql` - 索引定义

## 使用说明
1. 首次运行时自动加载
2. 测试执行时作为参照基准
3. 版本控制跟踪变更历史

## 版本信息
- 版本: v1.0.0
- 创建日期: 2024-07-22
- 创建方式: 脚本初始化
- 数据记录数: 0（空基准）
EOF

echo -e "${GREEN}✅ 快照初始化完成${NC}"

# 显示创建的文件
echo -e "${BLUE}📋 已创建的快照文件:${NC}"
ls -la test-data/snapshots/

echo ""
echo -e "${YELLOW}💡 下一步操作建议:${NC}"
echo "1. 运行应用程序，添加一些测试数据"
echo "2. 执行查询操作生成真实的快照数据"
echo "3. 使用 ./scripts/manage-snapshots.sh update 更新快照"
echo "4. 运行 ./scripts/manage-snapshots.sh verify 验证一致性"