-- 验证是否涉诉字段修复效果的SQL脚本
-- 用于验证减值准备表本年度累计回收字段更新逻辑的修复

-- 1. 检查是否存在同一债权人-债务人在同一年月有多条不同涉诉状态的记录
SELECT 
    债权人, 
    债务人, 
    年份, 
    月份, 
    COUNT(*) as 记录数,
    GROUP_CONCAT(是否涉诉 ORDER BY 是否涉诉) as 涉诉状态列表,
    GROUP_CONCAT(本月处置债权 ORDER BY 是否涉诉) as 处置金额列表,
    GROUP_CONCAT(本年度累计回收 ORDER BY 是否涉诉) as 累计回收列表
FROM 减值准备表 
WHERE 年份 >= 2025
GROUP BY 债权人, 债务人, 年份, 月份
HAVING COUNT(*) > 1
ORDER BY 债权人, 债务人, 年份, 月份;

-- 2. 验证债权转换场景：查找可能的非涉诉转涉诉案例
-- 查找同一债权人-债务人在同一时期既有涉诉又有非涉诉记录的情况
WITH debt_transfer_candidates AS (
    SELECT 
        债权人,
        债务人,
        期间,
        年份,
        COUNT(DISTINCT 是否涉诉) as 涉诉状态数,
        SUM(CASE WHEN 是否涉诉 = '是' THEN 本月处置债权 ELSE 0 END) as 涉诉处置总额,
        SUM(CASE WHEN 是否涉诉 = '否' THEN 本月处置债权 ELSE 0 END) as 非涉诉处置总额,
        SUM(CASE WHEN 是否涉诉 = '是' THEN 本年度累计回收 ELSE 0 END) as 涉诉累计回收,
        SUM(CASE WHEN 是否涉诉 = '否' THEN 本年度累计回收 ELSE 0 END) as 非涉诉累计回收
    FROM 减值准备表 
    WHERE 年份 >= 2025
    GROUP BY 债权人, 债务人, 期间, 年份
    HAVING COUNT(DISTINCT 是否涉诉) > 1
)
SELECT 
    债权人,
    债务人,
    期间,
    年份,
    涉诉状态数,
    涉诉处置总额,
    非涉诉处置总额,
    涉诉累计回收,
    非涉诉累计回收,
    CASE 
        WHEN 非涉诉处置总额 > 0 AND 涉诉处置总额 = 0 THEN '可能的转换案例：非涉诉有处置，涉诉无处置'
        WHEN 非涉诉处置总额 = 0 AND 涉诉处置总额 > 0 THEN '可能的转换案例：涉诉有处置，非涉诉无处置'
        ELSE '正常情况或其他'
    END as 转换分析
FROM debt_transfer_candidates
ORDER BY 债权人, 债务人, 年份;

-- 3. 验证修复前后的逻辑差异
-- 模拟修复前的计算逻辑（错误的逻辑）
WITH old_logic_simulation AS (
    SELECT 
        债权人,
        债务人,
        年份,
        月份,
        是否涉诉,
        本月处置债权,
        -- 模拟修复前的错误计算：不区分是否涉诉
        SUM(本月处置债权) OVER (
            PARTITION BY 债权人, 债务人, 年份 
            ORDER BY 月份 
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) as 错误的累计回收,
        -- 修复后的正确计算：区分是否涉诉
        SUM(本月处置债权) OVER (
            PARTITION BY 债权人, 债务人, 年份, 是否涉诉 
            ORDER BY 月份 
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) as 正确的累计回收,
        本年度累计回收 as 当前数据库值
    FROM 减值准备表 
    WHERE 年份 >= 2025 
      AND 本月处置债权 IS NOT NULL
)
SELECT 
    债权人,
    债务人,
    年份,
    月份,
    是否涉诉,
    本月处置债权,
    错误的累计回收,
    正确的累计回收,
    当前数据库值,
    CASE 
        WHEN 错误的累计回收 != 正确的累计回收 THEN '修复有效：计算结果不同'
        ELSE '修复无影响：计算结果相同'
    END as 修复效果分析,
    CASE 
        WHEN 当前数据库值 = 正确的累计回收 THEN '数据库值正确'
        WHEN 当前数据库值 = 错误的累计回收 THEN '数据库值需要更新'
        ELSE '数据库值异常'
    END as 数据库状态
FROM old_logic_simulation
WHERE 错误的累计回收 != 正确的累计回收
ORDER BY 债权人, 债务人, 年份, 月份, 是否涉诉;

-- 4. 检查数据完整性：确保所有记录都有正确的是否涉诉值
SELECT 
    '是否涉诉字段完整性检查' as 检查项目,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 是否涉诉 IS NULL THEN 1 ELSE 0 END) as 空值记录数,
    SUM(CASE WHEN 是否涉诉 NOT IN ('是', '否') THEN 1 ELSE 0 END) as 无效值记录数,
    SUM(CASE WHEN 是否涉诉 = '是' THEN 1 ELSE 0 END) as 涉诉记录数,
    SUM(CASE WHEN 是否涉诉 = '否' THEN 1 ELSE 0 END) as 非涉诉记录数
FROM 减值准备表 
WHERE 年份 >= 2025;

-- 5. 建议的修复后验证查询
-- 运行批量更新后，用此查询验证结果
SELECT 
    '修复后验证' as 验证阶段,
    债权人,
    债务人,
    年份,
    月份,
    是否涉诉,
    本月处置债权,
    本年度累计回收,
    -- 手工计算的期望值
    (
        SELECT SUM(IFNULL(t2.本月处置债权, 0))
        FROM 减值准备表 t2 
        WHERE t2.债权人 = t1.债权人 
          AND t2.债务人 = t1.债务人
          AND t2.年份 = t1.年份 
          AND t2.月份 <= t1.月份
          AND t2.是否涉诉 = t1.是否涉诉
    ) as 期望的累计回收,
    CASE 
        WHEN 本年度累计回收 = (
            SELECT SUM(IFNULL(t2.本月处置债权, 0))
            FROM 减值准备表 t2 
            WHERE t2.债权人 = t1.债权人 
              AND t2.债务人 = t1.债务人
              AND t2.年份 = t1.年份 
              AND t2.月份 <= t1.月份
              AND t2.是否涉诉 = t1.是否涉诉
        ) THEN '正确'
        ELSE '需要修正'
    END as 验证结果
FROM 减值准备表 t1
WHERE t1.年份 >= 2025 
  AND t1.本月处置债权 IS NOT NULL
ORDER BY t1.债权人, t1.债务人, t1.年份, t1.月份, t1.是否涉诉;