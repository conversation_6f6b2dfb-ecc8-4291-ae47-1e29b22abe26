#!/bin/bash
# 数据库名迁移脚本：从中文改为英文
# 仅修改数据库名，不修改表名

set -e

echo "开始数据库名迁移流程..."
echo "目标：将 'overdue_debt_db' 改为 'overdue_debt_db'"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 检查是否在项目根目录
if [ ! -f "pom.xml" ]; then
    echo -e "${RED}错误：请在项目根目录执行此脚本${NC}"
    exit 1
fi

echo -e "${YELLOW}步骤1：备份当前配置文件${NC}"
# 创建备份目录
BACKUP_DIR="backups/config-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 备份配置文件
find . -name "application*.yml" -o -name "application*.yaml" -o -name "docker-compose*.yml" | while read file; do
    if [ -f "$file" ]; then
        cp "$file" "$BACKUP_DIR/"
        echo "备份: $file"
    fi
done

echo -e "${YELLOW}步骤2：更新Spring Boot配置文件${NC}"
# 更新application.yml
if [ -f "api-gateway/src/main/resources/application.yml" ]; then
    sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' api-gateway/src/main/resources/application.yml
    echo "✓ 更新 application.yml"
fi

# 更新application-docker.yml
if [ -f "api-gateway/src/main/resources/application-docker.yml" ]; then
    # 替换URL编码的中文数据库名
    sed -i.bak 's/%E9%80%BE%E6%9C%9F%E5%80%BA%E6%9D%83%E6%95%B0%E6%8D%AE%E5%BA%93/overdue_debt_db/g' api-gateway/src/main/resources/application-docker.yml
    echo "✓ 更新 application-docker.yml"
fi

# 更新其他profile配置
find api-gateway/src/main/resources -name "application-*.yml" | while read file; do
    if grep -q "overdue_debt_db" "$file"; then
        sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' "$file"
        echo "✓ 更新 $(basename $file)"
    fi
done

echo -e "${YELLOW}步骤3：更新Docker Compose配置${NC}"
# 更新docker-compose.yml
if [ -f "docker-compose.yml" ]; then
    sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' docker-compose.yml
    echo "✓ 更新 docker-compose.yml"
fi

# 更新docker-compose.prod.yml
if [ -f "docker-compose.prod.yml" ]; then
    sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' docker-compose.prod.yml
    echo "✓ 更新 docker-compose.prod.yml"
fi

# 更新docker-compose.local.yml
if [ -f "docker-compose.local.yml" ]; then
    sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' docker-compose.local.yml
    echo "✓ 更新 docker-compose.local.yml"
fi

echo -e "${YELLOW}步骤4：更新数据库初始化脚本${NC}"
# 更新init-scripts
if [ -f "init-scripts/01-init-databases.sql" ]; then
    sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' init-scripts/01-init-databases.sql
    echo "✓ 更新 01-init-databases.sql"
fi

# 处理03-create-db-proxy.sql中的兼容性代码
if [ -f "init-scripts/03-create-db-proxy.sql" ]; then
    # 这个文件包含了中英文兼容代码，需要特殊处理
    echo "✓ 保留 03-create-db-proxy.sql 中的兼容性代码"
fi

echo -e "${YELLOW}步骤5：更新Java代码中的注释和文档${NC}"
# 更新Java文件中的注释（仅注释，不改变代码逻辑）
find . -name "*.java" | while read file; do
    if grep -q "overdue_debt_db" "$file"; then
        # 只更新注释中的数据库名
        sed -i.bak 's/\(.*\/\/.*\)overdue_debt_db/\1overdue_debt_db/g' "$file"
        sed -i.bak 's/\(.*\/\*.*\)overdue_debt_db/\1overdue_debt_db/g' "$file"
        echo "✓ 更新注释: $(basename $file)"
    fi
done

echo -e "${YELLOW}步骤6：清理备份文件${NC}"
# 询问是否删除.bak文件
read -p "是否删除所有.bak备份文件? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    find . -name "*.bak" -type f -delete
    echo "✓ 已删除所有.bak文件"
fi

echo -e "${GREEN}数据库名迁移脚本执行完成！${NC}"
echo
echo "下一步操作："
echo "1. 检查修改的文件是否正确"
echo "2. 提交代码到Git"
echo "3. 执行部署脚本"
echo
echo "如需回滚，请使用备份目录: $BACKUP_DIR"