#!/bin/bash

# 直接同步3月份数据脚本
# 解决数据编码和插入问题

set -e

LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

echo "🔄 开始直接同步3月份数据..."

# 创建临时同步脚本
cat > /tmp/direct_sync.sql << 'EOF'
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 删除现有2025年数据
DELETE FROM `新增表` WHERE `年份`=2025;

-- 插入本地数据
EOF

# 导出本地数据为INSERT语句（使用更兼容的格式）
mysql -h localhost -u root -p'Zlb&198838' --skip-ssl -D overdue_debt_db \
    --default-character-set=utf8mb4 -B -e \
    "SELECT CONCAT('INSERT INTO \`新增表\` VALUES (',
        序号, ',',
        QUOTE(管理公司), ',',
        QUOTE(债权人), ',',
        QUOTE(债务人), ',',
        年份, ',',
        QUOTE(到期时间), ',',
        QUOTE(科目名称), ',',
        QUOTE(债权性质), ',',
        QUOTE(是否涉诉), ',',
        QUOTE(期间), ',',
        'NULL,NULL,',
        \`1月\`, ',',
        \`2月\`, ',',
        \`3月\`, ',',
        \`4月\`, ',',
        \`5月\`, ',',
        \`6月\`, ',',
        \`7月\`, ',',
        \`8月\`, ',',
        \`9月\`, ',',
        \`10月\`, ',',
        \`11月\`, ',',
        \`12月\`, ',',
        处置金额, ',',
        现金处置, ',',
        分期还款, ',',
        资产抵债, ',',
        其他方式, ',',
        新增金额, ',',
        债权余额, ',',
        QUOTE(备注), ',',
        QUOTE(更新时间), ',',
        QUOTE(IFNULL(责任人, '')), ',',
        风险准备金计提金额,
        ');'
    ) as insert_stmt
    FROM 新增表 WHERE 年份=2025;" | tail -n +2 >> /tmp/direct_sync.sql

echo "📤 传输同步脚本到Linux服务器..."
scp /tmp/direct_sync.sql "$LINUX_SERVER:/tmp/"

echo "🔄 在Linux服务器上执行同步..."
ssh "$LINUX_SERVER" << 'EOF'
    echo "复制脚本到MySQL容器..."
    docker cp /tmp/direct_sync.sql financial-mysql:/tmp/
    
    echo "执行同步脚本..."
    docker exec financial-mysql mysql -uroot -p'Zlb&198838' \
        -D overdue_debt_db --default-character-set=utf8mb4 < /tmp/direct_sync.sql
    
    echo "验证同步结果..."
    docker exec financial-mysql mysql -uroot -p'Zlb&198838' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) as total_2025_records FROM 新增表 WHERE 年份=2025;"
    
    echo "验证3月份数据..."
    docker exec financial-mysql mysql -uroot -p'Zlb&198838' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) as march_records FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0);"
    
    # 清理临时文件
    rm -f /tmp/direct_sync.sql
    docker exec financial-mysql rm -f /tmp/direct_sync.sql
EOF

echo "✅ 直接同步完成！"

# 清理本地临时文件
rm -f /tmp/direct_sync.sql

echo "🔄 重启Linux应用服务..."
ssh "$LINUX_SERVER" "docker restart financial-backend"

echo "⏳ 等待应用服务启动..."
sleep 30

echo "✅ 3月份数据同步解决方案执行完成！"
echo ""
echo "🔍 验证建议："
echo "1. 刷新Linux端的数据更新观测平台页面"
echo "2. 检查2025年3月的新增数据是否正确显示"
echo "3. 对比本地和Linux端的数据一致性"