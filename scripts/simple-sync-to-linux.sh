#!/bin/bash

# 简化版本地数据同步到Linux服务器脚本
# 专门解决备份和传输问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"

# 数据库列表
DB_NAMES=("overdue_debt_db" "kingdee" "user_system")

echo -e "${BLUE}==================== 简化版数据同步 ====================${NC}"
echo -e "${YELLOW}从本地MySQL同步到Linux服务器 ($LINUX_SERVER)${NC}"
echo ""

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：同步单个数据库
sync_database() {
    local db_name=$1
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local dump_file="/tmp/${db_name}_sync_${timestamp}.sql"
    
    log_info "开始同步数据库: $db_name"
    
    # 1. 创建本地备份
    log_info "创建数据库备份..."
    mysqldump -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" \
        --skip-ssl \
        --single-transaction \
        --routines \
        --triggers \
        --add-drop-database \
        --databases "$db_name" > "$dump_file"
    
    if [ $? -ne 0 ]; then
        log_error "数据库 $db_name 备份失败"
        return 1
    fi
    
    log_success "备份创建成功: $dump_file"
    echo "备份文件大小: $(du -h $dump_file | cut -f1)"
    
    # 2. 传输到Linux服务器
    log_info "传输备份文件到Linux服务器..."
    scp "$dump_file" "${LINUX_SERVER}:/tmp/"
    
    if [ $? -ne 0 ]; then
        log_error "文件传输失败"
        rm -f "$dump_file"
        return 1
    fi
    
    log_success "文件传输成功"
    
    # 3. 在Linux服务器上恢复
    log_info "在Linux服务器上恢复数据库..."
    
    local remote_dump_file="/tmp/$(basename $dump_file)"
    
    ssh "$LINUX_SERVER" << EOF
        set -e
        echo "开始恢复数据库: $db_name"
        
        # 备份现有数据库
        echo "备份现有数据库..."
        docker exec financial-mysql mysqldump -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            --single-transaction \
            --databases $db_name > /tmp/${db_name}_backup_before_sync_${timestamp}.sql 2>/dev/null || echo "数据库 $db_name 可能不存在，跳过备份"
        
        # 复制备份文件到容器
        echo "复制备份文件到MySQL容器..."
        docker cp "$remote_dump_file" financial-mysql:/tmp/
        
        # 恢复数据库
        echo "恢复数据库..."
        docker exec financial-mysql mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' < /tmp/$(basename $dump_file)
        
        if [ \$? -eq 0 ]; then
            echo "数据库 $db_name 恢复成功"
            
            # 验证恢复结果
            echo "验证数据库表..."
            docker exec financial-mysql mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -D'$db_name' -e "SHOW TABLES;"
            
            # 清理临时文件
            rm -f "$remote_dump_file"
            docker exec financial-mysql rm -f /tmp/$(basename $dump_file)
            
            echo "数据库 $db_name 同步完成"
        else
            echo "数据库 $db_name 恢复失败"
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        log_success "数据库 $db_name 同步成功"
        rm -f "$dump_file"
        return 0
    else
        log_error "数据库 $db_name 同步失败"
        rm -f "$dump_file"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始简化版数据同步..."
    
    # 检查连接
    log_info "检查连接..."
    
    # 检查本地MySQL
    if ! mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SELECT 1;" > /dev/null 2>&1; then
        log_error "本地MySQL连接失败"
        exit 1
    fi
    log_success "本地MySQL连接正常"
    
    # 检查SSH连接
    if ! ssh -o ConnectTimeout=5 "$LINUX_SERVER" "echo 'SSH测试成功'" > /dev/null 2>&1; then
        log_error "SSH连接失败"
        exit 1
    fi
    log_success "SSH连接正常"
    
    # 检查Linux MySQL
    if ! ssh "$LINUX_SERVER" "docker exec financial-mysql mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' -e 'SELECT 1;'" > /dev/null 2>&1; then
        log_error "Linux服务器MySQL连接失败"
        exit 1
    fi
    log_success "Linux服务器MySQL连接正常"
    
    echo ""
    log_info "开始同步数据库..."
    
    local success_count=0
    local total_count=${#DB_NAMES[@]}
    
    for db_name in "${DB_NAMES[@]}"; do
        echo ""
        echo -e "${YELLOW}--- 同步数据库: $db_name ---${NC}"
        
        if sync_database "$db_name"; then
            success_count=$((success_count + 1))
        else
            log_error "数据库 $db_name 同步失败"
        fi
    done
    
    echo ""
    echo -e "${BLUE}=== 同步结果总结 ===${NC}"
    echo -e "${GREEN}✓ 成功同步: $success_count/$total_count 个数据库${NC}"
    
    if [ $success_count -eq $total_count ]; then
        log_success "所有数据库同步成功！"
        echo -e "${GREEN}您的本地数据已成功同步到Linux服务器${NC}"
    else
        log_error "部分数据库同步失败，请检查错误信息"
    fi
    
    echo ""
    echo -e "${YELLOW}后续步骤：${NC}"
    echo "1. 重启Linux服务器上的应用服务"
    echo "2. 验证应用程序功能"
    echo ""
    echo -e "${YELLOW}验证命令：${NC}"
    echo "ssh $LINUX_SERVER 'docker exec financial-mysql mysql -u$LOCAL_DB_USER -p$LOCAL_DB_PASSWORD -e \"SHOW DATABASES;\"'"
    
    echo -e "\n${BLUE}==================== 同步完成 ====================${NC}"
}

# 执行主函数
main "$@"