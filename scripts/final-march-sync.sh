#!/bin/bash

# 最终解决方案：完整的3月份数据同步
# 使用正确的表结构和字段值

set -e

echo "🎯 最终解决方案：3月份数据同步"

# 首先清理Linux端的2025年数据
ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e 'DELETE FROM 新增表 WHERE 年份=2025;'"

echo "✅ 已清理Linux端2025年数据"

# 使用本地到Linux的完整表同步
echo "📤 开始完整表同步..."

# 导出完整的新增表数据（只包含2025年）
mysqldump -h localhost -u root -p'Zlb&198838' --skip-ssl \
    --default-character-set=utf8mb4 \
    --single-transaction \
    --no-create-info \
    --complete-insert \
    --extended-insert=FALSE \
    --where="年份=2025" \
    overdue_debt_db 新增表 > /tmp/complete_xinzeng_2025.sql

echo "📁 数据导出完成，文件大小: $(du -h /tmp/complete_xinzeng_2025.sql | cut -f1)"

# 传输到Linux服务器
scp /tmp/complete_xinzeng_2025.sql admin@**********:/tmp/

# 在Linux服务器上执行同步
ssh admin@********** << 'EOF'
    echo "📥 复制SQL文件到MySQL容器..."
    docker cp /tmp/complete_xinzeng_2025.sql financial-mysql:/tmp/
    
    echo "🔄 执行数据恢复..."
    docker exec financial-mysql mysql -uroot -p'Zlb&198838' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        < /tmp/complete_xinzeng_2025.sql
    
    echo "✅ 数据恢复完成，验证结果..."
    
    # 验证总记录数
    total_count=$(docker exec financial-mysql mysql -uroot -p'Zlb&198838' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;" -s -N 2>/dev/null)
    
    echo "📊 2025年总记录数: $total_count"
    
    # 验证3月份数据
    march_count=$(docker exec financial-mysql mysql -uroot -p'Zlb&198838' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0);" -s -N 2>/dev/null)
    
    echo "📈 3月份有效数据: $march_count 条"
    
    # 显示几条示例数据
    echo "📋 示例数据预览:"
    docker exec financial-mysql mysql -uroot -p'Zlb&198838' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT 债权人, 债务人, \`3月\`, 新增金额 FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0) LIMIT 3;"
    
    # 清理临时文件
    rm -f /tmp/complete_xinzeng_2025.sql
    docker exec financial-mysql rm -f /tmp/complete_xinzeng_2025.sql
EOF

# 重启应用服务
echo "🔄 重启Linux应用服务..."
ssh admin@********** "docker restart financial-backend"

echo "⏳ 等待应用服务完全启动..."
sleep 35

# 验证应用服务状态
if ssh admin@********** "curl -f -s http://localhost:8080/actuator/health" > /dev/null 2>&1; then
    echo "✅ 应用服务重启成功"
else
    echo "⚠️ 应用服务状态检查失败，请手动验证"
fi

# 清理本地临时文件
rm -f /tmp/complete_xinzeng_2025.sql

echo ""
echo "🎉 3月份数据同步完成！"
echo ""
echo "📋 同步结果摘要:"
echo "• 已清理Linux端旧的2025年数据"
echo "• 已完整同步本地2025年数据到Linux"
echo "• 已重启Linux应用服务"
echo ""
echo "🔍 验证步骤:"
echo "1. 打开Linux端的数据更新观测平台"
echo "2. 选择2025年3月进行检查"
echo "3. 确认新增表等数据是否正确显示"
echo ""
echo "如果数据仍不显示，可能需要检查应用程序的缓存机制或重新登录系统。"