#!/bin/bash

# 逐条记录同步解决方案
# 确保每条记录都正确插入到Linux数据库

set -e

echo "🎯 开始逐条记录同步方案..."

# 清理测试记录
ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e 'DELETE FROM 新增表 WHERE 序号=9999;'"

echo "✅ 已清理测试记录"

# 先删除Linux端所有2025年数据
ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e 'DELETE FROM 新增表 WHERE 年份=2025;'"

echo "✅ 已清理Linux端2025年数据"

# 从本地数据库逐条读取2025年数据并插入到Linux
echo "📤 开始逐条同步本地2025年数据..."

# 获取本地2025年数据的SQL插入语句
mysql -h localhost -u root -p'Zlb&198838' --skip-ssl -D overdue_debt_db --default-character-set=utf8mb4 -B -e "
SELECT CONCAT(
    'INSERT INTO 新增表 (',
    '序号, 管理公司, 债权人, 债务人, 年份, 到期时间, 科目名称, 债权性质, 是否涉诉, 期间, ',
    '\`1月\`, \`2月\`, \`3月\`, \`4月\`, \`5月\`, \`6月\`, \`7月\`, \`8月\`, \`9月\`, \`10月\`, \`11月\`, \`12月\`, ',
    '处置金额, 现金处置, 分期还款, 资产抵债, 其他方式, 新增金额, 债权余额, 备注, 更新时间, 责任人, 风险准备金计提金额',
    ') VALUES (',
    序号, ', ',
    QUOTE(管理公司), ', ',
    QUOTE(债权人), ', ',
    QUOTE(债务人), ', ',
    年份, ', ',
    QUOTE(到期时间), ', ',
    QUOTE(科目名称), ', ',
    QUOTE(债权性质), ', ',
    QUOTE(是否涉诉), ', ',
    QUOTE(期间), ', ',
    \`1月\`, ', ',
    \`2月\`, ', ',
    \`3月\`, ', ',
    \`4月\`, ', ',
    \`5月\`, ', ',
    \`6月\`, ', ',
    \`7月\`, ', ',
    \`8月\`, ', ',
    \`9月\`, ', ',
    \`10月\`, ', ',
    \`11月\`, ', ',
    \`12月\`, ', ',
    处置金额, ', ',
    现金处置, ', ',
    分期还款, ', ',
    资产抵债, ', ',
    其他方式, ', ',
    新增金额, ', ',
    债权余额, ', ',
    QUOTE(备注), ', ',
    QUOTE(更新时间), ', ',
    QUOTE(IFNULL(责任人, '')), ', ',
    风险准备金计提金额,
    ');'
) as insert_stmt
FROM 新增表 
WHERE 年份=2025 
ORDER BY 序号;" | tail -n +2 > /tmp/individual_inserts.txt

echo "📁 生成了 $(wc -l < /tmp/individual_inserts.txt) 条插入语句"

# 逐条执行插入
local_count=$(wc -l < /tmp/individual_inserts.txt)
success_count=0
error_count=0

echo "🔄 开始逐条插入数据..."

while IFS= read -r insert_sql; do
    if ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e \"$insert_sql\"" 2>/dev/null; then
        ((success_count++))
        echo -ne "\r✅ 成功: $success_count/$local_count"
    else
        ((error_count++))
        echo -ne "\r❌ 错误: $error_count"
    fi
done < /tmp/individual_inserts.txt

echo ""
echo ""
echo "📊 同步结果统计:"
echo "• 总记录数: $local_count"
echo "• 成功插入: $success_count"
echo "• 插入失败: $error_count"

# 验证Linux端数据
linux_count=$(ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e 'SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;' -s -N" 2>/dev/null)

echo "• Linux端记录: $linux_count"

if [ "$success_count" -eq "$linux_count" ] && [ "$linux_count" -gt 0 ]; then
    echo "✅ 数据同步验证成功！"
    
    # 验证3月份数据
    march_count=$(ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e 'SELECT COUNT(*) FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0);' -s -N" 2>/dev/null)
    
    echo "📈 Linux端3月份有效数据: $march_count 条"
    
    # 显示示例数据
    echo ""
    echo "📋 Linux端数据示例:"
    ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e 'SELECT 债权人, 债务人, \`3月\`, 新增金额 FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0) LIMIT 5;'"
    
    # 重启应用服务
    echo ""
    echo "🔄 重启Linux应用服务..."
    ssh admin@********** "docker restart financial-backend"
    
    echo "⏳ 等待应用服务启动..."
    sleep 30
    
    echo ""
    echo "🎉 3月份数据同步完全成功！"
    echo ""
    echo "📋 验证步骤:"
    echo "1. 刷新Linux端数据更新观测平台页面"
    echo "2. 选择2025年3月查看数据"
    echo "3. 确认新增表数据显示正确"
    
else
    echo "❌ 数据同步验证失败，请检查错误"
fi

# 清理临时文件
rm -f /tmp/individual_inserts.txt

echo ""
echo "🏁 逐条记录同步方案执行完成！"