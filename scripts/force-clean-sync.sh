#!/bin/bash

# 强制清理并重新同步数据库
# 彻底解决数据不一致问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

echo -e "${PURPLE}⚡==================== 强制清理重新同步 ====================${NC}"
echo -e "${YELLOW}UltraThink彻底解决方案：完全清理后重新同步${NC}"
echo ""

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] INFO${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] SUCCESS${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR${NC} $1"
}

# 函数：完全删除并重建数据库
force_rebuild_database() {
    local db_name=$1
    
    log_info "🔥 强制重建数据库: $db_name"
    
    # 先删除Linux端的数据库
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -e 'DROP DATABASE IF EXISTS $db_name;'" 2>/dev/null || true
    
    log_info "✅ Linux端数据库 $db_name 已删除"
    
    # 生成本地数据库的完整备份
    local temp_file="/tmp/${db_name}_force_rebuild_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "📦 生成本地数据库完整备份..."
    mysqldump -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        --add-drop-database \
        --create-options \
        --databases "$db_name" > "$temp_file"
    
    if [ ! -s "$temp_file" ]; then
        log_error "❌ 数据库 $db_name 备份失败"
        rm -f "$temp_file"
        return 1
    fi
    
    local file_size=$(du -h "$temp_file" | cut -f1)
    log_info "📦 备份文件大小: $file_size"
    
    # 传输并恢复到Linux
    log_info "📤 传输并重建数据库..."
    
    if cat "$temp_file" | ssh "$LINUX_SERVER" "cat > /tmp/rebuild_$(basename $temp_file) && \
        docker cp /tmp/rebuild_$(basename $temp_file) $MYSQL_CONTAINER:/tmp/ && \
        docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            --default-character-set=utf8mb4 < /tmp/rebuild_$(basename $temp_file) && \
        rm -f /tmp/rebuild_$(basename $temp_file) && \
        docker exec $MYSQL_CONTAINER rm -f /tmp/rebuild_$(basename $temp_file)"; then
        
        log_success "✅ 数据库 $db_name 强制重建完成"
        rm -f "$temp_file"
        
        # 验证重建结果
        local linux_tables=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            -e \"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$db_name';\" -s -N" 2>/dev/null || echo "0")
        
        log_info "📊 重建后表数量: $linux_tables"
        return 0
    else
        log_error "❌ 数据库 $db_name 重建失败"
        rm -f "$temp_file"
        return 1
    fi
}

# 函数：详细验证数据
detailed_verification() {
    log_info "🔍 开始详细数据验证..."
    
    echo ""
    echo -e "${BLUE}📊 详细数据验证报告${NC}"
    echo "========================================"
    
    # 验证overdue_debt_db
    echo -e "${YELLOW}数据库: overdue_debt_db${NC}"
    
    local_total=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM 新增表;" -s -N 2>/dev/null || echo "0")
    
    linux_total=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e 'SELECT COUNT(*) FROM 新增表;' -s -N" 2>/dev/null || echo "0")
    
    echo "  • 新增表总记录: 本地($local_total) vs Linux($linux_total)"
    
    local_2025=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;" -s -N 2>/dev/null || echo "0")
    
    linux_2025=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e 'SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;' -s -N" 2>/dev/null || echo "0")
    
    echo "  • 2025年记录: 本地($local_2025) vs Linux($linux_2025)"
    
    local_march=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0);" -s -N 2>/dev/null || echo "0")
    
    linux_march=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e 'SELECT COUNT(*) FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0);' -s -N" 2>/dev/null || echo "0")
    
    echo "  • 3月份数据: 本地($local_march) vs Linux($linux_march)"
    
    # 显示具体的3月份数据示例
    echo ""
    echo -e "${YELLOW}Linux端3月份数据示例:${NC}"
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D overdue_debt_db --default-character-set=utf8mb4 \
        -e 'SELECT 债权人, 债务人, \`3月\`, 新增金额 FROM 新增表 WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0) LIMIT 3;'" 2>/dev/null
    
    # 验证user_system
    echo ""
    echo -e "${YELLOW}数据库: user_system${NC}"
    
    local_users=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -D user_system --default-character-set=utf8mb4 \
        -e "SELECT COUNT(*) FROM users;" -s -N 2>/dev/null || echo "0")
    
    linux_users=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        -D user_system --default-character-set=utf8mb4 \
        -e 'SELECT COUNT(*) FROM users;' -s -N" 2>/dev/null || echo "0")
    
    echo "  • 用户记录: 本地($local_users) vs Linux($linux_users)"
    
    echo "========================================"
    
    # 判断是否一致
    if [ "$local_total" -eq "$linux_total" ] && [ "$local_2025" -eq "$linux_2025" ] && [ "$local_march" -eq "$linux_march" ] && [ "$local_users" -eq "$linux_users" ]; then
        log_success "🎉 所有数据验证通过，完全一致！"
        return 0
    else
        log_error "❌ 数据验证仍然失败"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${PURPLE}🚀 开始强制清理重建...${NC}"
    echo ""
    
    # 强制重建所有数据库
    local databases=("overdue_debt_db" "kingdee" "user_system")
    local rebuild_success=true
    
    for db_name in "${databases[@]}"; do
        echo -e "${YELLOW}--- 强制重建: $db_name ---${NC}"
        
        if ! force_rebuild_database "$db_name"; then
            rebuild_success=false
            log_error "数据库 $db_name 重建失败"
        fi
        
        echo ""
    done
    
    if [ "$rebuild_success" = true ]; then
        log_success "🎉 所有数据库强制重建完成！"
        
        echo ""
        if detailed_verification; then
            # 重启应用服务
            echo ""
            log_info "🔄 重启Linux应用服务..."
            ssh "$LINUX_SERVER" "docker restart financial-backend"
            
            log_info "⏳ 等待应用服务完全启动..."
            sleep 40
            
            # 验证应用状态
            if ssh "$LINUX_SERVER" "curl -f -s http://localhost:8080/actuator/health" > /dev/null 2>&1; then
                log_success "✅ 应用服务重启成功"
            else
                log_info "⏳ 应用可能仍在启动中..."
            fi
            
            echo ""
            echo -e "${GREEN}🎉 强制清理重建解决方案执行完成！${NC}"
            echo ""
            echo -e "${YELLOW}📋 最终验证步骤:${NC}"
            echo "1. 等待2分钟让应用完全启动"
            echo "2. 清除浏览器缓存并重新登录"
            echo "3. 刷新Linux端数据更新观测平台"
            echo "4. 选择2025年3月验证数据一致性"
            echo ""
            echo -e "${GREEN}现在Linux端数据应该与本地完全一致！${NC}"
            
        else
            log_error "数据验证仍然失败，需要手动检查"
        fi
    else
        log_error "部分数据库重建失败"
        exit 1
    fi
    
    echo ""
    echo -e "${PURPLE}==================== 重建完成 ====================${NC}"
}

# 执行主函数
main "$@"