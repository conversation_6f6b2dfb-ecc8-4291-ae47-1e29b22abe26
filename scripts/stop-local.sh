#!/bin/bash

# 本地开发环境停止脚本

echo "🛑 停止本地开发环境..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 停止后端
if [ -f logs/backend.pid ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if ps -p $BACKEND_PID > /dev/null; then
        echo "停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        echo -e "${GREEN}✅ 后端服务已停止${NC}"
    else
        echo "后端服务未运行"
    fi
    rm -f logs/backend.pid
else
    echo "未找到后端PID文件"
fi

# 停止前端
if [ -f logs/frontend.pid ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null; then
        echo "停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        echo -e "${GREEN}✅ 前端服务已停止${NC}"
    else
        echo "前端服务未运行"
    fi
    rm -f logs/frontend.pid
else
    echo "未找到前端PID文件"
fi

# 额外检查并停止可能残留的进程
echo "检查残留进程..."

# 查找并停止Spring Boot进程
SPRING_PIDS=$(ps aux | grep '[s]pring-boot:run' | awk '{print $2}')
if [ ! -z "$SPRING_PIDS" ]; then
    echo "发现Spring Boot进程，正在停止..."
    echo $SPRING_PIDS | xargs kill -9
fi

# 查找并停止React进程
REACT_PIDS=$(ps aux | grep '[r]eact-scripts start' | awk '{print $2}')
if [ ! -z "$REACT_PIDS" ]; then
    echo "发现React进程，正在停止..."
    echo $REACT_PIDS | xargs kill -9
fi

echo -e "${GREEN}✅ 所有服务已停止${NC}"