#!/bin/bash
# 真实数据快照捕获脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 真实数据快照捕获 ===${NC}"

# 数据库配置
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_NAME=${DB_NAME:-overdue_debt_db}
DB_USER=${DB_USER:-root}
DB_PASS=${DB_PASS:-Zlb&198838}

# 检查数据库连接
echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
if ! mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} --skip-ssl -e "USE ${DB_NAME};" 2>/dev/null; then
    echo -e "${RED}❌ 数据库连接失败${NC}"
    echo -e "${YELLOW}💡 请确保：${NC}"
    echo "1. 数据库服务已启动"
    echo "2. 连接参数正确（host=${DB_HOST}, port=${DB_PORT}, database=${DB_NAME}）"
    echo "3. 用户权限充足"
    exit 1
fi

echo -e "${GREEN}✅ 数据库连接成功${NC}"

# 函数：执行SQL查询并转换为JSON
execute_query_to_json() {
    local query="$1"
    local output_file="$2"
    
    echo -e "${YELLOW}📊 执行查询: $(echo "$query" | tr '\n' ' ' | cut -c1-50)...${NC}"
    
    # 执行查询并转换为JSON格式
    mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} \
        --skip-ssl -e "$query" --batch --raw | \
    python3 -c "
import sys
import json
from datetime import datetime

lines = sys.stdin.read().strip().split('\n')
if len(lines) < 2:
    # 没有数据，返回空数组
    result = []
else:
    headers = lines[0].split('\t')
    result = []
    for line in lines[1:]:
        values = line.split('\t')
        row = {}
        for i, header in enumerate(headers):
            value = values[i] if i < len(values) else None
            if value == 'NULL' or value == '':
                value = None
            elif value and value.replace('.', '').replace('-', '').isdigit():
                try:
                    value = float(value) if '.' in value else int(value)
                except:
                    pass
            row[header] = value
        result.append(row)

# 创建完整的快照结构
snapshot = {
    'id': '$output_file'.replace('.json', '').replace('test-data/snapshots/', ''),
    'timestamp': datetime.now().isoformat(),
    'data': result,
    'metadata': {
        'version': '1.0.0',
        'description': '真实数据快照',
        'recordCount': len(result),
        'createdBy': 'capture_script',
        'source': 'database_query'
    }
}

print(json.dumps(snapshot, indent=2, ensure_ascii=False))
" > "$output_file"

    if [ $? -eq 0 ]; then
        local count=$(jq -r '.data | length' "$output_file" 2>/dev/null || echo "0")
        echo -e "${GREEN}✅ 快照已保存: $output_file (${count}条记录)${NC}"
    else
        echo -e "${RED}❌ 快照保存失败: $output_file${NC}"
    fi
}

# 1. 捕获新增表快照
echo -e "${BLUE}📸 捕获新增表快照...${NC}"
execute_query_to_json "
SELECT * FROM 新增表 
ORDER BY 债权人, 债务人, 年份
LIMIT 1000
" "test-data/snapshots/新增表_baseline.json"

# 2. 捕获处置表快照
echo -e "${BLUE}📸 捕获处置表快照...${NC}"
execute_query_to_json "
SELECT * FROM 处置表 
ORDER BY 债权人, 债务人, 年份, 月份
LIMIT 1000
" "test-data/snapshots/处置表_baseline.json"

# 3. 捕获减值准备表快照
echo -e "${BLUE}📸 捕获减值准备表快照...${NC}"
execute_query_to_json "
SELECT * FROM 减值准备表 
ORDER BY 债权人, 债务人, 年份, 月份
LIMIT 1000
" "test-data/snapshots/减值准备表_baseline.json"

# 4. 捕获汇总表快照
echo -e "${BLUE}📸 捕获汇总表快照...${NC}"
execute_query_to_json "
SELECT * FROM 汇总表 
ORDER BY 债权人, 年份
LIMIT 1000
" "test-data/snapshots/汇总表_baseline.json"

# 5. 捕获诉讼表快照
echo -e "${BLUE}📸 捕获诉讼表快照...${NC}"
execute_query_to_json "
SELECT * FROM 诉讼表 
ORDER BY 债权人, 债务人, 年份, 月份
LIMIT 1000
" "test-data/snapshots/诉讼表_baseline.json"

# 6. 捕获非诉讼表快照
echo -e "${BLUE}📸 捕获非诉讼表快照...${NC}"
execute_query_to_json "
SELECT * FROM 非诉讼表 
ORDER BY 债权人, 债务人, 年份, 月份
LIMIT 1000
" "test-data/snapshots/非诉讼表_baseline.json"

# 7. 捕获风险准备金表快照
echo -e "${BLUE}📸 捕获风险准备金表快照...${NC}"
execute_query_to_json "
SELECT * FROM 风险准备金表 
ORDER BY 债权人, 年份
LIMIT 1000
" "test-data/snapshots/风险准备金表_baseline.json"

# 8. 捕获债权统计快照（基于新增表）
echo -e "${BLUE}📸 捕获债权统计快照...${NC}"
execute_query_to_json "
SELECT 
    债权人,
    COUNT(*) as debt_count,
    SUM(金额) as total_amount,
    AVG(金额) as avg_amount,
    MAX(金额) as max_amount,
    MIN(金额) as min_amount
FROM 新增表 
WHERE 年份 >= 2024
GROUP BY 债权人
ORDER BY 债权人
" "test-data/snapshots/debt_statistics_baseline.json"

# 9. 捕获月度汇总快照
echo -e "${BLUE}📸 捕获月度汇总快照...${NC}"
execute_query_to_json "
SELECT 
    年份,
    SUM(COALESCE(\`1月\`, 0)) as jan_amount,
    SUM(COALESCE(\`2月\`, 0)) as feb_amount,
    SUM(COALESCE(\`3月\`, 0)) as mar_amount,
    SUM(COALESCE(\`4月\`, 0)) as apr_amount,
    SUM(COALESCE(\`5月\`, 0)) as may_amount,
    SUM(COALESCE(\`6月\`, 0)) as jun_amount,
    SUM(COALESCE(\`7月\`, 0)) as jul_amount,
    SUM(COALESCE(\`8月\`, 0)) as aug_amount,
    SUM(COALESCE(\`9月\`, 0)) as sep_amount,
    SUM(COALESCE(\`10月\`, 0)) as oct_amount,
    SUM(COALESCE(\`11月\`, 0)) as nov_amount,
    SUM(COALESCE(\`12月\`, 0)) as dec_amount
FROM 新增表 
WHERE 年份 >= 2024
GROUP BY 年份
ORDER BY 年份
" "test-data/snapshots/monthly_summary_baseline.json"

# 10. 捕获跨表验证快照
echo -e "${BLUE}📸 捕获跨表验证快照...${NC}"
execute_query_to_json "
SELECT 
    a.债权人,
    a.债务人,
    a.年份,
    a.金额 as debt_amount,
    COALESCE(d.处置总额, 0) as disposal_amount,
    COALESCE(i.期末余额, 0) as impairment_amount,
    (a.金额 - COALESCE(d.处置总额, 0)) as remaining_balance
FROM 新增表 a
LEFT JOIN (
    SELECT 债权人, 债务人, 年份, SUM(处置总额) as 处置总额
    FROM 处置表 
    GROUP BY 债权人, 债务人, 年份
) d ON a.债权人 = d.债权人 AND a.债务人 = d.债务人 AND a.年份 = d.年份
LEFT JOIN (
    SELECT 债权人, 债务人, 年份, SUM(期末余额) as 期末余额
    FROM 减值准备表 
    GROUP BY 债权人, 债务人, 年份
) i ON a.债权人 = i.债权人 AND a.债务人 = i.债务人 AND a.年份 = i.年份
WHERE a.年份 >= 2024
ORDER BY a.债权人, a.债务人, a.年份
LIMIT 100
" "test-data/snapshots/cross_table_validation_baseline.json"

# 4. 生成一致性检查快照
echo -e "${BLUE}📸 生成一致性检查快照...${NC}"

# 获取表记录数和总金额
DEBT_COUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COUNT(*) FROM 新增表" 2>/dev/null || echo "0")
DISPOSAL_COUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COUNT(*) FROM 处置表" 2>/dev/null || echo "0")
IMPAIRMENT_COUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COUNT(*) FROM 减值准备表" 2>/dev/null || echo "0")
LITIGATION_COUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COUNT(*) FROM 诉讼表" 2>/dev/null || echo "0")
NON_LITIGATION_COUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COUNT(*) FROM 非诉讼表" 2>/dev/null || echo "0")
SUMMARY_COUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COUNT(*) FROM 汇总表" 2>/dev/null || echo "0")
RISK_COUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COUNT(*) FROM 风险准备金表" 2>/dev/null || echo "0")

DEBT_AMOUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COALESCE(SUM(金额), 0) FROM 新增表" 2>/dev/null || echo "0")
DISPOSAL_AMOUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COALESCE(SUM(处置总额), 0) FROM 处置表" 2>/dev/null || echo "0")
IMPAIRMENT_AMOUNT=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} --skip-ssl -sN -e "SELECT COALESCE(SUM(期末余额), 0) FROM 减值准备表" 2>/dev/null || echo "0")

cat > test-data/snapshots/consistency_check_baseline.json << EOF
{
  "id": "consistency_check_baseline",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S)",
  "data": {
    "tableCounts": {
      "新增表": ${DEBT_COUNT},
      "处置表": ${DISPOSAL_COUNT},
      "减值准备表": ${IMPAIRMENT_COUNT},
      "诉讼表": ${LITIGATION_COUNT},
      "非诉讼表": ${NON_LITIGATION_COUNT},
      "汇总表": ${SUMMARY_COUNT},
      "风险准备金表": ${RISK_COUNT}
    },
    "totalAmounts": {
      "debt_amount": ${DEBT_AMOUNT},
      "disposal_amount": ${DISPOSAL_AMOUNT},
      "impairment_amount": ${IMPAIRMENT_AMOUNT}
    },
    "consistencyScore": 100
  },
  "metadata": {
    "version": "1.0.0",
    "description": "真实数据一致性检查快照（所有表）",
    "createdBy": "capture_script",
    "source": "database_aggregation"
  }
}
EOF

echo -e "${GREEN}✅ 一致性检查快照已保存${NC}"

# 显示结果
echo ""
echo -e "${BLUE}📋 快照捕获完成！${NC}"
echo -e "${YELLOW}📊 数据统计:${NC}"
echo "- 新增表记录数: ${DEBT_COUNT}"
echo "- 处置表记录数: ${DISPOSAL_COUNT}"
echo "- 减值准备表记录数: ${IMPAIRMENT_COUNT}"
echo "- 诉讼表记录数: ${LITIGATION_COUNT}"
echo "- 非诉讼表记录数: ${NON_LITIGATION_COUNT}"
echo "- 汇总表记录数: ${SUMMARY_COUNT}"
echo "- 风险准备金表记录数: ${RISK_COUNT}"
echo ""
echo "- 总债权金额: ${DEBT_AMOUNT}"
echo "- 总处置金额: ${DISPOSAL_AMOUNT}"
echo "- 总减值金额: ${IMPAIRMENT_AMOUNT}"

echo ""
echo -e "${YELLOW}📸 已生成的快照文件:${NC}"
echo "1. 新增表_baseline.json - 新增表完整数据快照"
echo "2. 处置表_baseline.json - 处置表完整数据快照"
echo "3. 减值准备表_baseline.json - 减值准备表完整数据快照"
echo "4. 汇总表_baseline.json - 汇总表完整数据快照"
echo "5. 诉讼表_baseline.json - 诉讼表完整数据快照"
echo "6. 非诉讼表_baseline.json - 非诉讼表完整数据快照"
echo "7. 风险准备金表_baseline.json - 风险准备金表完整数据快照"
echo "8. debt_statistics_baseline.json - 债权统计汇总快照"
echo "9. monthly_summary_baseline.json - 月度汇总快照"
echo "10. cross_table_validation_baseline.json - 跨表验证快照"
echo "11. consistency_check_baseline.json - 一致性检查快照"

echo ""
echo -e "${YELLOW}💡 下一步操作:${NC}"
echo "1. 查看快照内容: cat test-data/snapshots/新增表_baseline.json"
echo "2. 验证快照: ./scripts/manage-snapshots.sh verify"
echo "3. 列出快照: ./scripts/manage-snapshots.sh list"
echo "4. 修改代码后可以用这些快照验证一致性"
echo "5. 查看特定表快照: cat test-data/snapshots/处置表_baseline.json"