#!/bin/bash

# 本地开发环境启动脚本
# 使用方法: ./scripts/start-local.sh

echo "🚀 启动本地开发环境..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查必要的工具
check_requirements() {
    echo "📋 检查环境要求..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        echo -e "${RED}❌ Java未安装${NC}"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        echo -e "${RED}❌ Maven未安装${NC}"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js未安装${NC}"
        exit 1
    fi
    
    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        echo -e "${YELLOW}⚠️  MySQL客户端未安装，请确保MySQL服务器正在运行${NC}"
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 检查MySQL连接
check_mysql() {
    echo "🔍 检查MySQL连接..."
    
    # 尝试连接MySQL
    mysql -h localhost -u root -p"Zlb&198838" -e "SELECT 1" &> /dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ MySQL连接成功${NC}"
        
        # 检查数据库是否存在
        echo "📊 检查数据库..."
        mysql -h localhost -u root -p"Zlb&198838" -e "SHOW DATABASES;" | grep -q "overdue_debt_db"
        if [ $? -ne 0 ]; then
            echo -e "${YELLOW}⚠️  数据库 overdue_debt_db 不存在，请先创建数据库${NC}"
        fi
    else
        echo -e "${RED}❌ 无法连接到MySQL，请确保MySQL服务正在运行${NC}"
        echo "提示：使用以下命令启动MySQL："
        echo "  macOS: brew services start mysql"
        echo "  Linux: sudo systemctl start mysql"
        exit 1
    fi
}

# 启动后端
start_backend() {
    echo "🔧 启动后端服务..."
    
    cd api-gateway
    
    # 编译项目
    echo "📦 编译后端项目..."
    mvn clean compile -DskipTests
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 后端编译成功${NC}"
        
        # 启动Spring Boot应用
        echo "🚀 启动Spring Boot应用..."
        nohup mvn spring-boot:run -Dspring.profiles.active=local > ../logs/backend.log 2>&1 &
        BACKEND_PID=$!
        echo "后端进程PID: $BACKEND_PID"
        
        # 等待后端启动
        echo "⏳ 等待后端启动..."
        sleep 10
        
        # 检查后端是否启动成功
        curl -s http://localhost:8080/actuator/health > /dev/null
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 后端启动成功${NC}"
        else
            echo -e "${RED}❌ 后端启动失败，请查看日志：logs/backend.log${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ 后端编译失败${NC}"
        exit 1
    fi
    
    cd ..
}

# 启动前端
start_frontend() {
    echo "🎨 启动前端服务..."
    
    cd FinancialSystem-web
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo "📦 安装前端依赖..."
        npm install
    fi
    
    # 启动前端
    echo "🚀 启动React应用..."
    nohup npm start > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo "前端进程PID: $FRONTEND_PID"
    
    cd ..
    
    # 等待前端启动
    echo "⏳ 等待前端启动..."
    sleep 10
    
    echo -e "${GREEN}✅ 前端启动成功${NC}"
}

# 创建日志目录
mkdir -p logs

# 执行启动流程
check_requirements
check_mysql
start_backend
start_frontend

# 显示访问信息
echo ""
echo "🎉 本地开发环境启动完成！"
echo ""
echo "📌 访问地址："
echo "  前端: http://localhost:3000"
echo "  后端API: http://localhost:8080/api"
echo "  健康检查: http://localhost:8080/actuator/health"
echo ""
echo "📋 查看日志："
echo "  后端日志: tail -f logs/backend.log"
echo "  前端日志: tail -f logs/frontend.log"
echo ""
echo "🛑 停止服务："
echo "  ./scripts/stop-local.sh"
echo ""

# 保存PID到文件
echo $BACKEND_PID > logs/backend.pid
echo $FRONTEND_PID > logs/frontend.pid