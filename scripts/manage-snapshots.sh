#!/bin/bash
# 快照管理脚本

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== FinancialSystem 快照管理工具 ===${NC}"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  init      - 初始化快照（首次运行）"
    echo "  update    - 更新现有快照（业务逻辑变更后）"
    echo "  verify    - 验证快照一致性"
    echo "  list      - 列出所有快照"
    echo "  clean     - 清理过期快照"
    echo "  reset     - 重新创建所有快照"
    echo "  help      - 显示此帮助信息"
}

# 初始化快照
init_snapshots() {
    echo -e "${YELLOW}🚀 初始化快照...${NC}"
    
    mvn test -Dtest=AutoSnapshotDemo#captureBaselineSnapshots \
        -Dsnapshot.init=force \
        -Dspring.profiles.active=test
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 快照初始化成功${NC}"
    else
        echo -e "${RED}❌ 快照初始化失败${NC}"
        exit 1
    fi
}

# 更新快照
update_snapshots() {
    echo -e "${YELLOW}🔄 更新快照...${NC}"
    
    read -p "确认要更新快照吗？这会覆盖现有的基准数据 (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        mvn test -Dtest=*ConsistencyTest \
            -Dsnapshot.update=true \
            -Dspring.profiles.active=test
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 快照更新成功${NC}"
        else
            echo -e "${RED}❌ 快照更新失败${NC}"
            exit 1
        fi
    else
        echo -e "${YELLOW}⏸️ 取消更新${NC}"
    fi
}

# 验证快照
verify_snapshots() {
    echo -e "${YELLOW}🔍 验证快照一致性...${NC}"
    
    mvn test -Dtest=*ConsistencyTest \
        -Dsnapshot.verify=true \
        -Dspring.profiles.active=test
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 快照验证通过${NC}"
    else
        echo -e "${RED}❌ 发现数据不一致${NC}"
        echo -e "${YELLOW}💡 请检查 target/snapshot-diffs/ 目录查看差异报告${NC}"
        exit 1
    fi
}

# 列出快照
list_snapshots() {
    echo -e "${YELLOW}📋 快照列表:${NC}"
    
    if [ -d "test-data/snapshots" ]; then
        find test-data/snapshots -name "*.json" -exec basename {} .json \; | sort
        
        echo ""
        echo -e "${BLUE}快照统计:${NC}"
        echo "总数: $(find test-data/snapshots -name "*.json" | wc -l)"
        echo "大小: $(du -sh test-data/snapshots 2>/dev/null | cut -f1)"
    else
        echo -e "${YELLOW}⚠️ 快照目录不存在${NC}"
    fi
}

# 清理过期快照
clean_snapshots() {
    echo -e "${YELLOW}🧹 清理过期快照...${NC}"
    
    read -p "保留最近几天的快照？ (默认7天): " days
    days=${days:-7}
    
    if [ -d "test-data/snapshots" ]; then
        find test-data/snapshots -name "*.json" -mtime +$days -delete
        echo -e "${GREEN}✅ 已清理 ${days} 天前的快照${NC}"
    else
        echo -e "${YELLOW}⚠️ 快照目录不存在${NC}"
    fi
}

# 重置快照
reset_snapshots() {
    echo -e "${YELLOW}🔄 重置所有快照...${NC}"
    
    read -p "确认要删除并重新创建所有快照吗？ (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        # 删除现有快照
        if [ -d "test-data/snapshots" ]; then
            rm -rf test-data/snapshots/*
            echo -e "${YELLOW}🗑️ 已删除现有快照${NC}"
        fi
        
        # 重新创建
        init_snapshots
    else
        echo -e "${YELLOW}⏸️ 取消重置${NC}"
    fi
}

# 主逻辑
case $1 in
    init)
        init_snapshots
        ;;
    update)
        update_snapshots
        ;;
    verify)
        verify_snapshots
        ;;
    list)
        list_snapshots
        ;;
    clean)
        clean_snapshots
        ;;
    reset)
        reset_snapshots
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        echo -e "${RED}❌ 请指定操作${NC}"
        show_help
        exit 1
        ;;
    *)
        echo -e "${RED}❌ 未知选项: $1${NC}"
        show_help
        exit 1
        ;;
esac