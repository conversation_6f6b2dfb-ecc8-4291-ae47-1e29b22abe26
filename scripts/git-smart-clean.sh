#!/bin/bash
# Git智能清理脚本 - 深度清理工作区和仓库

# 颜色定义
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🧹 Git智能清理系统${NC}"
echo "================================"

# 清理级别
CLEAN_LEVEL=${1:-2}
if [ "$1" = "--help" ]; then
    echo "使用方法: ./git-smart-clean.sh [级别]"
    echo "级别："
    echo "  1 - 基础清理（安全）"
    echo "  2 - 标准清理（默认）"
    echo "  3 - 深度清理（谨慎）"
    exit 0
fi

# 统计信息
INITIAL_SIZE=$(du -sh .git 2>/dev/null | cut -f1)

echo -e "${YELLOW}📊 清理前状态：${NC}"
echo "  Git仓库大小: $INITIAL_SIZE"
echo "  当前分支: $(git branch --show-current)"
echo ""

# ========== 级别1：基础清理 ==========
echo -e "${CYAN}🔹 级别1：基础清理${NC}"

# 1. 保存当前工作
if [ -n "$(git status --porcelain)" ]; then
    echo "  💾 保存当前工作..."
    git stash push -m "Smart clean backup - $(date '+%Y-%m-%d %H:%M:%S')" --include-untracked
    echo -e "  ${GREEN}✓ 已备份当前工作${NC}"
fi

# 2. 清理未跟踪文件
UNTRACKED=$(git ls-files --others --exclude-standard | wc -l | tr -d ' ')
if [ "$UNTRACKED" -gt 0 ]; then
    echo "  🗑️  清理 $UNTRACKED 个未跟踪文件..."
    git clean -fd
    echo -e "  ${GREEN}✓ 已清理未跟踪文件${NC}"
fi

# 3. 垃圾回收
echo "  ♻️  执行垃圾回收..."
git gc --auto
echo -e "  ${GREEN}✓ 垃圾回收完成${NC}"

# ========== 级别2：标准清理 ==========
if [ "$CLEAN_LEVEL" -ge 2 ]; then
    echo ""
    echo -e "${CYAN}🔹 级别2：标准清理${NC}"
    
    # 4. 清理远程引用
    echo "  🌐 清理远程引用..."
    git remote prune origin 2>/dev/null || echo "  ⚠️  无法连接到origin"
    echo -e "  ${GREEN}✓ 远程引用已更新${NC}"
    
    # 5. 清理reflog
    echo "  📝 压缩reflog（保留30天）..."
    git reflog expire --expire=30.days --all
    echo -e "  ${GREEN}✓ Reflog已压缩${NC}"
    
    # 6. 清理旧stash
    OLD_STASH=$(git stash list | grep -E "WIP on|Auto" | tail -n +10 | wc -l | tr -d ' ')
    if [ "$OLD_STASH" -gt 0 ]; then
        echo "  📦 发现 $OLD_STASH 个旧stash（>10个）"
        echo "  清理旧stash？(y/n)"
        read -r response
        if [ "$response" = "y" ]; then
            # 保留最新的9个stash
            git stash list | grep -E "WIP on|Auto" | tail -n +10 | cut -d: -f1 | xargs -n1 git stash drop
            echo -e "  ${GREEN}✓ 已清理旧stash${NC}"
        fi
    fi
    
    # 7. 优化仓库
    echo "  🔧 优化Git仓库..."
    git gc --prune=now
    echo -e "  ${GREEN}✓ 仓库优化完成${NC}"
fi

# ========== 级别3：深度清理 ==========
if [ "$CLEAN_LEVEL" -ge 3 ]; then
    echo ""
    echo -e "${CYAN}🔹 级别3：深度清理${NC}"
    echo -e "${YELLOW}⚠️  警告：这将执行深度清理，可能影响性能${NC}"
    echo "  继续？(y/n)"
    read -r response
    if [ "$response" = "y" ]; then
        # 8. 清理所有reflog
        echo "  🗑️  清理所有reflog..."
        git reflog expire --expire=now --all
        echo -e "  ${GREEN}✓ Reflog完全清理${NC}"
        
        # 9. 深度垃圾回收
        echo "  ♻️  深度垃圾回收..."
        git gc --aggressive --prune=now
        echo -e "  ${GREEN}✓ 深度回收完成${NC}"
        
        # 10. 重新打包
        echo "  📦 重新打包对象..."
        git repack -a -d -f --depth=250 --window=250
        echo -e "  ${GREEN}✓ 重新打包完成${NC}"
        
        # 11. 清理.gitignore文件
        if [ -f ".gitignore" ]; then
            echo "  📄 优化.gitignore..."
            # 删除空行和注释
            sed -i.bak '/^$/d; /^#/d' .gitignore
            sort -u .gitignore -o .gitignore
            rm -f .gitignore.bak
            echo -e "  ${GREEN}✓ .gitignore已优化${NC}"
        fi
    fi
fi

# ========== 清理报告 ==========
echo ""
echo -e "${BLUE}📊 清理报告${NC}"
echo "================================"

# 计算清理后大小
FINAL_SIZE=$(du -sh .git 2>/dev/null | cut -f1)

echo "清理前: $INITIAL_SIZE"
echo "清理后: $FINAL_SIZE"

# 显示当前状态
echo ""
echo -e "${GREEN}✨ 清理完成！${NC}"
echo "当前状态："
git status --short

# 显示stash数量
STASH_COUNT=$(git stash list | wc -l | tr -d ' ')
if [ "$STASH_COUNT" -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}📦 剩余 $STASH_COUNT 个stash记录${NC}"
fi

# 建议
echo ""
echo -e "${BLUE}💡 建议：${NC}"
echo "• 定期运行此脚本保持仓库健康"
echo "• 使用级别2进行日常清理"
echo "• 每月执行一次级别3深度清理"

exit 0