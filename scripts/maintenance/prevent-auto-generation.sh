#!/bin/bash

# 防止自动生成文件夹的终极措施
# 创建占位文件来阻止目录的自动创建

PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
cd "$PROJECT_ROOT"

# 要阻止创建的目录列表
PREVENT_DIRS=(
    "api"
    "business-modules"
    "build"
    "backups"
    "shared/shared"
    "src"
)

echo "🛡️ 启用防止自动生成目录的保护措施..."

for dir in "${PREVENT_DIRS[@]}"; do
    if [ ! -e "$dir" ]; then
        # 创建一个占位文件，防止同名目录被创建
        touch "${dir}.DO_NOT_CREATE_THIS_DIR"
        echo "  创建保护文件: ${dir}.DO_NOT_CREATE_THIS_DIR"
    fi
done

echo "✅ 防护措施已启用！"
echo "💡 如果这些目录仍然被创建，请检查IDE设置和Maven配置"