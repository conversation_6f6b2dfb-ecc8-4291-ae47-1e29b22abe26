#!/bin/bash

# FinancialSystem紧急回滚脚本
# 用于快速恢复生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 获取备份时间戳
BACKUP_TIME=$(cat /tmp/backup_timestamp 2>/dev/null || echo "")
if [ -z "$BACKUP_TIME" ]; then
    log_error "无法找到备份时间戳文件"
    echo "请手动指定备份时间戳 (格式: YYYYMMDD_HHMMSS):"
    read -r BACKUP_TIME
    if [ -z "$BACKUP_TIME" ]; then
        log_error "未提供备份时间戳，退出"
        exit 1
    fi
fi

BACKUP_DIR="/backup/production_refactor_${BACKUP_TIME}"
PROJECT_DIR="/path/to/FinancialSystem"  # 请修改为实际项目路径

log_error "🚨 开始紧急回滚..."
log_info "使用备份时间戳: $BACKUP_TIME"
log_info "备份目录: $BACKUP_DIR"

# 检查备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    log_error "备份目录不存在: $BACKUP_DIR"
    exit 1
fi

# 1. 立即停止所有服务
log_info "步骤1: 停止所有服务..."
sudo systemctl stop financial-system 2>/dev/null || log_warn "应用服务停止失败或未运行"
sudo systemctl stop cron 2>/dev/null || log_warn "定时任务停止失败或未运行"

# 等待服务完全停止
sleep 5

# 2. 恢复数据库
log_info "步骤2: 恢复数据库..."

# 恢复逾期债权数据库
if [ -f "${BACKUP_DIR}/overdue_debt_backup.sql" ]; then
    log_info "恢复逾期债权数据库..."
    mysql -h *********** -u laoshu198838 -p逾期债权数据库 < ${BACKUP_DIR}/overdue_debt_backup.sql
    if [ $? -eq 0 ]; then
        log_info "✅ 逾期债权数据库恢复成功"
    else
        log_error "❌ 逾期债权数据库恢复失败"
    fi
else
    log_error "找不到逾期债权数据库备份文件"
fi

# 恢复用户系统数据库
if [ -f "${BACKUP_DIR}/user_system_backup.sql" ]; then
    log_info "恢复用户系统数据库..."
    mysql -h *********** -u laoshu198838 -puser_system < ${BACKUP_DIR}/user_system_backup.sql
    if [ $? -eq 0 ]; then
        log_info "✅ 用户系统数据库恢复成功"
    else
        log_error "❌ 用户系统数据库恢复失败"
    fi
else
    log_warn "找不到用户系统数据库备份文件"
fi

# 3. 恢复代码
log_info "步骤3: 恢复代码..."
cd $PROJECT_DIR

# 恢复到备份标签
if git tag | grep -q "production-backup-${BACKUP_TIME}"; then
    git reset --hard "production-backup-${BACKUP_TIME}"
    log_info "✅ 代码恢复到备份标签"
else
    log_warn "找不到备份标签，尝试从备份文件恢复"
    # 如果有代码备份文件，可以在这里添加恢复逻辑
fi

# 4. 清理编译缓存并重新编译
log_info "步骤4: 重新编译..."
mvn clean > /dev/null 2>&1
if mvn compile -DskipTests -q; then
    log_info "✅ 编译成功"
else
    log_error "❌ 编译失败"
    # 即使编译失败也继续，可能是环境问题
fi

# 5. 启动系统
log_info "步骤5: 启动系统..."
sudo systemctl start financial-system

# 等待启动
log_info "等待系统启动..."
sleep 30

# 检查启动状态
if systemctl is-active --quiet financial-system; then
    log_info "✅ 应用服务启动成功"
else
    log_error "❌ 应用服务启动失败"
    log_info "检查服务状态:"
    systemctl status financial-system --no-pager
fi

# 6. 启动定时任务
log_info "步骤6: 启动定时任务..."
sudo systemctl start cron

if systemctl is-active --quiet cron; then
    log_info "✅ 定时任务启动成功"
else
    log_error "❌ 定时任务启动失败"
fi

# 7. 基本功能验证
log_info "步骤7: 基本功能验证..."

# 等待系统完全启动
sleep 30

# 检查健康状态
for i in {1..5}; do
    if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
        log_info "✅ 系统健康检查通过"
        break
    else
        log_warn "健康检查失败，重试 $i/5"
        sleep 10
    fi
    
    if [ $i -eq 5 ]; then
        log_error "❌ 系统健康检查失败"
        log_info "请手动检查系统状态"
    fi
done

# 测试数据库连接
log_info "测试数据库连接..."
if mysql -h *********** -u laoshu198838 -p逾期债权数据库 -e "SELECT 1;" > /dev/null 2>&1; then
    log_info "✅ 主数据库连接正常"
else
    log_error "❌ 主数据库连接失败"
fi

# 8. 清理临时文件
log_info "步骤8: 清理临时文件..."
rm -f /tmp/backup_timestamp

log_info "🎉 紧急回滚完成！"
log_info ""
log_info "请验证以下功能："
log_info "1. 用户登录功能"
log_info "2. API接口访问"
log_info "3. 定时任务执行"
log_info "4. 数据完整性"
log_info ""
log_info "如果发现问题，请检查日志："
log_info "- 应用日志: /var/log/financial-system/application.log"
log_info "- 系统日志: journalctl -u financial-system"
log_info ""
log_warn "回滚完成后，请通知相关人员系统已恢复"
