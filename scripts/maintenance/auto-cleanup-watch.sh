#!/bin/bash

# 自动监控和清理脚本
# 监控自动生成目录的出现，如果检测到则自动清理

PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
CLEANUP_SCRIPT="$PROJECT_ROOT/scripts/maintenance/cleanup-auto-generated.sh"

# 需要监控的目录列表（添加api目录）
WATCH_DIRS=(
    "api"
    "business-modules"
    "build"
    "backups"
    "shared/shared" 
    "src"
)

echo "🔍 自动清理监控启动..."
echo "监控目录: ${WATCH_DIRS[*]}"
echo "项目路径: $PROJECT_ROOT"
echo ""

cd "$PROJECT_ROOT"

# 检查函数
check_and_cleanup() {
    local found_auto_generated=false
    
    for dir in "${WATCH_DIRS[@]}"; do
        if [ -d "$dir" ]; then
            echo "⚠️  检测到自动生成的目录: $dir"
            found_auto_generated=true
        fi
    done
    
    # 检查.iml文件
    if find . -name "*.iml" -type f | grep -q .; then
        echo "⚠️  检测到.iml文件"
        found_auto_generated=true
    fi
    
    # 检查target目录
    if find . -name "target" -type d | grep -q .; then
        echo "⚠️  检测到target目录"
        found_auto_generated=true
    fi
    
    if [ "$found_auto_generated" = true ]; then
        echo "🧹 执行自动清理..."
        if [ -x "$CLEANUP_SCRIPT" ]; then
            "$CLEANUP_SCRIPT"
        else
            echo "❌ 清理脚本不存在或不可执行: $CLEANUP_SCRIPT"
        fi
        echo ""
    fi
}

# 如果传入参数为 "once"，则只执行一次检查
if [ "$1" = "once" ]; then
    check_and_cleanup
    exit 0
fi

# 持续监控模式
while true; do
    check_and_cleanup
    echo "😴 等待60秒后继续监控..."
    sleep 60
done