#!/bin/bash

# 清理自动生成的目录和文件脚本
# 用于删除编译、构建、IDE和备份过程中自动生成的目录和文件

echo "🧹 开始清理自动生成的目录和文件..."

PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
cd "$PROJECT_ROOT"

# 要清理的目录列表（移除database-migration，它包含重要业务脚本）
AUTO_GENERATED_DIRS=(
    "api"
    "business-modules" 
    "build"
    "backups"
    "shared/shared"
    "src"
    "shared/data-processing/bin"
)

# 要清理的文件列表
AUTO_GENERATED_FILES=(
    ".idea/AugmentWebviewStateStore.xml"
    ".idea/workspace.xml"
    ".idea/tasks.xml"
    "package-lock.json"
    ".DS_Store"
    "var/log/financial-system-*.log"
)

# 清理目录
echo "📁 清理自动生成的目录..."
for dir in "${AUTO_GENERATED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "  删除目录: $dir"
        rm -rf "$dir"
    fi
done

# 清理文件
echo "📄 清理自动生成的文件..."
for file in "${AUTO_GENERATED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  删除文件: $file"
        rm -f "$file"
    fi
done

# 清理 target 目录（Maven编译输出）
echo "🎯 清理Maven target目录..."
find . -name "target" -type d -exec rm -rf {} + 2>/dev/null || true

# 清理 .iml 文件
echo "🔧 清理IDE模块文件..."
find . -name "*.iml" -type f -delete 2>/dev/null || true

# 清理同步冲突文件
echo "🔄 清理同步冲突文件..."
find . -name "*.sync-conflict-*" -type f -delete 2>/dev/null || true

# 清理临时文件
echo "🗑️ 清理临时文件..."
find . -name "*.tmp" -type f -delete 2>/dev/null || true
find . -name "*.bak" -type f -delete 2>/dev/null || true
find . -name "*~" -type f -delete 2>/dev/null || true

# 清理重复的日志文件
echo "📝 清理重复的日志文件..."
rm -f var/log/financial-system-*.log 2>/dev/null || true

# 清理旧的日志文件（保留最新的）
echo "📝 清理旧的日志文件..."
find . -name "*.log" -type f -mtime +7 -delete 2>/dev/null || true

# 清理Maven编译产物
echo "🎯 清理Maven target目录..."
find . -name "target" -type d -exec rm -rf {} + 2>/dev/null || true

# 清理系统文件
echo "🗑️ 清理系统文件..."
find . -name ".DS_Store" -type f -delete 2>/dev/null || true

echo "✅ 清理完成！"

# 显示清理后的项目根目录结构
echo ""
echo "📊 清理后的项目结构:"
ls -la | grep -v ".git"

# 显示清理统计
echo ""
echo "📈 清理统计:"
echo "- 已清理Maven编译产物"
echo "- 已清理重复日志文件"
echo "- 已清理系统缓存文件"
echo "- 已清理临时文件"

echo ""
echo "💡 提示: 如果这些目录再次出现，请检查:"
echo "   1. IDE配置中是否还有对这些目录的引用"
echo "   2. Maven配置是否正确"
echo "   3. .gitignore是否生效"