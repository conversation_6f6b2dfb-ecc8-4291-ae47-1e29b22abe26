#!/bin/bash

# FinancialSystem Maven构建产物清理脚本
# 用途: 清理Maven编译过程中在根目录生成的无用模块目录
# 作者: laoshu198838
# 日期: 2025-06-19

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在项目根目录
check_project_root() {
    if [ ! -f "pom.xml" ]; then
        log_error "请在项目根目录执行此脚本"
        exit 1
    fi
    
    if [ ! -d "api-gateway" ] || [ ! -d "shared" ]; then
        log_error "项目结构不正确，请确认在FinancialSystem根目录"
        exit 1
    fi
    
    log_info "项目根目录检查通过"
}

# 定义需要清理的目录列表
# 这些目录是Maven编译过程中在根目录生成的，但实际模块已移动到其他位置
CLEANUP_DIRS=(
    "common"
    "data-access" 
    "data-processing"
    "kingdee"
    "account"
    "audit"
    "webservice"
    "mysql_data"
    "treasury"
    "integrations"
)

# 检查目录是否为Maven生成的无用目录
is_maven_artifact_dir() {
    local dir=$1
    
    # 检查目录是否存在
    if [ ! -d "$dir" ]; then
        return 1
    fi
    
    # 检查是否只包含target目录（Maven编译产物）
    if [ -d "$dir/target" ] && [ ! -f "$dir/pom.xml" ]; then
        # 进一步检查target目录内容
        if [ -d "$dir/target/classes" ] || [ -d "$dir/target/generated-sources" ] || [ -d "$dir/target/test-classes" ]; then
            return 0  # 是Maven生成的无用目录
        fi
    fi
    
    return 1  # 不是Maven生成的无用目录
}

# 清理单个目录
cleanup_directory() {
    local dir=$1
    
    if is_maven_artifact_dir "$dir"; then
        log_warning "发现Maven生成的无用目录: $dir"
        
        # 显示目录内容
        echo "目录内容:"
        ls -la "$dir" | head -10
        
        # 询问是否删除
        if [ "$AUTO_CONFIRM" = "true" ]; then
            confirm="y"
        else
            read -p "是否删除此目录? (y/N): " confirm
        fi
        
        if [[ $confirm =~ ^[Yy]$ ]]; then
            rm -rf "$dir"
            log_success "已删除目录: $dir"
            return 0
        else
            log_info "跳过目录: $dir"
            return 1
        fi
    else
        log_info "目录 $dir 不是Maven生成的无用目录，跳过"
        return 1
    fi
}

# 验证项目结构
verify_project_structure() {
    log_info "验证项目结构..."
    
    # 检查pom.xml中声明的模块是否都存在
    if command -v xmllint >/dev/null 2>&1; then
        modules=$(xmllint --xpath "//module/text()" pom.xml 2>/dev/null || true)
    else
        # 如果没有xmllint，使用grep
        modules=$(grep -o '<module>[^<]*</module>' pom.xml | sed 's/<module>//g' | sed 's/<\/module>//g' || true)
    fi
    
    if [ -n "$modules" ]; then
        echo "$modules" | while read -r module; do
            if [ -n "$module" ] && [ ! -f "$module/pom.xml" ]; then
                log_warning "模块 $module 在pom.xml中声明但不存在"
            else
                log_success "模块 $module 存在"
            fi
        done
    fi
}

# 显示清理统计
show_cleanup_stats() {
    local cleaned_count=$1
    local total_count=$2
    
    echo ""
    echo "=================================="
    echo "清理统计:"
    echo "总检查目录数: $total_count"
    echo "清理目录数: $cleaned_count"
    echo "跳过目录数: $((total_count - cleaned_count))"
    echo "=================================="
}

# 主函数
main() {
    echo "=================================="
    echo "FinancialSystem Maven构建产物清理工具"
    echo "=================================="
    
    # 检查命令行参数
    AUTO_CONFIRM=false
    if [ "$1" = "--auto" ] || [ "$1" = "-y" ]; then
        AUTO_CONFIRM=true
        log_info "自动确认模式已启用"
    fi
    
    # 检查项目根目录
    check_project_root
    
    # 开始清理
    log_info "开始清理Maven生成的无用目录..."
    
    cleaned_count=0
    total_count=${#CLEANUP_DIRS[@]}
    
    for dir in "${CLEANUP_DIRS[@]}"; do
        if cleanup_directory "$dir"; then
            ((cleaned_count++))
        fi
    done
    
    # 显示统计信息
    show_cleanup_stats $cleaned_count $total_count
    
    # 验证项目结构
    verify_project_structure
    
    # 建议
    echo ""
    log_info "建议:"
    echo "1. 运行 'mvn clean compile' 验证项目编译正常"
    echo "2. 运行 'mvn spring-boot:run -pl api-gateway' 验证项目启动正常"
    echo "3. 定期运行此脚本防止无用目录重新出现"
    
    log_success "清理完成！"
}

# 执行主函数
main "$@"
