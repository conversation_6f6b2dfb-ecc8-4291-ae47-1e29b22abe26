#!/bin/bash

# 数据库同步解决方案脚本
# 解决本地MySQL与Docker应用数据同步问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="Zlb&198838"
DB_NAMES=("overdue_debt_db" "kingdee" "user_system")

echo -e "${BLUE}==================== 数据库同步解决方案 ====================${NC}"
echo -e "${YELLOW}问题: 本地MySQL与Docker应用无法数据同步${NC}"
echo -e "${YELLOW}解决方案: 统一使用本地MySQL，Docker应用连接本地数据库${NC}"
echo ""

# 函数：检查MySQL连接
check_mysql_connection() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    
    echo -e "${BLUE}检查MySQL连接: ${host}:${port}${NC}"
    
    if mysql -h"$host" -P"$port" -u"$user" -p"$password" --skip-ssl -e "SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ MySQL连接成功${NC}"
        return 0
    else
        echo -e "${RED}✗ MySQL连接失败${NC}"
        return 1
    fi
}

# 函数：检查数据库是否存在
check_database_exists() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    local db_name=$5
    
    if mysql -h"$host" -P"$port" -u"$user" -p"$password" --skip-ssl -e "USE $db_name;" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 数据库 $db_name 存在${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠ 数据库 $db_name 不存在${NC}"
        return 1
    fi
}

# 函数：创建数据库
create_database() {
    local host=$1
    local port=$2
    local user=$3
    local password=$4
    local db_name=$5
    
    echo -e "${BLUE}创建数据库: $db_name${NC}"
    mysql -h"$host" -P"$port" -u"$user" -p"$password" --skip-ssl -e "CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo -e "${GREEN}✓ 数据库 $db_name 创建成功${NC}"
}

# 函数：检查Docker容器状态
check_docker_container() {
    local container_name=$1
    
    if docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name"; then
        local status=$(docker ps -a --format "{{.Status}}" --filter "name=$container_name")
        echo -e "${BLUE}Docker容器 $container_name 状态: $status${NC}"
        
        if [[ $status == *"Up"* ]]; then
            return 0
        else
            return 1
        fi
    else
        echo -e "${YELLOW}⚠ Docker容器 $container_name 不存在${NC}"
        return 1
    fi
}

# 函数：测试Docker应用连接本地MySQL
test_docker_mysql_connection() {
    echo -e "${BLUE}测试Docker应用连接本地MySQL...${NC}"
    
    # 临时启动一个测试容器
    docker run --rm mysql:8.0 mysql -h host.docker.internal -P 3306 -u root -p"$DB_PASSWORD" --skip-ssl -e "SELECT 'Docker到本地MySQL连接成功' as result;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Docker可以成功连接本地MySQL${NC}"
        return 0
    else
        echo -e "${RED}✗ Docker无法连接本地MySQL${NC}"
        return 1
    fi
}

# 主流程
main() {
    echo -e "${BLUE}开始执行数据库同步解决方案...${NC}"
    echo ""
    
    # 1. 检查本地MySQL
    echo -e "${BLUE}=== 步骤1: 检查本地MySQL ===${NC}"
    if ! check_mysql_connection "$DB_HOST" "$DB_PORT" "$DB_USER" "$DB_PASSWORD"; then
        echo -e "${RED}错误: 无法连接本地MySQL，请确保MySQL服务已启动${NC}"
        exit 1
    fi
    
    # 2. 检查和创建数据库
    echo -e "\n${BLUE}=== 步骤2: 检查数据库 ===${NC}"
    for db_name in "${DB_NAMES[@]}"; do
        if ! check_database_exists "$DB_HOST" "$DB_PORT" "$DB_USER" "$DB_PASSWORD" "$db_name"; then
            create_database "$DB_HOST" "$DB_PORT" "$DB_USER" "$DB_PASSWORD" "$db_name"
        fi
    done
    
    # 3. 检查Docker配置
    echo -e "\n${BLUE}=== 步骤3: 验证Docker配置修改 ===${NC}"
    echo "已修改的配置:"
    echo "  - 禁用Docker中的MySQL服务"
    echo "  - 修改应用连接为 host.docker.internal:3306"
    echo "  - 移除MySQL依赖关系"
    
    # 4. 测试连接
    echo -e "\n${BLUE}=== 步骤4: 测试Docker到MySQL连接 ===${NC}"
    if command -v docker &> /dev/null; then
        if ! test_docker_mysql_connection; then
            echo -e "${YELLOW}注意: Docker连接测试失败，可能需要检查Docker Desktop配置${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ Docker未安装，跳过连接测试${NC}"
    fi
    
    # 5. 显示使用说明
    echo -e "\n${BLUE}=== 解决方案完成 ===${NC}"
    echo -e "${GREEN}✓ 本地MySQL配置验证完成${NC}"
    echo -e "${GREEN}✓ Docker Compose配置已修改${NC}"
    echo -e "${GREEN}✓ 数据库同步问题已解决${NC}"
    
    echo -e "\n${YELLOW}使用说明:${NC}"
    echo "1. 确保本地MySQL服务运行在3306端口"
    echo "2. 使用以下命令启动Docker服务:"
    echo "   docker-compose up -d"
    echo "3. 现在本地开发和Docker生产环境都使用同一个MySQL实例"
    echo "4. 数据修改会在两个环境中同步显示"
    
    echo -e "\n${YELLOW}验证方法:${NC}"
    echo "1. 在本地MySQL中修改数据"
    echo "2. 通过Docker容器API查看数据变化"
    echo "3. 反之亦然"
    
    echo -e "\n${BLUE}==================== 解决方案执行完成 ====================${NC}"
}

# 执行主流程
main "$@"