# FinancialSystem Docker镜像手动传输指南

echo "🚀 FinancialSystem Docker镜像手动传输指南"
echo "============================================"
echo ""
echo "由于网络连接不稳定，建议使用U盘手动传输文件"
echo ""

echo "📁 需要传输的文件列表："
echo "   mysql-8.0-amd64.tar (444MB)"
echo "   nginx-alpine-amd64.tar (41MB)"
echo "   node-18-alpine-amd64.tar (86MB)"
echo "   openjdk-21-jdk-amd64.tar (501MB)"
echo "   maven-3.9-temurin-21-amd64.tar (460MB)"
echo "   checksums.md5"
echo "   load-docker-images.sh"
echo "   verify-checksums.sh"
echo "   Docker镜像传输部署指南.md"
echo "   手动部署步骤指南.md"
echo "   README.txt"
echo ""

echo "📋 手动传输步骤："
echo "1. 将以上文件复制到U盘"
echo "2. 将U盘插入Linux服务器"
echo "3. 在Linux服务器上执行："
echo "   cd /home/<USER>/下载/"
echo "   cp /media/usb/docker-images/* ."
echo ""

echo "🔧 如果需要通过网络传输，请手动执行以下命令："
echo ""
echo "scp ~/Desktop/docker-images/mysql-8.0-amd64.tar admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/nginx-alpine-amd64.tar admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/node-18-alpine-amd64.tar admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/openjdk-21-jdk-amd64.tar admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/maven-3.9-temurin-21-amd64.tar admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/checksums.md5 admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/load-docker-images.sh admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/verify-checksums.sh admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/Docker镜像传输部署指南.md admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/手动部署步骤指南.md admin@10.25.1.85:~/下载/"
echo "scp ~/Desktop/docker-images/README.txt admin@10.25.1.85:~/下载/"
echo ""

echo "📖 下一步："
echo "1. 传输完成后，SSH连接到服务器："
echo "   ssh admin@10.25.1.85"
echo ""
echo "2. 查看手动部署指南："
echo "   cat /home/<USER>/下载/手动部署步骤指南.md"
echo ""
echo "3. 按照指南逐步执行部署"
