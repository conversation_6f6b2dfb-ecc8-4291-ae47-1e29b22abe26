#!/bin/bash

# FinancialSystem 华为云镜像拉取和部署自动化脚本
# 在Linux服务器上执行此脚本

set -e  # 遇到错误时停止执行

echo "🚀 FinancialSystem 华为云镜像拉取和部署"
echo "========================================"
echo ""

# 检查Docker是否运行
echo "📋 检查Docker服务状态..."
if ! systemctl is-active --quiet docker; then
    echo "启动Docker服务..."
    sudo systemctl start docker
fi

# 配置华为云镜像加速器
echo "📋 配置华为云Docker镜像加速器..."
sudo mkdir -p /etc/docker

# 备份现有配置
if [ -f /etc/docker/daemon.json ]; then
    sudo cp /etc/docker/daemon.json /etc/docker/daemon.json.backup
fi

# 创建新配置
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "registry-mirrors": [
    "https://repo.huaweicloud.com"
  ]
}
EOF

echo "重启Docker服务..."
sudo systemctl daemon-reload
sudo systemctl restart docker

# 等待Docker服务完全启动
sleep 5

echo ""
echo "📋 开始拉取Docker镜像..."

# 定义镜像列表
declare -a images=(
    "mysql:8.0"
    "nginx:alpine" 
    "node:18-alpine"
    "openjdk:21-jdk"
    "maven:3.9-eclipse-temurin-21"
)

# 拉取每个镜像
for image in "${images[@]}"; do
    echo ""
    echo "正在拉取: $image"
    if docker pull "$image"; then
        echo "✅ $image 拉取成功"
    else
        echo "❌ $image 拉取失败，尝试从华为云直接拉取..."
        huawei_image="repo.huaweicloud.com/library/$image"
        if docker pull "$huawei_image"; then
            docker tag "$huawei_image" "$image"
            echo "✅ $image 从华为云拉取并重新标记成功"
        else
            echo "❌ $image 拉取完全失败"
            exit 1
        fi
    fi
done

echo ""
echo "📋 验证镜像拉取结果..."
docker images

echo ""
echo "📋 开始部署FinancialSystem..."

# 检查部署目录是否存在
DEPLOY_DIR="/home/<USER>/下载/FinancialSystem-Production-Deploy"
if [ ! -d "$DEPLOY_DIR" ]; then
    echo "❌ 部署目录不存在: $DEPLOY_DIR"
    echo "请确保FinancialSystem部署文件已上传到服务器"
    exit 1
fi

cd "$DEPLOY_DIR"

# 停止现有容器
echo "停止现有容器..."
docker compose down

# 启动服务
echo "启动FinancialSystem服务..."
docker compose up -d

# 等待容器启动
sleep 10

echo ""
echo "📋 检查容器状态..."
docker ps -a

echo ""
echo "📋 检查服务日志..."
docker compose logs --tail=20

echo ""
echo "✅ 部署完成！"
echo ""
echo "🔍 验证步骤："
echo "1. 检查容器状态: docker ps"
echo "2. 查看服务日志: docker compose logs"
echo "3. 测试Web访问: curl -I http://localhost"
echo "4. 检查端口监听: netstat -tlnp | grep -E ':(80|3306|8080)'"
echo ""
echo "如有问题，请查看详细日志: docker compose logs <service_name>"
