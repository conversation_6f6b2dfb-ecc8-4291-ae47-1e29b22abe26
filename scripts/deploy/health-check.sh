#!/bin/bash
# 检查CI/CD状态脚本

set -e

echo "🔍 检查FinancialSystem CI/CD状态..."

# 检查本地Git状态
echo "📊 本地Git状态:"
git status --porcelain
echo "当前分支: $(git branch --show-current)"
echo "最新提交: $(git log -1 --oneline)"
echo

# 检查Linux服务器状态
echo "🖥️  Linux服务器状态:"
ssh admin@10.25.1.85 << 'REMOTE_EOF'
    echo "Webhook服务状态: $(systemctl is-active financial-webhook 2>/dev/null || echo '未运行')"
    echo "部署目录: $(ls -la /home/<USER>/下载/FinancialSystem-Production-Deploy | head -5)"
    echo "系统负载: $(uptime)"
REMOTE_EOF

echo "✅ 状态检查完成"
