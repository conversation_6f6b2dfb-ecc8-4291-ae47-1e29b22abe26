#!/bin/bash
# Git安全切换分支脚本 - 在切换前自动保存工作区

# 颜色定义
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${RED}错误：请提供要切换的分支名${NC}"
    echo "使用方法: ./git-safe-checkout.sh <branch-name>"
    exit 1
fi

TARGET=$1
CURRENT_BRANCH=$(git symbolic-ref --short HEAD 2>/dev/null || echo "detached")

echo -e "${BLUE}🔄 安全切换到: $TARGET${NC}"

# 检查是否有未提交的更改
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${YELLOW}📦 检测到未提交的更改，自动保存...${NC}"
    
    # 显示当前状态
    git status --short
    
    # 创建stash
    STASH_MSG="Auto-save from $CURRENT_BRANCH before checkout to $TARGET - $(date '+%Y-%m-%d %H:%M:%S')"
    git stash push -m "$STASH_MSG" --include-untracked
    
    echo -e "${GREEN}✓ 已保存到stash: $STASH_MSG${NC}"
    
    # 记录stash信息
    echo "$STASH_MSG" > .git/LAST_AUTO_STASH
fi

# 执行切换
echo -e "${YELLOW}➜ 切换分支...${NC}"
if git checkout "$TARGET"; then
    echo -e "${GREEN}✓ 成功切换到: $TARGET${NC}"
    
    # 如果有自动stash，提醒用户
    if [ -f .git/LAST_AUTO_STASH ]; then
        echo -e "${BLUE}💡 提示：如需恢复之前的工作，使用:${NC}"
        echo "   git stash pop"
    fi
else
    echo -e "${RED}❌ 切换失败${NC}"
    
    # 如果刚才创建了stash，询问是否恢复
    if [ -f .git/LAST_AUTO_STASH ]; then
        echo "是否恢复刚才保存的工作？(y/n)"
        read -r response
        if [ "$response" = "y" ]; then
            git stash pop
            rm -f .git/LAST_AUTO_STASH
        fi
    fi
    exit 1
fi

# 清理临时文件
rm -f .git/LAST_AUTO_STASH

exit 0