#!/bin/bash

# 本地数据库初始化脚本
# 用于创建本地开发所需的数据库

echo "🗄️  初始化本地数据库..."

# 数据库配置
DB_HOST="localhost"
DB_USER="root"
DB_PASS="Zlb&198838"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 创建数据库的SQL
create_databases() {
    echo "📊 创建数据库..."
    
    mysql -h $DB_HOST -u $DB_USER -p"$DB_PASS" <<EOF
-- 创建overdue_debt_db
CREATE DATABASE IF NOT EXISTS overdue_debt_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户系统数据库
CREATE DATABASE IF NOT EXISTS user_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建金蝶数据库
CREATE DATABASE IF NOT EXISTS kingdee CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 显示创建的数据库
SHOW DATABASES;
EOF

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库创建成功${NC}"
    else
        echo -e "${RED}❌ 数据库创建失败${NC}"
        exit 1
    fi
}

# 检查是否有初始化SQL文件
init_tables() {
    echo "📋 初始化数据表..."
    
    # 检查init-scripts目录
    if [ -d "init-scripts" ]; then
        for sql_file in init-scripts/*.sql; do
            if [ -f "$sql_file" ]; then
                echo "执行SQL文件: $sql_file"
                mysql -h $DB_HOST -u $DB_USER -p"$DB_PASS" < "$sql_file"
            fi
        done
    fi
    
    # 检查单独的init-db.sql文件
    if [ -f "init-db.sql" ]; then
        echo "执行init-db.sql..."
        mysql -h $DB_HOST -u $DB_USER -p"$DB_PASS" < init-db.sql
    fi
    
    echo -e "${GREEN}✅ 数据表初始化完成${NC}"
}

# 执行初始化
create_databases
init_tables

echo ""
echo "🎉 本地数据库初始化完成！"
echo ""
echo "📌 数据库信息："
echo "  主机: $DB_HOST"
echo "  用户: $DB_USER"
echo "  数据库:"
echo "    - overdue_debt_db"
echo "    - user_system (用户系统)"
echo "    - kingdee (金蝶数据)"