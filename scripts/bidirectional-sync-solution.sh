#!/bin/bash

# 增强版双向数据同步解决方案
# 基于历史同步方案重新设计，支持增量同步和冲突检测
# 特别解决3月份新增数据同步问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

# 关键数据表
SYNC_TABLES=("新增表" "诉讼表" "非诉讼表" "处置表" "减值准备表" "汇总表" "风险准备金表")

echo -e "${PURPLE}🔄==================== 双向数据同步方案 ====================${NC}"
echo -e "${CYAN}基于UltraThink分析的智能同步解决方案${NC}"
echo -e "${YELLOW}目标: 解决3月份数据不一致问题，建立双向同步机制${NC}"
echo ""

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] INFO${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] SUCCESS${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] WARNING${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR${NC} $1"
}

# 函数：获取表的最后更新时间
get_table_last_update() {
    local location=$1  # "local" or "linux"
    local table_name=$2
    
    if [ "$location" = "local" ]; then
        mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
            -D overdue_debt_db --default-character-set=utf8mb4 \
            -e "SELECT IFNULL(MAX(更新时间), '1970-01-01 00:00:00') as last_update FROM \`$table_name\`;" \
            -s -N 2>/dev/null || echo "1970-01-01 00:00:00"
    else
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            -D overdue_debt_db --default-character-set=utf8mb4 \
            -e \"SELECT IFNULL(MAX(更新时间), '1970-01-01 00:00:00') as last_update FROM \\\`$table_name\\\`;\" \
            -s -N 2>/dev/null || echo '1970-01-01 00:00:00'"
    fi
}

# 函数：获取表的记录数
get_table_count() {
    local location=$1
    local table_name=$2
    local condition=$3
    
    if [ "$location" = "local" ]; then
        mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
            -D overdue_debt_db --default-character-set=utf8mb4 \
            -e "SELECT COUNT(*) FROM \`$table_name\` $condition;" -s -N 2>/dev/null || echo "0"
    else
        ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            -D overdue_debt_db --default-character-set=utf8mb4 \
            -e \"SELECT COUNT(*) FROM \\\`$table_name\\\` $condition;\" -s -N 2>/dev/null || echo '0'"
    fi
}

# 函数：数据差异分析
analyze_data_differences() {
    log_info "🔍 分析本地和Linux数据差异..."
    
    echo ""
    echo -e "${CYAN}📊 数据差异报告${NC}"
    echo "========================================"
    
    for table in "${SYNC_TABLES[@]}"; do
        echo ""
        echo -e "${BLUE}表: $table${NC}"
        
        # 检查2025年3月数据
        local_march_count=$(get_table_count "local" "$table" "WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0)")
        linux_march_count=$(get_table_count "linux" "$table" "WHERE 年份=2025 AND (\\\`3月\\\` > 0 OR 新增金额 > 0)")
        
        echo "  • 2025年3月数据: 本地($local_march_count) vs Linux($linux_march_count)"
        
        # 检查最后更新时间
        local_last_update=$(get_table_last_update "local" "$table")
        linux_last_update=$(get_table_last_update "linux" "$table")
        
        echo "  • 最后更新时间: 本地($local_last_update) vs Linux($linux_last_update)"
        
        # 判断同步方向
        if [ "$local_march_count" -gt "$linux_march_count" ]; then
            echo -e "  • ${YELLOW}需要同步: 本地 → Linux${NC}"
        elif [ "$linux_march_count" -gt "$local_march_count" ]; then
            echo -e "  • ${YELLOW}需要同步: Linux → 本地${NC}"
        else
            echo -e "  • ${GREEN}数据一致${NC}"
        fi
    done
    
    echo ""
    echo "========================================"
}

# 函数：增量同步特定表
incremental_sync_table() {
    local table_name=$1
    local direction=$2  # "local_to_linux" or "linux_to_local"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    
    log_info "📤 开始增量同步表: $table_name (方向: $direction)"
    
    if [ "$direction" = "local_to_linux" ]; then
        # 从本地同步到Linux
        local dump_file="/tmp/${table_name}_incremental_${timestamp}.sql"
        
        # 导出2025年3月的新增数据
        log_info "导出本地2025年3月数据..."
        mysqldump -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
            --default-character-set=utf8mb4 \
            --single-transaction \
            --no-create-info \
            --where="年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0)" \
            overdue_debt_db "$table_name" > "$dump_file"
        
        if [ -s "$dump_file" ]; then
            log_info "传输数据到Linux服务器..."
            scp "$dump_file" "$LINUX_SERVER:/tmp/"
            
            log_info "在Linux服务器上执行增量更新..."
            ssh "$LINUX_SERVER" << EOF
                # 复制到容器并执行
                docker cp /tmp/$(basename $dump_file) $MYSQL_CONTAINER:/tmp/
                
                # 执行增量同步
                docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
                    -D overdue_debt_db --default-character-set=utf8mb4 < /tmp/$(basename $dump_file)
                
                # 清理临时文件
                rm -f /tmp/$(basename $dump_file)
                docker exec $MYSQL_CONTAINER rm -f /tmp/$(basename $dump_file)
EOF
            
            rm -f "$dump_file"
            log_success "表 $table_name 增量同步完成"
        else
            log_warning "表 $table_name 没有需要同步的增量数据"
            rm -f "$dump_file"
        fi
    fi
}

# 函数：双向同步执行
execute_bidirectional_sync() {
    log_info "🔄 执行双向同步..."
    
    for table in "${SYNC_TABLES[@]}"; do
        echo ""
        echo -e "${CYAN}--- 处理表: $table ---${NC}"
        
        # 获取数据计数
        local_count=$(get_table_count "local" "$table" "WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0)")
        linux_count=$(get_table_count "linux" "$table" "WHERE 年份=2025 AND (\\\`3月\\\` > 0 OR 新增金额 > 0)")
        
        if [ "$local_count" -gt "$linux_count" ]; then
            log_info "检测到本地有更多3月份数据，同步到Linux..."
            incremental_sync_table "$table" "local_to_linux"
        elif [ "$linux_count" -gt "$local_count" ]; then
            log_warning "Linux端有更多数据，需要手动检查"
        else
            log_success "表 $table 数据已同步"
        fi
    done
}

# 函数：同步后验证
verify_sync_result() {
    log_info "✅ 验证同步结果..."
    
    echo ""
    echo -e "${CYAN}📋 同步验证报告${NC}"
    echo "========================================"
    
    local all_synced=true
    
    for table in "${SYNC_TABLES[@]}"; do
        local_count=$(get_table_count "local" "$table" "WHERE 年份=2025 AND (\`3月\` > 0 OR 新增金额 > 0)")
        linux_count=$(get_table_count "linux" "$table" "WHERE 年份=2025 AND (\\\`3月\\\` > 0 OR 新增金额 > 0)")
        
        echo -e "${BLUE}表 $table:${NC} 本地($local_count) vs Linux($linux_count)"
        
        if [ "$local_count" -eq "$linux_count" ]; then
            echo -e "  ${GREEN}✅ 数据一致${NC}"
        else
            echo -e "  ${RED}❌ 数据不一致${NC}"
            all_synced=false
        fi
    done
    
    echo "========================================"
    
    if [ "$all_synced" = true ]; then
        log_success "🎉 所有数据同步验证通过！"
        return 0
    else
        log_error "⚠️ 部分数据同步验证失败"
        return 1
    fi
}

# 函数：重启Linux应用服务
restart_linux_application() {
    log_info "🔄 重启Linux应用服务以刷新数据..."
    
    ssh "$LINUX_SERVER" "docker restart financial-backend"
    
    log_info "等待应用服务启动..."
    sleep 30
    
    # 验证应用状态
    if ssh "$LINUX_SERVER" "curl -f -s http://localhost:8080/actuator/health" > /dev/null 2>&1; then
        log_success "Linux应用服务重启成功"
    else
        log_warning "应用服务重启后状态检查失败，请手动验证"
    fi
}

# 函数：创建同步监控脚本
create_sync_monitor() {
    log_info "📝 创建同步监控脚本..."
    
    cat > sync-monitor.sh << 'EOF'
#!/bin/bash
# 数据同步监控脚本 - 定期检查数据一致性

LINUX_SERVER="admin@**********"
TABLES=("新增表" "诉讼表" "非诉讼表" "处置表" "减值准备表")

echo "$(date): 开始数据一致性检查"

for table in "${TABLES[@]}"; do
    local_count=$(mysql -h localhost -u root -p'Zlb&198838' --skip-ssl -D overdue_debt_db --default-character-set=utf8mb4 -e "SELECT COUNT(*) FROM \`$table\` WHERE 年份=2025;" -s -N 2>/dev/null)
    linux_count=$(ssh $LINUX_SERVER "docker exec financial-mysql mysql -u'root' -p'Zlb&198838' -D overdue_debt_db --default-character-set=utf8mb4 -e \"SELECT COUNT(*) FROM \\\`$table\\\` WHERE 年份=2025;\" -s -N" 2>/dev/null)
    
    if [ "$local_count" != "$linux_count" ]; then
        echo "WARNING: $table 数据不一致 - 本地:$local_count, Linux:$linux_count"
    else
        echo "OK: $table 数据一致"
    fi
done

echo "$(date): 数据一致性检查完成"
EOF

    chmod +x sync-monitor.sh
    log_success "同步监控脚本创建完成: sync-monitor.sh"
}

# 主函数
main() {
    echo -e "${PURPLE}🚀 开始UltraThink双向同步解决方案...${NC}"
    echo ""
    
    # 阶段1: 数据差异分析
    echo -e "${CYAN}=== 阶段1: 数据差异分析 ===${NC}"
    analyze_data_differences
    
    echo ""
    read -p "$(echo -e ${YELLOW}"是否继续执行同步? (y/N): "${NC})" -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "同步已取消"
        exit 0
    fi
    
    # 阶段2: 执行双向同步
    echo ""
    echo -e "${CYAN}=== 阶段2: 执行双向同步 ===${NC}"
    execute_bidirectional_sync
    
    # 阶段3: 验证同步结果
    echo ""
    echo -e "${CYAN}=== 阶段3: 验证同步结果 ===${NC}"
    if verify_sync_result; then
        # 阶段4: 重启应用服务
        echo ""
        echo -e "${CYAN}=== 阶段4: 重启应用服务 ===${NC}"
        restart_linux_application
        
        # 阶段5: 创建监控机制
        echo ""
        echo -e "${CYAN}=== 阶段5: 创建监控机制 ===${NC}"
        create_sync_monitor
        
        echo ""
        echo -e "${GREEN}🎉 双向同步解决方案执行完成！${NC}"
        echo ""
        echo -e "${YELLOW}📋 后续建议:${NC}"
        echo "1. 定期运行 ./sync-monitor.sh 检查数据一致性"
        echo "2. 在Linux端验证3月份数据是否正确显示"
        echo "3. 考虑设置定时任务进行自动同步"
        echo ""
        echo -e "${YELLOW}🔗 验证命令:${NC}"
        echo "# 检查Linux端数据:"
        echo "ssh $LINUX_SERVER 'docker exec financial-mysql mysql -uroot -pZlb&198838 -D overdue_debt_db --default-character-set=utf8mb4 -e \"SELECT COUNT(*) FROM 新增表 WHERE 年份=2025 AND \\\`3月\\\` > 0;\"'"
        
    else
        log_error "同步验证失败，请检查错误信息"
        exit 1
    fi
    
    echo ""
    echo -e "${PURPLE}==================== 解决方案完成 ====================${NC}"
}

# 执行主函数
main "$@"