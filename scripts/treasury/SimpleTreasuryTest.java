import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;

/**
 * 简单的司库系统连接测试
 *
 * 这个独立的Java程序用于测试与司库系统的基本连接
 * 不依赖Spring Boot框架，可以直接运行
 */
public class SimpleTreasuryTest {

    // 司库系统配置
    private static final String ENDPOINT = "http://10.25.1.20:6767";
    private static final String USERNAME = "***********";
    private static final String DEFAULT_ACCOUNT = "8110701012901269085";

    public static void main(String[] args) {
        System.out.println("====================================");
        System.out.println("      司库系统连接测试");
        System.out.println("====================================");
        System.out.println();

        SimpleTreasuryTest test = new SimpleTreasuryTest();

        try {
            // 测试余额查询
            test.testBalanceQuery();

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试余额查询
     */
    public void testBalanceQuery() {
        System.out.println("🔍 开始测试余额查询...");
        System.out.println("司库系统地址: " + ENDPOINT);
        System.out.println("用户名: " + USERNAME);
        System.out.println("账户号: " + DEFAULT_ACCOUNT);
        System.out.println();

        try {
            // 构造XML请求
            String xmlRequest = buildBalanceQueryXml(DEFAULT_ACCOUNT);
            System.out.println("📤 发送的XML请求:");
            System.out.println(xmlRequest);
            System.out.println();

            // 发送HTTP请求
            String response = sendHttpRequest(xmlRequest);

            System.out.println("📥 收到的响应:");
            System.out.println(response);
            System.out.println();

            // 分析响应
            analyzeResponse(response);

        } catch (Exception e) {
            System.err.println("❌ 余额查询失败: " + e.getMessage());

            if (e instanceof ConnectException) {
                System.err.println("💡 可能的原因:");
                System.err.println("   1. 司库系统服务器不可达 (检查网络连接)");
                System.err.println("   2. 司库系统服务未启动");
                System.err.println("   3. 防火墙阻止了连接");
            } else if (e instanceof SocketTimeoutException) {
                System.err.println("💡 可能的原因:");
                System.err.println("   1. 司库系统响应超时");
                System.err.println("   2. 网络延迟过高");
            }
        }
    }

    /**
     * 构造余额查询XML
     */
    private String buildBalanceQueryXml(String accountNo) {
        return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
               "<stream>\n" +
               "  <action>SKBALQRY</action>\n" +
               "  <userName>" + USERNAME + "</userName>\n" +
               "  <list name=\"userDataList\">\n" +
               "    <row>\n" +
               "      <accountNo>" + accountNo + "</accountNo>\n" +
               "    </row>\n" +
               "  </list>\n" +
               "</stream>";
    }

    /**
     * 发送HTTP请求
     */
    private String sendHttpRequest(String xmlRequest) throws Exception {
        URL url = new URL(ENDPOINT);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            // 设置请求属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/xml;charset=GBK");
            connection.setRequestProperty("Accept", "application/xml");
            connection.setRequestProperty("User-Agent", "TreasuryClient/1.0");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setConnectTimeout(30000);  // 30秒连接超时
            connection.setReadTimeout(60000);     // 60秒读取超时

            System.out.println("🔗 正在连接到: " + ENDPOINT);
            System.out.println("📤 请求头信息:");
            System.out.println("   Content-Type: application/xml;charset=GBK");
            System.out.println("   Content-Length: " + xmlRequest.getBytes("GBK").length);

            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = xmlRequest.getBytes("GBK");
                os.write(input, 0, input.length);
                os.flush();
                System.out.println("✅ 请求数据发送完成");
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();
            System.out.println("📊 HTTP响应码: " + responseCode + " " + responseMessage);

            // 显示响应头
            System.out.println("📥 响应头信息:");
            connection.getHeaderFields().forEach((key, value) -> {
                System.out.println("   " + key + ": " + value);
            });

            InputStream inputStream;
            if (responseCode >= 200 && responseCode < 300) {
                inputStream = connection.getInputStream();
                System.out.println("✅ 使用正常响应流");
            } else {
                inputStream = connection.getErrorStream();
                System.out.println("⚠️ 使用错误响应流");
            }

            if (inputStream == null) {
                System.out.println("❌ 无法获取响应流，服务器可能提前关闭连接");
                throw new IOException("无法获取响应流 - 服务器提前关闭连接");
            }

            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(inputStream, "GBK"))) {
                StringBuilder response = new StringBuilder();
                String line;
                int lineCount = 0;
                while ((line = reader.readLine()) != null) {
                    response.append(line).append("\n");
                    lineCount++;
                    if (lineCount <= 5) {  // 只显示前5行
                        System.out.println("📄 响应行 " + lineCount + ": " + line);
                    }
                }

                if (lineCount > 5) {
                    System.out.println("📄 ... (共 " + lineCount + " 行响应数据)");
                }

                return response.toString();
            }

        } catch (ConnectException e) {
            System.out.println("❌ 连接被拒绝: " + e.getMessage());
            throw e;
        } catch (SocketTimeoutException e) {
            System.out.println("⏰ 连接超时: " + e.getMessage());
            throw e;
        } catch (IOException e) {
            System.out.println("🔍 IO异常: " + e.getMessage());
            throw e;
        } finally {
            connection.disconnect();
        }
    }

    /**
     * 分析响应内容
     */
    private void analyzeResponse(String response) {
        System.out.println("🔍 响应分析:");

        if (response == null || response.trim().isEmpty()) {
            System.out.println("   ❌ 响应为空");
            return;
        }

        // 检查是否包含XML声明
        if (response.contains("<?xml")) {
            System.out.println("   ✅ 响应包含XML格式");
        } else {
            System.out.println("   ⚠️  响应不是XML格式");
        }

        // 检查是否包含错误信息
        if (response.toLowerCase().contains("error") ||
            response.toLowerCase().contains("exception") ||
            response.toLowerCase().contains("失败")) {
            System.out.println("   ❌ 响应可能包含错误信息");
        } else {
            System.out.println("   ✅ 响应看起来正常");
        }

        // 检查响应长度
        System.out.println("   📏 响应长度: " + response.length() + " 字符");

        // 尝试提取关键信息
        if (response.contains("<balance>")) {
            System.out.println("   💰 响应包含余额信息");
        }

        if (response.contains("<accountNo>")) {
            System.out.println("   🏦 响应包含账户信息");
        }

        System.out.println();
        System.out.println("✅ 司库系统连接测试完成！");

        if (response.length() > 50) {
            System.out.println("🎉 恭喜！司库系统接口连接成功！");
            System.out.println("💡 您现在可以在财务系统中使用司库功能了。");
        } else {
            System.out.println("⚠️  连接成功但响应较短，请检查司库系统配置。");
        }
    }
}
