#!/bin/bash

# 司库系统API测试脚本
# 用于测试司库系统接口的连通性和功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
API_BASE_URL="http://localhost:8080/api"
TREASURY_API_URL="${API_BASE_URL}/treasury"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查财务系统服务状态..."
    
    if curl -s "${API_BASE_URL}/health" > /dev/null 2>&1; then
        log_success "财务系统服务运行正常"
        return 0
    else
        log_error "财务系统服务未运行，请先启动服务"
        log_info "启动命令: ./start-app.sh 或 cd api-gateway && mvn spring-boot:run"
        return 1
    fi
}

# 获取JWT Token
get_jwt_token() {
    log_info "获取JWT Token..."
    
    local response=$(curl -s -X POST "${API_BASE_URL}/auth/login" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "laoshu198838",
            "password": "Zlb&198838"
        }')
    
    if echo "$response" | grep -q '"success":true'; then
        JWT_TOKEN=$(echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        log_success "JWT Token获取成功"
        return 0
    else
        log_error "JWT Token获取失败"
        echo "响应: $response"
        return 1
    fi
}

# 测试司库健康检查
test_treasury_health() {
    log_info "测试司库模块健康检查..."
    
    local response=$(curl -s "${TREASURY_API_URL}/health")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "司库模块健康检查通过"
        echo "响应: $response"
        return 0
    else
        log_error "司库模块健康检查失败"
        echo "响应: $response"
        return 1
    fi
}

# 测试司库配置信息
test_treasury_config() {
    log_info "测试司库配置信息获取..."
    
    local response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" \
        "${TREASURY_API_URL}/config")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "司库配置信息获取成功"
        echo "响应: $response"
        return 0
    else
        log_error "司库配置信息获取失败"
        echo "响应: $response"
        return 1
    fi
}

# 测试司库连接
test_treasury_connection() {
    log_info "测试司库系统连接..."
    
    local response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" \
        "${TREASURY_API_URL}/test")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "司库系统连接测试成功"
        echo "响应: $response"
        return 0
    else
        log_warning "司库系统连接测试失败（可能是网络或司库系统不可用）"
        echo "响应: $response"
        return 1
    fi
}

# 测试余额查询
test_balance_query() {
    log_info "测试账户余额查询..."
    
    local response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" \
        "${TREASURY_API_URL}/balance")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "账户余额查询成功"
        echo "响应: $response"
        return 0
    else
        log_warning "账户余额查询失败（可能是网络或司库系统不可用）"
        echo "响应: $response"
        return 1
    fi
}

# 测试交易记录查询
test_transaction_query() {
    log_info "测试交易记录查询..."
    
    # 使用当前日期前7天到今天的范围
    local end_date=$(date +%Y%m%d)
    local start_date=$(date -d '7 days ago' +%Y%m%d)
    
    local response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" \
        "${TREASURY_API_URL}/transactions?startDate=${start_date}&endDate=${end_date}")
    
    if echo "$response" | grep -q '"success":true'; then
        log_success "交易记录查询成功"
        echo "响应: $response"
        return 0
    else
        log_warning "交易记录查询失败（可能是网络或司库系统不可用）"
        echo "响应: $response"
        return 1
    fi
}

# 主函数
main() {
    echo "======================================"
    echo "      司库系统API测试脚本"
    echo "======================================"
    echo ""
    
    # 检查curl命令
    if ! command -v curl &> /dev/null; then
        log_error "curl命令未找到，请安装curl"
        exit 1
    fi
    
    # 执行测试
    local test_count=0
    local success_count=0
    
    # 1. 检查服务状态
    ((test_count++))
    if check_service; then
        ((success_count++))
    fi
    
    echo ""
    
    # 2. 获取JWT Token
    ((test_count++))
    if get_jwt_token; then
        ((success_count++))
    else
        log_error "无法获取JWT Token，跳过需要认证的测试"
        exit 1
    fi
    
    echo ""
    
    # 3. 测试司库健康检查
    ((test_count++))
    if test_treasury_health; then
        ((success_count++))
    fi
    
    echo ""
    
    # 4. 测试司库配置信息
    ((test_count++))
    if test_treasury_config; then
        ((success_count++))
    fi
    
    echo ""
    
    # 5. 测试司库连接
    ((test_count++))
    if test_treasury_connection; then
        ((success_count++))
    fi
    
    echo ""
    
    # 6. 测试余额查询
    ((test_count++))
    if test_balance_query; then
        ((success_count++))
    fi
    
    echo ""
    
    # 7. 测试交易记录查询
    ((test_count++))
    if test_transaction_query; then
        ((success_count++))
    fi
    
    echo ""
    echo "======================================"
    echo "           测试结果汇总"
    echo "======================================"
    echo "总测试数: $test_count"
    echo "成功数: $success_count"
    echo "失败数: $((test_count - success_count))"
    
    if [ $success_count -eq $test_count ]; then
        log_success "所有测试通过！司库系统API集成成功！"
        exit 0
    elif [ $success_count -ge 4 ]; then
        log_warning "基础功能测试通过，部分高级功能可能需要司库系统在线"
        exit 0
    else
        log_error "多个测试失败，请检查系统配置"
        exit 1
    fi
}

# 执行主函数
main "$@"
