# 财务系统脚本中心

本目录包含了财务系统的所有自动化脚本，按功能分类组织。

## 📁 目录结构

```
scripts/
├── build/          # 构建脚本
├── deploy/         # 部署脚本
├── maintenance/    # 维护脚本
└── utils/          # 工具脚本
```

## 🔧 脚本分类

### 🏗️ 构建脚本 (build/)
- `build-backend.sh` - 后端项目构建
- `build-frontend.sh` - 前端项目构建
- `build-all.sh` - 全项目构建

### 🚀 部署脚本 (deploy/)
- `deploy.sh` - 主部署脚本
- `backup.sh` - 数据备份脚本
- `rollback.sh` - 版本回滚脚本
- `health-check.sh` - 部署后健康检查

### 🔧 维护脚本 (maintenance/)
- `cleanup.sh` - 系统清理脚本
- `log-rotate.sh` - 日志轮转脚本
- `db-backup.sh` - 数据库备份脚本

### 🛠️ 工具脚本 (utils/)
- `update-refs.sh` - 更新项目引用
- `env-check.sh` - 环境检查脚本
- `config-gen.sh` - 配置文件生成

## 🚀 快速使用

### 常用操作
```bash
# 完整部署
./scripts/deploy/deploy.sh

# 备份数据
./scripts/deploy/backup.sh

# 健康检查
./scripts/deploy/health-check.sh

# 系统清理
./scripts/maintenance/cleanup.sh
```

### 开发构建
```bash
# 构建后端
./scripts/build/build-backend.sh

# 构建前端
./scripts/build/build-frontend.sh

# 构建全部
./scripts/build/build-all.sh
```

## ⚠️ 使用注意事项

1. **权限要求**: 确保脚本有执行权限 (`chmod +x script.sh`)
2. **环境检查**: 运行前请检查系统环境和依赖
3. **备份优先**: 生产环境操作前务必备份
4. **日志查看**: 脚本执行后检查日志输出

## 📝 脚本开发规范

### 命名规范
- 使用小写字母和连字符
- 功能明确的描述性名称
- 统一的`.sh`后缀

### 代码规范
- 添加脚本头部说明
- 使用`set -e`确保错误退出
- 添加适当的日志输出
- 包含使用说明和示例

### 示例模板
```bash
#!/bin/bash
# 脚本名称: example.sh
# 功能描述: 示例脚本模板
# 作者: 开发团队
# 创建时间: 2025-06-23

set -e  # 遇到错误立即退出

# 脚本逻辑
echo "脚本开始执行..."
# ... 具体实现
echo "脚本执行完成!"
```

## 🔗 相关文档

- [部署指南](../docs/deployment/README.md)
- [开发指南](../docs/development/README.md)
- [故障排除](../docs/operations/troubleshooting.md)

---

*更新时间: 2025-06-23 - 建立标准化脚本管理体系*
