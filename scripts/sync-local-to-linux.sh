#!/bin/bash

# 本地数据同步到Linux服务器脚本
# 将本地MySQL数据同步到Linux服务器的MySQL数据库

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_PORT="3306"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"

LINUX_SERVER="admin@**********"
LINUX_DB_HOST="localhost"
LINUX_DB_PORT="3306"
LINUX_DB_USER="root"
LINUX_DB_PASSWORD="Zlb&198838"

# 数据库列表
DB_NAMES=("overdue_debt_db" "kingdee" "user_system")

# 创建临时目录
TEMP_DIR="/tmp/mysql-sync-$(date +%Y%m%d-%H%M%S)"
BACKUP_PREFIX="backup_$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}==================== 本地到Linux数据同步 ====================${NC}"
echo -e "${YELLOW}从本地MySQL同步数据到Linux服务器MySQL${NC}"
echo -e "${YELLOW}目标服务器: $LINUX_SERVER${NC}"
echo ""

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 函数：检查本地MySQL连接
check_local_mysql() {
    log_info "检查本地MySQL连接..."
    
    if mysql -h"$LOCAL_DB_HOST" -P"$LOCAL_DB_PORT" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -e "SELECT 1;" > /dev/null 2>&1; then
        log_success "本地MySQL连接成功"
        return 0
    else
        log_error "本地MySQL连接失败"
        return 1
    fi
}

# 函数：检查Linux服务器连接
check_linux_server() {
    log_info "检查Linux服务器连接..."
    
    if ssh -o ConnectTimeout=10 "$LINUX_SERVER" "echo 'SSH连接成功'" > /dev/null 2>&1; then
        log_success "Linux服务器SSH连接成功"
        return 0
    else
        log_error "Linux服务器SSH连接失败"
        return 1
    fi
}

# 函数：检查Linux服务器MySQL
check_linux_mysql() {
    log_info "检查Linux服务器MySQL（Docker容器）..."
    
    ssh "$LINUX_SERVER" "docker exec financial-mysql mysql -h'$LINUX_DB_HOST' -P'$LINUX_DB_PORT' -u'$LINUX_DB_USER' -p'$LINUX_DB_PASSWORD' -e 'SELECT 1;'" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "Linux服务器MySQL连接成功"
        return 0
    else
        log_error "Linux服务器MySQL连接失败"
        return 1
    fi
}

# 函数：创建数据库备份
create_database_dump() {
    local db_name=$1
    local dump_file="$TEMP_DIR/${db_name}_${BACKUP_PREFIX}.sql"
    
    log_info "备份数据库: $db_name"
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR"
    
    # 导出数据库
    mysqldump -h"$LOCAL_DB_HOST" -P"$LOCAL_DB_PORT" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" \
        --skip-ssl \
        --single-transaction \
        --routines \
        --triggers \
        --add-drop-database \
        --databases "$db_name" > "$dump_file"
    
    if [ $? -eq 0 ]; then
        log_success "数据库 $db_name 备份成功: $dump_file"
        echo "$dump_file"
        return 0
    else
        log_error "数据库 $db_name 备份失败"
        return 1
    fi
}

# 函数：传输数据库备份到Linux服务器
transfer_dump_to_linux() {
    local dump_file=$1
    local filename=$(basename "$dump_file")
    
    log_info "传输备份文件到Linux服务器: $filename"
    
    scp "$dump_file" "$LINUX_SERVER:/tmp/" > /dev/null
    
    if [ $? -eq 0 ]; then
        log_success "文件传输成功"
        return 0
    else
        log_error "文件传输失败"
        return 1
    fi
}

# 函数：在Linux服务器上恢复数据库
restore_database_on_linux() {
    local db_name=$1
    local dump_filename="${db_name}_${BACKUP_PREFIX}.sql"
    
    log_info "在Linux服务器上恢复数据库: $db_name"
    
    ssh "$LINUX_SERVER" bash -s << EOF
        set -e
        
        echo "开始恢复数据库: $db_name"
        
        # 备份现有数据库（如果存在）
        if docker exec financial-mysql mysql -h'$LINUX_DB_HOST' -u'$LINUX_DB_USER' -p'$LINUX_DB_PASSWORD' -e "USE $db_name;" 2>/dev/null; then
            echo "备份现有数据库 $db_name..."
            docker exec financial-mysql mysqldump -h'$LINUX_DB_HOST' -u'$LINUX_DB_USER' -p'$LINUX_DB_PASSWORD' \
                --single-transaction \
                --routines \
                --triggers \
                --databases $db_name > /tmp/${db_name}_backup_before_sync_\$(date +%Y%m%d_%H%M%S).sql
            echo "现有数据库备份完成"
        fi
        
        # 将备份文件复制到容器中
        echo "复制备份文件到MySQL容器..."
        docker cp /tmp/$dump_filename financial-mysql:/tmp/
        
        # 恢复数据库
        echo "恢复数据库从备份文件..."
        docker exec financial-mysql mysql -h'$LINUX_DB_HOST' -u'$LINUX_DB_USER' -p'$LINUX_DB_PASSWORD' < /tmp/$dump_filename
        
        # 验证恢复结果
        echo "验证数据库恢复结果..."
        docker exec financial-mysql mysql -h'$LINUX_DB_HOST' -u'$LINUX_DB_USER' -p'$LINUX_DB_PASSWORD' -e "USE $db_name; SHOW TABLES;" > /dev/null
        
        echo "数据库 $db_name 恢复成功"
        
        # 清理临时文件
        rm -f /tmp/$dump_filename
EOF
    
    if [ $? -eq 0 ]; then
        log_success "数据库 $db_name 在Linux服务器上恢复成功"
        return 0
    else
        log_error "数据库 $db_name 在Linux服务器上恢复失败"
        return 1
    fi
}

# 函数：验证同步结果
verify_sync_result() {
    local db_name=$1
    
    log_info "验证数据库 $db_name 同步结果..."
    
    # 获取本地表数量
    local local_table_count=$(mysql -h"$LOCAL_DB_HOST" -P"$LOCAL_DB_PORT" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl -D"$db_name" -e "SHOW TABLES;" 2>/dev/null | wc -l)
    local_table_count=$((local_table_count - 1))  # 减去标题行
    
    # 获取Linux服务器表数量
    local linux_table_count=$(ssh "$LINUX_SERVER" "docker exec financial-mysql mysql -h'$LINUX_DB_HOST' -u'$LINUX_DB_USER' -p'$LINUX_DB_PASSWORD' -D'$db_name' -e 'SHOW TABLES;' 2>/dev/null | wc -l")
    linux_table_count=$((linux_table_count - 1))  # 减去标题行
    
    log_info "本地数据库 $db_name 表数量: $local_table_count"
    log_info "Linux数据库 $db_name 表数量: $linux_table_count"
    
    if [ "$local_table_count" -eq "$linux_table_count" ]; then
        log_success "数据库 $db_name 表数量同步正确"
        return 0
    else
        log_warning "数据库 $db_name 表数量不匹配，可能需要进一步检查"
        return 1
    fi
}

# 函数：清理本地临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
        log_success "临时文件清理完成"
    fi
}

# 主函数
main() {
    log_info "开始本地到Linux数据同步流程..."
    echo ""
    
    # 1. 环境检查
    echo -e "${BLUE}=== 步骤1: 环境连接检查 ===${NC}"
    
    if ! check_local_mysql; then
        log_error "本地MySQL检查失败，请确保MySQL服务已启动"
        exit 1
    fi
    
    if ! check_linux_server; then
        log_error "Linux服务器连接失败，请检查SSH配置和网络连接"
        exit 1
    fi
    
    if ! check_linux_mysql; then
        log_error "Linux服务器MySQL连接失败，请检查MySQL服务和权限"
        exit 1
    fi
    
    # 2. 数据库同步
    echo -e "\n${BLUE}=== 步骤2: 数据库备份与同步 ===${NC}"
    
    local sync_success=true
    
    for db_name in "${DB_NAMES[@]}"; do
        echo -e "\n${YELLOW}--- 处理数据库: $db_name ---${NC}"
        
        # 创建备份
        local dump_file=$(create_database_dump "$db_name")
        if [ $? -ne 0 ]; then
            log_error "数据库 $db_name 备份失败，跳过"
            sync_success=false
            continue
        fi
        
        # 传输备份
        if ! transfer_dump_to_linux "$dump_file"; then
            log_error "数据库 $db_name 传输失败，跳过"
            sync_success=false
            continue
        fi
        
        # 恢复数据库
        if ! restore_database_on_linux "$db_name"; then
            log_error "数据库 $db_name 恢复失败"
            sync_success=false
            continue
        fi
        
        # 验证结果
        if ! verify_sync_result "$db_name"; then
            log_warning "数据库 $db_name 验证有问题，但同步已完成"
        fi
        
        log_success "数据库 $db_name 同步完成"
    done
    
    # 3. 清理临时文件
    echo -e "\n${BLUE}=== 步骤3: 清理资源 ===${NC}"
    cleanup_temp_files
    
    # 4. 同步结果总结
    echo -e "\n${BLUE}=== 同步结果总结 ===${NC}"
    if [ "$sync_success" = true ]; then
        log_success "所有数据库同步成功完成！"
        echo -e "${GREEN}✓ 本地数据已成功同步到Linux服务器${NC}"
        echo -e "${GREEN}✓ Linux服务器: $LINUX_SERVER${NC}"
        echo -e "${GREEN}✓ 同步的数据库: ${DB_NAMES[*]}${NC}"
    else
        log_warning "部分数据库同步可能有问题，请检查上面的错误信息"
    fi
    
    # 5. 后续建议
    echo -e "\n${YELLOW}后续建议:${NC}"
    echo "1. 在Linux服务器上重启应用服务以使用新数据"
    echo "2. 验证应用程序功能是否正常"
    echo "3. 考虑设置定期同步机制"
    echo ""
    echo -e "${YELLOW}验证命令:${NC}"
    echo "# 在Linux服务器上检查数据:"
    echo "ssh $LINUX_SERVER 'mysql -u$LINUX_DB_USER -p$LINUX_DB_PASSWORD -e \"SHOW DATABASES;\"'"
    
    echo -e "\n${BLUE}==================== 同步完成 ====================${NC}"
}

# 执行主函数
main "$@"