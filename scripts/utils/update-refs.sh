#!/bin/bash

# 更新项目中的旧引用脚本
# 用于将 webservice 和 business-modules 的引用更新为新名称

echo "开始更新项目引用..."

# 更新文档中的 webservice 引用为 api-gateway
echo "更新 webservice 引用为 api-gateway..."
find docs/ -name "*.md" -type f -exec sed -i '' 's/webservice/api-gateway/g' {} \;

# 更新文档中的 business-modules 引用为 services
echo "更新 business-modules 引用为 services..."
find docs/ -name "*.md" -type f -exec sed -i '' 's/business-modules/services/g' {} \;

# 更新特定的路径引用
echo "更新特定路径引用..."
find docs/ -name "*.md" -type f -exec sed -i '' 's/webservice\/src\/main/api-gateway\/src\/main/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/business-modules\/debt-management/services\/debt-management/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/business-modules\/account-management/services\/account-management/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/business-modules\/audit-management/services\/audit-management/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/business-modules\/report-management/services\/report-management/g' {} \;

# 更新Maven相关引用
echo "更新Maven相关引用..."
find docs/ -name "*.md" -type f -exec sed -i '' 's/<module>webservice<\/module>/<module>api-gateway<\/module>/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/<module>business-modules<\/module>/<module>services<\/module>/g' {} \;

# 更新Docker相关引用
echo "更新Docker相关引用..."
find docs/ -name "*.md" -type f -exec sed -i '' 's/webservice-\*\.jar/api-gateway-*.jar/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/webservice\.jar/api-gateway.jar/g' {} \;

# 更新启动命令引用
echo "更新启动命令引用..."
find docs/ -name "*.md" -type f -exec sed -i '' 's/cd webservice/cd api-gateway/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/-pl webservice/-pl api-gateway/g' {} \;

# 更新架构描述
echo "更新架构描述..."
find docs/ -name "*.md" -type f -exec sed -i '' 's/Web服务层 (webservice)/API网关 (api-gateway)/g' {} \;
find docs/ -name "*.md" -type f -exec sed -i '' 's/业务模块组 (business-modules)/业务服务模块 (services)/g' {} \;

echo "引用更新完成！"

# 显示更新统计
echo ""
echo "更新统计："
echo "- webservice → api-gateway"
echo "- business-modules → services"
echo "- 相关路径和配置已更新"
echo ""
echo "请检查更新结果，确保没有误更新的内容。"
