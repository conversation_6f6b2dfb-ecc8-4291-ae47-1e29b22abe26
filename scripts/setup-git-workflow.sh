#!/bin/bash
# Git工作流设置脚本 - 一键配置所有Git增强功能

# 颜色定义
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 FinancialSystem Git工作流设置${NC}"
echo "===================================="

# 检查是否在git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}❌ 错误：不在Git仓库中${NC}"
    exit 1
fi

# 1. 设置脚本执行权限
echo -e "${YELLOW}📝 步骤1: 设置脚本权限...${NC}"
chmod +x scripts/*.sh
echo -e "${GREEN}✓ 脚本权限设置完成${NC}"

# 2. 安装Git hooks
echo -e "${YELLOW}📝 步骤2: 安装Git hooks...${NC}"
chmod +x .git/hooks/pre-commit .git/hooks/post-checkout
echo -e "${GREEN}✓ Hooks安装完成${NC}"

# 3. 配置Git别名
echo -e "${YELLOW}📝 步骤3: 配置Git别名...${NC}"
git config --local include.path ../.gitconfig.local
echo -e "${GREEN}✓ Git别名配置完成${NC}"

# 4. 设置Git用户信息（如果未设置）
if [ -z "$(git config user.name)" ]; then
    echo -e "${YELLOW}📝 步骤4: 设置Git用户信息...${NC}"
    echo "请输入你的名字："
    read -r name
    git config --local user.name "$name"
    echo "请输入你的邮箱："
    read -r email
    git config --local user.email "$email"
    echo -e "${GREEN}✓ 用户信息设置完成${NC}"
else
    echo -e "${GREEN}✓ Git用户信息已配置${NC}"
fi

# 5. 创建必要的目录
echo -e "${YELLOW}📝 步骤5: 创建必要目录...${NC}"
mkdir -p .git/info
echo -e "${GREEN}✓ 目录创建完成${NC}"

# 6. 显示当前配置
echo ""
echo -e "${BLUE}📊 当前配置信息：${NC}"
echo "================================"
echo "用户名: $(git config user.name)"
echo "邮箱: $(git config user.email)"
echo "当前分支: $(git branch --show-current)"
echo ""

# 7. 显示可用命令
echo -e "${BLUE}🛠️  可用的增强命令：${NC}"
echo "================================"
echo -e "${GREEN}安全操作：${NC}"
echo "  git safe-reset <commit>     - 安全回退到指定版本"
echo "  git safe-checkout <branch>  - 安全切换分支"
echo "  git smart-clean [1-3]       - 智能清理（级别1-3）"
echo ""
echo -e "${GREEN}快捷命令：${NC}"
echo "  git sr <commit>   - safe-reset的缩写"
echo "  git sc <branch>   - safe-checkout的缩写"
echo "  git st           - 简洁状态显示"
echo "  git ll           - 最近10条提交"
echo "  git sl           - stash列表（带时间）"
echo ""
echo -e "${GREEN}Claude命令：${NC}"
echo "  /git --reset <commit>    - 通过Claude安全回退"
echo "  /git --clean            - 通过Claude清理工作区"
echo "  /git --stash list       - 通过Claude管理stash"
echo ""

# 8. 测试配置
echo -e "${YELLOW}🧪 测试配置...${NC}"
if git st > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Git别名测试通过${NC}"
else
    echo -e "${RED}❌ Git别名测试失败${NC}"
fi

# 9. 清理建议
STASH_COUNT=$(git stash list | wc -l | tr -d ' ')
if [ "$STASH_COUNT" -gt 5 ]; then
    echo ""
    echo -e "${YELLOW}💡 建议：你有 $STASH_COUNT 个stash，建议清理${NC}"
    echo "  运行: git smart-clean 2"
fi

echo ""
echo -e "${GREEN}✨ 设置完成！${NC}"
echo -e "${BLUE}提示：${NC}"
echo "• 使用 'git safe-reset' 代替 'git reset --hard'"
echo "• 使用 'git safe-checkout' 代替 'git checkout'"
echo "• 定期运行 'git smart-clean' 保持仓库健康"
echo "• 查看 .claude/docs/git-best-practices.md 了解更多"

# 询问是否立即执行清理
echo ""
echo "是否立即执行一次基础清理？(y/n)"
read -r response
if [ "$response" = "y" ]; then
    bash scripts/git-smart-clean.sh 1
fi

exit 0