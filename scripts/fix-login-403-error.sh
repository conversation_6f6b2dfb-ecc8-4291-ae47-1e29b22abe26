#!/bin/bash

# =====================================================
# 修复登录接口403错误的快速部署脚本
# 用于Linux服务器 10.25.1.85
# =====================================================

set -e  # 遇到错误立即退出

echo "=========================================="
echo "开始修复登录接口403错误"
echo "时间: $(date)"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker服务状态..."
    if ! docker ps >/dev/null 2>&1; then
        log_error "Docker服务未运行，请先启动Docker"
        exit 1
    fi
    log_success "Docker服务正常"
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."
    
    # 检查MySQL容器
    if docker ps | grep -q "financial-mysql"; then
        log_success "MySQL容器正在运行"
    else
        log_warning "MySQL容器未运行"
    fi
    
    # 检查后端容器
    if docker ps | grep -q "financial-backend"; then
        log_success "后端容器正在运行"
    else
        log_warning "后端容器未运行"
    fi
    
    # 检查前端容器
    if docker ps | grep -q "financial-nginx"; then
        log_success "前端容器正在运行"
    else
        log_warning "前端容器未运行"
    fi
}

# 停止现有服务
stop_services() {
    log_info "停止现有Docker服务..."
    docker-compose down || true
    log_success "服务已停止"
}

# 清理旧数据（可选）
cleanup_data() {
    read -p "是否清理MySQL数据卷以重新初始化数据库？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_warning "清理MySQL数据卷..."
        docker volume rm $(docker volume ls -q | grep mysql) 2>/dev/null || true
        log_success "数据卷已清理"
    else
        log_info "保留现有数据卷"
    fi
}

# 重新构建和启动服务
rebuild_services() {
    log_info "重新构建和启动服务..."
    
    # 强制重新构建
    docker-compose up -d --build --force-recreate
    
    log_success "服务启动完成"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待MySQL启动
    log_info "等待MySQL启动..."
    for i in {1..30}; do
        if docker exec financial-mysql mysqladmin ping -h localhost --silent; then
            log_success "MySQL已启动"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "MySQL启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    for i in {1..60}; do
        if curl -f http://localhost:8080/actuator/health >/dev/null 2>&1; then
            log_success "后端服务已启动"
            break
        fi
        if [ $i -eq 60 ]; then
            log_error "后端服务启动超时"
            exit 1
        fi
        sleep 3
    done
    
    # 等待前端启动
    log_info "等待前端服务启动..."
    for i in {1..30}; do
        if curl -f http://localhost/ >/dev/null 2>&1; then
            log_success "前端服务已启动"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "前端服务启动超时"
            exit 1
        fi
        sleep 2
    done
}

# 验证数据库
verify_database() {
    log_info "验证数据库..."
    
    # 检查数据库是否存在
    if docker exec financial-mysql mysql -u root -p'Zlb&198838' -e "USE user_system; SHOW TABLES;" >/dev/null 2>&1; then
        log_success "user_system数据库存在"
    else
        log_error "user_system数据库不存在"
        return 1
    fi
    
    # 检查用户数据
    USER_COUNT=$(docker exec financial-mysql mysql -u root -p'Zlb&198838' -e "USE user_system; SELECT COUNT(*) FROM users;" 2>/dev/null | tail -n 1)
    if [ "$USER_COUNT" -gt 0 ]; then
        log_success "用户数据存在 ($USER_COUNT 个用户)"
    else
        log_error "用户数据不存在"
        return 1
    fi
}

# 测试登录接口
test_login() {
    log_info "测试登录接口..."
    
    # 测试登录
    RESPONSE=$(curl -s -w "%{http_code}" -X POST http://localhost/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"username":"laoshu198838","password":"Zlb&198838"}' \
        -o /tmp/login_response.json)
    
    HTTP_CODE="${RESPONSE: -3}"
    
    if [ "$HTTP_CODE" = "200" ]; then
        log_success "登录测试成功 (HTTP $HTTP_CODE)"
        log_info "响应内容:"
        cat /tmp/login_response.json | jq . 2>/dev/null || cat /tmp/login_response.json
    else
        log_error "登录测试失败 (HTTP $HTTP_CODE)"
        log_info "响应内容:"
        cat /tmp/login_response.json
        return 1
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    log_info "服务日志 (最后10行):"
    echo "=== MySQL日志 ==="
    docker logs financial-mysql --tail=10 2>/dev/null || echo "MySQL日志不可用"
    
    echo "=== 后端日志 ==="
    docker logs financial-backend --tail=10 2>/dev/null || echo "后端日志不可用"
    
    echo "=== 前端日志 ==="
    docker logs financial-nginx --tail=10 2>/dev/null || echo "前端日志不可用"
}

# 主函数
main() {
    log_info "开始执行修复流程..."
    
    # 检查环境
    check_docker
    
    # 显示当前状态
    check_containers
    
    # 停止服务
    stop_services
    
    # 可选清理数据
    cleanup_data
    
    # 重新构建服务
    rebuild_services
    
    # 等待服务启动
    wait_for_services
    
    # 验证数据库
    if verify_database; then
        log_success "数据库验证通过"
    else
        log_error "数据库验证失败，请检查日志"
        show_status
        exit 1
    fi
    
    # 测试登录
    if test_login; then
        log_success "登录功能测试通过"
    else
        log_error "登录功能测试失败"
        show_status
        exit 1
    fi
    
    # 显示最终状态
    show_status
    
    echo "=========================================="
    log_success "修复完成！"
    log_info "前端访问地址: http://10.25.1.85"
    log_info "后端API地址: http://10.25.1.85/api"
    log_info "测试账号: laoshu198838 / Zlb&198838"
    echo "=========================================="
}

# 执行主函数
main "$@"
