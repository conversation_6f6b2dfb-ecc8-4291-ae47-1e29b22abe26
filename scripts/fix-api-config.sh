#!/bin/bash

# 修复前端API配置问题的脚本
# 用于解决硬编码localhost地址在生产环境的兼容性问题

echo "🔧 开始修复前端API配置..."
echo "📅 执行时间: $(date)"
echo

# 统计变量
FIXED_FILES=0
SKIPPED_FILES=0
ERROR_FILES=0

# 修复注册页面API配置
echo "1️⃣ 修复注册页面API配置"
SIGNUP_FILE="FinancialSystem-web/src/layouts/authentication/sign-up/index.js"

if [ -f "$SIGNUP_FILE" ]; then
    echo "  🔍 检查文件: $SIGNUP_FILE"
    
    # 检查是否包含硬编码的localhost地址
    if grep -q "http://localhost:8080" "$SIGNUP_FILE"; then
        # 创建备份
        backup_file="$SIGNUP_FILE.backup-$(date +%Y%m%d-%H%M%S)"
        cp "$SIGNUP_FILE" "$backup_file"
        echo "  💾 已创建备份: $(basename $backup_file)"
        
        # 执行替换
        sed -i "s|'http://localhost:8080'|window.location.origin|g" "$SIGNUP_FILE"
        
        # 验证修改
        if grep -q "window.location.origin" "$SIGNUP_FILE"; then
            echo "  ✅ 注册页面API配置已修复"
            echo "  🔄 localhost:8080 → window.location.origin"
            ((FIXED_FILES++))
        else
            echo "  ❌ 修复失败，请手动检查"
            ((ERROR_FILES++))
        fi
    else
        echo "  ℹ️  文件已是正确配置，跳过修复"
        ((SKIPPED_FILES++))
    fi
else
    echo "  ❌ 注册页面文件不存在: $SIGNUP_FILE"
    ((ERROR_FILES++))
fi

echo

# 修复setupProxy.js配置
echo "2️⃣ 修复setupProxy.js配置"
PROXY_FILE="FinancialSystem-web/src/setupProxy.js"

if [ -f "$PROXY_FILE" ]; then
    echo "  🔍 检查文件: $PROXY_FILE"
    
    # 检查是否包含硬编码的localhost地址
    if grep -q "http://localhost:8080" "$PROXY_FILE"; then
        # 创建备份
        backup_file="$PROXY_FILE.backup-$(date +%Y%m%d-%H%M%S)"
        cp "$PROXY_FILE" "$backup_file"
        echo "  💾 已创建备份: $(basename $backup_file)"
        
        # 执行替换
        sed -i "s|'http://localhost:8080'|'http://financial-backend:8080'|g" "$PROXY_FILE"
        
        # 验证修改
        if grep -q "financial-backend:8080" "$PROXY_FILE"; then
            echo "  ✅ setupProxy.js配置已修复"
            echo "  🔄 localhost:8080 → financial-backend:8080"
            ((FIXED_FILES++))
        else
            echo "  ❌ 修复失败，请手动检查"
            ((ERROR_FILES++))
        fi
    else
        echo "  ℹ️  文件已是正确配置，跳过修复"
        ((SKIPPED_FILES++))
    fi
else
    echo "  ❌ setupProxy.js文件不存在: $PROXY_FILE"
    ((ERROR_FILES++))
fi

echo

# 检查其他可能包含硬编码地址的文件
echo "3️⃣ 检查其他可能的硬编码地址"
echo "  🔍 搜索可能包含localhost的文件..."

# 搜索包含localhost的JavaScript文件
LOCALHOST_FILES=$(find FinancialSystem-web/src -name "*.js" -o -name "*.jsx" | xargs grep -l "localhost" 2>/dev/null | grep -v ".backup" | head -5)

if [ -n "$LOCALHOST_FILES" ]; then
    echo "  ⚠️  发现可能需要手动检查的文件:"
    echo "$LOCALHOST_FILES" | while read file; do
        echo "    📄 $file"
        # 显示包含localhost的行
        grep -n "localhost" "$file" | head -2 | while read line; do
            echo "      🔍 $line"
        done
    done
    echo "  💡 建议手动检查这些文件是否需要修复"
else
    echo "  ✅ 未发现其他包含localhost的文件"
fi

echo

# 输出修复结果统计
echo "📊 修复结果统计:"
echo "  ✅ 成功修复: $FIXED_FILES 个文件"
echo "  ⏭️  跳过处理: $SKIPPED_FILES 个文件"
echo "  ❌ 错误文件: $ERROR_FILES 个文件"
echo

if [ $FIXED_FILES -gt 0 ]; then
    echo "🎉 前端API配置修复完成！"
    echo "📝 建议接下来执行以下步骤:"
    echo "   1. cd FinancialSystem-web"
    echo "   2. npm run build"
    echo "   3. 测试API调用是否正常"
    echo
    echo "🔄 如需回滚，可使用以下命令:"
    echo "   # 恢复注册页面配置"
    echo "   cp $SIGNUP_FILE.backup-* $SIGNUP_FILE"
    echo "   # 恢复代理配置"
    echo "   cp $PROXY_FILE.backup-* $PROXY_FILE"
else
    echo "ℹ️  没有文件需要修复，所有配置都已是最新状态。"
fi

echo
echo "🏁 脚本执行完成！"
