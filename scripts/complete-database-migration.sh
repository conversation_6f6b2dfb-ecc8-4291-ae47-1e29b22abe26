#!/bin/bash

# 完整数据库迁移方案
# UltraThink系统性解决方案：本地→Linux完整数据传输

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置变量
LOCAL_DB_HOST="localhost"
LOCAL_DB_USER="root"
LOCAL_DB_PASSWORD="Zlb&198838"
LINUX_SERVER="admin@**********"
MYSQL_CONTAINER="financial-mysql"

# 需要同步的数据库
DATABASES=("overdue_debt_db" "kingdee" "user_system")

# 创建备份目录
BACKUP_DIR="./complete-migration-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo -e "${PURPLE}🔄==================== 完整数据库迁移方案 ====================${NC}"
echo -e "${CYAN}UltraThink系统性解决方案：确保Linux和本地数据完全一致${NC}"
echo -e "${YELLOW}备份目录: $BACKUP_DIR${NC}"
echo ""

# 函数：记录日志
log_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] INFO${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] SUCCESS${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] WARNING${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR${NC} $1"
}

# 函数：备份本地数据库
backup_local_database() {
    local db_name=$1
    local backup_file="$BACKUP_DIR/${db_name}_complete_backup.sql"
    
    log_info "🗃️ 开始备份本地数据库: $db_name"
    
    # 获取数据库大小信息
    local db_size=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' 
            FROM information_schema.tables 
            WHERE table_schema='$db_name';" -s -N 2>/dev/null || echo "0.0")
    
    log_info "数据库 $db_name 大小: ${db_size}MB"
    
    # 完整备份
    mysqldump -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --default-character-set=utf8mb4 \
        --add-drop-database \
        --databases "$db_name" > "$backup_file"
    
    if [ $? -eq 0 ] && [ -s "$backup_file" ]; then
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_success "✅ 数据库 $db_name 备份完成 (文件大小: $file_size)"
        echo "$backup_file"
        return 0
    else
        log_error "❌ 数据库 $db_name 备份失败"
        return 1
    fi
}

# 函数：备份Linux数据库（作为回滚保障）
backup_linux_database() {
    local db_name=$1
    local backup_file="linux_${db_name}_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "💾 备份Linux端数据库: $db_name (回滚保障)"
    
    ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysqldump -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
        --single-transaction --routines --triggers --events --hex-blob --default-character-set=utf8mb4 \
        --add-drop-database --databases $db_name > /tmp/$backup_file" 2>/dev/null || {
        log_warning "⚠️ Linux端数据库 $db_name 备份失败（可能不存在）"
        return 1
    }
    
    log_success "✅ Linux端数据库 $db_name 已备份"
}

# 函数：传输并恢复数据库
migrate_database() {
    local db_name=$1
    local backup_file=$2
    
    log_info "📤 开始迁移数据库: $db_name"
    
    # 传输备份文件
    local remote_file="/tmp/$(basename $backup_file)"
    scp "$backup_file" "$LINUX_SERVER:$remote_file"
    
    if [ $? -ne 0 ]; then
        log_error "❌ 文件传输失败: $db_name"
        return 1
    fi
    
    log_info "📁 文件传输完成，开始数据库恢复..."
    
    # 在Linux服务器上恢复数据库
    ssh "$LINUX_SERVER" << EOF
        set -e
        
        echo "🔄 复制备份文件到MySQL容器..."
        docker cp "$remote_file" $MYSQL_CONTAINER:/tmp/
        
        echo "🗃️ 恢复数据库 $db_name..."
        docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            --default-character-set=utf8mb4 < /tmp/$(basename $backup_file)
        
        echo "✅ 数据库 $db_name 恢复完成"
        
        # 验证恢复结果
        echo "🔍 验证数据库恢复结果..."
        table_count=\$(docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$db_name';" -s -N 2>/dev/null)
        
        echo "📊 数据库 $db_name 包含 \$table_count 个表"
        
        # 清理临时文件
        rm -f "$remote_file"
        docker exec $MYSQL_CONTAINER rm -f /tmp/$(basename $backup_file)
EOF
    
    if [ $? -eq 0 ]; then
        log_success "✅ 数据库 $db_name 迁移成功"
        return 0
    else
        log_error "❌ 数据库 $db_name 迁移失败"
        return 1
    fi
}

# 函数：验证数据一致性
verify_data_consistency() {
    log_info "🔍 开始验证数据一致性..."
    
    echo ""
    echo -e "${CYAN}📊 数据一致性验证报告${NC}"
    echo "========================================"
    
    local all_consistent=true
    
    for db_name in "${DATABASES[@]}"; do
        echo ""
        echo -e "${BLUE}数据库: $db_name${NC}"
        
        # 获取表数量
        local_table_count=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
            -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$db_name';" -s -N 2>/dev/null || echo "0")
        
        linux_table_count=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
            -e \"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$db_name';\" -s -N" 2>/dev/null || echo "0")
        
        echo "  • 表数量: 本地($local_table_count) vs Linux($linux_table_count)"
        
        if [ "$local_table_count" -eq "$linux_table_count" ] && [ "$local_table_count" -gt 0 ]; then
            echo -e "  ${GREEN}✅ 表数量一致${NC}"
            
            # 如果是overdue_debt_db，特别检查关键数据
            if [ "$db_name" = "overdue_debt_db" ]; then
                local_2025_count=$(mysql -h"$LOCAL_DB_HOST" -u"$LOCAL_DB_USER" -p"$LOCAL_DB_PASSWORD" --skip-ssl \
                    -D"$db_name" --default-character-set=utf8mb4 \
                    -e "SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;" -s -N 2>/dev/null || echo "0")
                
                linux_2025_count=$(ssh "$LINUX_SERVER" "docker exec $MYSQL_CONTAINER mysql -u'$LOCAL_DB_USER' -p'$LOCAL_DB_PASSWORD' \
                    -D'$db_name' --default-character-set=utf8mb4 \
                    -e 'SELECT COUNT(*) FROM 新增表 WHERE 年份=2025;' -s -N" 2>/dev/null || echo "0")
                
                echo "  • 2025年数据: 本地($local_2025_count) vs Linux($linux_2025_count)"
                
                if [ "$local_2025_count" -eq "$linux_2025_count" ]; then
                    echo -e "  ${GREEN}✅ 关键数据一致${NC}"
                else
                    echo -e "  ${RED}❌ 关键数据不一致${NC}"
                    all_consistent=false
                fi
            fi
        else
            echo -e "  ${RED}❌ 表数量不一致${NC}"
            all_consistent=false
        fi
    done
    
    echo ""
    echo "========================================"
    
    if [ "$all_consistent" = true ]; then
        log_success "🎉 所有数据库验证通过，数据完全一致！"
        return 0
    else
        log_error "⚠️ 数据验证发现不一致，需要进一步检查"
        return 1
    fi
}

# 函数：重启应用服务
restart_services() {
    log_info "🔄 重启Linux应用服务..."
    
    ssh "$LINUX_SERVER" "docker restart financial-backend"
    
    log_info "⏳ 等待应用服务完全启动..."
    sleep 35
    
    # 验证服务状态
    if ssh "$LINUX_SERVER" "curl -f -s http://localhost:8080/actuator/health" > /dev/null 2>&1; then
        log_success "✅ 应用服务重启成功"
    else
        log_warning "⚠️ 应用服务状态检查失败，请手动验证"
    fi
}

# 主函数
main() {
    echo -e "${PURPLE}🚀 开始UltraThink完整数据库迁移...${NC}"
    echo ""
    
    # 阶段1: 备份本地数据库
    echo -e "${CYAN}=== 阶段1: 备份本地数据库 ===${NC}"
    
    local backup_files=()
    local backup_success=true
    
    for db_name in "${DATABASES[@]}"; do
        backup_file=$(backup_local_database "$db_name")
        if [ $? -eq 0 ]; then
            backup_files+=("$backup_file")
        else
            backup_success=false
            break
        fi
    done
    
    if [ "$backup_success" = false ]; then
        log_error "本地数据库备份失败，终止迁移"
        exit 1
    fi
    
    log_success "🎉 所有本地数据库备份完成！"
    
    # 阶段2: 备份Linux数据库（回滚保障）
    echo ""
    echo -e "${CYAN}=== 阶段2: 备份Linux数据库（回滚保障） ===${NC}"
    
    for db_name in "${DATABASES[@]}"; do
        backup_linux_database "$db_name"
    done
    
    # 阶段3: 迁移数据库
    echo ""
    echo -e "${CYAN}=== 阶段3: 迁移数据库到Linux ===${NC}"
    
    local migration_success=true
    for i in "${!DATABASES[@]}"; do
        db_name="${DATABASES[$i]}"
        backup_file="${backup_files[$i]}"
        
        if ! migrate_database "$db_name" "$backup_file"; then
            migration_success=false
            break
        fi
    done
    
    if [ "$migration_success" = false ]; then
        log_error "数据库迁移失败，请检查错误信息"
        exit 1
    fi
    
    log_success "🎉 所有数据库迁移完成！"
    
    # 阶段4: 验证数据一致性
    echo ""
    echo -e "${CYAN}=== 阶段4: 验证数据一致性 ===${NC}"
    
    if verify_data_consistency; then
        # 阶段5: 重启应用服务
        echo ""
        echo -e "${CYAN}=== 阶段5: 重启应用服务 ===${NC}"
        restart_services
        
        echo ""
        echo -e "${GREEN}🎉 完整数据库迁移成功完成！${NC}"
        echo ""
        echo -e "${YELLOW}📋 迁移结果摘要:${NC}"
        echo "• 已完整迁移 ${#DATABASES[@]} 个数据库到Linux服务器"
        echo "• 数据一致性验证通过"
        echo "• 应用服务已重启"
        echo "• 备份文件保存在: $BACKUP_DIR"
        echo ""
        echo -e "${YELLOW}🔍 验证步骤:${NC}"
        echo "1. 刷新Linux端数据更新观测平台"
        echo "2. 对比本地和Linux端数据显示"
        echo "3. 确认数据完全一致"
        
    else
        log_error "数据一致性验证失败"
        exit 1
    fi
    
    echo ""
    echo -e "${PURPLE}==================== 迁移完成 ====================${NC}"
}

# 执行主函数
main "$@"