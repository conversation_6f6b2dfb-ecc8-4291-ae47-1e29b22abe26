package com.laoshu198838.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 债权管理模块配置类
 * 确保债权管理相关的服务类能被Spring Boot正确扫描和注册
 *
 * <AUTHOR>
 */
@Configuration
@ComponentScan(basePackages = {
    "com.laoshu198838.service",
    "com.laoshu198838.validator",
    "com.laoshu198838.exception",
    "com.laoshu198838.repository",
    "com.laoshu198838.repository.overdue_debt"
})
@ConditionalOnClass(name = "com.laoshu198838.service.DebtManagementService")
public class DebtManagementConfig {

    // 配置类可以在这里添加特定的Bean配置

}
