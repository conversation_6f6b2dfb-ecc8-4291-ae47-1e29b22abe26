package com.laoshu198838.report.strategy;

import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.laoshu198838.export.excel.ExcelDataWriterFactory;
import com.laoshu198838.export.sql.QueryBuilderFactory;
import com.laoshu198838.report.config.ReportConfigManager;
import com.laoshu198838.report.config.TableExportConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.laoshu198838.util.database.SqlUtils.executeQuery;

/**
 * 标准表格导出策略
 * 用于表3、表4、表5、表8等标准表格的导出
 * 
 * <AUTHOR>
 */
@Component
public class StandardTableExportStrategy implements ReportExportStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(StandardTableExportStrategy.class);
    
    @Autowired
    private QueryBuilderFactory queryBuilderFactory;
    
    @Autowired
    private ExcelDataWriterFactory excelDataWriterFactory;
    
    @Autowired
    private ReportConfigManager configManager;
    
    // 支持的表格
    private static final Set<String> SUPPORTED_TABLES = Set.of("table3", "table4", "table5", "table8");
    
    @Override
    public String getStrategyName() {
        return "StandardTableExportStrategy";
    }
    
    @Override
    public boolean supports(String tableKey) {
        return SUPPORTED_TABLES.contains(tableKey);
    }
    
    @Override
    public void export(Connection connection, Workbook workbook, String tableKey, 
                      int year, int month, Map<String, Object> params) throws Exception {
        
        logger.info("🔹 [标准策略] 开始导出表格: {}", tableKey);
        
        // 获取表格配置
        TableExportConfig config = configManager.getTableConfig(tableKey);
        if (config == null) {
            throw new IllegalArgumentException("未找到表格配置: " + tableKey);
        }
        
        // 构建SQL查询
        var queryBuilder = queryBuilderFactory.getQueryBuilder(config.getDatabaseTableName());
        String sql = queryBuilder.buildSql(config.getDatabaseTableName(), year, month, params);
        List<Object> parameters = queryBuilder.buildParameters(config.getDatabaseTableName(), year, month, params);
        
        logger.debug("  📋 SQL: {}", sql.substring(0, Math.min(sql.length(), 100)) + "...");
        logger.debug("  📊 参数: {}", parameters);
        
        // 执行查询并写入Excel
        try (ResultSet rs = executeQuery(connection, sql, parameters.toArray())) {
            // 获取Excel工作表
            Worksheet sheet = workbook.getWorksheets().get(config.getExcelTableName());
            if (sheet == null) {
                logger.warn("  ⚠️ 未找到工作表: {}", config.getExcelTableName());
                return;
            }
            
            // 获取数据写入器并写入数据
            var dataWriter = excelDataWriterFactory.getDataWriter(config.getDatabaseTableName());
            dataWriter.writeData(sheet, rs, config.getDatabaseTableName(), year, month);
            
            logger.info("  ✅ 表格导出完成: {}", config.getExcelTableName());
            
            // 特殊处理表5-减值准备，同时导出长投工作表
            if ("table5".equals(tableKey)) {
                handleTable5LongInvestment(connection, workbook, config, sql, parameters, year, month);
            }
        }
    }
    
    @Override
    public int getPriority() {
        return 50; // 中等优先级
    }
    
    /**
     * 处理表5的长投工作表特殊逻辑
     */
    private void handleTable5LongInvestment(Connection connection, Workbook workbook, 
                                          TableExportConfig config, String sql, List<Object> parameters,
                                          int year, int month) throws Exception {
        try (ResultSet rs2 = executeQuery(connection, sql, parameters.toArray())) {
            // 获取长投工作表
            Worksheet longInvestSheet = workbook.getWorksheets().get("表5-减值准备(长投)");
            if (longInvestSheet != null) {
                // 使用标准数据写入器写入长投数据
                var dataWriter = excelDataWriterFactory.getDataWriter("减值准备表");
                dataWriter.writeData(longInvestSheet, rs2, "减值准备(长投)", year, month);
                logger.info("  ✅ 长投工作表导出完成: 表5-减值准备(长投)");
            } else {
                logger.warn("  ⚠️ 未找到长投工作表: 表5-减值准备(长投)");
            }
        }
    }
}