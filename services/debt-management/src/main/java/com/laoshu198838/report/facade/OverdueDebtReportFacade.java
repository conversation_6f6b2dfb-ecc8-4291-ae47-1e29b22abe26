package com.laoshu198838.report.facade;

import com.aspose.cells.Workbook;
import com.laoshu198838.report.strategy.ReportExportStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 逾期债权报表导出业务门面
 * 统一协调各种导出策略，提供高层业务接口
 * 
 * <AUTHOR>
 */
@Service
public class OverdueDebtReportFacade {
    
    private static final Logger logger = LoggerFactory.getLogger(OverdueDebtReportFacade.class);
    
    private final List<ReportExportStrategy> exportStrategies;
    
    @Autowired
    public OverdueDebtReportFacade(List<ReportExportStrategy> exportStrategies) {
        this.exportStrategies = exportStrategies;
        // 按优先级排序，确保高优先级策略优先执行
        this.exportStrategies.sort(Comparator.comparingInt(ReportExportStrategy::getPriority));
    }
    
    /**
     * 导出指定表格到Excel工作簿
     * 
     * @param connection 数据库连接
     * @param workbook Excel工作簿
     * @param tableKey 表格键（如table3, table4等）
     * @param year 年份
     * @param month 月份
     * @param params 额外参数
     * @throws Exception 导出失败时抛出异常
     */
    public void exportTable(Connection connection, Workbook workbook, String tableKey, 
                           int year, int month, Map<String, Object> params) throws Exception {
        
        logger.info("🚀 [业务门面] 开始导出表格: {} ({}年{}月)", tableKey, year, month);
        
        // 查找支持该表格的策略
        ReportExportStrategy selectedStrategy = null;
        for (ReportExportStrategy strategy : exportStrategies) {
            if (strategy.supports(tableKey)) {
                selectedStrategy = strategy;
                break;
            }
        }
        
        if (selectedStrategy == null) {
            throw new IllegalArgumentException("没有找到支持表格 '" + tableKey + "' 的导出策略");
        }
        
        logger.info("  📋 选择导出策略: {} (优先级: {})", 
                   selectedStrategy.getStrategyName(), selectedStrategy.getPriority());
        
        try {
            // 执行导出
            selectedStrategy.export(connection, workbook, tableKey, year, month, params);
            
            logger.info("  ✅ [业务门面] 表格导出成功: {}", tableKey);
            
        } catch (Exception e) {
            logger.error("  ❌ [业务门面] 表格导出失败: {} - 策略: {}", 
                        tableKey, selectedStrategy.getStrategyName(), e);
            
            // 如果不是回退策略失败，尝试寻找下一个可用策略
            if (selectedStrategy.getPriority() < 900) {
                logger.warn("  🔄 [业务门面] 尝试寻找回退策略...");
                tryFallbackStrategy(connection, workbook, tableKey, year, month, params, selectedStrategy);
            } else {
                throw e; // 回退策略也失败了，直接抛出异常
            }
        }
    }
    
    /**
     * 批量导出多个表格
     * 
     * @param connection 数据库连接
     * @param workbook Excel工作簿
     * @param tableKeys 表格键列表
     * @param year 年份
     * @param month 月份
     * @param params 额外参数
     * @return 导出结果摘要
     */
    public ExportSummary exportMultipleTables(Connection connection, Workbook workbook, 
                                            List<String> tableKeys, int year, int month, 
                                            Map<String, Object> params) {
        
        logger.info("🚀 [业务门面] 开始批量导出 {} 个表格", tableKeys.size());
        
        ExportSummary summary = new ExportSummary();
        
        for (String tableKey : tableKeys) {
            try {
                exportTable(connection, workbook, tableKey, year, month, params);
                summary.addSuccess(tableKey);
                
            } catch (Exception e) {
                logger.error("  ❌ [批量导出] 表格导出失败: {}", tableKey, e);
                summary.addFailure(tableKey, e.getMessage());
            }
        }
        
        logger.info("✅ [业务门面] 批量导出完成 - 成功: {}, 失败: {}", 
                   summary.getSuccessCount(), summary.getFailureCount());
        
        return summary;
    }
    
    /**
     * 尝试使用回退策略
     */
    private void tryFallbackStrategy(Connection connection, Workbook workbook, String tableKey,
                                   int year, int month, Map<String, Object> params, 
                                   ReportExportStrategy failedStrategy) throws Exception {
        
        // 查找回退策略（优先级更低的策略）
        ReportExportStrategy fallbackStrategy = null;
        for (ReportExportStrategy strategy : exportStrategies) {
            if (strategy != failedStrategy && 
                strategy.supports(tableKey) && 
                strategy.getPriority() > failedStrategy.getPriority()) {
                
                fallbackStrategy = strategy;
                break;
            }
        }
        
        if (fallbackStrategy != null) {
            logger.warn("  🔄 [回退策略] 使用策略: {}", fallbackStrategy.getStrategyName());
            fallbackStrategy.export(connection, workbook, tableKey, year, month, params);
            logger.info("  ✅ [回退策略] 表格导出成功: {}", tableKey);
        } else {
            throw new Exception("没有可用的回退策略处理表格: " + tableKey);
        }
    }
    
    /**
     * 获取所有可用的导出策略信息
     */
    public List<StrategyInfo> getAvailableStrategies() {
        return exportStrategies.stream()
                .map(strategy -> new StrategyInfo(
                    strategy.getStrategyName(), 
                    strategy.getPriority(),
                    getSupportedTables(strategy)
                ))
                .toList();
    }
    
    /**
     * 获取策略支持的表格列表
     */
    private List<String> getSupportedTables(ReportExportStrategy strategy) {
        List<String> allTables = List.of("table3", "table4", "table5", "table6", 
                                       "table7", "table8", "table9", "table10");
        return allTables.stream()
                .filter(strategy::supports)
                .toList();
    }
    
    /**
     * 导出结果摘要
     */
    public static class ExportSummary {
        private int successCount = 0;
        private int failureCount = 0;
        private final Map<String, String> failures = new java.util.HashMap<>();
        
        public void addSuccess(String tableKey) {
            successCount++;
        }
        
        public void addFailure(String tableKey, String errorMessage) {
            failureCount++;
            failures.put(tableKey, errorMessage);
        }
        
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public Map<String, String> getFailures() { return failures; }
        
        public boolean hasFailures() { return failureCount > 0; }
        
        @Override
        public String toString() {
            return String.format("导出摘要 - 成功: %d, 失败: %d", successCount, failureCount);
        }
    }
    
    /**
     * 策略信息
     */
    public static class StrategyInfo {
        private final String name;
        private final int priority;
        private final List<String> supportedTables;
        
        public StrategyInfo(String name, int priority, List<String> supportedTables) {
            this.name = name;
            this.priority = priority;
            this.supportedTables = supportedTables;
        }
        
        public String getName() { return name; }
        public int getPriority() { return priority; }
        public List<String> getSupportedTables() { return supportedTables; }
    }
}