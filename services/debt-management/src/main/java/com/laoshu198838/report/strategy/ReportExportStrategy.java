package com.laoshu198838.report.strategy;

import com.aspose.cells.Workbook;
import java.sql.Connection;
import java.util.Map;

/**
 * 报表导出策略接口
 * 
 * <AUTHOR>
 */
public interface ReportExportStrategy {
    
    /**
     * 获取策略名称
     */
    String getStrategyName();
    
    /**
     * 判断是否支持指定的表格
     */
    boolean supports(String tableKey);
    
    /**
     * 执行导出
     * 
     * @param connection 数据库连接
     * @param workbook Excel工作簿
     * @param tableKey 表格标识
     * @param year 年份
     * @param month 月份
     * @param params 额外参数
     * @throws Exception 导出异常
     */
    void export(Connection connection, Workbook workbook, String tableKey, 
                int year, int month, Map<String, Object> params) throws Exception;
    
    /**
     * 获取策略优先级（数字越小优先级越高）
     */
    default int getPriority() {
        return 100;
    }
}