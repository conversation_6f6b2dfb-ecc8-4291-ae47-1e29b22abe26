package com.laoshu198838.report.config;

import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 报表配置管理器
 * 
 * <AUTHOR>
 */
@Component
public class ReportConfigManager {
    
    /**
     * 表格配置映射
     */
    private static final Map<String, TableExportConfig> TABLE_CONFIGS = new HashMap<>();
    
    static {
        // 初始化表格配置
        TABLE_CONFIGS.put("table3", new TableExportConfig("table3", "诉讼表", "表3-涉诉", "exportToTable3"));
        TABLE_CONFIGS.put("table4", new TableExportConfig("table4", "非诉讼表", "表4-非涉诉", "exportToTable4"));
        TABLE_CONFIGS.put("table5", new TableExportConfig("table5", "减值准备表", "表5-减值准备", "exportToTable5", true));
        TABLE_CONFIGS.put("table7", new TableExportConfig("table7", "减值准备表", "表7-10万元及以下应收债权明细表", "exportToTable7", true));
        TABLE_CONFIGS.put("table8", new TableExportConfig("table8", "处置表", "表8-临3表", "exportToTable8"));
        TABLE_CONFIGS.put("table9", new TableExportConfig("table9", "新增表", "表9-新增逾期债权明细表", "exportToTable9", true));
        TABLE_CONFIGS.put("table10", new TableExportConfig("table10", "处置表", "表10-债权处置明细表", "exportToTable10", true));
    }
    
    /**
     * 获取表格配置
     */
    public TableExportConfig getTableConfig(String tableKey) {
        return TABLE_CONFIGS.get(tableKey);
    }
    
    /**
     * 获取所有表格键
     */
    public Set<String> getAllTableKeys() {
        return TABLE_CONFIGS.keySet();
    }
    
    /**
     * 获取需要特殊处理的表格键
     */
    public Set<String> getSpecialHandlingTableKeys() {
        return TABLE_CONFIGS.entrySet().stream()
                .filter(entry -> entry.getValue().isRequiresSpecialHandling())
                .map(Map.Entry::getKey)
                .collect(java.util.stream.Collectors.toSet());
    }
    
    /**
     * 获取标准处理的表格键
     */
    public Set<String> getStandardTableKeys() {
        return TABLE_CONFIGS.entrySet().stream()
                .filter(entry -> !entry.getValue().isRequiresSpecialHandling())
                .map(Map.Entry::getKey)
                .collect(java.util.stream.Collectors.toSet());
    }
}