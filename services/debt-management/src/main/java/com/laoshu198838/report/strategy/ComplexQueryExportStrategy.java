package com.laoshu198838.report.strategy;

import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;
import com.laoshu198838.export.excel.ExcelDataWriterFactory;
import com.laoshu198838.export.sql.QueryBuilderFactory;
import com.laoshu198838.report.config.ReportConfigManager;
import com.laoshu198838.report.config.TableExportConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.laoshu198838.util.database.SqlUtils.executeQuery;

/**
 * 复杂查询导出策略
 * 用于表7、表9、表10等需要复杂SQL查询的表格导出
 * 
 * <AUTHOR>
 */
@Component
public class ComplexQueryExportStrategy implements ReportExportStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(ComplexQueryExportStrategy.class);
    
    @Autowired
    private QueryBuilderFactory queryBuilderFactory;
    
    @Autowired
    private ExcelDataWriterFactory excelDataWriterFactory;
    
    @Autowired
    private ReportConfigManager configManager;
    
    // 支持的表格
    private static final Set<String> SUPPORTED_TABLES = Set.of("table7", "table9", "table10");
    
    @Override
    public String getStrategyName() {
        return "ComplexQueryExportStrategy";
    }
    
    @Override
    public boolean supports(String tableKey) {
        return SUPPORTED_TABLES.contains(tableKey);
    }
    
    @Override
    public void export(Connection connection, Workbook workbook, String tableKey, 
                      int year, int month, Map<String, Object> params) throws Exception {
        
        logger.info("🔸 [复杂策略] 开始导出表格: {}", tableKey);
        
        // 获取表格配置
        TableExportConfig config = configManager.getTableConfig(tableKey);
        if (config == null) {
            throw new IllegalArgumentException("未找到表格配置: " + tableKey);
        }
        
        // 根据不同表格确定实际的查询表名
        String queryTableName = getQueryTableName(tableKey, config);
        
        // 构建SQL查询
        var queryBuilder = queryBuilderFactory.getQueryBuilder(queryTableName);
        String sql = queryBuilder.buildSql(queryTableName, year, month, params);
        List<Object> parameters = queryBuilder.buildParameters(queryTableName, year, month, params);
        
        logger.debug("  📋 SQL: {}", sql.substring(0, Math.min(sql.length(), 200)) + "...");
        logger.debug("  📊 参数: {}", parameters);
        
        // 执行查询并写入Excel
        try (ResultSet rs = executeQuery(connection, sql, parameters.toArray())) {
            // 获取Excel工作表
            Worksheet sheet = workbook.getWorksheets().get(config.getExcelTableName());
            if (sheet == null) {
                logger.warn("  ⚠️ 未找到工作表: {}", config.getExcelTableName());
                return;
            }
            
            // 获取数据写入器并写入数据
            var dataWriter = excelDataWriterFactory.getDataWriter(config.getExcelTableName());
            dataWriter.writeData(sheet, rs, config.getExcelTableName(), year, month);
            
            logger.info("  ✅ 复杂表格导出完成: {}", config.getExcelTableName());
        }
    }
    
    @Override
    public int getPriority() {
        return 30; // 高优先级，优先处理复杂查询
    }
    
    /**
     * 根据表格键确定实际的查询表名
     */
    private String getQueryTableName(String tableKey, TableExportConfig config) {
        // 对于复杂查询，使用Excel表名作为查询标识
        return switch (tableKey) {
            case "table7" -> "表7-10万元及以下应收债权明细表";
            case "table9" -> "表9-新增逾期债权明细表";
            case "table10" -> "表10-债权处置明细表";
            default -> config.getDatabaseTableName();
        };
    }
}