package com.laoshu198838.report.config;

/**
 * 表格导出配置
 * 
 * <AUTHOR>
 */
public class TableExportConfig {
    
    private final String tableKey;
    private final String databaseTableName;
    private final String excelTableName;
    private final String exportMethod;
    private final boolean requiresSpecialHandling;
    
    public TableExportConfig(String tableKey, String databaseTableName, 
                           String excelTableName, String exportMethod) {
        this(tableKey, databaseTableName, excelTableName, exportMethod, false);
    }
    
    public TableExportConfig(String tableKey, String databaseTableName, 
                           String excelTableName, String exportMethod, 
                           boolean requiresSpecialHandling) {
        this.tableKey = tableKey;
        this.databaseTableName = databaseTableName;
        this.excelTableName = excelTableName;
        this.exportMethod = exportMethod;
        this.requiresSpecialHandling = requiresSpecialHandling;
    }
    
    // Getters
    public String getTableKey() { return tableKey; }
    public String getDatabaseTableName() { return databaseTableName; }
    public String getExcelTableName() { return excelTableName; }
    public String getExportMethod() { return exportMethod; }
    public boolean isRequiresSpecialHandling() { return requiresSpecialHandling; }
    
    @Override
    public String toString() {
        return String.format("TableExportConfig{tableKey='%s', databaseTable='%s', excelTable='%s'}", 
                           tableKey, databaseTableName, excelTableName);
    }
}