package com.laoshu198838.report.strategy;

import com.aspose.cells.Workbook;
import com.laoshu198838.export.UnifiedOverdueDebtExporter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.util.Map;

/**
 * 遗留系统回退导出策略
 * 当其他策略都失败时，使用完整的参考代码实现
 * 
 * <AUTHOR>
 */
@Component
public class LegacyFallbackExportStrategy implements ReportExportStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(LegacyFallbackExportStrategy.class);
    
    private final UnifiedOverdueDebtExporter unifiedExporter = new UnifiedOverdueDebtExporter();
    
    @Override
    public String getStrategyName() {
        return "LegacyFallbackExportStrategy";
    }
    
    @Override
    public boolean supports(String tableKey) {
        // 支持所有表格作为最后的回退选项
        return true;
    }
    
    @Override
    public void export(Connection connection, Workbook workbook, String tableKey, 
                      int year, int month, Map<String, Object> params) throws Exception {
        
        logger.warn("🔴 [回退策略] 使用遗留导出器处理表格: {}", tableKey);
        
        // 根据tableKey调用对应的导出方法
        UnifiedOverdueDebtExporter exporter = new UnifiedOverdueDebtExporter();
        
        try {
            switch (tableKey) {
                case "table3" -> exporter.exportToTable3(connection, workbook, year, month);
                case "table4" -> exporter.exportToTable4(connection, workbook, year, month);
                case "table5" -> exporter.exportToTable5(connection, workbook, year, month);
                case "table6" -> exporter.exportToTable6(connection, workbook, year, month);
                case "table7" -> {
                    int amount = (Integer) params.getOrDefault("amount", 10);
                    exporter.exportToTable7(connection, workbook, year, month, amount);
                }
                case "table8" -> exporter.exportToTable8(connection, workbook, year, month);
                case "table9" -> exporter.exportToTable9(connection, workbook, year, month);
                case "table10" -> exporter.exportToTable10(connection, workbook, year, month);
                default -> throw new IllegalArgumentException("不支持的表格: " + tableKey);
            }
            
            logger.warn("  ✅ [回退策略] 表格导出完成: {}", tableKey);
            
        } catch (Exception e) {
            logger.error("  ❌ [回退策略] 表格导出失败: {}", tableKey, e);
            throw e;
        }
    }
    
    @Override
    public int getPriority() {
        return 999; // 最低优先级，作为最后的回退选项
    }
}