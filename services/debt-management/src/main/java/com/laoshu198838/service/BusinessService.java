package com.laoshu198838.service;

import java.util.List;

/**
 * 统一的业务服务接口
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 */
public interface BusinessService<T, ID> {
    
    /**
     * 保存实体
     * @param entity 实体对象
     * @return 保存后的实体
     */
    T save(T entity);
    
    /**
     * 更新实体
     * @param entity 实体对象
     * @return 更新后的实体
     */
    T update(T entity);
    
    /**
     * 根据ID删除实体
     * @param id 主键ID
     */
    void delete(ID id);
    
    /**
     * 根据ID查找实体
     * @param id 主键ID
     * @return 实体对象，如果不存在则返回null
     */
    T findById(ID id);
    
    /**
     * 查找所有实体
     * @return 实体列表
     */
    List<T> findAll();
}
