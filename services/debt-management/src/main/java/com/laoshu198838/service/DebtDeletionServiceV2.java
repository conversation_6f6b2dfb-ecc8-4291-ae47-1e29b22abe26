package com.laoshu198838.service;

import com.laoshu198838.dto.debt.DebtDeletionDTO;
import com.laoshu198838.dto.debt.DebtDeletionResult;
import com.laoshu198838.entity.overdue_debt.*;
import com.laoshu198838.repository.overdue_debt.*;
import com.laoshu198838.util.debt.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 债权删除服务V2 - 优化版本
 * 
 * 主要改进：
 * 1. 不创建新的负数处置记录，而是直接更新现有记录
 * 2. 使用累加的方式处理多次删除
 * 3. 避免主键冲突问题
 *
 * <AUTHOR>
 */
@Slf4j
@Service("debtDeletionServiceV2")
@Transactional
public class DebtDeletionServiceV2 {

    @Autowired
    private OverdueDebtAddRepository overdueDebtAddRepository;

    @Autowired
    private OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;

    @Autowired
    private ImpairmentReserveRepository impairmentReserveRepository;

    @Autowired
    private LitigationClaimRepository litigationClaimRepository;

    @Autowired
    private NonLitigationClaimRepository nonLitigationClaimRepository;

    @Autowired
    private DataConsistencyCheckService dataConsistencyCheckService;

    @Autowired
    private AuditLogService auditLogService;
    
    @Autowired
    private OverdueDebtUpdateService overdueDebtUpdateService;

    /**
     * 删除处置债权 - 优化版本
     */
    public DebtDeletionResult deleteDisposalDebt(DebtDeletionDTO dto) {
        long startTime = System.currentTimeMillis();
        
        log.info("开始删除处置债权(V2): 债权人={}, 债务人={}, 年={}, 月={}, 金额={}", 
                dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth(), dto.getAmount());

        try {
            // 1. 验证请求
            validateDeletionRequest(dto);

            // 2. 记录审计日志
            Long auditLogId = auditLogService.logDeletion(
                "DELETE_DISPOSAL", 
                dto, 
                "删除处置债权(V2)"
            );

            Map<String, Object> affectedRecords = new HashMap<>();

            // 3. 查找现有的处置记录
            OverdueDebtDecrease.OverdueDebtDecreaseKey key = new OverdueDebtDecrease.OverdueDebtDecreaseKey();
            key.setCreditor(dto.getCreditor());
            key.setDebtor(dto.getDebtor());
            key.setPeriod(dto.getPeriod());
            key.setIsLitigation(dto.getIsLitigation());
            key.setYear(dto.getYear());
            key.setMonth(new BigDecimal(dto.getMonth()));

            Optional<OverdueDebtDecrease> existingRecord = overdueDebtDecreaseRepository.findById(key);
            
            if (existingRecord.isPresent()) {
                // 4. 更新现有记录 - 减去要删除的金额
                OverdueDebtDecrease record = existingRecord.get();
                
                BigDecimal originalAmount = record.getMonthlyReduceAmount() != null ? 
                                           record.getMonthlyReduceAmount() : BigDecimal.ZERO;
                BigDecimal newAmount = originalAmount.subtract(dto.getAmount());
                
                log.info("更新处置记录: 原金额={}, 删除金额={}, 新金额={}", 
                        originalAmount, dto.getAmount(), newAmount);
                
                // 更新金额
                record.setMonthlyReduceAmount(newAmount);
                
                // 更新处置方式明细
                if (dto.getCashDisposal() != null) {
                    BigDecimal currentCash = record.getCashDisposal() != null ? 
                                           record.getCashDisposal() : BigDecimal.ZERO;
                    record.setCashDisposal(currentCash.subtract(dto.getCashDisposal()));
                }
                if (dto.getInstallmentRepayment() != null) {
                    BigDecimal currentInstallment = record.getInstallmentRepayment() != null ? 
                                                  record.getInstallmentRepayment() : BigDecimal.ZERO;
                    record.setInstallmentRepayment(currentInstallment.subtract(dto.getInstallmentRepayment()));
                }
                if (dto.getAssetDebt() != null) {
                    BigDecimal currentAsset = record.getAssetDebt() != null ? 
                                            record.getAssetDebt() : BigDecimal.ZERO;
                    record.setAssetDebt(currentAsset.subtract(dto.getAssetDebt()));
                }
                if (dto.getOtherWays() != null) {
                    BigDecimal currentOther = record.getOtherWays() != null ? 
                                            record.getOtherWays() : BigDecimal.ZERO;
                    record.setOtherWays(currentOther.subtract(dto.getOtherWays()));
                }
                
                // 更新备注
                String originalRemark = record.getRemark() != null ? record.getRemark() : "";
                record.setRemark(originalRemark + " | 删除处置: " + dto.getAmount() + 
                               " (" + dto.getDeleteReason() + " " + LocalDateTime.now() + ")");
                
                record.setUpdateTime(LocalDateTime.now());
                overdueDebtDecreaseRepository.save(record);
                
                affectedRecords.put("处置表", record);
            } else {
                // 如果没有找到记录，创建一条负数记录
                log.warn("未找到处置记录，创建负数记录");
                OverdueDebtDecrease negativeRecord = createNegativeDisposalRecord(dto);
                overdueDebtDecreaseRepository.save(negativeRecord);
                affectedRecords.put("处置表", negativeRecord);
            }

            // 5. 更新新增表的处置金额
            updateAddTableDisposalAmount(dto, affectedRecords);

            // 6. 重新计算删除月份的余额
            recalculateBalancesForMonth(dto);

            // 7. 处理后续月份更新
            if (FiveTableUpdateHelper.needsSubsequentMonthsUpdate(createUpdateContext(dto))) {
                updateSubsequentMonthsWithBalanceTransfer(dto, affectedRecords);
            }

            // 8. 验证数据一致性
            validateDataConsistency(dto);

            DebtDeletionResult result = DebtDeletionResult.success("处置债权删除成功(V2)", affectedRecords)
                    .withAffectedTables("处置表", "新增表", "减值准备表",
                                       "是".equals(dto.getIsLitigation()) ? "诉讼表" : "非诉讼表")
                    .withAuditLogId(auditLogId);
            
            result.withExecutionTime(startTime);
            return result;

        } catch (Exception e) {
            log.error("删除处置债权失败(V2)", e);
            return DebtDeletionResult.failure(
                "删除操作失败: " + e.getMessage(),
                e.toString(),
                "DELETE_ERROR"
            ).withExecutionTime(startTime);
        }
    }

    /**
     * 创建负数处置记录（仅在没有现有记录时使用）
     */
    private OverdueDebtDecrease createNegativeDisposalRecord(DebtDeletionDTO dto) {
        OverdueDebtDecrease record = new OverdueDebtDecrease();
        
        // 设置联合主键
        OverdueDebtDecrease.OverdueDebtDecreaseKey key = new OverdueDebtDecrease.OverdueDebtDecreaseKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setPeriod(dto.getPeriod());
        key.setIsLitigation(dto.getIsLitigation());
        key.setYear(dto.getYear());
        key.setMonth(new BigDecimal(dto.getMonth()));
        record.setId(key);
        
        // 设置负数金额
        BigDecimal negativeAmount = dto.getAmount().negate();
        record.setMonthlyReduceAmount(negativeAmount);
        
        // 设置处置方式明细
        if (dto.getDisposalDetails() != null) {
            record.setCashDisposal(dto.getCashDisposal() != null ? 
                                  dto.getCashDisposal().negate() : BigDecimal.ZERO);
            record.setInstallmentRepayment(dto.getInstallmentRepayment() != null ? 
                                          dto.getInstallmentRepayment().negate() : BigDecimal.ZERO);
            record.setAssetDebt(dto.getAssetDebt() != null ? 
                               dto.getAssetDebt().negate() : BigDecimal.ZERO);
            record.setOtherWays(dto.getOtherWays() != null ? 
                               dto.getOtherWays().negate() : BigDecimal.ZERO);
        } else {
            // 默认全部计入现金处置
            record.setCashDisposal(negativeAmount);
            record.setInstallmentRepayment(BigDecimal.ZERO);
            record.setAssetDebt(BigDecimal.ZERO);
            record.setOtherWays(BigDecimal.ZERO);
        }
        
        // 设置其他字段
        record.setManagementCompany(dto.getManagementCompany());
        record.setRemark("删除处置记录: " + dto.getDeleteReason() + " (" + LocalDateTime.now() + ")");
        record.setUpdateTime(LocalDateTime.now());
        
        // 设置序号（使用负数表示删除记录）
        record.setSequence(-1);
        
        return record;
    }

    // 以下方法与原版本相同，可以从 DebtDeletionService 复制
    private void validateDeletionRequest(DebtDeletionDTO dto) {
        // 验证逻辑
    }

    private void updateAddTableDisposalAmount(DebtDeletionDTO dto, Map<String, Object> affectedRecords) {
        // 更新新增表逻辑
    }

    private void recalculateBalancesForMonth(DebtDeletionDTO dto) {
        // 重新计算余额逻辑
    }

    private void updateSubsequentMonthsWithBalanceTransfer(DebtDeletionDTO dto, Map<String, Object> affectedRecords) {
        // 更新后续月份逻辑
    }

    private FiveTableUpdateHelper.UpdateContext createUpdateContext(DebtDeletionDTO dto) {
        FiveTableUpdateHelper.UpdateContext context = new FiveTableUpdateHelper.UpdateContext();
        context.setCreditor(dto.getCreditor());
        context.setDebtor(dto.getDebtor());
        context.setManagementCompany(dto.getManagementCompany());
        context.setIsLitigation(dto.getIsLitigation());
        context.setPeriod(dto.getPeriod());
        context.setYear(dto.getYear());
        context.setMonth(dto.getMonth());
        context.setAmount(dto.getNegativeAmount());
        context.setRemark(dto.getRemark());
        context.setOperationTime(LocalDateTime.now());
        return context;
    }

    private void validateDataConsistency(DebtDeletionDTO dto) {
        // 数据一致性验证逻辑
    }
}