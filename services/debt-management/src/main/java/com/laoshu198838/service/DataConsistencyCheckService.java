package com.laoshu198838.service;

import com.laoshu198838.config.ChecksConfiguration;
import com.laoshu198838.config.ChecksConfiguration.CheckItem;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import com.laoshu198838.model.datamonitor.dto.*;
import com.laoshu198838.repository.overdue_debt.*;
import com.laoshu198838.repository.overdue_debt.ConsistencyCheckRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 数据一致性检查服务
 * 实现对数据的各种一致性检查
 *
 * <AUTHOR>
 */
@Service
public class DataConsistencyCheckService {

    private static final Logger logger = LoggerFactory.getLogger(DataConsistencyCheckService.class);

    private final OverdueDebtAddRepository overdueDebtAddRepository;
    private final OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;
    private final LitigationClaimRepository litigationClaimRepository;
    private final NonLitigationClaimRepository nonLitigationClaimRepository;
    private final ImpairmentReserveRepository impairmentReserveRepository;
    private final ConsistencyCheckRepository consistencyCheckRepository;
    private final ChecksConfiguration checksConfig;

    /**
     * 构造函数
     */
    @Autowired
    public DataConsistencyCheckService(
            OverdueDebtAddRepository overdueDebtAddRepository,
            OverdueDebtDecreaseRepository overdueDebtDecreaseRepository,
            LitigationClaimRepository litigationClaimRepository,
            NonLitigationClaimRepository nonLitigationClaimRepository,
            ImpairmentReserveRepository impairmentReserveRepository,
            ConsistencyCheckRepository consistencyCheckRepository,
            ChecksConfiguration checksConfig) {
        this.overdueDebtAddRepository = overdueDebtAddRepository;
        this.overdueDebtDecreaseRepository = overdueDebtDecreaseRepository;
        this.litigationClaimRepository = litigationClaimRepository;
        this.nonLitigationClaimRepository = nonLitigationClaimRepository;
        this.impairmentReserveRepository = impairmentReserveRepository;
        this.consistencyCheckRepository = consistencyCheckRepository;
        this.checksConfig = checksConfig;
    }

    /**
     * 检查新增表数据一致性
     * <p>
     * 检查内容：
     * 1. 检查1-12月数据求和是否等于新增金额字段列的求和
     * 2. 检查处置金额是否等于现金处置+分期还款+资产抵债和其他方式的总和
     * 3. 检查新增金额列求和-处置金额列求和是否等于债权余额
     *
     * @param year  年份
     * @param month 月份
     * @return 新增表数据一致性检查结果
     */
    public AddTableConsistencyResult checkAddTableConsistency(String year, String month) {
        AddTableConsistencyResult result = new AddTableConsistencyResult();
        result.setMonthlySumEqualsTotal(true);
        result.setDisposalAmountEquals(true);
        result.setBalanceEquals(true);

        try {
            List<OverdueDebtAdd> allRecords = overdueDebtAddRepository.findAll();
            List<OverdueDebtAdd> addTableRecords = new ArrayList<>();

            for (OverdueDebtAdd record : allRecords) {
                String recordYear = record.getYear();
                if (recordYear != null && recordYear.equals(year)) {
                    addTableRecords.add(record);
                }
            }

            if (addTableRecords.isEmpty()) {
                logger.warn("未找到年份为{}的新增表数据", year);

                result.setSummaryData(new AddTableConsistencyResult.SummaryData());
                result.getSummaryData().setMonthlySum(BigDecimal.ZERO);
                result.getSummaryData().setAddAmount(BigDecimal.ZERO);
                result.getSummaryData().setDisposalDetailSum(BigDecimal.ZERO);
                result.getSummaryData().setDisposalAmount(BigDecimal.ZERO);
                result.getSummaryData().setCalculatedBalance(BigDecimal.ZERO);
                result.getSummaryData().setActualBalance(BigDecimal.ZERO);
                result.setInconsistentRows(new ArrayList<>());

                return result;
            }

            BigDecimal totalMonthlySum = BigDecimal.ZERO;
            BigDecimal totalAddAmount = BigDecimal.ZERO;
            BigDecimal totalDisposalAmount = BigDecimal.ZERO;
            BigDecimal totalDisposalDetailSum = BigDecimal.ZERO;
            BigDecimal totalActualBalance = BigDecimal.ZERO;

            for (OverdueDebtAdd record : addTableRecords) {
                BigDecimal monthlySum = calculateMonthlySum(record);

                BigDecimal addAmount = record.getNewOverdueDebtAmount();

                BigDecimal disposalDetailSum = calculateDisposalDetailSum(record);

                BigDecimal disposalAmount = record.getReductionAmount();

                BigDecimal balance = record.getDebtBalance();
                BigDecimal calculatedBalance = addAmount.subtract(disposalAmount);

                totalMonthlySum = totalMonthlySum.add(monthlySum);
                totalAddAmount = totalAddAmount.add(addAmount);
                totalDisposalAmount = totalDisposalAmount.add(disposalAmount);
                totalDisposalDetailSum = totalDisposalDetailSum.add(disposalDetailSum);
                totalActualBalance = totalActualBalance.add(balance);

                boolean monthlyEqualToAdd = isEqual(monthlySum, addAmount);
                if (!monthlyEqualToAdd) {
                    result.setMonthlySumEqualsTotal(false);
                    addInconsistentRecord(result, record, monthlySum, addAmount, disposalAmount,
                                          disposalDetailSum, calculatedBalance, balance, "月度数据求和不等于新增金额");
                }

                boolean disposalEqualToDetail = isEqual(disposalAmount, disposalDetailSum);
                if (!disposalEqualToDetail) {
                    result.setDisposalAmountEquals(false);
                    addInconsistentRecord(result, record, monthlySum, addAmount, disposalAmount,
                                          disposalDetailSum, calculatedBalance, balance, "处置金额不等于处置明细求和");
                }

                boolean balanceCorrect = isEqual(calculatedBalance, balance);
                if (!balanceCorrect) {
                    result.setBalanceEquals(false);
                    addInconsistentRecord(result, record, monthlySum, addAmount, disposalAmount,
                                          disposalDetailSum, calculatedBalance, balance, "新增金额-处置金额不等于债权余额");
                }
            }

            result.getSummaryData().setMonthlySum(totalMonthlySum);
            result.getSummaryData().setAddAmount(totalAddAmount);
            result.getSummaryData().setDisposalAmount(totalDisposalAmount);
            result.getSummaryData().setDisposalDetailSum(totalDisposalDetailSum);
            result.getSummaryData().setCalculatedBalance(totalAddAmount.subtract(totalDisposalAmount));
            result.getSummaryData().setActualBalance(totalActualBalance);

            return result;
        } catch (Exception e) {
            logger.error("检查新增表数据一致性时发生错误", e);
            throw new RuntimeException("检查新增表数据一致性失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查处置表数据一致性
     * <p>
     * 检查内容：
     * 1. 检查处置表中的每月处置金额求和是否与现金处置、分期还款、资产抵债和其他方式求和相同
     *
     * @param year  年份
     * @param month 月份（通常用于显示）
     * @return 处置表数据一致性检查结果
     */
    public DisposalTableConsistencyResult checkDisposalTableConsistency(String year, String month) {
        DisposalTableConsistencyResult result = new DisposalTableConsistencyResult();
        result.setAmountEquals(true);

        try {
            // 将传入的String类型年份和月份转换为BigDecimal类型
            BigDecimal yearBD = new BigDecimal(year);
            // 起始月份为1月
            BigDecimal startMonthBD = new BigDecimal("1");
            BigDecimal endMonthBD = new BigDecimal(month);

            logger.info("查询年份: {}, 月份范围: {} - {}", year, startMonthBD, endMonthBD);

            // 使用原生SQL查询直接从数据库获取按债务人分组的汇总数据
            List<Map<String, Object>> disposalRecords = overdueDebtDecreaseRepository.findRecordsByYearAndMonthsBetween(yearBD, startMonthBD, endMonthBD);

            logger.info("查询到的总记录数: {}", disposalRecords.size());

            if (disposalRecords.isEmpty()) {
                logger.warn("未找到年份{}、月份1-{}的处置表数据", year, month);

                result.setSummaryData(new DisposalTableConsistencyResult.SummaryData());
                result.getSummaryData().setDebtAmount(BigDecimal.ZERO);
                result.getSummaryData().setDisposalSum(BigDecimal.ZERO);

                result.setTotalAmount(BigDecimal.ZERO);
                result.setDetailSum(BigDecimal.ZERO);
                result.setInconsistentRows(new ArrayList<>());

                return result;
            }

            // 使用原生SQL查询获取总处置金额
            BigDecimal totalDisposalAmount = overdueDebtDecreaseRepository.calculateTotalDisposalAmount(yearBD, startMonthBD, endMonthBD);

            // 系统汇总数据 - 初始化变量
            BigDecimal totalDebtAmount = BigDecimal.ZERO;

            // 处理每条记录，筛选出不一致的情况
            for (Map<String, Object> record : disposalRecords) {
                // 提取记录数据
                String debtor = (String) record.get("债务人");

                // 从Map中获取金额数据 (已经是SQL通过GROUP BY聚合的结果)
                BigDecimal debtAmount = record.get("每月处置金额") != null ? new BigDecimal(record.get("每月处置金额").toString()) : BigDecimal.ZERO;
                BigDecimal cashDisposal = record.get("现金处置") != null ? new BigDecimal(record.get("现金处置").toString()) : BigDecimal.ZERO;
                BigDecimal installmentRepayment = record.get("分期还款") != null ? new BigDecimal(record.get("分期还款").toString()) : BigDecimal.ZERO;
                BigDecimal assetDebt = record.get("资产抵债") != null ? new BigDecimal(record.get("资产抵债").toString()) : BigDecimal.ZERO;
                BigDecimal otherWays = record.get("其他方式") != null ? new BigDecimal(record.get("其他方式").toString()) : BigDecimal.ZERO;

                // 计算处置方式总和
                BigDecimal disposalSum = cashDisposal.add(installmentRepayment).add(assetDebt).add(otherWays);
                BigDecimal difference = debtAmount.subtract(disposalSum);

                // 累计到总债权金额（直接从SQL查询结果中累加）
                totalDebtAmount = totalDebtAmount.add(debtAmount);

                // 检查处置金额是否等于处置方式总和
                if (!isEqual(debtAmount, disposalSum)) {
                    result.setAmountEquals(false);

                    // 添加不一致记录到结果中
                    DisposalTableConsistencyResult.InconsistentDisposalRow inconsistentRow = new DisposalTableConsistencyResult.InconsistentDisposalRow();
                    inconsistentRow.setDebtor(debtor);
                    // 管理公司字段不再存在于结果中，设置为空
                    inconsistentRow.setManagementCompany("");
                    inconsistentRow.setDebtAmount(debtAmount);
                    inconsistentRow.setCashDisposal(cashDisposal);
                    inconsistentRow.setInstallmentRepayment(installmentRepayment);
                    inconsistentRow.setAssetDebt(assetDebt);
                    inconsistentRow.setOtherWays(otherWays);
                    inconsistentRow.setDisposalSum(disposalSum);
                    // 使用绝对值表示差异
                    inconsistentRow.setDifference(difference.abs());

                    result.getInconsistentRows().add(inconsistentRow);
                }
            }

            // 设置汇总数据用于前端展示
            result.getSummaryData().setDebtAmount(totalDebtAmount);
            result.getSummaryData().setDisposalSum(totalDisposalAmount);

            // 设置总金额信息
            result.setTotalAmount(totalDebtAmount);
            result.setDetailSum(totalDisposalAmount);

            logger.info("处置表数据一致性检查完成。债权金额总和: {}, SQL查询处置明细总和: {}",
                        totalDebtAmount, totalDisposalAmount);

        } catch (Exception e) {
            logger.error("处置表数据一致性检查出错", e);
            throw new RuntimeException("处置表数据一致性检查出错: " + e.getMessage());
        }

        return result;
    }

    /**
     * 计算月度数据求和
     * 使用OverdueDebtAdd实体中的月度字段
     */
    private BigDecimal calculateMonthlySum(OverdueDebtAdd record) {
        BigDecimal sum = BigDecimal.ZERO;

        // 累加各月金额
        if (record.getAmountJan() != null) sum = sum.add(record.getAmountJan());
        if (record.getAmountFeb() != null) sum = sum.add(record.getAmountFeb());
        if (record.getAmountMar() != null) sum = sum.add(record.getAmountMar());
        if (record.getAmountApr() != null) sum = sum.add(record.getAmountApr());
        if (record.getAmountMay() != null) sum = sum.add(record.getAmountMay());
        if (record.getAmountJun() != null) sum = sum.add(record.getAmountJun());
        if (record.getAmountJul() != null) sum = sum.add(record.getAmountJul());
        if (record.getAmountAug() != null) sum = sum.add(record.getAmountAug());
        if (record.getAmountSep() != null) sum = sum.add(record.getAmountSep());
        if (record.getAmountOct() != null) sum = sum.add(record.getAmountOct());
        if (record.getAmountNov() != null) sum = sum.add(record.getAmountNov());
        if (record.getAmountDec() != null) sum = sum.add(record.getAmountDec());

        return sum;
    }

    /**
     * 计算处置明细总和
     */
    private BigDecimal calculateDisposalDetailSum(OverdueDebtAdd record) {
        BigDecimal sum = BigDecimal.ZERO;

        // 累加各个处置方式金额
        if (record.getCashDisposal() != null) sum = sum.add(record.getCashDisposal());
        if (record.getInstallmentRepayment() != null) sum = sum.add(record.getInstallmentRepayment());
        if (record.getAssetDebt() != null) sum = sum.add(record.getAssetDebt());
        if (record.getOtherMethods() != null) sum = sum.add(record.getOtherMethods());

        return sum;
    }

    /**
     * 比较两个BigDecimal是否相等，考虑精度误差
     */
    private boolean isEqual(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) return true;
        if (a == null || b == null) return false;

        // 允许0.01的误差
        return a.subtract(b).abs().compareTo(new BigDecimal("0.01")) <= 0;
    }

    /**
     * 添加不一致记录到结果中（新增表）
     */
    private void addInconsistentRecord(
            AddTableConsistencyResult result,
            OverdueDebtAdd record,
            BigDecimal monthlySum,
            BigDecimal addAmount,
            BigDecimal disposalAmount,
            BigDecimal disposalDetailSum,
            BigDecimal calculatedBalance,
            BigDecimal balance,
            String inconsistencyType) {

        AddTableConsistencyResult.InconsistentAddRow row = new AddTableConsistencyResult.InconsistentAddRow();
        row.setDebtor(record.getDebtor());
        row.setManagementCompany(record.getManagementCompany());
        row.setMonthlySum(monthlySum);
        row.setAddAmount(addAmount);
        row.setDisposalAmount(disposalAmount);
        row.setDisposalDetailSum(disposalDetailSum);
        row.setAddMinusDisposal(calculatedBalance);
        row.setBalance(balance);
        row.setInconsistencyType(inconsistencyType);

        // 添加到结果中
        result.getInconsistentRows().add(row);
    }

    /**
     * 检查不同表之间债权变动一致性
     * <p>
     * 检查内容：
     * 1. 新增表中根据年份和月份计算新增金额
     * 2. 诉讼表中根据年份和月份计算新增金额
     * 3. 非诉讼表中根据年份和月份计算新增金额
     * 4. 减值准备表中根据年份和月份计算新增金额
     * 5. 比较以上四个表的新增金额是否一致
     *
     * @param year  年份
     * @param month 月份
     * @return 债权变动一致性检查结果
     */
    public DebtChangeConsistencyResult checkDebtChangeConsistency(String year, String month) {
        DebtChangeConsistencyResult result = new DebtChangeConsistencyResult();
        result.setConsistent(true);

        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            logger.info("开始检查债权变动一致性: 年份={}, 月份={}", year, month);

            // 1. 获取新增表数据
            List<OverdueDebtAdd> addTableRecords = overdueDebtAddRepository.findAll();
            BigDecimal addTableAmount = BigDecimal.ZERO;

            // 根据年份筛选记录并累加指定月份的金额
            for (OverdueDebtAdd record : addTableRecords) {
                if (record.getYear() != null && record.getYear().equals(year)) {
                    BigDecimal monthlyAmount = getMonthAmount(record, monthInt);
                    if (monthlyAmount != null) {
                        addTableAmount = addTableAmount.add(monthlyAmount);
                    }
                }
            }

            // 2. 获取诉讼表数据
            BigDecimal litigationAmount = litigationClaimRepository.sumCurrentMonthNewDebtByYearAndMonth(yearInt, monthInt);

            // 3. 获取非诉讼表数据
            BigDecimal nonLitigationAmount = nonLitigationClaimRepository.sumCurrentMonthIncreaseByYearAndMonth(yearInt, monthInt);

            // 4. 获取减值准备表数据
            BigDecimal impairmentAmount = impairmentReserveRepository.sumCurrentMonthNewDebtByYearAndMonth(yearInt, monthInt);

            // 5. 计算最大差异值
            BigDecimal maxDifference = calculateMaxDifference(addTableAmount, litigationAmount, nonLitigationAmount, impairmentAmount);

            // 设置汇总数据
            result.getSummaryData().setAddTableAmount(addTableAmount);
            result.getSummaryData().setLitigationTableAmount(litigationAmount);
            result.getSummaryData().setNonLitigationTableAmount(nonLitigationAmount);
            result.getSummaryData().setImpairmentReserveAmount(impairmentAmount);
            result.getSummaryData().setMaxDifference(maxDifference);

            // 如果最大差异超过0.01，则认为数据不一致
            if (maxDifference.compareTo(new BigDecimal("0.01")) > 0) {
                result.setConsistent(false);
            }

            // 收集每个债务人/债权人的数据进行比较
            Map<String, BigDecimal> addTableByDebtor = new HashMap<>();
            Map<String, BigDecimal> litigationByDebtor = new HashMap<>();
            Map<String, BigDecimal> nonLitigationByDebtor = new HashMap<>();
            Map<String, BigDecimal> impairmentByDebtor = new HashMap<>();

            // 收集新增表数据
            for (OverdueDebtAdd record : addTableRecords) {
                if (record.getYear() != null && record.getYear().equals(year)) {
                    String key = record.getDebtor() + "_" + record.getCreditor();
                    BigDecimal monthlyAmount = getMonthAmount(record, monthInt);

                    if (monthlyAmount != null) {
                        addTableByDebtor.put(key, addTableByDebtor.getOrDefault(key, BigDecimal.ZERO).add(monthlyAmount));
                    }
                }
            }

            // 收集诉讼表数据
            List<LitigationClaim> litigationRecords = litigationClaimRepository.findByYearAndMonth(yearInt, monthInt);
            for (LitigationClaim record : litigationRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();
                BigDecimal amount = record.getCurrentMonthNewDebt() != null ? record.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                litigationByDebtor.put(key, litigationByDebtor.getOrDefault(key, BigDecimal.ZERO).add(amount));
            }

            // 收集非诉讼表数据
            List<NonLitigationClaim> nonLitigationRecords = nonLitigationClaimRepository.findByYearAndMonth(yearInt, monthInt);
            for (NonLitigationClaim record : nonLitigationRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();

                BigDecimal principalIncrease = record.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                                               record.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                BigDecimal interestIncrease = record.getCurrentMonthInterestIncreaseDecrease() != null ?
                                              record.getCurrentMonthInterestIncreaseDecrease() : BigDecimal.ZERO;
                BigDecimal penaltyIncrease = record.getCurrentMonthPenaltyIncreaseDecrease() != null ?
                                             record.getCurrentMonthPenaltyIncreaseDecrease() : BigDecimal.ZERO;

                BigDecimal totalIncrease = principalIncrease.add(interestIncrease).add(penaltyIncrease);
                nonLitigationByDebtor.put(key, nonLitigationByDebtor.getOrDefault(key, BigDecimal.ZERO).add(totalIncrease));
            }

            // 收集减值准备表数据
            List<ImpairmentReserve> impairmentRecords = impairmentReserveRepository.findByIdYearAndIdMonth(yearInt, monthInt);
            for (ImpairmentReserve record : impairmentRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();
                BigDecimal amount = record.getCurrentMonthNewDebt() != null ? record.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                impairmentByDebtor.put(key, impairmentByDebtor.getOrDefault(key, BigDecimal.ZERO).add(amount));
            }

            // 合并所有键
            Set<String> allKeys = new HashSet<>();
            allKeys.addAll(addTableByDebtor.keySet());
            allKeys.addAll(litigationByDebtor.keySet());
            allKeys.addAll(nonLitigationByDebtor.keySet());
            allKeys.addAll(impairmentByDebtor.keySet());

            // 对每个键检查数据一致性
            for (String key : allKeys) {
                BigDecimal addValue = addTableByDebtor.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal litigationValue = litigationByDebtor.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal nonLitigationValue = nonLitigationByDebtor.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal impairmentValue = impairmentByDebtor.getOrDefault(key, BigDecimal.ZERO);

                // 计算最大差异
                BigDecimal difference = calculateMaxDifference(addValue, litigationValue, nonLitigationValue, impairmentValue);

                // 如果差异大于0.01，添加到不一致列表
                if (difference.compareTo(new BigDecimal("0.01")) > 0) {
                    DebtChangeConsistencyResult.TableDataRow row = new DebtChangeConsistencyResult.TableDataRow();
                    row.setDebtorCreditor(key.replace("_", " / "));
                    row.setAddTableAmount(addValue);
                    row.setLitigationTableAmount(litigationValue);
                    row.setNonLitigationTableAmount(nonLitigationValue);
                    row.setImpairmentReserveAmount(impairmentValue);
                    row.setDifference(difference);
                    row.setConsistencyStatus(difference.compareTo(new BigDecimal("0.01")) > 0 ? "不一致" : "一致");

                    result.getTableData().add(row);
                }
            }

            logger.info("债权变动一致性检查完成。新增表金额: {}, 诉讼表金额: {}, 非诉讼表金额: {}, 减值准备表金额: {}, 最大差异: {}",
                        addTableAmount, litigationAmount, nonLitigationAmount, impairmentAmount, maxDifference);

        } catch (NumberFormatException e) {
            logger.error("检查债权变动一致性时发生错误：无效的年份或月份格式", e);
            throw new IllegalArgumentException("年份或月份格式无效: " + e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            logger.error("检查债权变动一致性时发生参数错误", e);
            throw e;
        } catch (Exception e) {
            logger.error("检查债权变动一致性时发生未知错误", e);
            throw new RuntimeException("检查债权变动一致性失败: " + e.getMessage(), e);
        }

        return result;
    }


    /**
     * 根据月份获取OverdueDebtAdd实体中对应月份的金额
     */
    private BigDecimal getMonthAmount(OverdueDebtAdd record, int month) {
        // 使用switch表达式返回对应月份的金额
        return switch (month) {
            case 1 -> record.getAmountJan();
            case 2 -> record.getAmountFeb();
            case 3 -> record.getAmountMar();
            case 4 -> record.getAmountApr();
            case 5 -> record.getAmountMay();
            case 6 -> record.getAmountJun();
            case 7 -> record.getAmountJul();
            case 8 -> record.getAmountAug();
            case 9 -> record.getAmountSep();
            case 10 -> record.getAmountOct();
            case 11 -> record.getAmountNov();
            case 12 -> record.getAmountDec();
            default -> BigDecimal.ZERO;
        };
    }

    /**
     * 计算四个值之间的最大差异
     */
    private BigDecimal calculateMaxDifference(BigDecimal a, BigDecimal b, BigDecimal c, BigDecimal d) {
        BigDecimal[] values = {a, b, c, d};
        BigDecimal max = a;
        BigDecimal min = a;

        for (BigDecimal value : values) {
            if (value.compareTo(max) > 0) {
                max = value;
            }
            if (value.compareTo(min) < 0) {
                min = value;
            }
        }

        return max.subtract(min);
    }

    /**
     * 计算多个BigDecimal值之间的最大差异
     *
     * @param values BigDecimal值列表
     * @return 最大差异值
     */
    private BigDecimal calculateMaxDifference(List<BigDecimal> values) {
        if (values == null || values.size() <= 1) {
            return BigDecimal.ZERO;
        }

        BigDecimal max = values.get(0);
        BigDecimal min = values.get(0);

        for (BigDecimal value : values) {
            if (value != null) {
                if (value.compareTo(max) > 0) {
                    max = value;
                }
                if (value.compareTo(min) < 0) {
                    min = value;
                }
            }
        }

        return max.subtract(min);
    }

    /**
     * 获取所有债务人/债权人组合
     */
    private Set<String> getAllDebtorCreditorKeys(int year, int month) {
        Set<String> keys = new HashSet<>();

        // 从新增表获取
        List<OverdueDebtAdd> addTableRecords = overdueDebtAddRepository.findAll();
        for (OverdueDebtAdd record : addTableRecords) {
            if (record.getYear() != null && record.getYear().equals(String.valueOf(year))) {
                String key = record.getDebtor() + "_" + record.getCreditor();
                keys.add(key);
            }
        }

        // 从诉讼表获取
        List<LitigationClaim> litigationRecords = litigationClaimRepository.findByYearAndMonth(year, month);
        for (LitigationClaim record : litigationRecords) {
            String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();
            keys.add(key);
        }

        // 从非诉讼表获取
        List<NonLitigationClaim> nonLitigationRecords = nonLitigationClaimRepository.findByYearAndMonth(year, month);
        for (NonLitigationClaim record : nonLitigationRecords) {
            String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();
            keys.add(key);
        }

        // 从减值准备表获取
        List<ImpairmentReserve> impairmentRecords = impairmentReserveRepository.findByIdYearAndIdMonth(year, month);
        for (ImpairmentReserve record : impairmentRecords) {
            String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();
            keys.add(key);
        }

        return keys;
    }

    /**
     * 获取各表的总金额
     */
    private Map<String, BigDecimal> getTableTotals(CheckItem checkItem, int year, int month) {
        Map<String, BigDecimal> tableTotals = new HashMap<>();
        Map<String, String> tableFields = checkItem.getTables();

        // 新增表总金额
        if (tableFields.containsKey("新增表")) {
            String fieldName = tableFields.get("新增表");
            BigDecimal total = BigDecimal.ZERO;

            List<OverdueDebtAdd> addTableRecords = overdueDebtAddRepository.findAll();
            for (OverdueDebtAdd record : addTableRecords) {
                if (record.getYear() != null && record.getYear().equals(String.valueOf(year))) {
                    // 根据字段名获取对应的值
                    BigDecimal value = null;
                    if ("本月新增金额".equals(fieldName)) {
                        value = record.getNewOverdueDebtAmount();
                    } else if ("本月处置金额".equals(fieldName)) {
                        // 处置金额是现金处置+分期还款+资产抵债+其他方式的总和
                        value = record.getCashDisposal()
                                .add(record.getInstallmentRepayment())
                                .add(record.getAssetDebt())
                                .add(record.getOtherMethods());
                    } else if ("债权余额".equals(fieldName)) {
                        value = record.getDebtBalance();
                    }

                    if (value != null) {
                        total = total.add(value);
                    }
                }
            }

            tableTotals.put("新增表", total);
        }

        // 诉讼表总金额
        if (tableFields.containsKey("诉讼表")) {
            String fieldName = tableFields.get("诉讼表");
            BigDecimal total = BigDecimal.ZERO;

            List<LitigationClaim> litigationRecords = litigationClaimRepository.findByYearAndMonth(year, month);
            for (LitigationClaim record : litigationRecords) {
                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposalDebt();
                } else if ("本月末债权余额".equals(fieldName)) {
                    value = record.getCurrentMonthDebtBalance();
                }

                if (value != null) {
                    total = total.add(value);
                }
            }

            tableTotals.put("诉讼表", total);
        }

        // 非诉讼表总金额
        if (tableFields.containsKey("非诉讼表")) {
            String fieldName = tableFields.get("非诉讼表");
            BigDecimal total = BigDecimal.ZERO;

            List<NonLitigationClaim> nonLitigationRecords = nonLitigationClaimRepository.findByYearAndMonth(year, month);
            for (NonLitigationClaim record : nonLitigationRecords) {
                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposedDebt();
                } else if ("本月末本金".equals(fieldName)) {
                    value = record.getCurrentMonthPrincipal();
                }

                if (value != null) {
                    total = total.add(value);
                }
            }

            tableTotals.put("非诉讼表", total);
        }

        // 减值准备表总金额
        if (tableFields.containsKey("减值准备表")) {
            String fieldName = tableFields.get("减值准备表");
            BigDecimal total = BigDecimal.ZERO;

            List<ImpairmentReserve> impairmentRecords = impairmentReserveRepository.findByIdYearAndIdMonth(year, month);
            for (ImpairmentReserve record : impairmentRecords) {
                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposeDebt();
                } else if ("本月末债权余额".equals(fieldName)) {
                    value = record.getCurrentMonthBalance();
                }

                if (value != null) {
                    total = total.add(value);
                }
            }

            tableTotals.put("减值准备表", total);
        }

        return tableTotals;
    }

    /**
     * 获取一致性检查明细
     * 返回指定检查项的明细数据，只返回有差异的记录
     *
     * @param checkKey  检查项键名 (newAmount, disposedAmount, endingBalance)
     * @param yearMonth 年月格式如 "2024-12"
     * @return 一致性检查明细列表
     */
    public List<ConsistencyDetailItem> getConsistencyDetail(String checkKey, String yearMonth) {
        List<ConsistencyDetailItem> result = new ArrayList<>();

        try {
            // 解析年月字符串
            String[] parts = yearMonth.split("-");
            if (parts.length != 2) {
                throw new IllegalArgumentException("无效的年月格式，应为 'YYYY-MM'");
            }

            String year = parts[0];
            String month = parts[1];
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            logger.info("开始获取一致性检查明细: 检查项={}, 年月={}", checkKey, yearMonth);

            // 根据检查项类型获取明细数据
            List<Map<String, Object>> detailData = null;

            if ("newAmount".equals(checkKey)) {
                detailData = consistencyCheckRepository.getNewAmountDetail(yearInt);
            } else if ("disposedAmount".equals(checkKey)) {
                detailData = consistencyCheckRepository.getDisposedAmountDetail(yearInt);
            } else if ("endingBalance".equals(checkKey)) {
                detailData = consistencyCheckRepository.getEndingBalanceDetail(yearInt, monthInt);
            } else {
                logger.warn("无效的检查项类型: {}", checkKey);
                return result;
            }

            // 处理查询结果
            if (detailData != null && !detailData.isEmpty()) {
                for (Map<String, Object> row : detailData) {
                    ConsistencyDetailItem detailItem = new ConsistencyDetailItem();
                    ConsistencyDetailItem.RecordId recordId = new ConsistencyDetailItem.RecordId();

                    // 设置记录ID
                    recordId.setDebtor((String) row.get("debtor"));
                    recordId.setCreditor((String) row.get("creditor"));
                    recordId.setPeriod(yearMonth);
                    detailItem.setId(recordId);

                    // 设置表值
                    Map<String, BigDecimal> tableValues = new HashMap<>();
                    tableValues.put("新增表", toBigDecimal(row.get("addTableAmount")));
                    tableValues.put("诉讼表", toBigDecimal(row.get("litigationAmount")));
                    tableValues.put("非诉讼表", toBigDecimal(row.get("nonLitigationAmount")));
                    tableValues.put("减值准备表", toBigDecimal(row.get("impairmentAmount")));
                    detailItem.setTableValues(tableValues);

                    // 设置最大差异
                    detailItem.setMaxDifference(toBigDecimal(row.get("maxDifference")));

                    result.add(detailItem);
                }
            }

            logger.info("一致性检查明细获取完成，共找到{}条不一致记录", result.size());

        } catch (NumberFormatException e) {
            logger.error("获取一致性检查明细时发生错误：无效的年月格式", e);
            throw new IllegalArgumentException("年月格式无效: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("获取一致性检查明细时发生错误", e);
            throw new RuntimeException("获取一致性检查明细失败: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 收集每个债务人/债权人的数据
     */
    private Map<String, Map<String, BigDecimal>> collectDebtorCreditorData(CheckItem checkItem, int year, int month) {
        Map<String, Map<String, BigDecimal>> debtorCreditorData = new HashMap<>();
        Map<String, String> tableFields = checkItem.getTables();

        // 收集新增表数据
        if (tableFields.containsKey("新增表")) {
            String fieldName = tableFields.get("新增表");
            List<OverdueDebtAdd> addTableRecords = overdueDebtAddRepository.findAll();

            for (OverdueDebtAdd record : addTableRecords) {
                if (record.getYear() != null && record.getYear().equals(String.valueOf(year))) {
                    String key = record.getDebtor() + "_" + record.getCreditor();

                    // 根据字段名获取对应的值
                    BigDecimal value = null;
                    if ("本月新增金额".equals(fieldName)) {
                        value = record.getNewOverdueDebtAmount();
                    } else if ("本月处置金额".equals(fieldName)) {
                        // 处置金额是现金处置+分期还款+资产抵债+其他方式的总和
                        value = record.getCashDisposal()
                                .add(record.getInstallmentRepayment())
                                .add(record.getAssetDebt())
                                .add(record.getOtherMethods());
                    } else if ("债权余额".equals(fieldName)) {
                        value = record.getDebtBalance();
                    }

                    if (value != null) {
                        Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                        tableValues.put("新增表", value);
                        debtorCreditorData.put(key, tableValues);
                    }
                }
            }
        }

        // 收集诉讼表数据
        if (tableFields.containsKey("诉讼表")) {
            String fieldName = tableFields.get("诉讼表");
            List<LitigationClaim> litigationRecords = litigationClaimRepository.findByYearAndMonth(year, month);

            for (LitigationClaim record : litigationRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();

                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposalDebt();
                } else if ("本月末债权余额".equals(fieldName)) {
                    value = record.getCurrentMonthDebtBalance();
                }

                if (value != null) {
                    Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                    tableValues.put("诉讼表", value);
                    debtorCreditorData.put(key, tableValues);
                }
            }
        }

        // 收集非诉讼表数据
        if (tableFields.containsKey("非诉讼表")) {
            String fieldName = tableFields.get("非诉讼表");
            List<NonLitigationClaim> nonLitigationRecords = nonLitigationClaimRepository.findByYearAndMonth(year, month);

            for (NonLitigationClaim record : nonLitigationRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();

                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposedDebt();
                } else if ("本月末本金".equals(fieldName)) {
                    value = record.getCurrentMonthPrincipal();
                }

                if (value != null) {
                    Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                    tableValues.put("非诉讼表", value);
                    debtorCreditorData.put(key, tableValues);
                }
            }
        }

        // 收集减值准备表数据
        if (tableFields.containsKey("减值准备表")) {
            String fieldName = tableFields.get("减值准备表");
            List<ImpairmentReserve> impairmentRecords = impairmentReserveRepository.findByIdYearAndIdMonth(year, month);

            for (ImpairmentReserve record : impairmentRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();

                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposeDebt();
                } else if ("本月末债权余额".equals(fieldName)) {
                    value = record.getCurrentMonthBalance();
                }

                if (value != null) {
                    Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                    tableValues.put("减值准备表", value);
                    debtorCreditorData.put(key, tableValues);
                }
            }
        }

        return debtorCreditorData;
    }

    /**
     * 获取数据总览
     * 返回不同表之间的新增金额、处置金额和期末余额的汇总数据
     *
     * @param year  年份
     * @param month 月份
     * @return 数据总览结果
     */
    public DataOverviewResult getDataOverview(String year, String month) {
        DataOverviewResult result = new DataOverviewResult();

        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            logger.info("开始获取数据总览: 年份={}, 月份={}", year, month);

            // 获取年初余额汇总数据（上一年12月期末余额）
            Map<String, Object> initialBalanceData = consistencyCheckRepository.getInitialBalanceSummary(yearInt);
            if (initialBalanceData != null) {
                result.setAmount("initialBalance", "新增表", toBigDecimal(initialBalanceData.get("addTableAmount")));
                result.setAmount("initialBalance", "诉讼表", toBigDecimal(initialBalanceData.get("litigationAmount")));
                result.setAmount("initialBalance", "非诉讼表", toBigDecimal(initialBalanceData.get("nonLitigationAmount")));
                result.setAmount("initialBalance", "减值准备表", toBigDecimal(initialBalanceData.get("impairmentAmount")));
            }

            // 获取当月新增金额汇总数据
            Map<String, Object> newAmountData = consistencyCheckRepository.getNewAmountSummary(yearInt, monthInt);
            if (newAmountData != null) {
                result.setAmount("newAmount", "新增表", toBigDecimal(newAmountData.get("addTableAmount")));
                result.setAmount("newAmount", "诉讼表", toBigDecimal(newAmountData.get("litigationAmount")));
                result.setAmount("newAmount", "非诉讼表", toBigDecimal(newAmountData.get("nonLitigationAmount")));
                result.setAmount("newAmount", "减值准备表", toBigDecimal(newAmountData.get("impairmentAmount")));
            }

            // 获取当年累计新增金额汇总数据
            Map<String, Object> yearToDateNewAmountData = consistencyCheckRepository.getYearToDateNewAmountSummary(yearInt, monthInt);
            if (yearToDateNewAmountData != null) {
                result.setAmount("yearNewAmount", "新增表", toBigDecimal(yearToDateNewAmountData.get("addTableAmount")));
                result.setAmount("yearNewAmount", "诉讼表", toBigDecimal(yearToDateNewAmountData.get("litigationAmount")));
                result.setAmount("yearNewAmount", "非诉讼表", toBigDecimal(yearToDateNewAmountData.get("nonLitigationAmount")));
                result.setAmount("yearNewAmount", "减值准备表", toBigDecimal(yearToDateNewAmountData.get("impairmentAmount")));
            }

            // 获取当月处置金额汇总数据
            Map<String, Object> disposedAmountData = consistencyCheckRepository.getDisposedAmountSummary(yearInt, monthInt);
            if (disposedAmountData != null) {
                result.setAmount("disposedAmount", "新增表", toBigDecimal(disposedAmountData.get("addTableAmount")));
                result.setAmount("disposedAmount", "诉讼表", toBigDecimal(disposedAmountData.get("litigationAmount")));
                result.setAmount("disposedAmount", "非诉讼表", toBigDecimal(disposedAmountData.get("nonLitigationAmount")));
                result.setAmount("disposedAmount", "减值准备表", toBigDecimal(disposedAmountData.get("impairmentAmount")));
            }

            // 获取当年累计处置金额汇总数据
            Map<String, Object> yearToDateDisposedAmountData = consistencyCheckRepository.getYearToDateDisposedAmountSummary(yearInt, monthInt);
            if (yearToDateDisposedAmountData != null) {
                result.setAmount("yearDisposedAmount", "新增表", toBigDecimal(yearToDateDisposedAmountData.get("addTableAmount")));
                result.setAmount("yearDisposedAmount", "诉讼表", toBigDecimal(yearToDateDisposedAmountData.get("litigationAmount")));
                result.setAmount("yearDisposedAmount", "非诉讼表", toBigDecimal(yearToDateDisposedAmountData.get("nonLitigationAmount")));
                result.setAmount("yearDisposedAmount", "减值准备表", toBigDecimal(yearToDateDisposedAmountData.get("impairmentAmount")));
            }

            // 获取期末余额汇总数据
            Map<String, Object> endingBalanceData = consistencyCheckRepository.getEndingBalanceSummary(yearInt, monthInt);
            if (endingBalanceData != null) {
                result.setAmount("endingBalance", "新增表", toBigDecimal(endingBalanceData.get("addTableAmount")));
                result.setAmount("endingBalance", "诉讼表", toBigDecimal(endingBalanceData.get("litigationAmount")));
                result.setAmount("endingBalance", "非诉讼表", toBigDecimal(endingBalanceData.get("nonLitigationAmount")));
                result.setAmount("endingBalance", "减值准备表", toBigDecimal(endingBalanceData.get("impairmentAmount")));
            }

            logger.info("数据总览获取完成");

        } catch (NumberFormatException e) {
            logger.error("获取数据总览时发生错误：无效的年份或月份格式", e);
            throw new IllegalArgumentException("年份或月份格式无效: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("获取数据总览时发生错误", e);
            throw new RuntimeException("获取数据总览失败: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 将对象转换为BigDecimal
     */
    private BigDecimal toBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 收集不一致的行数据
     */
    private void collectInconsistentRows(CrossTableConsistencyResult.CheckItemResult checkResult,
                                         CheckItem checkItem,
                                         Set<String> allDebtorCreditorKeys,
                                         int year,
                                         int month) {
        Map<String, String> tableFields = checkItem.getTables();

        // 收集每个债务人/债权人的数据
        Map<String, Map<String, BigDecimal>> debtorCreditorData = new HashMap<>();

        // 收集新增表数据
        if (tableFields.containsKey("新增表")) {
            String fieldName = tableFields.get("新增表");
            List<OverdueDebtAdd> addTableRecords = overdueDebtAddRepository.findAll();

            for (OverdueDebtAdd record : addTableRecords) {
                if (record.getYear() != null && record.getYear().equals(String.valueOf(year))) {
                    String key = record.getDebtor() + "_" + record.getCreditor();

                    // 根据字段名获取对应的值
                    BigDecimal value = null;
                    if ("本月新增金额".equals(fieldName)) {
                        value = record.getNewOverdueDebtAmount();
                    } else if ("本月处置金额".equals(fieldName)) {
                        // 处置金额是现金处置+分期还款+资产抵债+其他方式的总和
                        value = record.getCashDisposal()
                                .add(record.getInstallmentRepayment())
                                .add(record.getAssetDebt())
                                .add(record.getOtherMethods());
                    } else if ("债权余额".equals(fieldName)) {
                        value = record.getDebtBalance();
                    }

                    if (value != null) {
                        Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                        tableValues.put("新增表", value);
                        debtorCreditorData.put(key, tableValues);
                    }
                }
            }
        }

        // 收集诉讼表数据
        if (tableFields.containsKey("诉讼表")) {
            String fieldName = tableFields.get("诉讼表");
            List<LitigationClaim> litigationRecords = litigationClaimRepository.findByYearAndMonth(year, month);

            for (LitigationClaim record : litigationRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();

                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposalDebt();
                } else if ("本月末债权余额".equals(fieldName)) {
                    value = record.getCurrentMonthDebtBalance();
                }

                if (value != null) {
                    Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                    tableValues.put("诉讼表", value);
                    debtorCreditorData.put(key, tableValues);
                }
            }
        }

        // 收集非诉讼表数据
        if (tableFields.containsKey("非诉讼表")) {
            String fieldName = tableFields.get("非诉讼表");
            List<NonLitigationClaim> nonLitigationRecords = nonLitigationClaimRepository.findByYearAndMonth(year, month);

            for (NonLitigationClaim record : nonLitigationRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();

                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposedDebt();
                } else if ("本月末本金".equals(fieldName)) {
                    value = record.getCurrentMonthPrincipal();
                }

                if (value != null) {
                    Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                    tableValues.put("非诉讼表", value);
                    debtorCreditorData.put(key, tableValues);
                }
            }
        }

        // 收集减值准备表数据
        if (tableFields.containsKey("减值准备表")) {
            String fieldName = tableFields.get("减值准备表");
            List<ImpairmentReserve> impairmentRecords = impairmentReserveRepository.findByIdYearAndIdMonth(year, month);

            for (ImpairmentReserve record : impairmentRecords) {
                String key = record.getId().getDebtor() + "_" + record.getId().getCreditor();

                // 根据字段名获取对应的值
                BigDecimal value = null;
                if ("本月新增债权".equals(fieldName)) {
                    value = record.getCurrentMonthNewDebt();
                } else if ("本月处置债权".equals(fieldName)) {
                    value = record.getCurrentMonthDisposeDebt();
                } else if ("本月末债权余额".equals(fieldName)) {
                    value = record.getCurrentMonthBalance();
                }

                if (value != null) {
                    Map<String, BigDecimal> tableValues = debtorCreditorData.getOrDefault(key, new HashMap<>());
                    tableValues.put("减值准备表", value);
                    debtorCreditorData.put(key, tableValues);
                }
            }
        }

        // 对每个债务人/债权人检查数据一致性
        for (String key : debtorCreditorData.keySet()) {
            Map<String, BigDecimal> tableValues = debtorCreditorData.get(key);

            // 只有当至少有两个表有数据时才进行比较
            if (tableValues.size() >= 2) {
                List<BigDecimal> values = new ArrayList<>(tableValues.values());
                BigDecimal maxDifference = calculateMaxDifference(values);

                // 如果最大差异超过0.01，则认为数据不一致
                if (maxDifference.compareTo(new BigDecimal("0.01")) > 0) {
                    CrossTableConsistencyResult.InconsistentRow row = new CrossTableConsistencyResult.InconsistentRow();
                    row.setDebtorCreditor(key.replace("_", " / "));
                    row.setTableValues(tableValues);
                    row.setMaxDifference(maxDifference);

                    checkResult.getInconsistentRows().add(row);
                }
            }
        }
    }
}