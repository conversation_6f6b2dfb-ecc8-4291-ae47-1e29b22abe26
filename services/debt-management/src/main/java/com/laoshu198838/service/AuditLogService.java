package com.laoshu198838.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.laoshu198838.dto.debt.DebtDeletionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 审计日志服务
 * 记录债权操作的审计日志
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuditLogService {

    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 记录删除操作日志
     */
    @Transactional
    public Long logDeletion(String operationType, DebtDeletionDTO dto, String description) {
        log.info("记录删除操作审计日志: 类型={}, 债权人={}, 债务人={}", 
                operationType, dto.getCreditor(), dto.getDebtor());

        try {
            // 如果没有配置审计日志表，只记录日志
            if (jdbcTemplate == null) {
                log.warn("审计日志表未配置，仅记录日志");
                return System.currentTimeMillis(); // 返回时间戳作为ID
            }

            // 构造SQL
            String sql = """
                INSERT INTO 审计日志表 (
                    操作类型, 表名, 债权人, 债务人, 期间, 年份, 月份,
                    删除金额, 删除原因, 操作人, 操作人ID, 操作时间, 操作IP, 
                    操作前数据, 操作后数据
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

            // 获取操作人信息（实际应该从安全上下文获取）
            String operatorName = dto.getOperatorName() != null ? dto.getOperatorName() : "系统";
            String operatorId = dto.getOperatorId() != null ? dto.getOperatorId() : "SYSTEM";
            String operationIp = dto.getOperationIp() != null ? dto.getOperationIp() : "127.0.0.1";

            // 序列化DTO为JSON
            String dtoJson = objectMapper.writeValueAsString(dto);

            // 执行插入
            jdbcTemplate.update(sql,
                operationType,
                getTableNameByType(operationType),
                dto.getCreditor(),
                dto.getDebtor(),
                dto.getPeriod(),
                dto.getYear(),
                dto.getMonth(),
                dto.getNegativeAmount(),
                dto.getDeleteReason(),
                operatorName,
                operatorId,
                LocalDateTime.now(),
                operationIp,
                dtoJson,  // 操作前数据
                null      // 操作后数据（删除完成后更新）
            );

            // 获取插入的ID（简化处理）
            Long auditId = jdbcTemplate.queryForObject("SELECT LAST_INSERT_ID()", Long.class);
            
            log.info("审计日志记录成功，ID: {}", auditId);
            return auditId;

        } catch (Exception e) {
            log.error("记录审计日志失败", e);
            // 审计日志失败不应该影响主业务
            return -1L;
        }
    }

    /**
     * 更新审计日志的操作后数据
     */
    public void updateAuditLogResult(Long auditId, Object resultData) {
        if (auditId == null || auditId <= 0) {
            return;
        }

        try {
            if (jdbcTemplate == null) {
                return;
            }

            String resultJson = objectMapper.writeValueAsString(resultData);
            String sql = "UPDATE 审计日志表 SET 操作后数据 = ? WHERE id = ?";
            jdbcTemplate.update(sql, resultJson, auditId);

        } catch (Exception e) {
            log.error("更新审计日志失败", e);
        }
    }

    /**
     * 记录通用操作日志
     */
    public void logOperation(String operationType, String tableName, 
                           String description, Object operationData) {
        log.info("记录操作日志: 类型={}, 表={}, 描述={}", operationType, tableName, description);
        
        try {
            if (jdbcTemplate == null) {
                return;
            }

            String dataJson = objectMapper.writeValueAsString(operationData);
            
            String sql = """
                INSERT INTO 操作日志表 (
                    操作类型, 表名, 描述, 操作数据, 操作时间
                ) VALUES (?, ?, ?, ?, ?)
                """;

            jdbcTemplate.update(sql, operationType, tableName, description, 
                              dataJson, LocalDateTime.now());

        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }

    /**
     * 根据操作类型获取表名
     */
    private String getTableNameByType(String operationType) {
        switch (operationType) {
            case "DELETE_ADDITION":
                return "新增表";
            case "DELETE_DISPOSAL":
                return "处置表";
            default:
                return "未知表";
        }
    }

    /**
     * 创建审计日志表（如果不存在）
     * 注意：实际生产环境应该通过数据库迁移脚本创建
     */
    public void createAuditTableIfNotExists() {
        if (jdbcTemplate == null) {
            return;
        }

        String createTableSql = """
            CREATE TABLE IF NOT EXISTS 审计日志表 (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                操作类型 VARCHAR(50) NOT NULL,
                表名 VARCHAR(50) NOT NULL,
                债权人 VARCHAR(30) NOT NULL,
                债务人 VARCHAR(30) NOT NULL,
                期间 VARCHAR(30),
                年份 INT,
                月份 INT,
                删除金额 DECIMAL(15,2),
                操作前数据 JSON,
                操作后数据 JSON,
                删除原因 VARCHAR(500),
                操作人 VARCHAR(50),
                操作人ID VARCHAR(50),
                操作时间 DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                操作IP VARCHAR(50),
                INDEX idx_creditor_debtor (债权人, 债务人),
                INDEX idx_operation_time (操作时间)
            ) COMMENT='债权删除操作审计日志' DEFAULT CHARSET=utf8mb4
            """;

        try {
            jdbcTemplate.execute(createTableSql);
            log.info("审计日志表创建成功");
        } catch (Exception e) {
            log.debug("审计日志表可能已存在: {}", e.getMessage());
        }
    }
}