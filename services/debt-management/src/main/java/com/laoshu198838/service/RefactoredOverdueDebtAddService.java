package com.laoshu198838.service;

import com.laoshu198838.entity.overdue_debt.*;
import com.laoshu198838.dto.debt.entity.OverdueDebtAddDTO;
import com.laoshu198838.repository.overdue_debt.*;
import com.laoshu198838.util.business.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 重构后的新增逾期债权服务类
 * 
 * 使用业务逻辑工具类重构原有的OverdueDebtAddService，提供：
 * 1. 更清晰的业务逻辑分离
 * 2. 更完善的数据验证
 * 3. 更准确的计算公式
 * 4. 更好的错误处理
 * 5. 完整的汇总关系验证
 * 
 * <AUTHOR>
 */
@Service
public class RefactoredOverdueDebtAddService {
    
    private static final Logger logger = LoggerFactory.getLogger(RefactoredOverdueDebtAddService.class);
    
    // Repository依赖
    private final OverdueDebtAddRepository overdueDebtAddRepository;
    private final ImpairmentReserveRepository impairmentReserveRepository;
    private final LitigationClaimRepository litigationClaimRepository;
    private final NonLitigationClaimRepository nonLitigationClaimRepository;
    private final OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;
    
    // 业务逻辑工具类依赖
    private final AddTableBusinessLogicUtil addTableUtil;
    private final ImpairmentReserveBusinessLogicUtil impairmentReserveUtil;
    private final LitigationBusinessLogicUtil litigationUtil;
    private final NonLitigationBusinessLogicUtil nonLitigationUtil;
    private final FieldMappingUtil fieldMappingUtil;
    private final BusinessRuleValidator businessRuleValidator;
    private final DataConsistencyValidator dataConsistencyValidator;
    
    @Autowired
    public RefactoredOverdueDebtAddService(
            OverdueDebtAddRepository overdueDebtAddRepository,
            ImpairmentReserveRepository impairmentReserveRepository,
            LitigationClaimRepository litigationClaimRepository,
            NonLitigationClaimRepository nonLitigationClaimRepository,
            OverdueDebtDecreaseRepository overdueDebtDecreaseRepository,
            AddTableBusinessLogicUtil addTableUtil,
            ImpairmentReserveBusinessLogicUtil impairmentReserveUtil,
            LitigationBusinessLogicUtil litigationUtil,
            NonLitigationBusinessLogicUtil nonLitigationUtil,
            FieldMappingUtil fieldMappingUtil,
            BusinessRuleValidator businessRuleValidator,
            DataConsistencyValidator dataConsistencyValidator) {
        
        this.overdueDebtAddRepository = overdueDebtAddRepository;
        this.impairmentReserveRepository = impairmentReserveRepository;
        this.litigationClaimRepository = litigationClaimRepository;
        this.nonLitigationClaimRepository = nonLitigationClaimRepository;
        this.overdueDebtDecreaseRepository = overdueDebtDecreaseRepository;
        this.addTableUtil = addTableUtil;
        this.impairmentReserveUtil = impairmentReserveUtil;
        this.litigationUtil = litigationUtil;
        this.nonLitigationUtil = nonLitigationUtil;
        this.fieldMappingUtil = fieldMappingUtil;
        this.businessRuleValidator = businessRuleValidator;
        this.dataConsistencyValidator = dataConsistencyValidator;
    }
    
    /**
     * 新增逾期债权的主要入口方法
     * 重构后的版本，使用业务逻辑工具类进行处理
     * 
     * @param dto 前端提交的逾期债权数据传输对象
     * @return 保存后的 OverdueDebtAdd 实体
     */
    @Transactional
    public OverdueDebtAdd addOverdueDebt(OverdueDebtAddDTO dto) {
        logger.info("开始处理新增逾期债权: 债权人={}, 债务人={}, 金额={}", 
                   dto.getCreditor(), dto.getDebtor(), dto.getOverdueAmount());
        
        try {
            // 1. 前端数据映射和验证
            Map<String, Object> frontendData = convertDtoToMap(dto);
            
            // 2. 更新新增表
            OverdueDebtAdd addRecord = updateAddTable(frontendData);
            
            // 3. 业务规则验证
            BusinessRuleValidator.BusinessRuleValidationResult validationResult = 
                businessRuleValidator.validateDebtAdditionRules(addRecord);
            
            if (!validationResult.isValid()) {
                logger.error("业务规则验证失败: {}", validationResult.getViolations());
                throw new RuntimeException("业务规则验证失败: " + String.join(", ", validationResult.getViolations()));
            }
            
            // 4. 更新减值准备表
            ImpairmentReserve impairmentReserve = updateImpairmentReserveTable(dto, addRecord);
            
            // 5. 根据是否涉诉更新相应表
            List<LitigationClaim> litigationClaims = new ArrayList<>();
            List<NonLitigationClaim> nonLitigationClaims = new ArrayList<>();
            
            if ("是".equals(dto.getIsLitigation())) {
                LitigationClaim litigationClaim = updateLitigationClaimTable(dto, addRecord);
                litigationClaims.add(litigationClaim);
            } else {
                NonLitigationClaim nonLitigationClaim = updateNonLitigationClaimTable(dto, addRecord);
                nonLitigationClaims.add(nonLitigationClaim);
            }
            
            // 6. 获取处置记录用于验证
            List<OverdueDebtDecrease> disposalRecords = getDisposalRecords(
                dto.getCreditor(), dto.getDebtor(), dto.getPeriod(), dto.getAddDate().split("-")[0]);
            
            // 7. 全面数据一致性验证
            String[] dateParts = dto.getAddDate().split("-");
            int year = Integer.parseInt(dateParts[0]);
            int month = Integer.parseInt(dateParts[1]);
            
            DataConsistencyValidator.ValidationResult consistencyResult = 
                dataConsistencyValidator.validateAllTablesConsistency(
                    dto.getCreditor(), dto.getDebtor(), dto.getPeriod(), year, month,
                    addRecord, impairmentReserve, litigationClaims, nonLitigationClaims, disposalRecords);
            
            if (!consistencyResult.isValid()) {
                logger.error("数据一致性验证失败: {}", consistencyResult.getErrors());
                throw new RuntimeException("数据一致性验证失败: " + String.join(", ", consistencyResult.getErrors()));
            }
            
            // 8. 记录警告信息
            if (!consistencyResult.getWarnings().isEmpty()) {
                logger.warn("数据一致性警告: {}", consistencyResult.getWarnings());
            }

            // 9. 更新后续月份数据
            updateSubsequentMonthsData(dto);

            logger.info("新增逾期债权处理完成: 债权人={}, 债务人={}, ID={}",
                       dto.getCreditor(), dto.getDebtor(), addRecord.getId());

            return addRecord;
            
        } catch (Exception e) {
            logger.error("新增逾期债权处理失败: 债权人={}, 债务人={}", dto.getCreditor(), dto.getDebtor(), e);
            throw new RuntimeException("新增逾期债权处理失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 更新新增表
     * 使用业务逻辑工具类进行处理
     */
    private OverdueDebtAdd updateAddTable(Map<String, Object> frontendData) {
        // 1. 映射前端数据到实体
        OverdueDebtAdd entity = fieldMappingUtil.mapToAddTableEntity(frontendData);
        
        // 2. 查找现有记录
        String creditor = entity.getCreditor();
        String debtor = entity.getDebtor();
        String period = entity.getPeriod();
        String isLitigation = entity.getIsLitigation();
        String year = entity.getYear();
        
        OverdueDebtAdd existingEntity = overdueDebtAddRepository
            .findByCreditorAndDebtorAndPeriodAndIsLitigationAndYear(
                creditor, debtor, period, isLitigation, year);
        
        if (existingEntity != null) {
            // 3. 更新现有记录
            logger.info("找到现有新增表记录，进行更新: ID={}", existingEntity.getId());
            
            // 合并数据（保留重要的历史信息）
            mergeAddTableData(existingEntity, entity);
            entity = existingEntity;
        } else {
            logger.info("创建新的新增表记录");
        }
        
        // 4. 获取处置记录并更新处置金额和债权余额
        List<OverdueDebtDecrease> disposalRecords = getDisposalRecords(creditor, debtor, period, year);
        addTableUtil.updateDisposalAndBalance(entity, disposalRecords);
        
        // 5. 验证数据完整性
        if (!addTableUtil.validateAddTableData(entity)) {
            throw new RuntimeException("新增表数据验证失败");
        }
        
        // 6. 保存实体
        entity.setUpdateTime(LocalDateTime.now());
        if (entity.getUpdateTime() == null) {
            entity.setUpdateTime(LocalDateTime.now());
        }
        
        return overdueDebtAddRepository.save(entity);
    }
    
    /**
     * 更新减值准备表
     * 使用业务逻辑工具类进行处理
     */
    private ImpairmentReserve updateImpairmentReserveTable(OverdueDebtAddDTO dto, OverdueDebtAdd addRecord) {
        String[] dateParts = dto.getAddDate().split("-");
        int year = Integer.parseInt(dateParts[0]);
        int month = Integer.parseInt(dateParts[1]);
        
        // 1. 构造主键查找现有记录
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setPeriod(dto.getPeriod());
        key.setIsLitigation(dto.getIsLitigation());
        key.setYear(year);
        key.setMonth(month);
        
        ImpairmentReserve entity = impairmentReserveRepository.findById(key).orElse(null);
        
        if (entity == null) {
            // 2. 创建新记录
            entity = fieldMappingUtil.mapAddToImpairmentReserve(addRecord, dto.getIsLitigation(), year, month);
            logger.info("创建新的减值准备表记录");
        } else {
            logger.info("找到现有减值准备表记录，进行更新: ID={}", entity.getId());
        }
        
        // 3. 更新债权相关字段
        BigDecimal newDebtAmount = dto.getOverdueAmount() != null ? dto.getOverdueAmount() : BigDecimal.ZERO;
        
        // 更新本月新增债权
        BigDecimal currentNewDebt = entity.getCurrentMonthNewDebt() != null ? 
            entity.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        entity.setCurrentMonthNewDebt(currentNewDebt.add(newDebtAmount));
        
        // 4. 计算债权余额平衡
        BigDecimal lastMonthBalance = entity.getLastMonthBalance() != null ? 
            entity.getLastMonthBalance() : BigDecimal.ZERO;
        BigDecimal currentMonthNewDebt = entity.getCurrentMonthNewDebt();
        BigDecimal currentMonthDisposeDebt = entity.getCurrentMonthDisposeDebt() != null ? 
            entity.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;
        
        BigDecimal currentMonthBalance = impairmentReserveUtil.calculateCurrentMonthDebtBalance(
            lastMonthBalance, currentMonthNewDebt, currentMonthDisposeDebt);
        entity.setCurrentMonthBalance(currentMonthBalance);
        
        // 5. 处理减值准备计提
        if (dto.getProvisionAmount() != null) {
            BigDecimal provisionAmount = dto.getProvisionAmount();
            BigDecimal previousBalance = entity.getPreviousMonthBalance() != null ? 
                entity.getPreviousMonthBalance() : BigDecimal.ZERO;
            
            // 设置本月增减
            entity.setCurrentMonthIncreaseDecrease(provisionAmount);
            
            // 计算本月末减值准备余额
            BigDecimal currentMonthAmount = previousBalance.add(provisionAmount);
            entity.setCurrentMonthAmount(currentMonthAmount);
            entity.setImpairmentAmount(currentMonthAmount);
            
            // 判断是否全额计提坏账
            boolean isFullImpairment = impairmentReserveUtil.isFullImpairment(
                currentMonthBalance, currentMonthAmount);
            entity.setIsAllImpaired(isFullImpairment ? "是" : "否");
        }
        
        // 6. 验证数据完整性
        if (!impairmentReserveUtil.validateImpairmentReserveData(entity)) {
            throw new RuntimeException("减值准备表数据验证失败");
        }
        
        // 7. 保存实体
        entity.setUpdateTime(LocalDateTime.now());
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(LocalDateTime.now());
        }
        
        return impairmentReserveRepository.save(entity);
    }
    
    /**
     * 更新诉讼表
     * 使用业务逻辑工具类进行处理
     */
    private LitigationClaim updateLitigationClaimTable(OverdueDebtAddDTO dto, OverdueDebtAdd addRecord) {
        String[] dateParts = dto.getAddDate().split("-");
        int year = Integer.parseInt(dateParts[0]);
        int month = Integer.parseInt(dateParts[1]);
        
        // 1. 查找现有记录
        Optional<LitigationClaim> existingRecord = litigationClaimRepository
            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                dto.getCreditor(), dto.getDebtor(), year, month, dto.getPeriod());
        
        LitigationClaim entity;
        if (existingRecord.isPresent()) {
            entity = existingRecord.get();
            logger.info("找到现有诉讼表记录，进行更新: ID={}", entity.getId());
        } else {
            // 2. 创建新记录
            entity = fieldMappingUtil.mapAddToLitigationClaim(addRecord, year, month);
            logger.info("创建新的诉讼表记录");
        }
        
        // 3. 更新案件名称和余额调整
        litigationUtil.updateCaseNameAndBalance(entity);
        
        // 4. 更新债权相关字段
        BigDecimal newDebtAmount = dto.getOverdueAmount() != null ? dto.getOverdueAmount() : BigDecimal.ZERO;
        
        // 更新本月新增债权
        BigDecimal currentNewDebt = entity.getCurrentMonthNewDebt() != null ? 
            entity.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        entity.setCurrentMonthNewDebt(currentNewDebt.add(newDebtAmount));
        
        // 5. 计算本月末债权余额
        BigDecimal lastMonthBalance = entity.getLastMonthDebtBalance() != null ? 
            entity.getLastMonthDebtBalance() : BigDecimal.ZERO;
        BigDecimal currentMonthDisposal = entity.getCurrentMonthDisposalDebt() != null ? 
            entity.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
        
        BigDecimal currentMonthBalance = litigationUtil.calculateCurrentMonthDebtBalance(
            lastMonthBalance, entity.getCurrentMonthNewDebt(), currentMonthDisposal);
        entity.setCurrentMonthDebtBalance(currentMonthBalance);
        
        // 6. 验证数据完整性
        if (!litigationUtil.validateLitigationClaimData(entity)) {
            throw new RuntimeException("诉讼表数据验证失败");
        }
        
        // 7. 保存实体
        return litigationClaimRepository.save(entity);
    }
    
    // 辅助方法
    private Map<String, Object> convertDtoToMap(OverdueDebtAddDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("creditor", dto.getCreditor());
        map.put("debtor", dto.getDebtor());
        map.put("period", dto.getPeriod());
        map.put("isLitigation", dto.getIsLitigation());
        map.put("year", dto.getAddDate().split("-")[0]);
        map.put("managementCompany", dto.getManagementCompany());
        map.put("subjectName", dto.getSubjectName());
        map.put("debtNature", dto.getDebtNature());
        map.put("debtCategory", dto.getDebtCategory());
        map.put("responsiblePerson", dto.getResponsiblePerson());
        map.put("measures", dto.getMeasures());
        map.put("overdueDate", dto.getOverdueDate());
        map.put("overdueAmount", dto.getOverdueAmount());
        return map;
    }
    
    private void mergeAddTableData(OverdueDebtAdd existing, OverdueDebtAdd newData) {
        // 合并重要字段，保留历史信息
        if (newData.getManagementCompany() != null) {
            existing.setManagementCompany(newData.getManagementCompany());
        }
        if (newData.getSubjectName() != null) {
            existing.setSubjectName(newData.getSubjectName());
        }
        if (newData.getDebtNature() != null) {
            existing.setDebtNature(newData.getDebtNature());
        }
        if (newData.getResponsiblePerson() != null) {
            // 追加责任人，避免重复
            String currentPerson = existing.getResponsiblePerson();
            if (currentPerson == null || !currentPerson.contains(newData.getResponsiblePerson())) {
                existing.setResponsiblePerson(
                    currentPerson != null ? currentPerson + ", " + newData.getResponsiblePerson() 
                                         : newData.getResponsiblePerson());
            }
        }
        if (newData.getRemark() != null) {
            // 追加备注
            String currentRemark = existing.getRemark();
            existing.setRemark(
                currentRemark != null ? currentRemark + "; " + newData.getRemark() 
                                     : newData.getRemark());
        }
    }
    
    /**
     * 更新非诉讼表
     * 使用业务逻辑工具类进行处理
     */
    private NonLitigationClaim updateNonLitigationClaimTable(OverdueDebtAddDTO dto, OverdueDebtAdd addRecord) {
        String[] dateParts = dto.getAddDate().split("-");
        int year = Integer.parseInt(dateParts[0]);
        int month = Integer.parseInt(dateParts[1]);

        // 1. 查找现有记录
        Optional<NonLitigationClaim> existingRecord = nonLitigationClaimRepository
            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                dto.getCreditor(), dto.getDebtor(), year, month, dto.getPeriod());

        NonLitigationClaim entity;
        if (existingRecord.isPresent()) {
            entity = existingRecord.get();
            logger.info("找到现有非诉讼表记录，进行更新: ID={}", entity.getId());
        } else {
            // 2. 创建新记录
            entity = fieldMappingUtil.mapAddToNonLitigationClaim(addRecord, year, month);
            logger.info("创建新的非诉讼表记录");
        }

        // 3. 更新债权相关字段
        BigDecimal newDebtAmount = dto.getOverdueAmount() != null ? dto.getOverdueAmount() : BigDecimal.ZERO;

        // 更新本月新增债权
        BigDecimal currentNewDebt = entity.getCurrentMonthNewDebt() != null ?
            entity.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        entity.setCurrentMonthNewDebt(currentNewDebt.add(newDebtAmount));

        // 4. 使用业务逻辑工具类进行完整计算
        nonLitigationUtil.updateCompleteCalculation(entity);

        // 5. 验证数据完整性
        if (!nonLitigationUtil.validateNonLitigationClaimData(entity)) {
            throw new RuntimeException("非诉讼表数据验证失败");
        }

        // 6. 保存实体
        return nonLitigationClaimRepository.save(entity);
    }

    /**
     * 批量更新后续月份数据
     * 当在非当前月份新增债权时，需要更新后续所有月份的数据
     */
    @Transactional
    public void updateSubsequentMonthsData(OverdueDebtAddDTO dto) {
        String[] dateParts = dto.getAddDate().split("-");
        int addYear = Integer.parseInt(dateParts[0]);
        int addMonth = Integer.parseInt(dateParts[1]);

        // 获取当前年月
        Calendar currentCal = Calendar.getInstance();
        int currentYear = currentCal.get(Calendar.YEAR);
        int currentMonth = currentCal.get(Calendar.MONTH) + 1;

        // 如果新增月份不是当前月份，则需要更新后续月份
        if (addYear < currentYear || (addYear == currentYear && addMonth < currentMonth)) {
            logger.info("检测到在非当前月份({}-{})新增债权，将更新后续月份数据", addYear, addMonth);

            // 从下一个月开始更新
            Calendar nextMonthCal = Calendar.getInstance();
            nextMonthCal.set(Calendar.YEAR, addYear);
            nextMonthCal.set(Calendar.MONTH, addMonth - 1);
            nextMonthCal.add(Calendar.MONTH, 1);

            while (nextMonthCal.get(Calendar.YEAR) < currentYear ||
                   (nextMonthCal.get(Calendar.YEAR) == currentYear &&
                    nextMonthCal.get(Calendar.MONTH) + 1 <= currentMonth)) {

                int nextYear = nextMonthCal.get(Calendar.YEAR);
                int nextMonth = nextMonthCal.get(Calendar.MONTH) + 1;

                // 更新减值准备表
                updateSubsequentImpairmentReserve(dto, nextYear, nextMonth);

                // 根据是否涉诉更新相应表
                if ("是".equals(dto.getIsLitigation())) {
                    updateSubsequentLitigationClaim(dto, nextYear, nextMonth);
                } else {
                    updateSubsequentNonLitigationClaim(dto, nextYear, nextMonth);
                }

                nextMonthCal.add(Calendar.MONTH, 1);
            }
        }
    }

    /**
     * 更新后续月份的减值准备表
     */
    private void updateSubsequentImpairmentReserve(OverdueDebtAddDTO dto, int year, int month) {
        ImpairmentReserve.ImpairmentReserveKey key = new ImpairmentReserve.ImpairmentReserveKey();
        key.setCreditor(dto.getCreditor());
        key.setDebtor(dto.getDebtor());
        key.setPeriod(dto.getPeriod());
        key.setIsLitigation(dto.getIsLitigation());
        key.setYear(year);
        key.setMonth(month);

        Optional<ImpairmentReserve> existingRecord = impairmentReserveRepository.findById(key);

        if (existingRecord.isPresent()) {
            ImpairmentReserve entity = existingRecord.get();

            // 获取上月数据进行更新
            ImpairmentReserve.ImpairmentReserveKey prevKey = new ImpairmentReserve.ImpairmentReserveKey();
            prevKey.setCreditor(dto.getCreditor());
            prevKey.setDebtor(dto.getDebtor());
            prevKey.setPeriod(dto.getPeriod());
            prevKey.setIsLitigation(dto.getIsLitigation());
            prevKey.setYear(month == 1 ? year - 1 : year);
            prevKey.setMonth(month == 1 ? 12 : month - 1);

            Optional<ImpairmentReserve> prevRecord = impairmentReserveRepository.findById(prevKey);

            if (prevRecord.isPresent()) {
                ImpairmentReserve prev = prevRecord.get();

                // 更新上月末余额
                entity.setPreviousMonthBalance(prev.getCurrentMonthAmount());
                entity.setLastMonthBalance(prev.getCurrentMonthBalance());

                // 重新计算本月末余额
                BigDecimal increaseDecrease = entity.getCurrentMonthIncreaseDecrease() != null ?
                    entity.getCurrentMonthIncreaseDecrease() : BigDecimal.ZERO;
                BigDecimal currentMonthAmount = prev.getCurrentMonthAmount().add(increaseDecrease);
                entity.setCurrentMonthAmount(currentMonthAmount);
                entity.setImpairmentAmount(currentMonthAmount);

                // 重新计算债权余额
                BigDecimal newDebt = entity.getCurrentMonthNewDebt() != null ?
                    entity.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                BigDecimal disposeDebt = entity.getCurrentMonthDisposeDebt() != null ?
                    entity.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;

                BigDecimal currentMonthBalance = impairmentReserveUtil.calculateCurrentMonthDebtBalance(
                    prev.getCurrentMonthBalance(), newDebt, disposeDebt);
                entity.setCurrentMonthBalance(currentMonthBalance);

                entity.setUpdateTime(LocalDateTime.now());
                impairmentReserveRepository.save(entity);

                logger.info("更新后续月份({}-{})减值准备表数据完成", year, month);
            }
        }
    }

    /**
     * 更新后续月份的诉讼表
     */
    private void updateSubsequentLitigationClaim(OverdueDebtAddDTO dto, int year, int month) {
        Optional<LitigationClaim> existingRecord = litigationClaimRepository
            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                dto.getCreditor(), dto.getDebtor(), year, month, dto.getPeriod());

        if (existingRecord.isPresent()) {
            LitigationClaim entity = existingRecord.get();

            // 获取上月数据
            Optional<LitigationClaim> prevRecord = litigationClaimRepository
                .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                    dto.getCreditor(), dto.getDebtor(),
                    month == 1 ? year - 1 : year,
                    month == 1 ? 12 : month - 1,
                    dto.getPeriod());

            if (prevRecord.isPresent()) {
                LitigationClaim prev = prevRecord.get();

                // 更新上月末债权余额
                entity.setLastMonthDebtBalance(prev.getCurrentMonthDebtBalance());

                // 重新计算本月末债权余额
                BigDecimal newDebt = entity.getCurrentMonthNewDebt() != null ?
                    entity.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                BigDecimal disposeDebt = entity.getCurrentMonthDisposalDebt() != null ?
                    entity.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;

                BigDecimal currentMonthBalance = litigationUtil.calculateCurrentMonthDebtBalance(
                    prev.getCurrentMonthDebtBalance(), newDebt, disposeDebt);
                entity.setCurrentMonthDebtBalance(currentMonthBalance);

                litigationClaimRepository.save(entity);

                logger.info("更新后续月份({}-{})诉讼表数据完成", year, month);
            }
        }
    }

    /**
     * 更新后续月份的非诉讼表
     */
    private void updateSubsequentNonLitigationClaim(OverdueDebtAddDTO dto, int year, int month) {
        Optional<NonLitigationClaim> existingRecord = nonLitigationClaimRepository
            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                dto.getCreditor(), dto.getDebtor(), year, month, dto.getPeriod());

        if (existingRecord.isPresent()) {
            NonLitigationClaim entity = existingRecord.get();

            // 获取上月数据
            Optional<NonLitigationClaim> prevRecord = nonLitigationClaimRepository
                .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                    dto.getCreditor(), dto.getDebtor(),
                    month == 1 ? year - 1 : year,
                    month == 1 ? 12 : month - 1,
                    dto.getPeriod());

            if (prevRecord.isPresent()) {
                NonLitigationClaim prev = prevRecord.get();

                // 更新上月末数据
                entity.setLastMonthPrincipal(prev.getCurrentMonthPrincipal());
                entity.setLastMonthInterest(prev.getCurrentMonthInterest());
                entity.setLastMonthPenalty(prev.getCurrentMonthPenalty());

                // 使用业务逻辑工具类重新计算
                nonLitigationUtil.updateCompleteCalculation(entity);

                nonLitigationClaimRepository.save(entity);

                logger.info("更新后续月份({}-{})非诉讼表数据完成", year, month);
            }
        }
    }

    private List<OverdueDebtDecrease> getDisposalRecords(String creditor, String debtor, String period, String year) {
        return overdueDebtDecreaseRepository.findByDebtorAndPeriod(debtor, period);
    }
}
