package com.laoshu198838.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 负数处置逻辑测试类
 * 
 * 测试通过负数实现删除处置记录的逻辑，包括：
 * 1. 负数处置记录的创建和验证
 * 2. 负数处置对债权余额的影响
 * 3. 后续月份更新逻辑
 * 4. 数据一致性验证
 * 
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
public class NegativeDisposalLogicTest {
    
    private static final String TEST_CREDITOR = "测试债权人";
    private static final String TEST_DEBTOR = "测试债务人";
    private static final String TEST_PERIOD = "2024新增债权";
    private static final String TEST_IS_LITIGATION = "否";
    private static final int TEST_YEAR = 2024;
    private static final int TEST_MONTH = 2; // 非当前月份，用于测试后续月份更新
    
    @Test
    void testNegativeDisposalCreation() {
        // 测试负数处置记录的创建
        Map<String, Object> negativeDisposalData = createNegativeDisposalData(new BigDecimal("-500.00"));
        
        // 验证负数处置数据的格式
        assertEquals(new BigDecimal("-500.00"), negativeDisposalData.get("monthlyReduceAmount"));
        assertEquals(new BigDecimal("-500.00"), negativeDisposalData.get("cashDisposal"));
        
        // 验证其他分类金额为0
        assertEquals(BigDecimal.ZERO, negativeDisposalData.get("installmentRepayment"));
        assertEquals(BigDecimal.ZERO, negativeDisposalData.get("assetDebt"));
        assertEquals(BigDecimal.ZERO, negativeDisposalData.get("otherWays"));
    }
    
    @Test
    void testNegativeDisposalValidation() {
        // 测试负数处置的验证逻辑
        
        // 1. 测试0金额处置（应该失败）
        Map<String, Object> zeroDisposalData = createNegativeDisposalData(BigDecimal.ZERO);
        assertThrows(RuntimeException.class, () -> {
            validateDisposalAmount(zeroDisposalData);
        }, "处置金额不能为0");
        
        // 2. 测试null金额处置（应该失败）
        Map<String, Object> nullDisposalData = createNegativeDisposalData(null);
        assertThrows(RuntimeException.class, () -> {
            validateDisposalAmount(nullDisposalData);
        }, "处置金额不能为空");
        
        // 3. 测试合理的负数处置（应该成功）
        Map<String, Object> validNegativeData = createNegativeDisposalData(new BigDecimal("-300.00"));
        assertDoesNotThrow(() -> {
            validateDisposalAmount(validNegativeData);
        });
    }
    
    @Test
    void testNegativeDisposalMethodBreakdown() {
        // 测试负数处置的方式分类
        Map<String, Object> disposalData = createNegativeDisposalData(new BigDecimal("-1000.00"));
        
        // 设置分类金额
        disposalData.put("cashDisposal", new BigDecimal("-600.00"));
        disposalData.put("installmentRepayment", new BigDecimal("-300.00"));
        disposalData.put("assetDebt", new BigDecimal("-100.00"));
        disposalData.put("otherWays", BigDecimal.ZERO);
        
        // 验证分类金额总和等于处置金额
        BigDecimal totalBreakdown = ((BigDecimal) disposalData.get("cashDisposal"))
            .add((BigDecimal) disposalData.get("installmentRepayment"))
            .add((BigDecimal) disposalData.get("assetDebt"))
            .add((BigDecimal) disposalData.get("otherWays"));
        
        assertEquals(disposalData.get("monthlyReduceAmount"), totalBreakdown);
    }
    
    @Test
    void testNegativeDisposalImpactOnDebtBalance() {
        // 测试负数处置对债权余额的影响
        
        // 假设初始债权余额为5000，已处置1000，当前余额4000
        BigDecimal initialBalance = new BigDecimal("5000.00");
        BigDecimal existingDisposal = new BigDecimal("1000.00");
        BigDecimal currentBalance = initialBalance.subtract(existingDisposal); // 4000
        
        // 执行负数处置-500（删除500的处置记录）
        BigDecimal negativeDisposal = new BigDecimal("-500.00");
        
        // 计算新的债权余额：当前余额 - 负数处置 = 当前余额 + 500
        BigDecimal newBalance = currentBalance.subtract(negativeDisposal);
        
        // 验证余额增加了500
        assertEquals(new BigDecimal("4500.00"), newBalance);
        
        // 验证总处置金额减少了500
        BigDecimal newTotalDisposal = existingDisposal.add(negativeDisposal);
        assertEquals(new BigDecimal("500.00"), newTotalDisposal);
    }
    
    @Test
    void testSubsequentMonthsUpdateAfterNegativeDisposal() {
        // 测试负数处置后的后续月份更新逻辑
        
        // 模拟在2月份执行负数处置，当前是5月份
        int disposalMonth = 2;
        int currentMonth = 5;
        
        // 负数处置金额
        BigDecimal negativeDisposal = new BigDecimal("-800.00");
        
        // 模拟后续月份（3月、4月、5月）的余额更新
        for (int month = disposalMonth + 1; month <= currentMonth; month++) {
            // 每个月的债权余额都应该增加800（因为删除了800的处置）
            BigDecimal monthlyImpact = negativeDisposal.negate(); // 800
            
            // 验证月份更新逻辑
            assertTrue(monthlyImpact.compareTo(BigDecimal.ZERO) > 0);
            assertEquals(new BigDecimal("800.00"), monthlyImpact);
        }
    }
    
    @Test
    void testNegativeDisposalAuditTrail() {
        // 测试负数处置的审计追踪
        Map<String, Object> negativeDisposalData = createNegativeDisposalData(new BigDecimal("-600.00"));
        
        // 验证审计字段
        assertNotNull(negativeDisposalData.get("creditor"));
        assertNotNull(negativeDisposalData.get("debtor"));
        assertNotNull(negativeDisposalData.get("period"));
        assertNotNull(negativeDisposalData.get("year"));
        assertNotNull(negativeDisposalData.get("month"));
        
        // 验证备注字段包含删除信息
        String remark = (String) negativeDisposalData.get("remark");
        assertTrue(remark.contains("删除") || remark.contains("负数处置"));
    }
    
    @Test
    void testCompleteNegativeDisposalWorkflow() {
        // 测试完整的负数处置工作流程
        
        // 1. 创建初始处置记录（正数）
        Map<String, Object> initialDisposal = createPositiveDisposalData(new BigDecimal("1000.00"));
        
        // 2. 创建负数处置记录（删除部分处置）
        Map<String, Object> negativeDisposal = createNegativeDisposalData(new BigDecimal("-400.00"));
        
        // 3. 验证最终状态
        BigDecimal finalDisposal = ((BigDecimal) initialDisposal.get("monthlyReduceAmount"))
            .add((BigDecimal) negativeDisposal.get("monthlyReduceAmount"));
        
        assertEquals(new BigDecimal("600.00"), finalDisposal);
        
        // 4. 验证数据一致性
        assertTrue(finalDisposal.compareTo(BigDecimal.ZERO) >= 0);
    }
    
    // 辅助方法
    
    private Map<String, Object> createNegativeDisposalData(BigDecimal amount) {
        Map<String, Object> data = new HashMap<>();
        data.put("creditor", TEST_CREDITOR);
        data.put("debtor", TEST_DEBTOR);
        data.put("period", TEST_PERIOD);
        data.put("isLitigation", TEST_IS_LITIGATION);
        data.put("year", TEST_YEAR);
        data.put("month", new BigDecimal(TEST_MONTH));
        data.put("monthlyReduceAmount", amount);
        data.put("cashDisposal", amount);
        data.put("installmentRepayment", BigDecimal.ZERO);
        data.put("assetDebt", BigDecimal.ZERO);
        data.put("otherWays", BigDecimal.ZERO);
        data.put("managementCompany", "测试管理公司");
        data.put("debtRisk", "测试风险等级");
        data.put("remark", "负数处置测试 - 删除处置记录");
        
        return data;
    }
    
    private Map<String, Object> createPositiveDisposalData(BigDecimal amount) {
        Map<String, Object> data = createNegativeDisposalData(amount);
        data.put("remark", "正数处置测试");
        return data;
    }
    
    private void validateDisposalAmount(Map<String, Object> disposalData) {
        BigDecimal amount = (BigDecimal) disposalData.get("monthlyReduceAmount");
        
        if (amount == null) {
            throw new RuntimeException("处置金额不能为空");
        }
        
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            throw new RuntimeException("处置金额不能为0");
        }
        
        // 其他验证逻辑...
    }
}
