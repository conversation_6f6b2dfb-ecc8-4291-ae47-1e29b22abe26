package com.laoshu198838.config;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 测试专用配置类
 * 用于设置测试环境的组件扫描、实体扫描和存储库配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableAutoConfiguration
@ComponentScan(basePackages = {
    "com.laoshu198838.service",
    "com.laoshu198838.validator",
    "com.laoshu198838.exception",
    "com.laoshu198838.repository",
    "com.laoshu198838.config"
})
@EntityScan(basePackages = "com.laoshu198838.entity")
@EnableJpaRepositories(basePackages = "com.laoshu198838.repository")
public class TestConfig {
    
    // 这里可以添加测试环境特定的Bean配置
    
}
