#!/bin/bash

# 数据库NULL值清理触发器执行脚本
# 使用方法: ./execute_triggers.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="localhost"
DB_USER="root"
DB_NAME="overdue_debt_db"

echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}数据库NULL值自动转换触发器部署脚本${NC}"
echo -e "${GREEN}========================================${NC}"

# 提示输入密码
echo -e "${YELLOW}请输入MySQL root密码:${NC}"
read -s DB_PASS

# 1. 检查连接
echo -e "\n${YELLOW}1. 检查数据库连接...${NC}"
if mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASS} -e "USE ${DB_NAME};" 2>/dev/null; then
    echo -e "${GREEN}✓ 数据库连接成功${NC}"
else
    echo -e "${RED}✗ 数据库连接失败，请检查密码${NC}"
    exit 1
fi

# 2. 显示当前NULL值统计
echo -e "\n${YELLOW}2. 当前NULL值统计...${NC}"
mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASS} ${DB_NAME} -e "
SELECT 
    '新增表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 1月 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 新增表
UNION ALL
SELECT 
    '汇总表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 年初逾期债权余额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 汇总表
UNION ALL
SELECT 
    '处置表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 每月处置金额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 处置表
UNION ALL
SELECT 
    '诉讼表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 上月末债权余额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 诉讼表
UNION ALL
SELECT 
    '非诉讼表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 上月末本金 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 非诉讼表
UNION ALL
SELECT 
    '减值准备表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 本月末债权余额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 减值准备表;"

# 3. 确认是否继续
echo -e "\n${YELLOW}是否继续执行？这将：${NC}"
echo "1. 创建自动转换触发器"
echo "2. 清理现有NULL值"
echo -e "${YELLOW}请输入 (y/n):${NC}"
read -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${RED}操作已取消${NC}"
    exit 1
fi

# 4. 执行触发器创建脚本
echo -e "\n${YELLOW}3. 创建触发器...${NC}"
mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASS} ${DB_NAME} < sql/create_null_to_zero_triggers.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 触发器创建成功${NC}"
else
    echo -e "${RED}✗ 触发器创建失败${NC}"
    exit 1
fi

# 5. 显示创建的触发器
echo -e "\n${YELLOW}4. 已创建的触发器列表:${NC}"
mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASS} ${DB_NAME} -e "
SELECT TRIGGER_NAME, EVENT_OBJECT_TABLE, EVENT_MANIPULATION 
FROM information_schema.TRIGGERS 
WHERE TRIGGER_SCHEMA = '${DB_NAME}' 
  AND TRIGGER_NAME LIKE '%null_to_zero%';"

# 6. 显示清理后的统计
echo -e "\n${YELLOW}5. 清理后的NULL值统计:${NC}"
mysql -h${DB_HOST} -u${DB_USER} -p${DB_PASS} ${DB_NAME} -e "
SELECT 
    '新增表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 1月 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 新增表
UNION ALL
SELECT 
    '汇总表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 年初逾期债权余额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 汇总表
UNION ALL
SELECT 
    '处置表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 每月处置金额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 处置表
UNION ALL
SELECT 
    '诉讼表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 上月末债权余额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 诉讼表
UNION ALL
SELECT 
    '非诉讼表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 上月末本金 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 非诉讼表
UNION ALL
SELECT 
    '减值准备表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 本月末债权余额 IS NULL THEN 1 ELSE 0 END) as NULL值数量
FROM 减值准备表;"

echo -e "\n${GREEN}========================================${NC}"
echo -e "${GREEN}✓ 部署完成！${NC}"
echo -e "${GREEN}触发器已成功创建并清理了存量NULL值${NC}"
echo -e "${GREEN}========================================${NC}"