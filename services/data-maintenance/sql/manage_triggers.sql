-- ========================================
-- 触发器管理脚本
-- 用于启用、禁用、删除触发器
-- ========================================

USE overdue_debt_db;

-- ========================================
-- 1. 查看所有NULL转0触发器的状态
-- ========================================
SELECT 
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING,
    CREATED
FROM information_schema.TRIGGERS
WHERE TRIGGER_SCHEMA = 'overdue_debt_db'
  AND TRIGGER_NAME LIKE '%null_to_zero%'
ORDER BY EVENT_OBJECT_TABLE, EVENT_MANIPULATION;

-- ========================================
-- 2. 禁用所有触发器（维护模式）
-- ========================================
-- 注意：MySQL不支持直接禁用触发器，需要先删除
/*
DROP TRIGGER IF EXISTS trg_disposal_insert_null_to_zero;
DROP TRIGGER IF EXISTS trg_disposal_update_null_to_zero;
DROP TRIGGER IF EXISTS trg_litigation_insert_null_to_zero;
DROP TRIGGER IF EXISTS trg_litigation_update_null_to_zero;
DROP TRIGGER IF EXISTS trg_nonlitigation_insert_null_to_zero;
DROP TRIGGER IF EXISTS trg_nonlitigation_update_null_to_zero;
DROP TRIGGER IF EXISTS trg_impairment_insert_null_to_zero;
DROP TRIGGER IF EXISTS trg_impairment_update_null_to_zero;
*/

-- ========================================
-- 3. 检查哪些记录有NULL值
-- ========================================

-- 处置表NULL值统计
SELECT 
    '处置表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 每月处置金额 IS NULL THEN 1 ELSE 0 END) as 每月处置金额_NULL数,
    SUM(CASE WHEN 现金处置 IS NULL THEN 1 ELSE 0 END) as 现金处置_NULL数,
    SUM(CASE WHEN 分期还款 IS NULL THEN 1 ELSE 0 END) as 分期还款_NULL数,
    SUM(CASE WHEN 资产抵债 IS NULL THEN 1 ELSE 0 END) as 资产抵债_NULL数,
    SUM(CASE WHEN 其他方式 IS NULL THEN 1 ELSE 0 END) as 其他方式_NULL数
FROM 处置表;

-- 诉讼表NULL值统计
SELECT 
    '诉讼表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 上月末债权余额 IS NULL THEN 1 ELSE 0 END) as 上月末债权余额_NULL数,
    SUM(CASE WHEN 涉诉债权本金 IS NULL THEN 1 ELSE 0 END) as 涉诉债权本金_NULL数,
    SUM(CASE WHEN 涉诉债权应收利息 IS NULL THEN 1 ELSE 0 END) as 涉诉债权应收利息_NULL数,
    SUM(CASE WHEN 本月末债权余额 IS NULL THEN 1 ELSE 0 END) as 本月末债权余额_NULL数,
    SUM(CASE WHEN 诉讼费 IS NULL THEN 1 ELSE 0 END) as 诉讼费_NULL数,
    SUM(CASE WHEN 中介费 IS NULL THEN 1 ELSE 0 END) as 中介费_NULL数
FROM 诉讼表;

-- 非诉讼表NULL值统计
SELECT 
    '非诉讼表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN `2022年4月30日` IS NULL THEN 1 ELSE 0 END) as 基准日期_NULL数,
    SUM(CASE WHEN 上月末本金 IS NULL THEN 1 ELSE 0 END) as 上月末本金_NULL数,
    SUM(CASE WHEN 上月末利息 IS NULL THEN 1 ELSE 0 END) as 上月末利息_NULL数,
    SUM(CASE WHEN 上月末违约金 IS NULL THEN 1 ELSE 0 END) as 上月末违约金_NULL数,
    SUM(CASE WHEN 本月末本金 IS NULL THEN 1 ELSE 0 END) as 本月末本金_NULL数
FROM 非诉讼表;

-- 减值准备表NULL值统计
SELECT 
    '减值准备表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN `2022年4月30日` IS NULL THEN 1 ELSE 0 END) as 基准日期_NULL数,
    SUM(CASE WHEN 本月末债权余额 IS NULL THEN 1 ELSE 0 END) as 本月末债权余额_NULL数,
    SUM(CASE WHEN 计提减值金额 IS NULL THEN 1 ELSE 0 END) as 计提减值金额_NULL数,
    SUM(CASE WHEN 本年度回收目标 IS NULL THEN 1 ELSE 0 END) as 本年度回收目标_NULL数,
    SUM(CASE WHEN 本年度累计回收 IS NULL THEN 1 ELSE 0 END) as 本年度累计回收_NULL数
FROM 减值准备表;

-- 新增表NULL值统计
SELECT 
    '新增表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN 1月 IS NULL THEN 1 ELSE 0 END) as 1月_NULL数,
    SUM(CASE WHEN 处置金额 IS NULL THEN 1 ELSE 0 END) as 处置金额_NULL数,
    SUM(CASE WHEN 现金处置 IS NULL THEN 1 ELSE 0 END) as 现金处置_NULL数,
    SUM(CASE WHEN 新增金额 IS NULL THEN 1 ELSE 0 END) as 新增金额_NULL数,
    SUM(CASE WHEN 债权余额 IS NULL THEN 1 ELSE 0 END) as 债权余额_NULL数
FROM 新增表;

-- 汇总表NULL值统计
SELECT 
    '汇总表' as 表名,
    COUNT(*) as 总记录数,
    SUM(CASE WHEN `2022年4月30日` IS NULL THEN 1 ELSE 0 END) as 基准日期_NULL数,
    SUM(CASE WHEN 年初逾期债权余额 IS NULL THEN 1 ELSE 0 END) as 年初逾期债权余额_NULL数,
    SUM(CASE WHEN 本年增加债权金额 IS NULL THEN 1 ELSE 0 END) as 本年增加债权金额_NULL数,
    SUM(CASE WHEN 本年期末债权余额 IS NULL THEN 1 ELSE 0 END) as 本年期末债权余额_NULL数,
    SUM(CASE WHEN 年初减值准备金额 IS NULL THEN 1 ELSE 0 END) as 年初减值准备金额_NULL数
FROM 汇总表;

-- ========================================
-- 4. 备份原始数据（创建备份表）
-- ========================================
/*
-- 创建备份表（执行前请确认）
CREATE TABLE 处置表_backup_20250125 AS SELECT * FROM 处置表;
CREATE TABLE 诉讼表_backup_20250125 AS SELECT * FROM 诉讼表;
CREATE TABLE 非诉讼表_backup_20250125 AS SELECT * FROM 非诉讼表;
CREATE TABLE 减值准备表_backup_20250125 AS SELECT * FROM 减值准备表;
*/

-- ========================================
-- 5. 验证触发器效果
-- ========================================
-- 测试INSERT触发器
/*
INSERT INTO 处置表 (序号, 管理公司, 债权人, 债务人, 年份, 月份, 是否涉诉, 期间, 
                  每月处置金额, 现金处置, 分期还款, 资产抵债, 其他方式)
VALUES (9999, '测试公司', '测试债权人', '测试债务人', 2025, 1, '是', '202501',
        NULL, NULL, NULL, NULL, NULL);

-- 查看结果（应该都是0.00）
SELECT 每月处置金额, 现金处置, 分期还款, 资产抵债, 其他方式
FROM 处置表
WHERE 序号 = 9999;

-- 清理测试数据
DELETE FROM 处置表 WHERE 序号 = 9999;
*/

-- ========================================
-- 6. 性能监控
-- ========================================
-- 查看触发器执行时间（需要开启性能监控）
/*
SELECT * FROM performance_schema.events_statements_summary_by_digest
WHERE DIGEST_TEXT LIKE '%TRIGGER%'
ORDER BY SUM_TIMER_WAIT DESC
LIMIT 10;
*/