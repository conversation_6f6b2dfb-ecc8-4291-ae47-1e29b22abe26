-- ========================================
-- NULL值转换为0.00的触发器脚本
-- 适用数据库：overdue_debt_db
-- 创建时间：2025-01-25
-- ========================================

USE overdue_debt_db;

DELIMITER $$

-- ========================================
-- 1. 处置表触发器
-- ========================================

-- INSERT触发器
DROP TRIGGER IF EXISTS trg_disposal_insert_null_to_zero$$
CREATE TRIGGER trg_disposal_insert_null_to_zero
BEFORE INSERT ON 处置表
FOR EACH ROW
BEGIN
    -- 将所有可能为NULL的decimal字段设置为0.00
    SET NEW.每月处置金额 = IFNULL(NEW.每月处置金额, 0.00);
    SET NEW.现金处置 = IFNULL(NEW.现金处置, 0.00);
    SET NEW.分期还款 = IFNULL(NEW.分期还款, 0.00);
    SET NEW.资产抵债 = IFNULL(NEW.资产抵债, 0.00);
    SET NEW.其他方式 = IFNULL(NEW.其他方式, 0.00);
END$$

-- UPDATE触发器
DROP TRIGGER IF EXISTS trg_disposal_update_null_to_zero$$
CREATE TRIGGER trg_disposal_update_null_to_zero
BEFORE UPDATE ON 处置表
FOR EACH ROW
BEGIN
    -- 将所有可能为NULL的decimal字段设置为0.00
    SET NEW.每月处置金额 = IFNULL(NEW.每月处置金额, 0.00);
    SET NEW.现金处置 = IFNULL(NEW.现金处置, 0.00);
    SET NEW.分期还款 = IFNULL(NEW.分期还款, 0.00);
    SET NEW.资产抵债 = IFNULL(NEW.资产抵债, 0.00);
    SET NEW.其他方式 = IFNULL(NEW.其他方式, 0.00);
END$$

-- ========================================
-- 2. 诉讼表触发器
-- ========================================

-- INSERT触发器
DROP TRIGGER IF EXISTS trg_litigation_insert_null_to_zero$$
CREATE TRIGGER trg_litigation_insert_null_to_zero
BEFORE INSERT ON 诉讼表
FOR EACH ROW
BEGIN
    SET NEW.上月末债权余额 = IFNULL(NEW.上月末债权余额, 0.00);
    SET NEW.涉诉债权本金 = IFNULL(NEW.涉诉债权本金, 0.00);
    SET NEW.涉诉债权应收利息罚息服务费 = IFNULL(NEW.涉诉债权应收利息罚息服务费, 0.00);
    SET NEW.本月末债权余额 = IFNULL(NEW.本月末债权余额, 0.00);
    SET NEW.诉讼主张本金 = IFNULL(NEW.诉讼主张本金, 0.00);
    SET NEW.诉讼主张应收利息及罚金 = IFNULL(NEW.诉讼主张应收利息及罚金, 0.00);
    SET NEW.诉讼费 = IFNULL(NEW.诉讼费, 0.00);
    SET NEW.中介费 = IFNULL(NEW.中介费, 0.00);
    SET NEW.终审判决仲裁裁决调解和解金额 = IFNULL(NEW.终审判决仲裁裁决调解和解金额, 0.00);
    SET NEW.申请执行金额 = IFNULL(NEW.申请执行金额, 0.00);
    SET NEW.实际执行回款 = IFNULL(NEW.实际执行回款, 0.00);
    SET NEW.实际支付费用 = IFNULL(NEW.实际支付费用, 0.00);
    SET NEW.本年度累计回收 = IFNULL(NEW.本年度累计回收, 0.00);
    SET NEW.本月新增债权 = IFNULL(NEW.本月新增债权, 0.00);
    SET NEW.本月处置债权 = IFNULL(NEW.本月处置债权, 0.00);
END$$

-- UPDATE触发器
DROP TRIGGER IF EXISTS trg_litigation_update_null_to_zero$$
CREATE TRIGGER trg_litigation_update_null_to_zero
BEFORE UPDATE ON 诉讼表
FOR EACH ROW
BEGIN
    SET NEW.上月末债权余额 = IFNULL(NEW.上月末债权余额, 0.00);
    SET NEW.涉诉债权本金 = IFNULL(NEW.涉诉债权本金, 0.00);
    SET NEW.涉诉债权应收利息罚息服务费 = IFNULL(NEW.涉诉债权应收利息罚息服务费, 0.00);
    SET NEW.本月末债权余额 = IFNULL(NEW.本月末债权余额, 0.00);
    SET NEW.诉讼主张本金 = IFNULL(NEW.诉讼主张本金, 0.00);
    SET NEW.诉讼主张应收利息及罚金 = IFNULL(NEW.诉讼主张应收利息及罚金, 0.00);
    SET NEW.诉讼费 = IFNULL(NEW.诉讼费, 0.00);
    SET NEW.中介费 = IFNULL(NEW.中介费, 0.00);
    SET NEW.终审判决仲裁裁决调解和解金额 = IFNULL(NEW.终审判决仲裁裁决调解和解金额, 0.00);
    SET NEW.申请执行金额 = IFNULL(NEW.申请执行金额, 0.00);
    SET NEW.实际执行回款 = IFNULL(NEW.实际执行回款, 0.00);
    SET NEW.实际支付费用 = IFNULL(NEW.实际支付费用, 0.00);
    SET NEW.本年度累计回收 = IFNULL(NEW.本年度累计回收, 0.00);
    SET NEW.本月新增债权 = IFNULL(NEW.本月新增债权, 0.00);
    SET NEW.本月处置债权 = IFNULL(NEW.本月处置债权, 0.00);
END$$

-- ========================================
-- 3. 非诉讼表触发器
-- ========================================

-- INSERT触发器
DROP TRIGGER IF EXISTS trg_nonlitigation_insert_null_to_zero$$
CREATE TRIGGER trg_nonlitigation_insert_null_to_zero
BEFORE INSERT ON 非诉讼表
FOR EACH ROW
BEGIN
    SET NEW.`2022年4月30日债权账面余额` = IFNULL(NEW.`2022年4月30日债权账面余额`, 0.00);
    SET NEW.上月末本金 = IFNULL(NEW.上月末本金, 0.00);
    SET NEW.上月末利息 = IFNULL(NEW.上月末利息, 0.00);
    SET NEW.上月末违约金 = IFNULL(NEW.上月末违约金, 0.00);
    SET NEW.本月本金增减 = IFNULL(NEW.本月本金增减, 0.00);
    SET NEW.本月利息增减 = IFNULL(NEW.本月利息增减, 0.00);
    SET NEW.本月违约金增减 = IFNULL(NEW.本月违约金增减, 0.00);
    SET NEW.本月末本金 = IFNULL(NEW.本月末本金, 0.00);
    SET NEW.本月末利息 = IFNULL(NEW.本月末利息, 0.00);
    SET NEW.本月末违约金 = IFNULL(NEW.本月末违约金, 0.00);
    SET NEW.下月回收预计 = IFNULL(NEW.下月回收预计, 0.00);
    SET NEW.本年度回收目标 = IFNULL(NEW.本年度回收目标, 0.00);
    SET NEW.本年度累计回收 = IFNULL(NEW.本年度累计回收, 0.00);
    SET NEW.本月新增债权 = IFNULL(NEW.本月新增债权, 0.00);
    SET NEW.本月处置债权 = IFNULL(NEW.本月处置债权, 0.00);
END$$

-- UPDATE触发器
DROP TRIGGER IF EXISTS trg_nonlitigation_update_null_to_zero$$
CREATE TRIGGER trg_nonlitigation_update_null_to_zero
BEFORE UPDATE ON 非诉讼表
FOR EACH ROW
BEGIN
    SET NEW.`2022年4月30日债权账面余额` = IFNULL(NEW.`2022年4月30日债权账面余额`, 0.00);
    SET NEW.上月末本金 = IFNULL(NEW.上月末本金, 0.00);
    SET NEW.上月末利息 = IFNULL(NEW.上月末利息, 0.00);
    SET NEW.上月末违约金 = IFNULL(NEW.上月末违约金, 0.00);
    SET NEW.本月本金增减 = IFNULL(NEW.本月本金增减, 0.00);
    SET NEW.本月利息增减 = IFNULL(NEW.本月利息增减, 0.00);
    SET NEW.本月违约金增减 = IFNULL(NEW.本月违约金增减, 0.00);
    SET NEW.本月末本金 = IFNULL(NEW.本月末本金, 0.00);
    SET NEW.本月末利息 = IFNULL(NEW.本月末利息, 0.00);
    SET NEW.本月末违约金 = IFNULL(NEW.本月末违约金, 0.00);
    SET NEW.下月回收预计 = IFNULL(NEW.下月回收预计, 0.00);
    SET NEW.本年度回收目标 = IFNULL(NEW.本年度回收目标, 0.00);
    SET NEW.本年度累计回收 = IFNULL(NEW.本年度累计回收, 0.00);
    SET NEW.本月新增债权 = IFNULL(NEW.本月新增债权, 0.00);
    SET NEW.本月处置债权 = IFNULL(NEW.本月处置债权, 0.00);
END$$

-- ========================================
-- 4. 新增表触发器
-- ========================================

-- INSERT触发器
DROP TRIGGER IF EXISTS trg_addition_insert_null_to_zero$$
CREATE TRIGGER trg_addition_insert_null_to_zero
BEFORE INSERT ON 新增表
FOR EACH ROW
BEGIN
    -- 将所有可能为NULL的decimal字段设置为0.00
    SET NEW.1月 = IFNULL(NEW.1月, 0.00);
    SET NEW.2月 = IFNULL(NEW.2月, 0.00);
    SET NEW.3月 = IFNULL(NEW.3月, 0.00);
    SET NEW.4月 = IFNULL(NEW.4月, 0.00);
    SET NEW.5月 = IFNULL(NEW.5月, 0.00);
    SET NEW.6月 = IFNULL(NEW.6月, 0.00);
    SET NEW.7月 = IFNULL(NEW.7月, 0.00);
    SET NEW.8月 = IFNULL(NEW.8月, 0.00);
    SET NEW.9月 = IFNULL(NEW.9月, 0.00);
    SET NEW.10月 = IFNULL(NEW.10月, 0.00);
    SET NEW.11月 = IFNULL(NEW.11月, 0.00);
    SET NEW.12月 = IFNULL(NEW.12月, 0.00);
    SET NEW.处置金额 = IFNULL(NEW.处置金额, 0.00);
    SET NEW.现金处置 = IFNULL(NEW.现金处置, 0.00);
    SET NEW.分期还款 = IFNULL(NEW.分期还款, 0.00);
    SET NEW.资产抵债 = IFNULL(NEW.资产抵债, 0.00);
    SET NEW.其他方式 = IFNULL(NEW.其他方式, 0.00);
    SET NEW.新增金额 = IFNULL(NEW.新增金额, 0.00);
    SET NEW.债权余额 = IFNULL(NEW.债权余额, 0.00);
    SET NEW.风险准备金计提金额 = IFNULL(NEW.风险准备金计提金额, 0.00);
END$$

-- UPDATE触发器
DROP TRIGGER IF EXISTS trg_addition_update_null_to_zero$$
CREATE TRIGGER trg_addition_update_null_to_zero
BEFORE UPDATE ON 新增表
FOR EACH ROW
BEGIN
    -- 将所有可能为NULL的decimal字段设置为0.00
    SET NEW.1月 = IFNULL(NEW.1月, 0.00);
    SET NEW.2月 = IFNULL(NEW.2月, 0.00);
    SET NEW.3月 = IFNULL(NEW.3月, 0.00);
    SET NEW.4月 = IFNULL(NEW.4月, 0.00);
    SET NEW.5月 = IFNULL(NEW.5月, 0.00);
    SET NEW.6月 = IFNULL(NEW.6月, 0.00);
    SET NEW.7月 = IFNULL(NEW.7月, 0.00);
    SET NEW.8月 = IFNULL(NEW.8月, 0.00);
    SET NEW.9月 = IFNULL(NEW.9月, 0.00);
    SET NEW.10月 = IFNULL(NEW.10月, 0.00);
    SET NEW.11月 = IFNULL(NEW.11月, 0.00);
    SET NEW.12月 = IFNULL(NEW.12月, 0.00);
    SET NEW.处置金额 = IFNULL(NEW.处置金额, 0.00);
    SET NEW.现金处置 = IFNULL(NEW.现金处置, 0.00);
    SET NEW.分期还款 = IFNULL(NEW.分期还款, 0.00);
    SET NEW.资产抵债 = IFNULL(NEW.资产抵债, 0.00);
    SET NEW.其他方式 = IFNULL(NEW.其他方式, 0.00);
    SET NEW.新增金额 = IFNULL(NEW.新增金额, 0.00);
    SET NEW.债权余额 = IFNULL(NEW.债权余额, 0.00);
    SET NEW.风险准备金计提金额 = IFNULL(NEW.风险准备金计提金额, 0.00);
END$$

-- ========================================
-- 5. 汇总表触发器
-- ========================================

-- INSERT触发器
DROP TRIGGER IF EXISTS trg_summary_insert_null_to_zero$$
CREATE TRIGGER trg_summary_insert_null_to_zero
BEFORE INSERT ON 汇总表
FOR EACH ROW
BEGIN
    -- 将所有可能为NULL的decimal字段设置为0.00
    SET NEW.`2022年4月30日余额` = IFNULL(NEW.`2022年4月30日余额`, 0.00);
    SET NEW.年初逾期债权余额 = IFNULL(NEW.年初逾期债权余额, 0.00);
    SET NEW.本年增加债权金额 = IFNULL(NEW.本年增加债权金额, 0.00);
    SET NEW.本年减少债权金额 = IFNULL(NEW.本年减少债权金额, 0.00);
    SET NEW.本年期末债权余额 = IFNULL(NEW.本年期末债权余额, 0.00);
    SET NEW.年初减值准备金额 = IFNULL(NEW.年初减值准备金额, 0.00);
    SET NEW.本年减值准备变化金额 = IFNULL(NEW.本年减值准备变化金额, 0.00);
    SET NEW.本年末减值准备余额 = IFNULL(NEW.本年末减值准备余额, 0.00);
END$$

-- UPDATE触发器
DROP TRIGGER IF EXISTS trg_summary_update_null_to_zero$$
CREATE TRIGGER trg_summary_update_null_to_zero
BEFORE UPDATE ON 汇总表
FOR EACH ROW
BEGIN
    -- 将所有可能为NULL的decimal字段设置为0.00
    SET NEW.`2022年4月30日余额` = IFNULL(NEW.`2022年4月30日余额`, 0.00);
    SET NEW.年初逾期债权余额 = IFNULL(NEW.年初逾期债权余额, 0.00);
    SET NEW.本年增加债权金额 = IFNULL(NEW.本年增加债权金额, 0.00);
    SET NEW.本年减少债权金额 = IFNULL(NEW.本年减少债权金额, 0.00);
    SET NEW.本年期末债权余额 = IFNULL(NEW.本年期末债权余额, 0.00);
    SET NEW.年初减值准备金额 = IFNULL(NEW.年初减值准备金额, 0.00);
    SET NEW.本年减值准备变化金额 = IFNULL(NEW.本年减值准备变化金额, 0.00);
    SET NEW.本年末减值准备余额 = IFNULL(NEW.本年末减值准备余额, 0.00);
END$$

-- ========================================
-- 6. 减值准备表触发器
-- ========================================

-- INSERT触发器
DROP TRIGGER IF EXISTS trg_impairment_insert_null_to_zero$$
CREATE TRIGGER trg_impairment_insert_null_to_zero
BEFORE INSERT ON 减值准备表
FOR EACH ROW
BEGIN
    SET NEW.`2022年4月30日债权金额` = IFNULL(NEW.`2022年4月30日债权金额`, 0.00);
    SET NEW.本月初债权余额 = IFNULL(NEW.本月初债权余额, 0.00);
    SET NEW.本月末债权余额 = IFNULL(NEW.本月末债权余额, 0.00);
    SET NEW.计提减值金额 = IFNULL(NEW.计提减值金额, 0.00);
    SET NEW.上月末余额 = IFNULL(NEW.上月末余额, 0.00);
    SET NEW.本月增减 = IFNULL(NEW.本月增减, 0.00);
    SET NEW.本月末余额 = IFNULL(NEW.本月末余额, 0.00);
    SET NEW.本年度回收目标 = IFNULL(NEW.本年度回收目标, 0.00);
    SET NEW.本年度累计回收 = IFNULL(NEW.本年度累计回收, 0.00);
    SET NEW.本月新增债权 = IFNULL(NEW.本月新增债权, 0.00);
    SET NEW.本月处置债权 = IFNULL(NEW.本月处置债权, 0.00);
END$$

-- UPDATE触发器
DROP TRIGGER IF EXISTS trg_impairment_update_null_to_zero$$
CREATE TRIGGER trg_impairment_update_null_to_zero
BEFORE UPDATE ON 减值准备表
FOR EACH ROW
BEGIN
    SET NEW.`2022年4月30日债权金额` = IFNULL(NEW.`2022年4月30日债权金额`, 0.00);
    SET NEW.本月初债权余额 = IFNULL(NEW.本月初债权余额, 0.00);
    SET NEW.本月末债权余额 = IFNULL(NEW.本月末债权余额, 0.00);
    SET NEW.计提减值金额 = IFNULL(NEW.计提减值金额, 0.00);
    SET NEW.上月末余额 = IFNULL(NEW.上月末余额, 0.00);
    SET NEW.本月增减 = IFNULL(NEW.本月增减, 0.00);
    SET NEW.本月末余额 = IFNULL(NEW.本月末余额, 0.00);
    SET NEW.本年度回收目标 = IFNULL(NEW.本年度回收目标, 0.00);
    SET NEW.本年度累计回收 = IFNULL(NEW.本年度累计回收, 0.00);
    SET NEW.本月新增债权 = IFNULL(NEW.本月新增债权, 0.00);
    SET NEW.本月处置债权 = IFNULL(NEW.本月处置债权, 0.00);
END$$

DELIMITER ;

-- ========================================
-- 7. 一次性清理现有NULL值的脚本
-- ========================================

-- 新增表
UPDATE 新增表 SET
    1月 = IFNULL(1月, 0.00),
    2月 = IFNULL(2月, 0.00),
    3月 = IFNULL(3月, 0.00),
    4月 = IFNULL(4月, 0.00),
    5月 = IFNULL(5月, 0.00),
    6月 = IFNULL(6月, 0.00),
    7月 = IFNULL(7月, 0.00),
    8月 = IFNULL(8月, 0.00),
    9月 = IFNULL(9月, 0.00),
    10月 = IFNULL(10月, 0.00),
    11月 = IFNULL(11月, 0.00),
    12月 = IFNULL(12月, 0.00),
    处置金额 = IFNULL(处置金额, 0.00),
    现金处置 = IFNULL(现金处置, 0.00),
    分期还款 = IFNULL(分期还款, 0.00),
    资产抵债 = IFNULL(资产抵债, 0.00),
    其他方式 = IFNULL(其他方式, 0.00),
    新增金额 = IFNULL(新增金额, 0.00),
    债权余额 = IFNULL(债权余额, 0.00),
    风险准备金计提金额 = IFNULL(风险准备金计提金额, 0.00)
WHERE 1月 IS NULL 
   OR 2月 IS NULL
   OR 3月 IS NULL
   OR 4月 IS NULL
   OR 5月 IS NULL
   OR 6月 IS NULL
   OR 7月 IS NULL
   OR 8月 IS NULL
   OR 9月 IS NULL
   OR 10月 IS NULL
   OR 11月 IS NULL
   OR 12月 IS NULL
   OR 处置金额 IS NULL
   OR 现金处置 IS NULL
   OR 分期还款 IS NULL
   OR 资产抵债 IS NULL
   OR 其他方式 IS NULL
   OR 新增金额 IS NULL
   OR 债权余额 IS NULL
   OR 风险准备金计提金额 IS NULL;

-- 汇总表
UPDATE 汇总表 SET
    `2022年4月30日余额` = IFNULL(`2022年4月30日余额`, 0.00),
    年初逾期债权余额 = IFNULL(年初逾期债权余额, 0.00),
    本年增加债权金额 = IFNULL(本年增加债权金额, 0.00),
    本年减少债权金额 = IFNULL(本年减少债权金额, 0.00),
    本年期末债权余额 = IFNULL(本年期末债权余额, 0.00),
    年初减值准备金额 = IFNULL(年初减值准备金额, 0.00),
    本年减值准备变化金额 = IFNULL(本年减值准备变化金额, 0.00),
    本年末减值准备余额 = IFNULL(本年末减值准备余额, 0.00)
WHERE `2022年4月30日余额` IS NULL
   OR 年初逾期债权余额 IS NULL
   OR 本年增加债权金额 IS NULL
   OR 本年减少债权金额 IS NULL
   OR 本年期末债权余额 IS NULL
   OR 年初减值准备金额 IS NULL
   OR 本年减值准备变化金额 IS NULL
   OR 本年末减值准备余额 IS NULL;

-- 处置表
UPDATE 处置表 SET
    每月处置金额 = IFNULL(每月处置金额, 0.00),
    现金处置 = IFNULL(现金处置, 0.00),
    分期还款 = IFNULL(分期还款, 0.00),
    资产抵债 = IFNULL(资产抵债, 0.00),
    其他方式 = IFNULL(其他方式, 0.00)
WHERE 每月处置金额 IS NULL 
   OR 现金处置 IS NULL 
   OR 分期还款 IS NULL 
   OR 资产抵债 IS NULL 
   OR 其他方式 IS NULL;

-- 诉讼表
UPDATE 诉讼表 SET
    上月末债权余额 = IFNULL(上月末债权余额, 0.00),
    涉诉债权本金 = IFNULL(涉诉债权本金, 0.00),
    涉诉债权应收利息罚息服务费 = IFNULL(涉诉债权应收利息罚息服务费, 0.00),
    本月末债权余额 = IFNULL(本月末债权余额, 0.00),
    诉讼主张本金 = IFNULL(诉讼主张本金, 0.00),
    诉讼主张应收利息及罚金 = IFNULL(诉讼主张应收利息及罚金, 0.00),
    诉讼费 = IFNULL(诉讼费, 0.00),
    中介费 = IFNULL(中介费, 0.00),
    终审判决仲裁裁决调解和解金额 = IFNULL(终审判决仲裁裁决调解和解金额, 0.00),
    申请执行金额 = IFNULL(申请执行金额, 0.00),
    实际执行回款 = IFNULL(实际执行回款, 0.00),
    实际支付费用 = IFNULL(实际支付费用, 0.00),
    本年度累计回收 = IFNULL(本年度累计回收, 0.00),
    本月新增债权 = IFNULL(本月新增债权, 0.00),
    本月处置债权 = IFNULL(本月处置债权, 0.00)
WHERE 上月末债权余额 IS NULL 
   OR 涉诉债权本金 IS NULL
   OR 涉诉债权应收利息罚息服务费 IS NULL
   OR 本月末债权余额 IS NULL
   OR 诉讼主张本金 IS NULL
   OR 诉讼主张应收利息及罚金 IS NULL
   OR 诉讼费 IS NULL
   OR 中介费 IS NULL
   OR 终审判决仲裁裁决调解和解金额 IS NULL
   OR 申请执行金额 IS NULL
   OR 实际执行回款 IS NULL
   OR 实际支付费用 IS NULL
   OR 本年度累计回收 IS NULL
   OR 本月新增债权 IS NULL
   OR 本月处置债权 IS NULL;

-- 非诉讼表
UPDATE 非诉讼表 SET
    `2022年4月30日债权账面余额` = IFNULL(`2022年4月30日债权账面余额`, 0.00),
    上月末本金 = IFNULL(上月末本金, 0.00),
    上月末利息 = IFNULL(上月末利息, 0.00),
    上月末违约金 = IFNULL(上月末违约金, 0.00),
    本月本金增减 = IFNULL(本月本金增减, 0.00),
    本月利息增减 = IFNULL(本月利息增减, 0.00),
    本月违约金增减 = IFNULL(本月违约金增减, 0.00),
    本月末本金 = IFNULL(本月末本金, 0.00),
    本月末利息 = IFNULL(本月末利息, 0.00),
    本月末违约金 = IFNULL(本月末违约金, 0.00),
    下月回收预计 = IFNULL(下月回收预计, 0.00),
    本年度回收目标 = IFNULL(本年度回收目标, 0.00),
    本年度累计回收 = IFNULL(本年度累计回收, 0.00),
    本月新增债权 = IFNULL(本月新增债权, 0.00),
    本月处置债权 = IFNULL(本月处置债权, 0.00)
WHERE `2022年4月30日债权账面余额` IS NULL
   OR 上月末本金 IS NULL
   OR 上月末利息 IS NULL
   OR 上月末违约金 IS NULL
   OR 本月本金增减 IS NULL
   OR 本月利息增减 IS NULL
   OR 本月违约金增减 IS NULL
   OR 本月末本金 IS NULL
   OR 本月末利息 IS NULL
   OR 本月末违约金 IS NULL
   OR 下月回收预计 IS NULL
   OR 本年度回收目标 IS NULL
   OR 本年度累计回收 IS NULL
   OR 本月新增债权 IS NULL
   OR 本月处置债权 IS NULL;

-- 减值准备表
UPDATE 减值准备表 SET
    `2022年4月30日债权金额` = IFNULL(`2022年4月30日债权金额`, 0.00),
    本月初债权余额 = IFNULL(本月初债权余额, 0.00),
    本月末债权余额 = IFNULL(本月末债权余额, 0.00),
    计提减值金额 = IFNULL(计提减值金额, 0.00),
    上月末余额 = IFNULL(上月末余额, 0.00),
    本月增减 = IFNULL(本月增减, 0.00),
    本月末余额 = IFNULL(本月末余额, 0.00),
    本年度回收目标 = IFNULL(本年度回收目标, 0.00),
    本年度累计回收 = IFNULL(本年度累计回收, 0.00),
    本月新增债权 = IFNULL(本月新增债权, 0.00),
    本月处置债权 = IFNULL(本月处置债权, 0.00)
WHERE `2022年4月30日债权金额` IS NULL
   OR 本月初债权余额 IS NULL
   OR 本月末债权余额 IS NULL
   OR 计提减值金额 IS NULL
   OR 上月末余额 IS NULL
   OR 本月增减 IS NULL
   OR 本月末余额 IS NULL
   OR 本年度回收目标 IS NULL
   OR 本年度累计回收 IS NULL
   OR 本月新增债权 IS NULL
   OR 本月处置债权 IS NULL;

-- ========================================
-- 查看创建的触发器
-- ========================================
SHOW TRIGGERS FROM overdue_debt_db;