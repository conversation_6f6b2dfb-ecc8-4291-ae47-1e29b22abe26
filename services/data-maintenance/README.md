# 数据库NULL值自动转换触发器

## 概述

本方案使用MySQL触发器自动将数字类型列的NULL值转换为0.00，确保财务数据的完整性和一致性。

## 涉及的表和字段

### 1. 新增表
- 12个月份字段（1月-12月）
- 处置金额、现金处置、分期还款、资产抵债、其他方式
- 新增金额、债权余额、风险准备金计提金

### 2. 汇总表  
- 2022年4月30日基准金额
- 年初逾期债权余额、本年增加债权金额、本年减少债权金额
- 本年期末债权余额、年初减值准备金额等

### 3. 处置表
- 每月处置金额、现金处置、分期还款、资产抵债、其他方式

### 4. 诉讼表
- 所有decimal类型的金额字段（15个字段）

### 5. 非诉讼表
- 所有decimal类型的金额字段（15个字段）

### 6. 减值准备表
- 所有decimal类型的金额字段（12个字段）

## 实施步骤

### 1. 备份数据（重要！）
```sql
-- 创建备份表
CREATE TABLE 新增表_backup_20250125 AS SELECT * FROM 新增表;
CREATE TABLE 汇总表_backup_20250125 AS SELECT * FROM 汇总表;
CREATE TABLE 处置表_backup_20250125 AS SELECT * FROM 处置表;
CREATE TABLE 诉讼表_backup_20250125 AS SELECT * FROM 诉讼表;
CREATE TABLE 非诉讼表_backup_20250125 AS SELECT * FROM 非诉讼表;
CREATE TABLE 减值准备表_backup_20250125 AS SELECT * FROM 减值准备表;
```

### 2. 检查当前NULL值情况
```sql
-- 执行管理脚本中的统计查询
mysql -u root -p overdue_debt_db < sql/manage_triggers.sql
```

### 3. 创建触发器
```sql
-- 执行触发器创建脚本
mysql -u root -p overdue_debt_db < sql/create_null_to_zero_triggers.sql
```

### 4. 验证触发器
```sql
-- 查看已创建的触发器
SHOW TRIGGERS FROM overdue_debt_db;
```

## 文件说明

- `sql/create_null_to_zero_triggers.sql` - 主要触发器创建脚本
  - 包含INSERT和UPDATE触发器
  - 包含一次性清理现有NULL值的UPDATE语句
  
- `sql/manage_triggers.sql` - 触发器管理脚本
  - 查看触发器状态
  - 统计NULL值情况
  - 备份和恢复命令
  - 性能监控查询

## 使用说明

### 执行顺序
1. 先执行备份
2. 运行统计查询，记录当前NULL值数量
3. 执行触发器创建脚本
4. 验证触发器工作正常
5. 执行一次性清理脚本

### 触发器行为
- **INSERT触发器**：在插入新记录时，自动将NULL值转换为0.00
- **UPDATE触发器**：在更新记录时，自动将NULL值转换为0.00
- 不影响已有的非NULL值
- 只处理decimal类型的字段

### 维护建议
- 定期检查触发器状态
- 监控触发器性能影响
- 保留数据备份至少30天
- 记录所有数据清理操作

## 注意事项

1. **性能影响**：触发器会在每次INSERT/UPDATE时执行，可能影响性能
2. **数据一致性**：确保业务逻辑允许将NULL转换为0.00
3. **测试环境**：建议先在测试环境验证
4. **监控告警**：设置适当的监控，关注异常情况

## 回滚方案

如果需要回滚：

```sql
-- 1. 删除触发器
DROP TRIGGER IF EXISTS trg_disposal_insert_null_to_zero;
DROP TRIGGER IF EXISTS trg_disposal_update_null_to_zero;
-- ... 删除所有触发器

-- 2. 从备份表恢复数据（谨慎操作）
-- RENAME TABLE 处置表 TO 处置表_with_triggers;
-- RENAME TABLE 处置表_backup_20250125 TO 处置表;
```

## 联系方式

如有问题，请联系数据库管理员。