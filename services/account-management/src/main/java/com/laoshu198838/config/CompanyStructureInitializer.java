package com.laoshu198838.config;

import com.laoshu198838.service.CompanyStructureService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 公司架构数据初始化器
 * 在应用启动时检查并初始化公司架构数据
 * 
 * <AUTHOR>
 */
@Component
@Order(1) // 设置较高优先级，确保在其他组件之前执行
public class CompanyStructureInitializer implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(CompanyStructureInitializer.class);

    @Autowired
    private CompanyStructureService companyStructureService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            logger.info("📋 检查公司架构数据初始化状态...");
            
            // 检查是否需要初始化
            if (companyStructureService.needsInitialization()) {
                logger.info("🔄 检测到公司数据不完整，开始自动初始化...");
                
                // 执行初始化
                companyStructureService.initializeCompanyStructure();
                
                // 获取并显示初始化结果
                CompanyStructureService.CompanyStructureStats stats = companyStructureService.getStructureStats();
                logger.info("✅ 公司架构数据初始化完成:");
                logger.info("   📊 总公司数: {}", stats.getTotalCompanies());
                logger.info("   🟢 活跃公司: {}", stats.getActiveCompanies());
                logger.info("   🏢 管理公司: {}", stats.getManagementCompanies());
                
            } else {
                CompanyStructureService.CompanyStructureStats stats = companyStructureService.getStructureStats();
                logger.info("✅ 公司架构数据已完整:");
                logger.info("   📊 总公司数: {}", stats.getTotalCompanies());
                logger.info("   🟢 活跃公司: {}", stats.getActiveCompanies());
                logger.info("   🏢 管理公司: {}", stats.getManagementCompanies());
            }
            
        } catch (Exception e) {
            logger.error("❌ 公司架构数据初始化失败", e);
            // 不抛出异常，避免影响应用启动
            logger.warn("⚠️  应用将继续启动，但公司数据可能不完整");
        }
    }
}