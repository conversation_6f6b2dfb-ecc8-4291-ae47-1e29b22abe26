package com.laoshu198838.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 账户管理模块配置类
 * 确保账户管理相关的服务类能被Spring Boot正确扫描和注册
 *
 * <AUTHOR>
 */
@Configuration
@ComponentScan(basePackages = {
    "com.laoshu198838.service",
    "com.laoshu198838.validator",
    "com.laoshu198838.exception"
})
@ConditionalOnClass(name = "com.laoshu198838.service.AccountManagementService")
public class AccountManagementConfig {

    // 配置类可以在这里添加特定的Bean配置

}
