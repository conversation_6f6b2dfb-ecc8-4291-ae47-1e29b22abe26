package com.laoshu198838.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.laoshu198838.entity.user_system.Company;
import com.laoshu198838.repository.user_system.CompanyRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 公司架构数据服务
 * 负责从配置文件初始化公司数据
 * 
 * <AUTHOR>
 */
@Service
public class CompanyStructureService {
    
    private static final Logger logger = LoggerFactory.getLogger(CompanyStructureService.class);
    
    @Autowired
    private CompanyRepository companyRepository;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 从配置文件初始化公司数据
     */
    @Transactional
    public void initializeCompanyStructure() {
        try {
            logger.info("开始初始化公司架构数据...");
            
            // 读取配置文件
            ClassPathResource resource = new ClassPathResource("company-structure.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            JsonNode companiesNode = rootNode.get("companyStructure").get("companies");
            
            if (companiesNode != null && companiesNode.isArray()) {
                List<Company> companiesToSave = new ArrayList<>();
                
                for (JsonNode companyNode : companiesNode) {
                    Long id = companyNode.get("id").asLong();
                    String companyName = companyNode.get("companyName").asText();
                    String companyShortName = companyNode.get("companyShortName").asText();
                    String companyCode = companyNode.get("companyCode").asText();
                    Long parentCompanyId = companyNode.has("parentCompanyId") && 
                                          !companyNode.get("parentCompanyId").isNull() ? 
                                          companyNode.get("parentCompanyId").asLong() : null;
                    Integer level = companyNode.get("level").asInt();
                    boolean isManagementCompany = companyNode.get("isManagementCompany").asBoolean();
                    String status = companyNode.get("status").asText();
                    
                    // 检查公司是否已存在
                    Optional<Company> existingCompany = companyRepository.findById(id);
                    Company company;
                    
                    if (existingCompany.isPresent()) {
                        // 更新现有公司
                        company = existingCompany.get();
                        logger.debug("更新现有公司: {}", companyName);
                    } else {
                        // 创建新公司
                        company = new Company();
                        company.setId(id);
                        logger.debug("创建新公司: {}", companyName);
                    }
                    
                    // 设置公司属性
                    company.setCompanyName(companyName);
                    company.setCompanyShortName(companyShortName);
                    company.setCompanyCode(companyCode);
                    company.setParentCompanyId(parentCompanyId);
                    company.setLevel(level);
                    company.setIsManagementCompany(isManagementCompany);
                    company.setStatus("active".equals(status) ? 
                                    Company.CompanyStatus.active : 
                                    Company.CompanyStatus.inactive);
                    
                    companiesToSave.add(company);
                }
                
                // 批量保存
                companyRepository.saveAll(companiesToSave);
                logger.info("成功初始化 {} 个公司数据", companiesToSave.size());
                
            } else {
                logger.warn("配置文件中未找到有效的公司数据");
            }
            
        } catch (IOException e) {
            logger.error("读取公司架构配置文件失败", e);
            throw new RuntimeException("初始化公司架构数据失败", e);
        } catch (Exception e) {
            logger.error("初始化公司架构数据失败", e);
            throw new RuntimeException("初始化公司架构数据失败", e);
        }
    }
    
    /**
     * 检查是否需要初始化公司数据
     */
    public boolean needsInitialization() {
        try {
            long count = companyRepository.count();
            logger.info("当前数据库中公司总数: {}", count);
            
            // 如果公司数量少于预期，需要初始化
            return count < 30; // 根据配置文件，我们有32个公司
            
        } catch (Exception e) {
            logger.error("检查公司数据初始化状态失败", e);
            return true; // 出错时默认需要初始化
        }
    }
    
    /**
     * 强制重新初始化公司数据
     */
    @Transactional
    public void forceReinitialize() {
        logger.info("强制重新初始化公司架构数据...");
        initializeCompanyStructure();
    }
    
    /**
     * 获取公司架构统计信息
     */
    public CompanyStructureStats getStructureStats() {
        try {
            long totalCompanies = companyRepository.count();
            long activeCompanies = companyRepository.findAllActiveCompanies().size();
            long managementCompanies = companyRepository.findManagementCompanies().size();
            
            return new CompanyStructureStats(totalCompanies, activeCompanies, managementCompanies);
            
        } catch (Exception e) {
            logger.error("获取公司架构统计信息失败", e);
            return new CompanyStructureStats(0, 0, 0);
        }
    }
    
    /**
     * 公司架构统计信息
     */
    public static class CompanyStructureStats {
        private final long totalCompanies;
        private final long activeCompanies;
        private final long managementCompanies;
        
        public CompanyStructureStats(long totalCompanies, long activeCompanies, long managementCompanies) {
            this.totalCompanies = totalCompanies;
            this.activeCompanies = activeCompanies;
            this.managementCompanies = managementCompanies;
        }
        
        public long getTotalCompanies() { return totalCompanies; }
        public long getActiveCompanies() { return activeCompanies; }
        public long getManagementCompanies() { return managementCompanies; }
    }
}