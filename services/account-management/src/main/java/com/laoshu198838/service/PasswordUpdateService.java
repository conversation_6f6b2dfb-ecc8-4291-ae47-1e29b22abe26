package com.laoshu198838.service;

import java.util.List;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 密码更新服务
 * 使用user_system数据库进行密码管理
 * <AUTHOR>
 */
@Service
public class PasswordUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(PasswordUpdateService.class);

    @Autowired
    private BCryptPasswordEncoder bCryptPasswordEncoder;

    private final JdbcTemplate userSystemJdbcTemplate;

    public PasswordUpdateService(@Qualifier("userSystemDataSource") DataSource userSystemDataSource) {
        this.userSystemJdbcTemplate = new JdbcTemplate(userSystemDataSource);
        logger.info("PasswordUpdateService初始化完成，使用userSystemDataSource");
    }

    public void updatePasswords() {
        logger.info("🔥🔥🔥 开始批量更新用户密码加密（user_system数据库）");

        try {
            // 查询所有需要更新密码的用户（密码不以$2a$开头的用户）
            String selectSql = "SELECT id, username, password FROM users WHERE password NOT LIKE '$2a$%'";

            List<UserPasswordInfo> usersToUpdate = userSystemJdbcTemplate.query(selectSql, (rs, rowNum) -> {
                UserPasswordInfo info = new UserPasswordInfo();
                info.id = rs.getLong("id");
                info.username = rs.getString("username");
                info.password = rs.getString("password");
                return info;
            });

            logger.info("🔥🔥🔥 找到 {} 个需要更新密码的用户", usersToUpdate.size());

            int updatedCount = 0;
            String updateSql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";

            for (UserPasswordInfo userInfo : usersToUpdate) {
                // 使用BCrypt加密明文密码
                String encodedPassword = bCryptPasswordEncoder.encode(userInfo.password);

                // 更新数据库中的密码
                int rowsAffected = userSystemJdbcTemplate.update(updateSql, encodedPassword, userInfo.id);

                if (rowsAffected > 0) {
                    updatedCount++;
                    logger.info("🔥🔥🔥 更新用户密码成功: {}", userInfo.username);
                }
            }

            logger.info("🔥🔥🔥 批量密码更新完成，共更新 {} 个用户", updatedCount);

        } catch (Exception e) {
            logger.error("🔥🔥🔥 批量更新用户密码失败", e);
            throw new RuntimeException("批量更新用户密码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 内部类：用户密码信息
     */
    private static class UserPasswordInfo {
        Long id;
        String username;
        String password;
    }
}