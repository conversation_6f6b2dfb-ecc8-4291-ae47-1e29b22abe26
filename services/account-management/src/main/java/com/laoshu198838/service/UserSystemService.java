package com.laoshu198838.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.laoshu198838.entity.user_system.User;
import com.laoshu198838.entity.user_system.Role;
import com.laoshu198838.model.user.dto.UserDTO;
import com.laoshu198838.model.user.dto.UserRegistrationDTO;

import javax.sql.DataSource;

/**
 * 用户系统服务
 * 使用user_system数据库进行用户管理
 * <AUTHOR>
 */
@Service
public class UserSystemService {

    private static final Logger logger = LoggerFactory.getLogger(UserSystemService.class);

    // 移除Repository依赖，统一使用JdbcTemplate
    // @Autowired
    // private UserSystemUserRepository userRepository;

    // @Autowired
    // private UserSystemRoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private final JdbcTemplate userSystemJdbcTemplate;

    public UserSystemService(@Qualifier("userSystemDataSource") DataSource userSystemDataSource) {
        this.userSystemJdbcTemplate = new JdbcTemplate(userSystemDataSource);
        logger.info("UserSystemService初始化完成，使用userSystemDataSource");
    }

    /**
     * 用户注册
     */
    @Transactional
    public User registerUser(UserRegistrationDTO registrationDTO) {
        logger.info("🔥🔥🔥 开始处理用户注册: {}", registrationDTO.getUsername());

        try {
            // 检查用户名是否已存在
            String checkUserSql = "SELECT COUNT(*) FROM users WHERE username = ?";
            Integer userCount = userSystemJdbcTemplate.queryForObject(checkUserSql, Integer.class, registrationDTO.getUsername());

            if (userCount != null && userCount > 0) {
                logger.warn("🔥🔥🔥 用户名已存在: {}", registrationDTO.getUsername());
                throw new RuntimeException("用户名已存在");
            }

            // 检查默认用户角色是否存在
            String checkRoleSql = "SELECT role_name FROM roles WHERE role_id = ?";
            String roleName = userSystemJdbcTemplate.queryForObject(checkRoleSql, String.class, 2); // 2 = USER角色

            if (roleName == null) {
                logger.error("🔥🔥🔥 找不到默认用户角色 (roleId=2)");
                throw new RuntimeException("系统配置错误：找不到默认用户角色");
            }

            // 插入新用户
            String insertSql = "INSERT INTO users (username, password, name, companyname, department, role_id, status, created_at, updated_at) " +
                              "VALUES (?, ?, ?, ?, ?, ?, 'PENDING', NOW(), NOW())";

            int insertedRows = userSystemJdbcTemplate.update(insertSql,
                registrationDTO.getUsername(),
                passwordEncoder.encode(registrationDTO.getPassword()),
                registrationDTO.getName(),
                registrationDTO.getCompany(),
                registrationDTO.getDepartment(),
                2 // USER角色ID
            );

            if (insertedRows == 0) {
                throw new RuntimeException("用户注册失败");
            }

            logger.info("🔥🔥🔥 用户注册成功: {}", registrationDTO.getUsername());

            // 返回新创建的用户信息
            return findByUsername(registrationDTO.getUsername());
        } catch (Exception e) {
            logger.error("🔥🔥🔥 用户注册失败", e);
            throw new RuntimeException("用户注册失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有用户
     */
    @Cacheable(value = "users", key = "'all_users'")
    public List<UserDTO> getAllUsers() {
        logger.info("🔥🔥🔥 UserSystemService.getAllUsers() 开始执行");

        try {
            String sql = "SELECT u.id, u.username, u.name, u.companyname, u.department, u.status, r.role_name, r.role_id " +
                        "FROM users u LEFT JOIN roles r ON u.role_id = r.role_id " +
                        "ORDER BY u.id";

            logger.info("🔥🔥🔥 执行SQL查询: {}", sql);
            logger.info("🔥🔥🔥 使用JdbcTemplate: {}", userSystemJdbcTemplate.getClass().getName());

            List<UserDTO> users = userSystemJdbcTemplate.query(sql, (rs, rowNum) -> {
                UserDTO dto = new UserDTO();
                dto.setId(rs.getLong("id"));
                dto.setUsername(rs.getString("username"));
                dto.setName(rs.getString("name"));
                dto.setCompany(rs.getString("companyname"));
                dto.setDepartment(rs.getString("department"));
                dto.setStatus(rs.getString("status"));
                dto.setRole(rs.getString("role_name"));

                // 处理可能为null的role_id
                Object roleIdObj = rs.getObject("role_id");
                if (roleIdObj != null) {
                    dto.setRoleId(rs.getInt("role_id"));
                }
                return dto;
            });

            logger.info("🔥🔥🔥 查询到 {} 个用户", users.size());
            return users;
        } catch (Exception e) {
            logger.error("🔥🔥🔥 获取用户列表失败", e);
            throw new RuntimeException("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @Transactional
    @CacheEvict(value = "users", allEntries = true)
    public void updateUserStatus(Long userId, String status) {
        logger.info("🔥🔥🔥 更新用户状态: userId={}, status={}", userId, status);

        try {
            // 先检查用户是否存在
            String checkSql = "SELECT username FROM users WHERE id = ?";
            String username = userSystemJdbcTemplate.queryForObject(checkSql, String.class, userId);

            if (username == null) {
                throw new RuntimeException("用户不存在");
            }

            // 更新用户状态
            String updateSql = "UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?";
            int updatedRows = userSystemJdbcTemplate.update(updateSql, status, userId);

            if (updatedRows == 0) {
                throw new RuntimeException("更新用户状态失败");
            }

            logger.info("🔥🔥🔥 用户状态更新成功: {} -> {}", username, status);
        } catch (Exception e) {
            logger.error("🔥🔥🔥 更新用户状态失败", e);
            throw new RuntimeException("更新用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户角色
     */
    @Transactional
    @CacheEvict(value = "users", allEntries = true)
    public void updateUserRole(Long userId, Integer roleId) {
        logger.info("🔥🔥🔥 更新用户角色: userId={}, roleId={}", userId, roleId);

        try {
            // 先检查用户是否存在
            String checkUserSql = "SELECT username FROM users WHERE id = ?";
            String username = userSystemJdbcTemplate.queryForObject(checkUserSql, String.class, userId);

            if (username == null) {
                throw new RuntimeException("用户不存在");
            }

            // 检查角色是否存在
            String checkRoleSql = "SELECT role_name FROM roles WHERE role_id = ?";
            String roleName = userSystemJdbcTemplate.queryForObject(checkRoleSql, String.class, roleId);

            if (roleName == null) {
                throw new RuntimeException("角色不存在");
            }

            // 更新用户角色
            String updateSql = "UPDATE users SET role_id = ?, updated_at = NOW() WHERE id = ?";
            int updatedRows = userSystemJdbcTemplate.update(updateSql, roleId, userId);

            if (updatedRows == 0) {
                throw new RuntimeException("更新用户角色失败");
            }

            logger.info("🔥🔥🔥 用户角色更新成功: {} -> {}", username, roleName);
        } catch (Exception e) {
            logger.error("🔥🔥🔥 更新用户角色失败", e);
            throw new RuntimeException("更新用户角色失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户（软删除）
     */
    @Transactional
    public void deleteUser(Long userId) {
        logger.info("🔥🔥🔥 删除用户: {}", userId);

        try {
            // 先检查用户是否存在
            String checkSql = "SELECT username FROM users WHERE id = ?";
            String username = userSystemJdbcTemplate.queryForObject(checkSql, String.class, userId);

            if (username == null) {
                throw new RuntimeException("用户不存在");
            }

            // 软删除用户
            String updateSql = "UPDATE users SET status = 'DELETED', updated_at = NOW() WHERE id = ?";
            int updatedRows = userSystemJdbcTemplate.update(updateSql, userId);

            if (updatedRows == 0) {
                throw new RuntimeException("删除用户失败");
            }

            logger.info("🔥🔥🔥 用户删除成功: {}", username);
        } catch (Exception e) {
            logger.error("🔥🔥🔥 删除用户失败", e);
            throw new RuntimeException("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    public void resetPassword(String username, String newPassword) {
        logger.info("🔥🔥🔥 重置用户密码: {}", username);

        try {
            // 加密新密码
            String encodedPassword = passwordEncoder.encode(newPassword);

            // 更新密码
            String sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE username = ?";
            int updatedRows = userSystemJdbcTemplate.update(sql, encodedPassword, username);

            if (updatedRows == 0) {
                throw new RuntimeException("用户不存在: " + username);
            }

            logger.info("🔥🔥🔥 用户密码重置成功: {}", username);
        } catch (Exception e) {
            logger.error("🔥🔥🔥 重置用户密码失败: {}", username, e);
            throw new RuntimeException("重置密码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据用户名查找用户（返回基本信息）
     */
    @Cacheable(value = "users", key = "'user_' + #username")
    public User findByUsername(String username) {
        logger.info("🔥🔥🔥 根据用户名查找用户: {}", username);

        try {
            String sql = "SELECT u.id, u.username, u.name, u.companyname, u.department, u.status, u.role_id " +
                        "FROM users u WHERE u.username = ?";

            return userSystemJdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
                User user = new User();
                user.setId(rs.getLong("id"));
                user.setUsername(rs.getString("username"));
                user.setName(rs.getString("name"));
                user.setCompanyname(rs.getString("companyname"));
                user.setDepartment(rs.getString("department"));
                user.setStatus(rs.getString("status"));
                // 注意：这里不设置role对象，只设置基本信息
                return user;
            }, username);
        } catch (Exception e) {
            logger.warn("🔥🔥🔥 用户不存在: {}", username);
            return null;
        }
    }

    /**
     * 根据ID获取用户
     */
    @Cacheable(value = "users", key = "'user_id_' + #userId")
    public User getUserById(Long userId) {
        logger.info("🔥🔥🔥 根据ID获取用户: {}", userId);

        try {
            String sql = "SELECT u.id, u.username, u.name, u.companyname, u.department, u.status, u.role_id " +
                        "FROM users u WHERE u.id = ?";

            return userSystemJdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
                User user = new User();
                user.setId(rs.getLong("id"));
                user.setUsername(rs.getString("username"));
                user.setName(rs.getString("name"));
                user.setCompanyname(rs.getString("companyname"));
                user.setDepartment(rs.getString("department"));
                user.setStatus(rs.getString("status"));
                // 注意：这里不设置role对象，只设置基本信息
                return user;
            }, userId);
        } catch (Exception e) {
            logger.warn("🔥🔥🔥 用户不存在: {}", userId);
            return null;
        }
    }

}
