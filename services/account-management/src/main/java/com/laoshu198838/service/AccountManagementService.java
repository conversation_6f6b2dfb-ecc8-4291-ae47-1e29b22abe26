package com.laoshu198838.service;

import com.laoshu198838.entity.user_system.User;
import com.laoshu198838.model.user.dto.UserDTO;
import com.laoshu198838.model.user.dto.UserRegistrationDTO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 账户管理统一服务类
 * 整合所有用户账户相关的业务操作
 * 使用user_system数据库
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AccountManagementService {

    private static final Logger logger = LoggerFactory.getLogger(AccountManagementService.class);

    @Autowired
    private UserSystemService userSystemService;

    @Autowired
    private PasswordUpdateService passwordUpdateService;
    
    // ==================== 用户注册相关 ====================

    /**
     * 注册新用户
     */
    @Transactional
    public User registerUser(UserRegistrationDTO registrationDTO) {
        logger.info("开始注册用户: {}", registrationDTO.getUsername());
        try {
            return userSystemService.registerUser(registrationDTO);
        } catch (Exception e) {
            logger.error("用户注册失败", e);
            throw new RuntimeException("用户注册失败: " + e.getMessage(), e);
        }
    }

    // ==================== 用户管理相关 ====================

    /**
     * 获取所有用户列表
     */
    public List<UserDTO> getAllUsers() {
        logger.info("🔥🔥🔥 AccountManagementService.getAllUsers() 开始执行");
        logger.info("🔥🔥🔥 userSystemService实例: {}", userSystemService.getClass().getName());

        try {
            List<UserDTO> result = userSystemService.getAllUsers();
            logger.info("🔥🔥🔥 AccountManagementService.getAllUsers() 执行完成，返回 {} 个用户", result.size());
            return result;
        } catch (Exception e) {
            logger.error("🔥🔥🔥 AccountManagementService.getAllUsers() 执行失败", e);
            throw e;
        }
    }

    /**
     * 更新用户状态
     */
    @Transactional
    public void updateUserStatus(Long userId, String status) {
        logger.info("更新用户状态: userId={}, status={}", userId, status);
        try {
            userSystemService.updateUserStatus(userId, status);
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            throw new RuntimeException("更新用户状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新用户角色
     */
    @Transactional
    public void updateUserRoles(Long userId, String roleName) {
        logger.info("更新用户角色: userId={}, role={}", userId, roleName);
        try {
            // 根据角色名称获取角色ID
            Integer roleId = "ADMIN".equals(roleName) ? 1 : 2;
            userSystemService.updateUserRole(userId, roleId);
        } catch (Exception e) {
            logger.error("更新用户角色失败", e);
            throw new RuntimeException("更新用户角色失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long userId) {
        logger.info("删除用户: userId={}", userId);
        try {
            userSystemService.deleteUser(userId);
        } catch (Exception e) {
            logger.error("删除用户失败", e);
            throw new RuntimeException("删除用户失败: " + e.getMessage(), e);
        }
    }
    
    // ==================== 密码管理相关 ====================
    
    /**
     * 批量更新用户密码加密
     */
    public void updatePasswords() {
        logger.info("开始批量更新用户密码加密");
        try {
            passwordUpdateService.updatePasswords();
            logger.info("批量更新用户密码加密完成");
        } catch (Exception e) {
            logger.error("批量更新用户密码加密失败", e);
            throw new RuntimeException("批量更新用户密码加密失败: " + e.getMessage(), e);
        }
    }
}
