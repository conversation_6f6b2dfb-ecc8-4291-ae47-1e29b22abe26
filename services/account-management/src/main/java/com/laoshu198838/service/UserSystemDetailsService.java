//package com.laoshu198838.service;
//
//import java.util.Optional;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.core.userdetails.UserDetails;
//import org.springframework.security.core.userdetails.UserDetailsService;
//import org.springframework.security.core.userdetails.UsernameNotFoundException;
//import org.springframework.stereotype.Service;
//
//import com.laoshu198838.entity.user_system.User;
//import com.laoshu198838.repository.user_system.UserSystemUserRepository;
//
/// **
// * 用户系统认证服务
// * 使用user_system数据库进行用户认证
// * <AUTHOR>
// */
//@Service("userSystemDetailsService")
//public class UserSystemDetailsService implements UserDetailsService {
//
//    private static final Logger logger = LoggerFactory.getLogger(UserSystemDetailsService.class);
//
//    @Autowired
//    private UserSystemUserRepository userRepository;
//
//    @Override
//    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
//        logger.info("从user_system数据库加载用户: {}", username);
//
//        // 从user_system数据库查询用户
//        Optional<User> userOptional = userRepository.findByUsername(username);
//        if (userOptional.isEmpty()) {
//            logger.warn("用户不存在: {}", username);
//            throw new UsernameNotFoundException("用户不存在: " + username);
//        }
//
//        User user = userOptional.get();
//
//        // 检查用户状态
//        if (!"ACTIVE".equals(user.getStatus())) {
//            logger.warn("用户状态不活跃: {} (状态: {})", username, user.getStatus());
//            throw new UsernameNotFoundException("用户账户未激活或已被禁用");
//        }
//
//        logger.info("用户认证成功: {} (角色: {})", username,
//                   user.getRole() != null ? user.getRole().getRoleName() : "无角色");
//
//        return user;
//    }
//
//    /**
//     * 检查用户是否存在且活跃
//     */
//    public boolean isUserActiveByUsername(String username) {
//        Optional<User> userOptional = userRepository.findByUsername(username);
//        if (userOptional.isEmpty()) {
//            return false;
//        }
//
//        User user = userOptional.get();
//        return "ACTIVE".equals(user.getStatus());
//    }
//
//    /**
//     * 获取用户信息（不包含密码）
//     */
//    public User getUserInfo(String username) {
//        Optional<User> userOptional = userRepository.findByUsername(username);
//        if (userOptional.isEmpty()) {
//            return null;
//        }
//
//        User user = userOptional.get();
//        // 清除密码信息
//        user.setPassword(null);
//        return user;
//    }
//}
