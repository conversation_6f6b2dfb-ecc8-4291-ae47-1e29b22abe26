package com.laoshu198838.service;

import com.laoshu198838.entity.user_system.Company;
import com.laoshu198838.entity.user_system.User;
import com.laoshu198838.entity.user_system.UserCompanyPermission;
import com.laoshu198838.repository.user_system.CompanyRepository;
import com.laoshu198838.repository.user_system.UserCompanyPermissionRepository;
import com.laoshu198838.repository.user_system.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 数据权限管理服务
 * 
 * <AUTHOR>
 */
@Service
@Transactional
public class DataPermissionService {

    private static final Logger logger = LoggerFactory.getLogger(DataPermissionService.class);

    @Autowired
    private UserCompanyPermissionRepository permissionRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 检查用户是否有访问指定公司数据的权限
     */
    public boolean hasCompanyAccess(Long userId, String companyName) {
        if (userId == null || companyName == null) {
            return false;
        }

        try {
            Optional<UserCompanyPermission> permission = permissionRepository
                    .findValidPermissionByUserIdAndCompanyName(userId, companyName, LocalDateTime.now());
            
            boolean hasAccess = permission.isPresent();
            
            if (hasAccess) {
                logger.debug("用户 {} 有权访问公司 {} 的数据", userId, companyName);
            } else {
                logger.warn("用户 {} 尝试访问公司 {} 的数据但没有权限", userId, companyName);
            }
            
            return hasAccess;
        } catch (Exception e) {
            logger.error("检查用户 {} 对公司 {} 的权限时出错", userId, companyName, e);
            return false;
        }
    }

    /**
     * 获取用户可访问的公司列表
     */
    public List<Company> getUserAccessibleCompanies(Long userId) {
        List<UserCompanyPermission> permissions = permissionRepository
                .findValidPermissionsByUserId(userId, LocalDateTime.now());
        
        return permissions.stream()
                .map(UserCompanyPermission::getCompany)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户可访问的公司名称集合
     */
    public Set<String> getUserAccessibleCompanyNames(Long userId) {
        List<UserCompanyPermission> permissions = permissionRepository
                .findValidPermissionsByUserId(userId, LocalDateTime.now());
        
        return permissions.stream()
                .map(permission -> permission.getCompany().getCompanyName())
                .collect(Collectors.toSet());
    }

    /**
     * 检查用户是否为万润科技的管理员
     */
    public boolean isManagementAdmin(Long userId) {
        try {
            Optional<User> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) {
                return false;
            }

            User user = userOpt.get();
            
            // 检查是否为管理员角色
            if (user.getRole() == null || user.getRole().getRoleId() != 1) {
                return false;
            }

            // 检查是否属于万润科技
            return "万润科技".equals(user.getCompanyname());
            
        } catch (Exception e) {
            logger.error("检查用户 {} 是否为万润科技管理员时出错", userId, e);
            return false;
        }
    }

    /**
     * 为用户授权访问指定公司的权限
     */
    public UserCompanyPermission grantCompanyAccess(Long userId, Long companyId, 
                                                   UserCompanyPermission.PermissionType permissionType, 
                                                   Long grantedById, LocalDateTime expiresAt, String remarks) {
        try {
            // 检查用户和公司是否存在
            Optional<User> userOpt = userRepository.findById(userId);
            Optional<Company> companyOpt = companyRepository.findById(companyId);
            Optional<User> granterOpt = userRepository.findById(grantedById);

            if (userOpt.isEmpty() || companyOpt.isEmpty() || granterOpt.isEmpty()) {
                throw new IllegalArgumentException("用户、公司或授权人不存在");
            }

            User user = userOpt.get();
            Company company = companyOpt.get();
            User granter = granterOpt.get();

            // 检查是否已存在权限记录
            Optional<UserCompanyPermission> existingPermission = 
                    permissionRepository.findByUserIdAndCompanyId(userId, companyId);

            UserCompanyPermission permission;
            if (existingPermission.isPresent()) {
                // 更新现有权限
                permission = existingPermission.get();
                permission.setPermissionType(permissionType);
                permission.setGrantedBy(granter);
                permission.setExpiresAt(expiresAt);
                permission.setRemarks(remarks);
                permission.setStatus(UserCompanyPermission.PermissionStatus.active);
                permission.setGrantedAt(LocalDateTime.now());
            } else {
                // 创建新权限
                permission = new UserCompanyPermission(user, company, permissionType, false);
                permission.setGrantedBy(granter);
                permission.setExpiresAt(expiresAt);
                permission.setRemarks(remarks);
            }

            permission = permissionRepository.save(permission);
            
            logger.info("用户 {} 被授权访问公司 {} 的数据，权限类型：{}，授权人：{}", 
                       user.getUsername(), company.getCompanyName(), permissionType, granter.getUsername());
            
            return permission;
            
        } catch (Exception e) {
            logger.error("为用户 {} 授权公司 {} 访问权限时出错", userId, companyId, e);
            throw new RuntimeException("授权失败：" + e.getMessage());
        }
    }

    /**
     * 撤销用户对指定公司的访问权限
     */
    public boolean revokeCompanyAccess(Long userId, Long companyId) {
        try {
            int updatedRows = permissionRepository.revokePermission(userId, companyId, LocalDateTime.now());
            
            if (updatedRows > 0) {
                logger.info("已撤销用户 {} 对公司 {} 的访问权限", userId, companyId);
                return true;
            }
            
            return false;
        } catch (Exception e) {
            logger.error("撤销用户 {} 对公司 {} 的权限时出错", userId, companyId, e);
            return false;
        }
    }

    /**
     * 获取用户的权限详情
     */
    public List<UserCompanyPermission> getUserPermissions(Long userId) {
        return permissionRepository.findAllPermissionsByUserId(userId);
    }

    /**
     * 获取公司的用户权限列表
     */
    public List<UserCompanyPermission> getCompanyUserPermissions(Long companyId) {
        return permissionRepository.findActiveUserPermissionsByCompanyId(companyId, LocalDateTime.now());
    }

    /**
     * 自动过期权限
     */
    @Transactional
    public int expirePermissions() {
        try {
            int expiredCount = permissionRepository.expirePermissions(LocalDateTime.now());
            if (expiredCount > 0) {
                logger.info("自动过期了 {} 个权限记录", expiredCount);
            }
            return expiredCount;
        } catch (Exception e) {
            logger.error("自动过期权限时出错", e);
            return 0;
        }
    }

    /**
     * 获取即将过期的权限列表（7天内过期）
     */
    public List<UserCompanyPermission> getExpiringPermissions() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiryThreshold = now.plusDays(7);
        return permissionRepository.findExpiringPermissions(now, expiryThreshold);
    }

    /**
     * 创建用户的默认公司权限
     */
    public UserCompanyPermission createDefaultPermission(User user, String companyName) {
        try {
            Optional<Company> companyOpt = companyRepository.findByCompanyName(companyName);
            if (companyOpt.isEmpty()) {
                throw new IllegalArgumentException("公司不存在：" + companyName);
            }

            Company company = companyOpt.get();
            
            // 根据用户角色确定权限类型
            UserCompanyPermission.PermissionType permissionType = 
                    (user.getRole() != null && user.getRole().getRoleId() == 1) ? 
                    UserCompanyPermission.PermissionType.admin : 
                    UserCompanyPermission.PermissionType.write;

            UserCompanyPermission permission = new UserCompanyPermission(user, company, permissionType, true);
            permission.setRemarks("用户注册时的默认权限");
            
            permission = permissionRepository.save(permission);
            
            logger.info("为用户 {} 创建了对公司 {} 的默认权限", user.getUsername(), companyName);
            
            return permission;
            
        } catch (Exception e) {
            logger.error("为用户 {} 创建默认权限时出错", user.getUsername(), e);
            throw new RuntimeException("创建默认权限失败：" + e.getMessage());
        }
    }

    /**
     * 验证SQL查询条件中的公司过滤
     * 用于在Repository查询中动态添加公司过滤条件
     */
    public String buildCompanyFilter(Long userId, String tableAlias) {
        if (isManagementAdmin(userId)) {
            // 万润科技管理员可以查看所有数据
            return "";
        }

        Set<String> accessibleCompanies = getUserAccessibleCompanyNames(userId);
        if (accessibleCompanies.isEmpty()) {
            // 没有任何权限，返回永远为false的条件
            return " AND 1=0 ";
        }

        // 构建IN查询条件
        String companyList = accessibleCompanies.stream()
                .map(company -> "'" + company.replace("'", "''") + "'")
                .collect(Collectors.joining(","));
        
        return String.format(" AND %s.管理公司 IN (%s) ", tableAlias, companyList);
    }
}