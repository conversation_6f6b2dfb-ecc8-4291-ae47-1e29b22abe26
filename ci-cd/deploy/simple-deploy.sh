#!/bin/bash

# FinancialSystem 简化版自动部署脚本
# 专门用于修复常见的部署问题

set -e

# 配置变量
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
LINUX_SERVER="admin@10.25.1.85"
DEPLOY_DIR="/opt/FinancialSystem"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 主要部署函数
deploy() {
    log_info "🚀 开始简化部署流程..."
    
    cd "$PROJECT_ROOT"
    
    # 1. 创建部署包
    log_info "📦 创建部署包..."
    DEPLOY_PACKAGE="/tmp/financial-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # 排除不需要的文件
    tar --exclude='.git' \
        --exclude='node_modules' \
        --exclude='target' \
        --exclude='FinancialSystem-web/node_modules' \
        --exclude='FinancialSystem-web/build' \
        --exclude='*.log' \
        --exclude='.DS_Store' \
        -czf "$DEPLOY_PACKAGE" . || {
        log_error "创建部署包失败"
        return 1
    }
    
    log_success "部署包创建成功: $DEPLOY_PACKAGE"
    
    # 2. 传输到服务器
    log_info "📤 传输到服务器..."
    scp "$DEPLOY_PACKAGE" "$LINUX_SERVER:/tmp/" || {
        log_error "传输失败"
        return 1
    }
    
    # 3. 在服务器上部署
    log_info "🚀 在服务器上部署..."
    ssh "$LINUX_SERVER" bash -s << 'EOF'
        set -e
        
        # 配置变量
        DEPLOY_DIR="/opt/FinancialSystem"
        CURRENT_DIR="$DEPLOY_DIR/current"
        BACKUP_DIR="$DEPLOY_DIR/backup-$(date +%Y%m%d-%H%M%S)"
        
        echo "🔧 准备部署环境..."
        
        # 创建必要目录
        mkdir -p "$DEPLOY_DIR"
        
        # 备份现有部署
        if [ -d "$CURRENT_DIR" ]; then
            echo "📦 备份现有部署..."
            mv "$CURRENT_DIR" "$BACKUP_DIR"
        fi
        
        # 停止现有服务
        echo "🛑 停止现有服务..."
        pkill -f "api-gateway.*jar" || true
        pkill -f "java.*jar" || true
        
        # 清理端口占用
        echo "🔧 清理端口占用..."
        for port in 8080 3306 80; do
            pid=$(lsof -ti:$port 2>/dev/null || true)
            if [ -n "$pid" ]; then
                echo "⚠️  杀死占用端口 $port 的进程 $pid"
                kill -9 $pid 2>/dev/null || true
                sleep 2
            fi
        done
        
        # 停止Docker服务
        echo "🐳 停止Docker服务..."
        if command -v docker &> /dev/null; then
            docker stop $(docker ps -q) 2>/dev/null || true
            docker rm $(docker ps -aq) 2>/dev/null || true
        fi
        
        # 解压新版本
        echo "📦 解压新版本..."
        mkdir -p "$CURRENT_DIR"
        tar -xzf /tmp/financial-deploy-*.tar.gz -C "$CURRENT_DIR" --strip-components=0
        
        # 进入部署目录
        cd "$CURRENT_DIR"
        
        # 确保使用本地镜像配置
        if [ -f docker-compose.local.yml ]; then
            echo "🔧 使用本地镜像配置..."
            cp docker-compose.local.yml docker-compose.yml
        fi
        
        # 检查本地镜像状态
        echo "🔍 检查本地镜像..."
        docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(mysql:8.0|maven:3.9-eclipse-temurin-21|openjdk:21-jdk|node:18-alpine|nginx:alpine)" || echo "⚠️  部分镜像可能缺失，但继续尝试部署"
        
        # 设置执行权限
        find . -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
        
        # 等待端口释放
        echo "⏳ 等待端口释放..."
        sleep 5
        
        # 启动Docker服务
        if [ -f docker-compose.yml ]; then
            echo "🚀 启动Docker服务..."
            
            # 拉取或确认镜像存在
            docker compose config --services | while read service; do
                echo "🔍 检查服务 $service 的镜像..."
            done
            
            # 启动服务
            docker compose up -d --remove-orphans || {
                echo "❌ Docker启动失败，尝试修复..."
                
                # 清理Docker资源
                docker system prune -f || true
                
                # 再次尝试启动
                docker compose up -d --remove-orphans || {
                    echo "❌ Docker启动仍然失败，回滚..."
                    exit 1
                }
            }
        fi
        
        # 等待服务启动
        echo "⏳ 等待服务启动..."
        sleep 30
        
        # 检查服务状态
        echo "🔍 检查服务状态..."
        if command -v docker &> /dev/null; then
            docker compose ps
        fi
        
        echo "✅ 部署完成"
EOF
    
    local deploy_result=$?
    
    # 清理临时文件
    rm -f "$DEPLOY_PACKAGE"
    
    if [ $deploy_result -eq 0 ]; then
        log_success "✅ 部署成功完成"
        
        # 验证部署
        log_info "🔍 验证部署..."
        sleep 10
        
        # 简单的健康检查
        if curl -sf "http://10.25.1.85:8080/actuator/health" > /dev/null; then
            log_success "✅ 后端服务健康检查通过"
        else
            log_warning "⚠️  后端服务健康检查失败，但部署可能仍在进行中"
        fi
        
        if curl -sf "http://10.25.1.85/" > /dev/null; then
            log_success "✅ 前端服务健康检查通过"
        else
            log_warning "⚠️  前端服务健康检查失败，但部署可能仍在进行中"
        fi
        
        return 0
    else
        log_error "❌ 部署失败"
        return 1
    fi
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem简化自动部署..."
    
    # 检查依赖
    for cmd in curl ssh scp tar; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要的命令: $cmd"
            exit 1
        fi
    done
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 执行部署
    if deploy; then
        log_success "🎉 部署成功完成！"
        log_info "📊 部署信息:"
        log_info "   - 服务器: $LINUX_SERVER"
        log_info "   - 部署目录: $DEPLOY_DIR/current"
        log_info "   - 时间: $(date)"
        
        # 显示访问地址
        log_info "🌐 访问地址:"
        log_info "   - 前端: http://10.25.1.85/"
        log_info "   - 后端API: http://10.25.1.85:8080/"
        log_info "   - 健康检查: http://10.25.1.85:8080/actuator/health"
        
        exit 0
    else
        log_error "❌ 部署失败"
        exit 1
    fi
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi