#!/bin/bash
# 修复后端启动问题

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

cd /opt/FinancialSystem/current

log "🔧 开始修复后端启动问题..."

# 1. 停止后端服务
log "停止后端服务..."
docker compose -f docker-compose.prod.yml stop backend

# 2. 确保数据库正常运行
log "检查数据库状态..."
if docker exec financial-mysql mysqladmin ping -h localhost -uroot -p'Zlb&198838' >/dev/null 2>&1; then
    success "数据库运行正常"
else
    error "数据库未运行，尝试重启..."
    docker compose -f docker-compose.prod.yml restart mysql
    sleep 30
fi

# 3. 确保数据库和表存在
log "确保数据库存在..."
docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e "
CREATE DATABASE IF NOT EXISTS \`逾期债权数据库\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS \`user_system\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS \`kingdee\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
" 2>/dev/null || true

# 4. 执行数据库初始化脚本
if [ -f "init-scripts/01-init-databases.sql" ]; then
    log "执行数据库初始化脚本..."
    docker exec -i financial-mysql mysql -uroot -p'Zlb&198838' < init-scripts/01-init-databases.sql 2>/dev/null || true
fi

# 5. 测试数据库连接
log "测试数据库连接..."
docker run --rm --network financial-network mysql:8.0 \
    mysql -h financial-mysql -uroot -p'Zlb&198838' -e "SHOW DATABASES;" 2>/dev/null | grep -E '(逾期|user_system|kingdee)' && \
    success "数据库连接测试成功" || error "数据库连接测试失败"

# 6. 创建临时测试脚本
log "创建后端启动测试..."
cat > test-backend.sh << 'EOF'
#!/bin/bash
cd /app
echo "测试JAR文件..."
if [ -f api-gateway-1.0-SNAPSHOT.jar ]; then
    echo "JAR文件存在，尝试启动..."
    java -jar api-gateway-1.0-SNAPSHOT.jar \
        --spring.profiles.active=production \
        --spring.datasource.primary.url="*****************************************************************************************************************************************************************************************" \
        --spring.datasource.secondary.url="***************************************************************************************************************************************************************************" \
        --spring.datasource.user-system.url="*************************************************************************************************************************************************************************************************************" \
        --spring.datasource.primary.username=root \
        --spring.datasource.primary.password="Zlb&198838" \
        --spring.datasource.secondary.username=root \
        --spring.datasource.secondary.password="Zlb&198838" \
        --spring.datasource.user-system.username=root \
        --spring.datasource.user-system.password="Zlb&198838"
else
    echo "错误: JAR文件不存在"
    exit 1
fi
EOF

chmod +x test-backend.sh

# 7. 重新启动后端，使用修改的启动命令
log "重新启动后端服务..."
docker compose -f docker-compose.prod.yml rm -f backend

# 创建新的docker-compose覆盖文件
cat > docker-compose.override.yml << 'EOF'
services:
  backend:
    command: >
      sh -c "
        echo '等待数据库就绪...' &&
        sleep 10 &&
        echo '测试数据库连接...' &&
        apt-get update && apt-get install -y default-mysql-client &&
        until mysql -h financial-mysql -uroot -p'Zlb&198838' -e 'SELECT 1' >/dev/null 2>&1; do
          echo '等待MySQL...'
          sleep 5
        done &&
        echo '数据库连接成功，启动应用...' &&
        java -jar /app/api-gateway-1.0-SNAPSHOT.jar \
          --spring.profiles.active=production \
          --spring.datasource.primary.url='*****************************************************************************************************************************************************************************************' \
          --spring.datasource.primary.username=root \
          --spring.datasource.primary.password='Zlb&198838'
      "
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - SPRING_DATASOURCE_PRIMARY_URL=*****************************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_PRIMARY_USERNAME=root
      - SPRING_DATASOURCE_PRIMARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_SECONDARY_URL=***************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_SECONDARY_USERNAME=root
      - SPRING_DATASOURCE_SECONDARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_USER_SYSTEM_URL=*************************************************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USER_SYSTEM_USERNAME=root
      - SPRING_DATASOURCE_USER_SYSTEM_PASSWORD=Zlb&198838
EOF

# 使用覆盖文件启动
docker compose -f docker-compose.prod.yml -f docker-compose.override.yml up -d backend

log "等待后端启动..."
sleep 30

# 8. 检查后端状态
log "检查后端状态..."
if curl -sf http://localhost:8080/actuator/health >/dev/null 2>&1; then
    success "🎉 后端启动成功！"
else
    error "后端启动失败，查看日志..."
    docker compose -f docker-compose.prod.yml logs backend --tail 50
fi

log "修复脚本执行完成"