#!/bin/bash

# 增强版健康检查和监控脚本
# 提供持续监控、自动恢复和告警功能

set -e

# 配置
PROJECT_ROOT="/opt/FinancialSystem/current"
LOG_DIR="/var/log/financial-system"
METRICS_FILE="$LOG_DIR/health-metrics.json"
ALERT_WEBHOOK="http://**********:9000/alert"
CHECK_INTERVAL=60  # 检查间隔（秒）
MAX_FAILURES=3     # 最大失败次数
RECOVERY_ENABLED=true

# 健康检查端点
HEALTH_ENDPOINTS=(
    "http://localhost:8080/actuator/health"
    "http://localhost:8080/api/health"
    "http://localhost/"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log_info() {
    local msg="[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${BLUE}${msg}${NC}"
    echo "$msg" >> "$LOG_DIR/monitor.log"
}

log_success() {
    local msg="[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${GREEN}${msg}${NC}"
    echo "$msg" >> "$LOG_DIR/monitor.log"
}

log_warning() {
    local msg="[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${YELLOW}${msg}${NC}"
    echo "$msg" >> "$LOG_DIR/monitor.log"
}

log_error() {
    local msg="[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${RED}${msg}${NC}"
    echo "$msg" >> "$LOG_DIR/monitor.log"
}

# 发送告警
send_alert() {
    local severity="$1"
    local component="$2"
    local message="$3"
    local details="$4"
    
    local alert_data="{
        \"severity\": \"$severity\",
        \"component\": \"$component\",
        \"message\": \"$message\",
        \"details\": \"$details\",
        \"timestamp\": \"$(date -Iseconds)\",
        \"host\": \"$(hostname)\"
    }"
    
    # 发送Webhook告警
    curl -X POST "$ALERT_WEBHOOK" \
        -H "Content-Type: application/json" \
        -d "$alert_data" \
        2>/dev/null || log_warning "告警发送失败"
    
    # 记录到日志
    echo "$alert_data" >> "$LOG_DIR/alerts.log"
}

# 检查HTTP端点
check_http_endpoint() {
    local endpoint="$1"
    local timeout=10
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" \
        --connect-timeout $timeout \
        --max-time $timeout \
        "$endpoint" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        return 0
    else
        return 1
    fi
}

# 检查端口
check_port() {
    local port="$1"
    local service="$2"
    
    if nc -z localhost "$port" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查进程
check_process() {
    local process_pattern="$1"
    
    if pgrep -f "$process_pattern" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 检查Docker容器
check_docker_container() {
    local container_name="$1"
    
    local status=$(docker inspect -f '{{.State.Status}}' "$container_name" 2>/dev/null || echo "not_found")
    
    if [ "$status" = "running" ]; then
        # 检查容器健康状态
        local health=$(docker inspect -f '{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "none")
        if [ "$health" = "healthy" ] || [ "$health" = "none" ]; then
            return 0
        fi
    fi
    
    return 1
}

# 检查数据库连接
check_database() {
    # MySQL健康检查
    if docker exec financial-mysql mysqladmin ping -h localhost --silent 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 系统资源检查
check_system_resources() {
    local cpu_threshold=80
    local memory_threshold=85
    local disk_threshold=90
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}' | cut -d. -f1)
    
    # 内存使用率
    local memory_usage=$(free | grep Mem | awk '{print int($3/$2 * 100)}')
    
    # 磁盘使用率
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    local warnings=""
    
    if [ "$cpu_usage" -gt "$cpu_threshold" ]; then
        warnings="CPU使用率过高: ${cpu_usage}% "
    fi
    
    if [ "$memory_usage" -gt "$memory_threshold" ]; then
        warnings="${warnings}内存使用率过高: ${memory_usage}% "
    fi
    
    if [ "$disk_usage" -gt "$disk_threshold" ]; then
        warnings="${warnings}磁盘使用率过高: ${disk_usage}% "
    fi
    
    if [ -n "$warnings" ]; then
        log_warning "系统资源告警: $warnings"
        send_alert "warning" "system_resources" "系统资源使用率过高" "$warnings"
        return 1
    fi
    
    return 0
}

# 记录健康指标
record_metrics() {
    local component="$1"
    local status="$2"
    local response_time="$3"
    
    local metric="{
        \"component\": \"$component\",
        \"status\": \"$status\",
        \"response_time\": $response_time,
        \"timestamp\": \"$(date -Iseconds)\"
    }"
    
    # 追加到指标文件
    echo "$metric" >> "$METRICS_FILE"
    
    # 保持文件大小（只保留最近10000条记录）
    if [ $(wc -l < "$METRICS_FILE") -gt 10000 ]; then
        tail -n 10000 "$METRICS_FILE" > "$METRICS_FILE.tmp"
        mv "$METRICS_FILE.tmp" "$METRICS_FILE"
    fi
}

# 自动恢复功能
auto_recovery() {
    local component="$1"
    local failure_count="$2"
    
    if [ "$RECOVERY_ENABLED" != "true" ]; then
        return 1
    fi
    
    log_warning "尝试自动恢复组件: $component (失败次数: $failure_count)"
    
    case "$component" in
        "backend")
            log_info "重启后端服务..."
            cd "$PROJECT_ROOT"
            
            # 停止现有服务
            pkill -f "api-gateway.*jar" || true
            docker compose stop backend || true
            
            sleep 5
            
            # 重启服务
            if [ -f "docker-compose.yml" ]; then
                docker compose up -d backend
            else
                nohup java -jar api-gateway/target/api-gateway-1.0-SNAPSHOT.jar > /dev/null 2>&1 &
            fi
            ;;
            
        "mysql")
            log_info "重启MySQL服务..."
            docker restart financial-mysql || docker compose restart mysql
            ;;
            
        "frontend")
            log_info "重启前端服务..."
            docker compose restart nginx || true
            ;;
            
        *)
            log_warning "未知组件: $component"
            return 1
            ;;
    esac
    
    # 等待服务启动
    sleep 30
    
    # 验证恢复
    if perform_health_check | grep -q "$component.*UP"; then
        log_success "组件 $component 恢复成功"
        send_alert "info" "$component" "组件自动恢复成功" "组件已成功重启并通过健康检查"
        return 0
    else
        log_error "组件 $component 恢复失败"
        return 1
    fi
}

# 执行完整健康检查
perform_health_check() {
    local overall_status="UP"
    local failed_components=()
    
    echo "===== 健康检查报告 $(date) ====="
    
    # 1. 检查后端服务
    echo -n "后端服务: "
    local start_time=$(date +%s%N)
    if check_http_endpoint "${HEALTH_ENDPOINTS[0]}"; then
        local end_time=$(date +%s%N)
        local response_time=$((($end_time - $start_time) / 1000000))
        echo -e "${GREEN}UP${NC} (响应时间: ${response_time}ms)"
        record_metrics "backend" "UP" "$response_time"
    else
        echo -e "${RED}DOWN${NC}"
        overall_status="DOWN"
        failed_components+=("backend")
        record_metrics "backend" "DOWN" "0"
    fi
    
    # 2. 检查数据库
    echo -n "数据库服务: "
    if check_database; then
        echo -e "${GREEN}UP${NC}"
        record_metrics "database" "UP" "0"
    else
        echo -e "${RED}DOWN${NC}"
        overall_status="DOWN"
        failed_components+=("mysql")
        record_metrics "database" "DOWN" "0"
    fi
    
    # 3. 检查前端服务
    echo -n "前端服务: "
    local start_time=$(date +%s%N)
    if check_http_endpoint "${HEALTH_ENDPOINTS[2]}"; then
        local end_time=$(date +%s%N)
        local response_time=$((($end_time - $start_time) / 1000000))
        echo -e "${GREEN}UP${NC} (响应时间: ${response_time}ms)"
        record_metrics "frontend" "UP" "$response_time"
    else
        echo -e "${RED}DOWN${NC}"
        overall_status="DOWN"
        failed_components+=("frontend")
        record_metrics "frontend" "DOWN" "0"
    fi
    
    # 4. 检查Docker容器
    echo -n "Docker容器: "
    local container_status="UP"
    for container in financial-mysql financial-backend financial-frontend financial-nginx; do
        if ! check_docker_container "$container" 2>/dev/null; then
            container_status="PARTIAL"
        fi
    done
    if [ "$container_status" = "UP" ]; then
        echo -e "${GREEN}全部运行${NC}"
    else
        echo -e "${YELLOW}部分运行${NC}"
    fi
    
    # 5. 检查系统资源
    echo -n "系统资源: "
    if check_system_resources; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${YELLOW}告警${NC}"
    fi
    
    # 6. 检查端口
    echo -n "端口监听: "
    local ports_ok=true
    for port in 8080 3306 80; do
        if ! check_port "$port"; then
            ports_ok=false
        fi
    done
    if [ "$ports_ok" = "true" ]; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常${NC}"
        overall_status="DOWN"
    fi
    
    echo "================================="
    echo -n "整体状态: "
    if [ "$overall_status" = "UP" ]; then
        echo -e "${GREEN}健康${NC}"
    else
        echo -e "${RED}不健康${NC}"
        echo "失败组件: ${failed_components[*]}"
    fi
    
    # 返回失败的组件
    if [ ${#failed_components[@]} -gt 0 ]; then
        echo "${failed_components[*]}"
        return 1
    else
        return 0
    fi
}

# 持续监控模式
continuous_monitoring() {
    log_info "启动持续监控模式 (检查间隔: ${CHECK_INTERVAL}秒)"
    
    # 组件失败计数器
    declare -A failure_counts
    
    while true; do
        # 执行健康检查
        if failed_components=$(perform_health_check 2>&1 | tail -1); then
            # 重置失败计数
            for component in "${!failure_counts[@]}"; do
                failure_counts[$component]=0
            done
        else
            # 处理失败的组件
            for component in $failed_components; do
                # 增加失败计数
                failure_counts[$component]=$((${failure_counts[$component]:-0} + 1))
                
                # 检查是否需要告警
                if [ ${failure_counts[$component]} -eq 1 ]; then
                    send_alert "warning" "$component" "组件健康检查失败" "首次检测到失败"
                elif [ ${failure_counts[$component]} -eq $MAX_FAILURES ]; then
                    send_alert "critical" "$component" "组件持续失败" "连续失败${MAX_FAILURES}次"
                    
                    # 尝试自动恢复
                    auto_recovery "$component" ${failure_counts[$component]}
                fi
            done
        fi
        
        # 等待下一次检查
        sleep "$CHECK_INTERVAL"
    done
}

# 生成健康报告
generate_health_report() {
    local report_file="$LOG_DIR/health-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>FinancialSystem健康检查报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status-up { color: green; }
        .status-down { color: red; }
        .status-warning { color: orange; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>FinancialSystem健康检查报告</h1>
    <p>生成时间: $(date)</p>
    
    <h2>当前状态</h2>
EOF
    
    # 添加健康检查结果
    perform_health_check >> "$report_file"
    
    cat >> "$report_file" << 'EOF'
    
    <h2>最近24小时指标</h2>
    <canvas id="metricsChart"></canvas>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 这里可以添加图表展示代码
    </script>
</body>
</html>
EOF
    
    log_success "健康报告已生成: $report_file"
}

# 清理旧日志
cleanup_old_logs() {
    log_info "清理30天前的日志文件..."
    find "$LOG_DIR" -name "*.log" -mtime +30 -delete
    find "$LOG_DIR" -name "health-report-*.html" -mtime +7 -delete
}

# 主菜单
show_menu() {
    echo ""
    echo "========== 健康检查和监控系统 =========="
    echo "1. 执行一次健康检查"
    echo "2. 启动持续监控"
    echo "3. 生成健康报告"
    echo "4. 查看最近告警"
    echo "5. 清理旧日志"
    echo "6. 测试自动恢复"
    echo "0. 退出"
    echo "========================================"
}

# 查看最近告警
show_recent_alerts() {
    if [ -f "$LOG_DIR/alerts.log" ]; then
        echo "===== 最近10条告警 ====="
        tail -n 10 "$LOG_DIR/alerts.log" | jq '.'
    else
        echo "没有告警记录"
    fi
}

# 主函数
main() {
    case "$1" in
        check)
            perform_health_check
            ;;
        monitor)
            continuous_monitoring
            ;;
        report)
            generate_health_report
            ;;
        alerts)
            show_recent_alerts
            ;;
        cleanup)
            cleanup_old_logs
            ;;
        test-recovery)
            auto_recovery "$2" 1
            ;;
        daemon)
            # 作为守护进程运行
            nohup "$0" monitor > /dev/null 2>&1 &
            echo $! > "$LOG_DIR/monitor.pid"
            log_success "监控守护进程已启动 (PID: $!)"
            ;;
        stop)
            # 停止守护进程
            if [ -f "$LOG_DIR/monitor.pid" ]; then
                kill $(cat "$LOG_DIR/monitor.pid") 2>/dev/null
                rm -f "$LOG_DIR/monitor.pid"
                log_success "监控守护进程已停止"
            else
                log_warning "监控守护进程未运行"
            fi
            ;;
        *)
            # 交互式菜单
            while true; do
                show_menu
                read -p "请选择操作: " choice
                
                case $choice in
                    1) perform_health_check ;;
                    2) continuous_monitoring ;;
                    3) generate_health_report ;;
                    4) show_recent_alerts ;;
                    5) cleanup_old_logs ;;
                    6) 
                        read -p "输入要测试的组件 (backend/mysql/frontend): " component
                        auto_recovery "$component" 1
                        ;;
                    0) exit 0 ;;
                    *) echo "无效选择" ;;
                esac
                
                read -p "按Enter继续..."
            done
            ;;
    esac
}

main "$@"