#!/bin/bash
# 将中文数据库名迁移到英文的自动化脚本

set -e

echo "=== 开始数据库名称迁移 ==="
echo "从 'overdue_debt_db' 迁移到 'overdue_debt_db'"
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 项目路径
PROJECT_DIR="/opt/FinancialSystem/current"
BACKUP_DIR="${PROJECT_DIR}/backup/db-migration-$(date +%Y%m%d_%H%M%S)"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

echo -e "${YELLOW}步骤 1: 备份当前配置文件${NC}"
cd "$PROJECT_DIR"

# 备份所有配置文件
find . -name "*.yml" -o -name "*.yaml" -o -name "*.properties" | while read file; do
    if grep -q "overdue_debt_db" "$file" 2>/dev/null; then
        cp "$file" "$BACKUP_DIR/$(basename $file).backup"
        echo "备份: $file"
    fi
done

echo -e "${YELLOW}步骤 2: 数据库数据迁移${NC}"

# 检查MySQL容器是否运行
if docker ps | grep -q financial-mysql; then
    echo "MySQL容器运行中，开始数据迁移..."
    
    # 1. 创建英文数据库
    docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e "
    CREATE DATABASE IF NOT EXISTS overdue_debt_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    "
    
    # 2. 导出中文数据库
    echo "导出中文数据库数据..."
    docker exec financial-mysql mysqldump -uroot -p'Zlb&198838' --single-transaction --routines --triggers 'overdue_debt_db' > "$BACKUP_DIR/chinese_db_backup.sql"
    
    # 3. 导入到英文数据库
    echo "导入数据到英文数据库..."
    docker exec -i financial-mysql mysql -uroot -p'Zlb&198838' overdue_debt_db < "$BACKUP_DIR/chinese_db_backup.sql"
    
    # 4. 验证数据
    echo "验证数据迁移..."
    CHINESE_TABLES=$(docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e "SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA='overdue_debt_db';" -s -N)
    ENGLISH_TABLES=$(docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e "SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA='overdue_debt_db';" -s -N)
    
    echo "中文数据库表数量: $CHINESE_TABLES"
    echo "英文数据库表数量: $ENGLISH_TABLES"
    
    if [ "$CHINESE_TABLES" -eq "$ENGLISH_TABLES" ]; then
        echo -e "${GREEN}✓ 数据迁移验证成功${NC}"
    else
        echo -e "${RED}✗ 数据迁移验证失败，表数量不匹配${NC}"
        exit 1
    fi
fi

echo -e "${YELLOW}步骤 3: 批量修改配置文件${NC}"

# 修改所有yml配置文件
find . -name "*.yml" -o -name "*.yaml" | while read file; do
    if grep -q "overdue_debt_db" "$file" 2>/dev/null; then
        echo "修改配置文件: $file"
        sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' "$file"
        # URL编码的中文数据库名也要替换
        sed -i 's/%E9%80%BE%E6%9C%9F%E5%80%BA%E6%9D%83%E6%95%B0%E6%8D%AE%E5%BA%93/overdue_debt_db/g' "$file"
    fi
done

echo -e "${YELLOW}步骤 4: 批量修改Java源代码${NC}"

# 修改Java文件中的硬编码数据库名
find . -name "*.java" -type f | while read file; do
    if grep -q "overdue_debt_db" "$file" 2>/dev/null; then
        echo "修改Java文件: $file"
        sed -i.bak 's/overdue_debt_db/overdue_debt_db/g' "$file"
    fi
done

echo -e "${YELLOW}步骤 5: 重新编译后端项目${NC}"

# 重新编译
cd "$PROJECT_DIR"
mvn clean package -DskipTests

echo -e "${YELLOW}步骤 6: 更新Docker配置${NC}"

# 创建新的docker-compose配置
cat > docker-compose.prod.new.yml << 'EOF'
# FinancialSystem 生产环境 Docker Compose 配置
# 使用英文数据库名

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: financial-mysql
    pull_policy: never
    environment:
      MYSQL_ROOT_PASSWORD: Zlb&198838
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 后端运行服务
  backend:
    image: openjdk:21-jdk
    platform: linux/amd64
    container_name: financial-backend
    pull_policy: never
    ports:
      - "8080:8080"
    volumes:
      - ./api-gateway/target:/app
      - ./api-gateway/logs:/logs
    working_dir: /app
    command: >
      sh -c "
        echo '等待数据库完全启动...' &&
        sleep 30 &&
        echo '启动Spring Boot应用...' &&
        java -Dfile.encoding=UTF-8 -jar api-gateway-1.0-SNAPSHOT.jar --spring.profiles.active=production
      "
    environment:
      - SPRING_PROFILES_ACTIVE=production,docker
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
      - SPRING_DATASOURCE_PRIMARY_URL=***********************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_PRIMARY_USERNAME=root
      - SPRING_DATASOURCE_PRIMARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_SECONDARY_URL=***************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_SECONDARY_USERNAME=root
      - SPRING_DATASOURCE_SECONDARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_USER_SYSTEM_URL=*************************************************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USER_SYSTEM_USERNAME=root
      - SPRING_DATASOURCE_USER_SYSTEM_PASSWORD=Zlb&198838
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx Web服务器
  nginx:
    image: nginx:alpine
    platform: linux/amd64
    container_name: financial-nginx
    pull_policy: never
    ports:
      - "80:80"
    volumes:
      - ./FinancialSystem-web/build:/usr/share/nginx/html:ro
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      backend:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local

networks:
  default:
    name: financial-network
    driver: bridge
EOF

echo -e "${YELLOW}步骤 7: 重启服务${NC}"

# 停止当前服务
docker-compose -f docker-compose.prod.yml down

# 使用新配置启动
mv docker-compose.prod.yml docker-compose.prod.old.yml
mv docker-compose.prod.new.yml docker-compose.prod.yml
docker-compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

echo -e "${YELLOW}步骤 8: 验证服务${NC}"

# 检查后端健康状态
if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 后端服务启动成功${NC}"
    
    # 创建迁移成功标记
    cat > "$BACKUP_DIR/migration-success.txt" << EOF
迁移成功！
时间: $(date '+%Y-%m-%d %H:%M:%S')
原数据库: overdue_debt_db
新数据库: overdue_debt_db
备份位置: $BACKUP_DIR
EOF
    
else
    echo -e "${RED}✗ 后端服务启动失败${NC}"
    echo "正在回滚..."
    
    # 回滚配置
    mv docker-compose.prod.old.yml docker-compose.prod.yml
    docker-compose -f docker-compose.prod.yml up -d
    
    echo "查看错误日志："
    docker logs financial-backend --tail 50
    exit 1
fi

echo -e "${GREEN}=== 数据库名称迁移完成 ===${NC}"
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "重要提示："
echo "1. 原中文数据库 'overdue_debt_db' 已保留作为备份"
echo "2. 新数据库 'overdue_debt_db' 现在是主数据库"
echo "3. 所有备份文件保存在: $BACKUP_DIR"
echo "4. 如需回滚，请使用: $BACKUP_DIR 中的备份文件"