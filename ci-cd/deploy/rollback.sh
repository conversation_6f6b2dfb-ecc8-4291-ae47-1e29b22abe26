#!/bin/bash
# 回滚脚本 - 恢复到指定备份版本

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
DEPLOY_DIR="/opt/FinancialSystem"

# 检查参数
if [ $# -eq 0 ]; then
    error "请指定要回滚的备份名称"
    echo "用法: $0 <backup-name>"
    echo ""
    echo "可用的备份:"
    ls -la $DEPLOY_DIR/backups/ 2>/dev/null | grep backup- || echo "没有找到备份"
    exit 1
fi

BACKUP_NAME=$1
BACKUP_FILE="$DEPLOY_DIR/backups/$BACKUP_NAME.tar.gz"

# 检查备份文件
if [ ! -f "$BACKUP_FILE" ]; then
    error "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

log "🔄 开始回滚到备份: $BACKUP_NAME"

# 停止当前服务
log "停止当前服务..."
cd $DEPLOY_DIR/current
docker compose down || true

# 备份当前版本（以防回滚失败）
log "备份当前版本..."
CURRENT_BACKUP="rollback-backup-$(date +%Y%m%d-%H%M%S)"
cd $DEPLOY_DIR
tar -czf backups/$CURRENT_BACKUP.tar.gz current/ || true

# 清理当前目录
log "清理当前目录..."
rm -rf current/*

# 恢复备份
log "恢复备份..."
cd $DEPLOY_DIR
tar -xzf "$BACKUP_FILE"

# 启动服务
log "启动服务..."
cd current
docker compose up -d

# 等待服务启动
log "等待服务启动..."
sleep 30

# 检查服务状态
log "检查服务状态..."
docker compose ps

# 健康检查
log "执行健康检查..."
if curl -sf http://localhost:8080/actuator/health >/dev/null 2>&1; then
    success "后端服务正常"
else
    error "后端服务健康检查失败"
fi

if curl -sf http://localhost/ >/dev/null 2>&1; then
    success "前端服务正常"
else
    error "前端服务检查失败"
fi

success "🎉 回滚完成！"
log "当前版本已备份到: $DEPLOY_DIR/backups/$CURRENT_BACKUP.tar.gz"