#!/bin/bash

# Docker本地镜像管理脚本
# 用于优化本地镜像的构建、缓存和管理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
PROJECT_ROOT="/opt/FinancialSystem/current"
REQUIRED_IMAGES=(
    "mysql:8.0"
    "openjdk:21-jdk"
    "maven:3.9-eclipse-temurin-21"
    "node:18-alpine"
    "nginx:alpine"
)

# 本地镜像仓库配置（如果有）
LOCAL_REGISTRY="localhost:5000"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查本地镜像
check_local_images() {
    log_info "检查本地Docker镜像..."
    
    local missing_images=()
    
    for image in "${REQUIRED_IMAGES[@]}"; do
        if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
            log_success "✓ 镜像已存在: $image"
        else
            log_warning "✗ 镜像缺失: $image"
            missing_images+=("$image")
        fi
    done
    
    if [ ${#missing_images[@]} -eq 0 ]; then
        log_success "所有必需的镜像都已存在"
        return 0
    else
        log_warning "缺失 ${#missing_images[@]} 个镜像"
        echo "${missing_images[@]}"
        return 1
    fi
}

# 预拉取镜像
prefetch_images() {
    log_info "预拉取必需的Docker镜像..."
    
    local failed_images=()
    
    for image in "${REQUIRED_IMAGES[@]}"; do
        log_info "拉取镜像: $image"
        
        # 首先尝试从本地仓库拉取（如果配置了）
        if [ -n "$LOCAL_REGISTRY" ]; then
            local local_image="$LOCAL_REGISTRY/$image"
            if docker pull "$local_image" 2>/dev/null; then
                docker tag "$local_image" "$image"
                log_success "从本地仓库拉取成功: $image"
                continue
            fi
        fi
        
        # 从Docker Hub拉取
        if docker pull "$image"; then
            log_success "拉取成功: $image"
            
            # 推送到本地仓库（如果配置了）
            if [ -n "$LOCAL_REGISTRY" ]; then
                docker tag "$image" "$LOCAL_REGISTRY/$image"
                docker push "$LOCAL_REGISTRY/$image" 2>/dev/null || true
            fi
        else
            log_error "拉取失败: $image"
            failed_images+=("$image")
        fi
    done
    
    if [ ${#failed_images[@]} -eq 0 ]; then
        log_success "所有镜像拉取成功"
        return 0
    else
        log_error "有 ${#failed_images[@]} 个镜像拉取失败"
        return 1
    fi
}

# 构建项目镜像
build_project_images() {
    log_info "构建项目自定义镜像..."
    
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # 构建后端镜像
    if [ -f "Dockerfile" ]; then
        log_info "构建后端Docker镜像..."
        
        # 使用构建缓存
        if docker build \
            --cache-from financial-backend:latest \
            -t financial-backend:latest \
            -t financial-backend:$(date +%Y%m%d-%H%M%S) \
            .; then
            log_success "后端镜像构建成功"
        else
            log_error "后端镜像构建失败"
            return 1
        fi
    fi
    
    # 构建前端镜像
    if [ -f "FinancialSystem-web/Dockerfile" ]; then
        log_info "构建前端Docker镜像..."
        
        cd FinancialSystem-web
        if docker build \
            --cache-from financial-frontend:latest \
            -t financial-frontend:latest \
            -t financial-frontend:$(date +%Y%m%d-%H%M%S) \
            .; then
            log_success "前端镜像构建成功"
        else
            log_error "前端镜像构建失败"
            return 1
        fi
        cd ..
    fi
    
    return 0
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧的Docker镜像..."
    
    # 保留最新的3个版本
    local keep_versions=3
    
    # 清理financial-backend旧版本
    local backend_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "^financial-backend:" | grep -v ":latest" | sort -r)
    local backend_count=$(echo "$backend_images" | wc -l)
    
    if [ $backend_count -gt $keep_versions ]; then
        echo "$backend_images" | tail -n +$((keep_versions + 1)) | while read image; do
            log_info "删除旧镜像: $image"
            docker rmi "$image" 2>/dev/null || true
        done
    fi
    
    # 清理financial-frontend旧版本
    local frontend_images=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep "^financial-frontend:" | grep -v ":latest" | sort -r)
    local frontend_count=$(echo "$frontend_images" | wc -l)
    
    if [ $frontend_count -gt $keep_versions ]; then
        echo "$frontend_images" | tail -n +$((keep_versions + 1)) | while read image; do
            log_info "删除旧镜像: $image"
            docker rmi "$image" 2>/dev/null || true
        done
    fi
    
    # 清理悬空镜像
    log_info "清理悬空镜像..."
    docker image prune -f
    
    # 显示磁盘使用情况
    log_info "Docker磁盘使用情况:"
    docker system df
}

# 导出镜像用于离线部署
export_images() {
    local export_dir="/tmp/docker-images-export"
    mkdir -p "$export_dir"
    
    log_info "导出Docker镜像到: $export_dir"
    
    for image in "${REQUIRED_IMAGES[@]}"; do
        local filename=$(echo "$image" | sed 's/[:/]/-/g').tar
        log_info "导出镜像: $image -> $filename"
        docker save "$image" -o "$export_dir/$filename"
    done
    
    # 创建导入脚本
    cat > "$export_dir/import-images.sh" << 'EOF'
#!/bin/bash
# 导入Docker镜像脚本
echo "开始导入Docker镜像..."
for file in *.tar; do
    echo "导入: $file"
    docker load -i "$file"
done
echo "导入完成"
EOF
    
    chmod +x "$export_dir/import-images.sh"
    
    # 打包所有镜像
    cd /tmp
    tar -czf docker-images-$(date +%Y%m%d-%H%M%S).tar.gz docker-images-export/
    
    log_success "镜像导出完成: /tmp/docker-images-$(date +%Y%m%d-%H%M%S).tar.gz"
}

# 设置本地镜像仓库
setup_local_registry() {
    log_info "设置本地Docker镜像仓库..."
    
    # 检查是否已有registry容器
    if docker ps -a | grep -q "local-registry"; then
        log_info "本地仓库已存在"
        docker start local-registry 2>/dev/null || true
    else
        log_info "创建本地Docker仓库..."
        docker run -d \
            --name local-registry \
            --restart=always \
            -p 5000:5000 \
            -v /opt/docker-registry:/var/lib/registry \
            registry:2
    fi
    
    # 等待registry启动
    sleep 5
    
    # 测试registry
    if curl -s http://localhost:5000/v2/_catalog > /dev/null; then
        log_success "本地仓库运行正常"
    else
        log_error "本地仓库启动失败"
        return 1
    fi
}

# 优化Docker构建
optimize_docker_build() {
    log_info "优化Docker构建配置..."
    
    # 启用BuildKit
    export DOCKER_BUILDKIT=1
    
    # 配置构建缓存
    if [ ! -d "/opt/docker-build-cache" ]; then
        mkdir -p /opt/docker-build-cache
    fi
    
    # 创建优化的docker-compose配置
    if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        log_info "优化docker-compose配置..."
        
        # 备份原配置
        cp "$PROJECT_ROOT/docker-compose.yml" "$PROJECT_ROOT/docker-compose.yml.bak"
        
        # 添加构建优化配置
        cat > "$PROJECT_ROOT/docker-compose.override.yml" << 'EOF'
version: '3.8'

x-build-args: &build-args
  BUILDKIT_INLINE_CACHE: 1

services:
  backend-builder:
    build:
      args:
        <<: *build-args
      cache_from:
        - financial-backend:latest
    
  frontend-builder:
    build:
      args:
        <<: *build-args
      cache_from:
        - financial-frontend:latest
EOF
        
        log_success "Docker构建优化配置完成"
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo "========== Docker本地镜像管理 =========="
    echo "1. 检查本地镜像"
    echo "2. 预拉取必需镜像"
    echo "3. 构建项目镜像"
    echo "4. 清理旧镜像"
    echo "5. 导出镜像（离线部署）"
    echo "6. 设置本地镜像仓库"
    echo "7. 优化Docker构建"
    echo "8. 执行完整优化流程"
    echo "0. 退出"
    echo "========================================"
}

# 执行完整优化流程
run_full_optimization() {
    log_info "开始执行完整的Docker优化流程..."
    
    # 1. 检查并拉取镜像
    if ! check_local_images; then
        prefetch_images
    fi
    
    # 2. 优化构建配置
    optimize_docker_build
    
    # 3. 构建项目镜像
    build_project_images
    
    # 4. 清理旧镜像
    cleanup_old_images
    
    log_success "Docker优化流程完成！"
}

# 主函数
main() {
    # 如果提供了参数，直接执行对应功能
    case "$1" in
        check)
            check_local_images
            ;;
        prefetch)
            prefetch_images
            ;;
        build)
            build_project_images
            ;;
        cleanup)
            cleanup_old_images
            ;;
        export)
            export_images
            ;;
        registry)
            setup_local_registry
            ;;
        optimize)
            optimize_docker_build
            ;;
        full)
            run_full_optimization
            ;;
        *)
            # 交互式菜单
            while true; do
                show_menu
                read -p "请选择操作: " choice
                
                case $choice in
                    1) check_local_images ;;
                    2) prefetch_images ;;
                    3) build_project_images ;;
                    4) cleanup_old_images ;;
                    5) export_images ;;
                    6) setup_local_registry ;;
                    7) optimize_docker_build ;;
                    8) run_full_optimization ;;
                    0) exit 0 ;;
                    *) echo "无效选择" ;;
                esac
                
                read -p "按Enter继续..."
            done
            ;;
    esac
}

# 确保以root权限运行某些操作
if [ "$EUID" -ne 0 ] && [[ "$1" =~ ^(registry|optimize|full)$ ]]; then
    log_warning "某些操作需要root权限，尝试使用sudo..."
    exec sudo "$0" "$@"
fi

main "$@"