#!/bin/bash

# Docker镜像预缓存脚本
# 确保所有必要的Docker镜像在本地可用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 必要的Docker镜像列表
REQUIRED_IMAGES=(
    "mysql:8.0"
    "openjdk:21-jdk"
    "maven:3.9-eclipse-temurin-21"
    "node:18-alpine"
    "nginx:alpine"
)

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker服务状态..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        exit 1
    fi
    
    log_success "Docker服务正常"
}

# 检查镜像是否存在
check_image_exists() {
    local image="$1"
    docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$image$"
}

# 拉取镜像
pull_image() {
    local image="$1"
    log_info "拉取镜像: $image"
    
    if docker pull "$image"; then
        log_success "镜像拉取成功: $image"
        return 0
    else
        log_error "镜像拉取失败: $image"
        return 1
    fi
}

# 并行拉取镜像
pull_images_parallel() {
    local missing_images=("$@")
    
    if [ ${#missing_images[@]} -eq 0 ]; then
        log_info "所有镜像已存在，无需拉取"
        return 0
    fi
    
    log_info "开始并行拉取 ${#missing_images[@]} 个镜像..."
    
    # 启动后台拉取进程
    pids=()
    for image in "${missing_images[@]}"; do
        (pull_image "$image") &
        pids+=($!)
    done
    
    # 等待所有拉取完成
    success_count=0
    for i in "${!pids[@]}"; do
        if wait "${pids[$i]}"; then
            ((success_count++))
        fi
    done
    
    log_info "并行拉取完成: $success_count/${#missing_images[@]} 成功"
    
    if [ $success_count -eq ${#missing_images[@]} ]; then
        log_success "所有镜像拉取成功"
        return 0
    else
        log_warning "部分镜像拉取失败"
        return 1
    fi
}

# 验证镜像完整性
verify_images() {
    log_info "验证镜像完整性..."
    
    for image in "${REQUIRED_IMAGES[@]}"; do
        if check_image_exists "$image"; then
            # 尝试运行容器测试镜像
            if docker run --rm "$image" echo "Image test OK" &> /dev/null; then
                log_success "镜像验证通过: $image"
            else
                log_warning "镜像可能损坏: $image"
            fi
        else
            log_error "镜像不存在: $image"
        fi
    done
}

# 清理悬挂镜像
cleanup_dangling_images() {
    log_info "清理悬挂镜像..."
    
    dangling_images=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling_images" ]; then
        docker rmi $dangling_images || log_warning "清理悬挂镜像时出现问题"
        log_success "悬挂镜像清理完成"
    else
        log_info "无悬挂镜像需要清理"
    fi
}

# 主函数
main() {
    log_info "🐳 开始Docker镜像预缓存..."
    
    # 检查Docker
    check_docker
    
    # 检查现有镜像
    log_info "检查现有镜像..."
    missing_images=()
    
    for image in "${REQUIRED_IMAGES[@]}"; do
        if check_image_exists "$image"; then
            log_success "镜像已存在: $image"
        else
            log_warning "镜像缺失: $image"
            missing_images+=("$image")
        fi
    done
    
    # 拉取缺失的镜像
    if [ ${#missing_images[@]} -gt 0 ]; then
        pull_images_parallel "${missing_images[@]}"
    fi
    
    # 验证镜像
    verify_images
    
    # 清理悬挂镜像
    cleanup_dangling_images
    
    # 显示镜像统计
    log_info "📊 Docker镜像统计:"
    echo "总镜像数: $(docker images | wc -l)"
    echo "所需镜像:"
    for image in "${REQUIRED_IMAGES[@]}"; do
        if check_image_exists "$image"; then
            echo "  ✅ $image"
        else
            echo "  ❌ $image"
        fi
    done
    
    log_success "🎉 Docker镜像预缓存完成!"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi