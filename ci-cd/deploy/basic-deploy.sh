#!/bin/bash

# FinancialSystem 基础部署脚本
# 仅部署后端JAR包，不依赖复杂的Docker镜像

set -e

# 配置变量
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
LINUX_SERVER="admin@10.25.1.85"
DEPLOY_DIR="/opt/FinancialSystem"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 本地构建项目
build_project() {
    log_info "📦 本地构建项目..."
    cd "$PROJECT_ROOT"
    
    # 构建后端
    if mvn clean package -DskipTests -pl api-gateway -am; then
        log_success "后端构建成功"
    else
        log_error "后端构建失败"
        return 1
    fi
    
    # 检查JAR文件是否存在
    if [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
        log_success "JAR文件已生成: api-gateway/target/api-gateway-1.0-SNAPSHOT.jar"
    else
        log_error "JAR文件未找到"
        return 1
    fi
    
    return 0
}

# 部署到服务器
deploy_to_server() {
    log_info "🚀 部署到服务器..."
    
    # 1. 创建部署包
    log_info "📦 创建部署包..."
    DEPLOY_PACKAGE="/tmp/financial-basic-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    tar --exclude='.git' \
        --exclude='node_modules' \
        --exclude='FinancialSystem-web/node_modules' \
        --exclude='FinancialSystem-web/build' \
        --exclude='*.log' \
        --exclude='.DS_Store' \
        -czf "$DEPLOY_PACKAGE" . || {
        log_error "创建部署包失败"
        return 1
    }
    
    log_success "部署包创建成功: $DEPLOY_PACKAGE"
    
    # 2. 传输到服务器
    log_info "📤 传输到服务器..."
    scp "$DEPLOY_PACKAGE" "$LINUX_SERVER:/tmp/" || {
        log_error "传输失败"
        return 1
    }
    
    # 3. 在服务器上部署
    log_info "🚀 在服务器上部署..."
    ssh "$LINUX_SERVER" bash -s "$DEPLOY_PACKAGE" << 'EOF'
        set -e
        
        DEPLOY_PACKAGE="$1"
        DEPLOY_DIR="/opt/FinancialSystem"
        CURRENT_DIR="$DEPLOY_DIR/current"
        BACKUP_DIR="$DEPLOY_DIR/backup-$(date +%Y%m%d-%H%M%S)"
        
        echo "🔧 准备部署环境..."
        
        # 创建必要目录
        mkdir -p "$DEPLOY_DIR"
        
        # 停止现有服务
        echo "🛑 停止现有服务..."
        pkill -f "api-gateway.*jar" || true
        pkill -f "java.*jar" || true
        
        # 停止Docker服务（如果有）
        echo "🐳 停止Docker服务..."
        if [ -d "$CURRENT_DIR" ] && [ -f "$CURRENT_DIR/docker-compose.yml" ]; then
            cd "$CURRENT_DIR"
            docker compose down || true
        fi
        
        # 清理端口占用
        echo "🔧 清理端口占用..."
        for port in 8080 3306 80; do
            pid=$(lsof -ti:$port 2>/dev/null || true)
            if [ -n "$pid" ]; then
                echo "⚠️  杀死占用端口 $port 的进程 $pid"
                kill -9 $pid 2>/dev/null || true
            fi
        done
        
        # 等待端口释放
        sleep 5
        
        # 备份现有部署
        if [ -d "$CURRENT_DIR" ]; then
            echo "📦 备份现有部署..."
            mv "$CURRENT_DIR" "$BACKUP_DIR"
        fi
        
        # 创建新的部署目录
        mkdir -p "$CURRENT_DIR"
        
        # 解压部署包
        echo "📦 解压部署包..."
        tar -xzf "/tmp/$(basename $DEPLOY_PACKAGE)" -C "$CURRENT_DIR" --strip-components=0
        
        # 进入部署目录
        cd "$CURRENT_DIR"
        
        # 设置执行权限
        find . -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
        
        # 检查JAR文件
        if [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
            echo "✅ JAR文件存在，准备启动后端服务"
            
            # 启动后端服务
            echo "🚀 启动后端服务..."
            cd api-gateway/target
            nohup java -jar api-gateway-1.0-SNAPSHOT.jar \
                --spring.profiles.active=production \
                --spring.datasource.primary.url="***********************************************************************************************************************************************************************" \
                --spring.datasource.secondary.url="*************************************************************************************************************************" \
                --spring.datasource.user-system.url="***********************************************************************************************************************************************************" \
                > ../financial-system.log 2>&1 &
            
            echo "🔍 后端服务启动中，PID: $!"
            echo "📄 日志文件: $CURRENT_DIR/api-gateway/financial-system.log"
            
        else
            echo "❌ JAR文件不存在，无法启动后端服务"
            exit 1
        fi
        
        echo "✅ 基础部署完成"
EOF
    
    local deploy_result=$?
    
    # 清理临时文件
    rm -f "$DEPLOY_PACKAGE"
    
    if [ $deploy_result -eq 0 ]; then
        log_success "✅ 部署成功完成"
        return 0
    else
        log_error "❌ 部署失败"
        return 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "🔍 验证部署..."
    
    # 等待服务启动
    log_info "⏳ 等待服务启动..."
    sleep 30
    
    # 检查服务状态
    ssh "$LINUX_SERVER" << 'EOF'
        echo "📊 检查服务进程..."
        ps aux | grep -v grep | grep "api-gateway.*jar" || echo "❌ 后端服务进程未找到"
        
        echo "📄 检查日志文件..."
        if [ -f "/opt/FinancialSystem/current/api-gateway/financial-system.log" ]; then
            echo "最近的日志："
            tail -20 /opt/FinancialSystem/current/api-gateway/financial-system.log
        fi
EOF
    
    # 健康检查
    local max_attempts=10
    
    for attempt in $(seq 1 $max_attempts); do
        log_info "健康检查尝试 $attempt/$max_attempts..."
        
        # 检查后端
        if curl -sf "http://10.25.1.85:8080/actuator/health" > /dev/null; then
            log_success "✅ 后端服务健康检查通过"
            return 0
        else
            log_warning "⚠️  后端服务健康检查失败"
        fi
        
        if [ $attempt -lt $max_attempts ]; then
            log_info "等待10秒后重试..."
            sleep 10
        fi
    done
    
    log_warning "⚠️  服务健康检查未通过，但部署可能仍在进行中"
    return 1
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem基础部署..."
    
    # 检查依赖
    for cmd in mvn curl ssh scp tar; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要的命令: $cmd"
            exit 1
        fi
    done
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 构建项目
    if ! build_project; then
        log_error "项目构建失败"
        exit 1
    fi
    
    # 执行部署
    if deploy_to_server; then
        log_success "🎉 基础部署成功完成！"
        
        # 验证部署
        if verify_deployment; then
            log_success "🎉 部署验证成功！"
        else
            log_warning "⚠️  部署验证未完全通过，请检查服务状态"
        fi
        
        # 显示访问信息
        log_info "🌐 访问地址:"
        log_info "   - 后端API: http://10.25.1.85:8080/"
        log_info "   - 健康检查: http://10.25.1.85:8080/actuator/health"
        log_info "   - 日志查看: ssh admin@10.25.1.85 'tail -f /opt/FinancialSystem/current/api-gateway/financial-system.log'"
        
        exit 0
    else
        log_error "❌ 基础部署失败"
        exit 1
    fi
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi