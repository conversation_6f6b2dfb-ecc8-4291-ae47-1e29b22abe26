#!/bin/bash

# FinancialSystem Docker镜像准备脚本
# 在网络良好的情况下预先准备所需的Docker镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 需要的镜像列表
IMAGES=(
    "mysql:8.0"
    "maven:3.9-eclipse-temurin-21"
    "openjdk:21-jdk"
    "node:18-alpine"
    "nginx:alpine"
)

# 检查Docker是否运行
check_docker() {
    if ! docker info &>/dev/null; then
        log_error "Docker未运行或无法连接"
        exit 1
    fi
}

# 拉取镜像
pull_image() {
    local image="$1"
    log_info "拉取镜像: $image"
    
    if docker pull "$image"; then
        log_success "镜像拉取成功: $image"
        return 0
    else
        log_error "镜像拉取失败: $image"
        return 1
    fi
}

# 检查镜像是否存在
check_image() {
    local image="$1"
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
        log_success "镜像已存在: $image"
        return 0
    else
        log_warning "镜像不存在: $image"
        return 1
    fi
}

# 导出镜像
export_images() {
    local export_dir="$1"
    mkdir -p "$export_dir"
    
    log_info "导出镜像到: $export_dir"
    
    for image in "${IMAGES[@]}"; do
        if check_image "$image"; then
            local filename=$(echo "$image" | tr '/:' '_')
            local export_path="$export_dir/${filename}.tar"
            
            log_info "导出镜像: $image -> $export_path"
            if docker save "$image" -o "$export_path"; then
                log_success "镜像导出成功: $export_path"
            else
                log_error "镜像导出失败: $image"
            fi
        fi
    done
}

# 导入镜像
import_images() {
    local import_dir="$1"
    
    if [ ! -d "$import_dir" ]; then
        log_error "导入目录不存在: $import_dir"
        return 1
    fi
    
    log_info "从目录导入镜像: $import_dir"
    
    for tar_file in "$import_dir"/*.tar; do
        if [ -f "$tar_file" ]; then
            log_info "导入镜像: $tar_file"
            if docker load -i "$tar_file"; then
                log_success "镜像导入成功: $tar_file"
            else
                log_error "镜像导入失败: $tar_file"
            fi
        fi
    done
}

# 主函数
main() {
    local command="$1"
    local target_dir="$2"
    
    check_docker
    
    case "$command" in
        "pull")
            log_info "开始拉取所有必要的Docker镜像..."
            
            local success_count=0
            local total_count=${#IMAGES[@]}
            
            for image in "${IMAGES[@]}"; do
                if check_image "$image"; then
                    log_info "跳过已存在的镜像: $image"
                    success_count=$((success_count + 1))
                elif pull_image "$image"; then
                    success_count=$((success_count + 1))
                fi
            done
            
            log_info "镜像拉取完成: $success_count/$total_count"
            
            if [ $success_count -eq $total_count ]; then
                log_success "所有镜像准备完成"
            else
                log_warning "部分镜像准备失败"
            fi
            ;;
            
        "export")
            if [ -z "$target_dir" ]; then
                target_dir="./docker-images"
            fi
            
            log_info "导出Docker镜像到: $target_dir"
            export_images "$target_dir"
            ;;
            
        "import")
            if [ -z "$target_dir" ]; then
                target_dir="./docker-images"
            fi
            
            log_info "导入Docker镜像从: $target_dir"
            import_images "$target_dir"
            ;;
            
        "check")
            log_info "检查Docker镜像状态..."
            
            local missing_count=0
            for image in "${IMAGES[@]}"; do
                if ! check_image "$image"; then
                    missing_count=$((missing_count + 1))
                fi
            done
            
            if [ $missing_count -eq 0 ]; then
                log_success "所有必要的镜像都已准备好"
            else
                log_warning "有 $missing_count 个镜像缺失"
                log_info "运行 '$0 pull' 来拉取缺失的镜像"
            fi
            ;;
            
        *)
            echo "用法: $0 {pull|export|import|check} [目录]"
            echo ""
            echo "命令说明:"
            echo "  pull   - 拉取所有必要的Docker镜像"
            echo "  export - 导出镜像到指定目录（默认: ./docker-images）"
            echo "  import - 从指定目录导入镜像（默认: ./docker-images）"
            echo "  check  - 检查所有必要镜像是否存在"
            echo ""
            echo "示例:"
            echo "  $0 pull                    # 拉取所有镜像"
            echo "  $0 export /tmp/images      # 导出镜像到 /tmp/images"
            echo "  $0 import /tmp/images      # 从 /tmp/images 导入镜像"
            echo "  $0 check                   # 检查镜像状态"
            exit 1
            ;;
    esac
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi