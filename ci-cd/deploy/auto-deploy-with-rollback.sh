#!/bin/bash

# FinancialSystem 增强版自动部署脚本 - 支持失败自动回滚
# 当合并到main分支时自动触发，失败时自动回滚到上一个稳定版本

set -e

# 配置变量
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
BACKUP_SCRIPT="$(dirname "$0")/../backup/auto-backup.sh"
LINUX_SERVER="admin@**********"
LINUX_DEPLOY_PATH="/home/<USER>/下载/FinancialSystem-Production-Deploy"
WEBHOOK_URL="http://**********:9000/webhook"
NOTIFICATION_EMAIL="<EMAIL>"
DEPLOY_HISTORY_FILE="/tmp/financial-deploy-history.json"
MAX_DEPLOY_ATTEMPTS=3

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_deploy() {
    echo -e "${PURPLE}[DEPLOY]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_rollback() {
    echo -e "${CYAN}[ROLLBACK]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 部署历史管理
save_deploy_history() {
    local commit_hash="$1"
    local status="$2"
    local backup_dir="$3"
    local deploy_time="$(date -Iseconds)"
    
    # 创建历史记录
    local history_entry="{
        \"commit\": \"$commit_hash\",
        \"status\": \"$status\",
        \"backup_dir\": \"$backup_dir\",
        \"deploy_time\": \"$deploy_time\"
    }"
    
    # 保存到历史文件
    if [ ! -f "$DEPLOY_HISTORY_FILE" ]; then
        echo "[]" > "$DEPLOY_HISTORY_FILE"
    fi
    
    # 添加新记录
    jq ". + [$history_entry]" "$DEPLOY_HISTORY_FILE" > "${DEPLOY_HISTORY_FILE}.tmp" && \
        mv "${DEPLOY_HISTORY_FILE}.tmp" "$DEPLOY_HISTORY_FILE"
}

# 获取最后一个成功的部署
get_last_successful_deploy() {
    if [ -f "$DEPLOY_HISTORY_FILE" ]; then
        jq -r '.[] | select(.status == "success") | .backup_dir' "$DEPLOY_HISTORY_FILE" | tail -1
    fi
}

# 获取部署锁
acquire_deploy_lock() {
    local lock_file="/tmp/financial-deploy.lock"
    local timeout=300  # 5分钟超时
    local elapsed=0
    
    while [ -f "$lock_file" ]; do
        local lock_pid=$(cat "$lock_file" 2>/dev/null || echo "unknown")
        log_warning "另一个部署正在进行中 (PID: $lock_pid)，等待..."
        
        if [ $elapsed -ge $timeout ]; then
            log_error "获取部署锁超时"
            return 1
        fi
        
        sleep 5
        elapsed=$((elapsed + 5))
    done
    
    echo $$ > "$lock_file"
    return 0
}

# 释放部署锁
release_deploy_lock() {
    rm -f "/tmp/financial-deploy.lock"
}

# 健康检查函数
health_check() {
    local max_attempts=30
    local attempt=1
    
    log_info "执行健康检查..."
    
    while [ $attempt -le $max_attempts ]; do
        # 检查后端健康
        if curl -sf "http://${LINUX_SERVER#*@}:8080/actuator/health" > /dev/null; then
            log_success "后端健康检查通过"
            
            # 检查前端
            if curl -sf "http://${LINUX_SERVER#*@}/" > /dev/null; then
                log_success "前端健康检查通过"
                return 0
            fi
        fi
        
        log_info "健康检查尝试 $attempt/$max_attempts..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    log_error "健康检查失败"
    return 1
}

# 自动回滚函数
auto_rollback() {
    local reason="$1"
    log_rollback "开始自动回滚，原因: $reason"
    
    # 获取最后一个成功的部署备份
    local last_backup=$(get_last_successful_deploy)
    
    if [ -z "$last_backup" ]; then
        log_error "没有找到可用的备份进行回滚"
        return 1
    fi
    
    log_rollback "回滚到备份: $last_backup"
    
    # 在Linux服务器上执行回滚
    ssh "$LINUX_SERVER" bash -s "$last_backup" << 'EOF'
        set -e
        
        BACKUP_DIR="$1"
        DEPLOY_DIR="/opt/FinancialSystem"
        CURRENT_DIR="$DEPLOY_DIR/current"
        
        echo "🔄 开始回滚到: $BACKUP_DIR"
        
        # 停止当前服务
        echo "🛑 停止当前服务..."
        pkill -f "api-gateway.*jar" || true
        
        if [ -d "$CURRENT_DIR" ]; then
            cd "$CURRENT_DIR"
            if [ -f "docker-compose.yml" ]; then
                docker compose down || true
            fi
        fi
        
        # 备份失败的部署
        if [ -d "$CURRENT_DIR" ]; then
            FAILED_BACKUP="$DEPLOY_DIR/failed-$(date +%Y%m%d-%H%M%S)"
            mv "$CURRENT_DIR" "$FAILED_BACKUP"
            echo "❌ 失败的部署已备份到: $FAILED_BACKUP"
        fi
        
        # 恢复备份
        if [ -d "$BACKUP_DIR" ]; then
            cp -r "$BACKUP_DIR" "$CURRENT_DIR"
            cd "$CURRENT_DIR"
            
            # 重启服务
            echo "🚀 重启服务..."
            if [ -f "docker-compose.yml" ]; then
                # Docker部署
                docker compose up -d
            elif [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
                # JAR部署
                nohup java -jar api-gateway/target/api-gateway-1.0-SNAPSHOT.jar > financial-system.log 2>&1 &
            fi
            
            echo "✅ 回滚完成"
        else
            echo "❌ 备份目录不存在: $BACKUP_DIR"
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        log_success "回滚成功完成"
        
        # 验证回滚后的服务
        if health_check; then
            log_success "回滚后服务健康检查通过"
            return 0
        else
            log_error "回滚后服务健康检查失败"
            return 1
        fi
    else
        log_error "回滚失败"
        return 1
    fi
}

# 增强的部署验证
enhanced_verify_deployment() {
    log_deploy "执行增强部署验证..."
    
    # 基础健康检查
    if ! health_check; then
        return 1
    fi
    
    # 功能测试
    log_info "执行功能测试..."
    
    # 测试登录接口
    local login_test=$(curl -sf -X POST "http://${LINUX_SERVER#*@}:8080/api/login" \
        -H "Content-Type: application/json" \
        -d '{"username":"test","password":"test"}' \
        -w "%{http_code}" -o /dev/null || echo "000")
    
    if [ "$login_test" != "000" ]; then
        log_success "API功能测试通过 (HTTP $login_test)"
    else
        log_error "API功能测试失败"
        return 1
    fi
    
    # 数据库连接测试
    ssh "$LINUX_SERVER" << 'EOF'
        echo "🗄️  测试数据库连接..."
        
        # 检查MySQL容器
        if docker ps | grep -q mysql; then
            if docker exec $(docker ps -q -f name=mysql) \
                mysql -uroot -p'Zlb&198838' -e "SELECT 1" &>/dev/null; then
                echo "✅ 数据库连接正常"
            else
                echo "❌ 数据库连接失败"
                exit 1
            fi
        fi
EOF
    
    if [ $? -eq 0 ]; then
        log_success "所有验证测试通过"
        return 0
    else
        log_error "验证测试失败"
        return 1
    fi
}

# 部署监控和自动修复
deploy_monitor_and_fix() {
    local deploy_attempt=1
    local deploy_success=false
    
    while [ $deploy_attempt -le $MAX_DEPLOY_ATTEMPTS ] && [ "$deploy_success" = "false" ]; do
        log_deploy "部署尝试 $deploy_attempt/$MAX_DEPLOY_ATTEMPTS"
        
        # 执行部署
        if deploy_with_monitoring; then
            # 验证部署
            if enhanced_verify_deployment; then
                deploy_success=true
                save_deploy_history "$CURRENT_COMMIT" "success" "$CURRENT_BACKUP_DIR"
                log_success "部署成功！"
            else
                log_error "部署验证失败，尝试修复..."
                
                # 尝试自动修复常见问题
                if [ $deploy_attempt -lt $MAX_DEPLOY_ATTEMPTS ]; then
                    auto_fix_common_issues
                fi
            fi
        else
            log_error "部署执行失败"
        fi
        
        if [ "$deploy_success" = "false" ] && [ $deploy_attempt -lt $MAX_DEPLOY_ATTEMPTS ]; then
            log_warning "等待30秒后重试..."
            sleep 30
        fi
        
        deploy_attempt=$((deploy_attempt + 1))
    done
    
    # 如果所有尝试都失败，执行回滚
    if [ "$deploy_success" = "false" ]; then
        log_error "所有部署尝试都失败，开始自动回滚"
        auto_rollback "部署验证失败超过最大尝试次数"
    fi
    
    return $([ "$deploy_success" = "true" ] && echo 0 || echo 1)
}

# 自动修复常见问题
auto_fix_common_issues() {
    log_info "尝试自动修复常见问题..."
    
    ssh "$LINUX_SERVER" << 'EOF'
        # 修复端口占用
        echo "🔧 检查端口占用..."
        for port in 8080 3306 80; do
            pid=$(lsof -ti:$port 2>/dev/null || true)
            if [ -n "$pid" ]; then
                echo "⚠️  端口 $port 被进程 $pid 占用，尝试释放..."
                kill -9 $pid 2>/dev/null || true
            fi
        done
        
        # 清理Docker资源
        echo "🔧 清理Docker资源..."
        docker system prune -f --volumes || true
        
        # 重置网络
        echo "🔧 重置Docker网络..."
        docker network prune -f || true
        docker network create financial-network 2>/dev/null || true
        
        # 修复权限问题
        echo "🔧 修复文件权限..."
        DEPLOY_DIR="/opt/FinancialSystem/current"
        if [ -d "$DEPLOY_DIR" ]; then
            chown -R admin:admin "$DEPLOY_DIR" 2>/dev/null || true
            find "$DEPLOY_DIR" -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
        fi
EOF
}

# 包装原有的部署函数
deploy_with_monitoring() {
    # 这里调用原有的部署逻辑
    # 为了代码清晰，我们创建一个简化的部署流程
    
    cd "$PROJECT_ROOT"
    
    # 获取当前提交信息
    CURRENT_COMMIT=$(git rev-parse HEAD)
    CURRENT_BRANCH=$(git branch --show-current)
    
    # 执行备份
    log_deploy "执行备份..."
    if [ -f "$BACKUP_SCRIPT" ]; then
        CURRENT_BACKUP_DIR=$("$BACKUP_SCRIPT")
        if [ $? -eq 0 ] && [ -n "$CURRENT_BACKUP_DIR" ]; then
            log_success "备份完成: $CURRENT_BACKUP_DIR"
        else
            log_error "备份失败"
            return 1
        fi
    fi
    
    # 调用原有部署脚本的核心功能
    # 这里简化处理，实际应该调用原脚本的函数
    log_deploy "执行部署..."
    
    # 创建部署包
    local deploy_package="/tmp/financial-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"
    tar --exclude='.git' --exclude='node_modules' --exclude='target' \
        -czf "$deploy_package" . || return 1
    
    # 传输到服务器
    scp "$deploy_package" "$LINUX_SERVER:/tmp/" || return 1
    
    # 在服务器上部署
    ssh "$LINUX_SERVER" "
        set -e
        
        # 停止现有服务
        echo '🛑 停止现有服务...'
        cd /opt/FinancialSystem/current 2>/dev/null || true
        if [ -f docker-compose.yml ]; then
            docker compose down || true
        fi
        
        # 清理端口占用
        echo '🔧 清理端口占用...'
        for port in 8080 3306 80; do
            pid=\$(lsof -ti:\$port 2>/dev/null || true)
            if [ -n \"\$pid\" ]; then
                echo \"⚠️  杀死占用端口 \$port 的进程 \$pid\"
                kill -9 \$pid 2>/dev/null || true
            fi
        done
        
        # 部署新版本
        echo '📦 部署新版本...'
        cd /opt/FinancialSystem
        rm -rf current || true
        mkdir -p current
        tar -xzf /tmp/$(basename $deploy_package) -C current/
        cd current
        
        # 确保使用本地镜像配置
        if [ -f docker-compose.local.yml ]; then
            cp docker-compose.local.yml docker-compose.yml
        fi
        
        # 使用本地镜像的Docker部署
        if [ -f docker-compose.yml ]; then
            echo '🚀 启动Docker服务...'
            docker compose up -d
        fi
        
        echo '✅ 部署完成'
    "
    
    return $?
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem增强版自动部署流程..."
    
    # 获取部署锁
    if ! acquire_deploy_lock; then
        log_error "无法获取部署锁，退出"
        exit 1
    fi
    
    # 设置清理函数
    trap 'release_deploy_lock; cleanup' EXIT
    
    # 检查依赖
    for cmd in jq curl ssh scp; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要的命令: $cmd"
            exit 1
        fi
    done
    
    # 执行部署监控流程
    if deploy_monitor_and_fix; then
        log_success "🎉 自动部署完成！"
        send_notification "success" "部署成功完成，所有健康检查通过"
    else
        log_error "❌ 部署最终失败"
        send_notification "failed" "部署失败并已回滚到上一个稳定版本"
        exit 1
    fi
}

# 发送通知函数
send_notification() {
    local status="$1"
    local message="$2"
    
    # Webhook通知
    curl -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{
            \"event\": \"deployment_$status\",
            \"message\": \"$message\",
            \"timestamp\": \"$(date -Iseconds)\"
        }" &> /dev/null || true
}

# 清理函数
cleanup() {
    rm -f /tmp/financial-deploy-*.tar.gz
}

# 执行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi