#!/bin/bash

# 部署状态监控脚本
# 监控服务健康状态并提供故障诊断

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_monitor() {
    echo -e "${PURPLE}[MONITOR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Docker服务状态
check_docker_services() {
    log_monitor "检查Docker服务状态..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        return 1
    fi
    
    log_info "Docker服务正常运行"
    
    # 检查容器状态
    echo "📋 容器状态报告:"
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(financial|mysql)"; then
        echo ""
    else
        log_warning "未发现相关容器运行"
    fi
    
    return 0
}

# 检查端口占用
check_ports() {
    log_monitor "检查端口占用状态..."
    
    ports=(3306 8080 80)
    
    for port in "${ports[@]}"; do
        # 兼容macOS和Linux的netstat命令
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if netstat -an | grep "LISTEN" | grep ":$port " > /dev/null; then
                log_success "端口 $port 正在监听"
            else
                log_warning "端口 $port 未监听"
            fi
        else
            # Linux
            if netstat -tlnp | grep ":$port " > /dev/null; then
                process=$(netstat -tlnp | grep ":$port " | awk '{print $7}' | head -1)
                log_success "端口 $port 正在监听 ($process)"
            else
                log_warning "端口 $port 未监听"
            fi
        fi
    done
}

# 检查服务健康状态
check_service_health() {
    log_monitor "检查服务健康状态..."
    
    # 检查MySQL
    echo "🗄️ MySQL健康检查:"
    if docker ps | grep -q mysql; then
        mysql_container=$(docker ps | grep mysql | awk '{print $1}' | head -1)
        if docker exec "$mysql_container" mysqladmin ping -h localhost --silent 2>/dev/null; then
            log_success "MySQL服务健康"
        else
            log_error "MySQL服务不健康"
        fi
    else
        log_warning "未发现MySQL容器"
    fi
    
    # 检查后端服务
    echo "🚀 后端服务健康检查:"
    if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
        health_status=$(curl -s http://localhost:8080/actuator/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        if [ "$health_status" = "UP" ]; then
            log_success "后端服务健康 (状态: $health_status)"
        else
            log_warning "后端服务状态异常 (状态: $health_status)"
        fi
    else
        log_error "后端服务不可访问"
    fi
    
    # 检查前端服务
    echo "🌐 前端服务健康检查:"
    if curl -s http://localhost/ > /dev/null 2>&1; then
        log_success "前端服务健康"
    else
        log_error "前端服务不可访问"
    fi
}

# 检查系统资源
check_system_resources() {
    log_monitor "检查系统资源使用..."
    
    # 内存使用
    memory_info=$(free -h | awk 'NR==2{printf "内存使用: %s/%s (%.2f%%)", $3,$2,$3*100/$2 }')
    echo "💾 $memory_info"
    
    # 磁盘使用
    disk_info=$(df -h / | awk 'NR==2{printf "磁盘使用: %s/%s (%s)", $3,$2,$5}')
    echo "💿 $disk_info"
    
    # CPU负载
    load_avg=$(uptime | awk -F'load average:' '{ print $2 }')
    echo "⚡ CPU负载:$load_avg"
    
    # Docker资源使用
    if docker info &> /dev/null; then
        echo "🐳 Docker统计:"
        echo "  容器数: $(docker ps -q | wc -l) 运行中, $(docker ps -a -q | wc -l) 总计"
        echo "  镜像数: $(docker images -q | wc -l)"
        
        # 容器资源使用（如果有运行的容器）
        if [ "$(docker ps -q | wc -l)" -gt 0 ]; then
            echo "  容器资源使用:"
            docker stats --no-stream --format "    {{.Name}}: CPU {{.CPUPerc}}, 内存 {{.MemUsage}}" 2>/dev/null || true
        fi
    fi
}

# 检查日志错误
check_logs() {
    log_monitor "检查最近的错误日志..."
    
    # 检查应用日志
    if [ -f "financial-system.log" ]; then
        echo "📝 应用日志最后10行:"
        tail -10 financial-system.log | sed 's/^/    /'
        
        # 检查错误日志
        error_count=$(grep -i error financial-system.log | wc -l)
        if [ "$error_count" -gt 0 ]; then
            log_warning "发现 $error_count 个错误日志条目"
            echo "   最近的错误:"
            grep -i error financial-system.log | tail -3 | sed 's/^/    /'
        else
            log_success "应用日志无错误"
        fi
    else
        log_warning "未找到应用日志文件"
    fi
    
    # 检查Docker日志
    if docker ps | grep -q financial; then
        echo "🐳 Docker容器日志 (最后5行):"
        for container in $(docker ps --format "{{.Names}}" | grep financial); do
            echo "  容器: $container"
            docker logs --tail=5 "$container" 2>&1 | sed 's/^/    /' || true
        done
    fi
}

# 生成诊断报告
generate_diagnostic_report() {
    log_monitor "生成诊断报告..."
    
    report_file="deployment-diagnostic-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "FinancialSystem 部署诊断报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "1. 系统信息:"
        uname -a
        echo ""
        
        echo "2. Docker信息:"
        docker version --format "Docker: {{.Server.Version}}" 2>/dev/null || echo "Docker未安装或未运行"
        echo ""
        
        echo "3. Java信息:"
        java -version 2>&1 || echo "Java未安装"
        echo ""
        
        echo "4. 端口监听:"
        if [[ "$OSTYPE" == "darwin"* ]]; then
            netstat -an | grep "LISTEN" | grep -E ":(3306|8080|80) "
        else
            netstat -tlnp | grep -E ":(3306|8080|80) "
        fi
        echo ""
        
        echo "5. 进程信息:"
        ps aux | grep -E "(java|mysql|nginx)" | grep -v grep
        echo ""
        
        echo "6. 容器状态:"
        docker ps -a 2>/dev/null || echo "Docker不可用"
        echo ""
        
        echo "7. 系统资源:"
        free -h
        df -h
        echo ""
        
    } > "$report_file"
    
    log_success "诊断报告已生成: $report_file"
}

# 快速修复建议
suggest_fixes() {
    log_monitor "分析问题并提供修复建议..."
    
    issues_found=false
    
    # 检查MySQL
    if [[ "$OSTYPE" == "darwin"* ]]; then
        mysql_listening=$(netstat -an | grep "LISTEN" | grep ":3306 " | wc -l)
        backend_listening=$(netstat -an | grep "LISTEN" | grep ":8080 " | wc -l)
        frontend_listening=$(netstat -an | grep "LISTEN" | grep ":80 " | wc -l)
    else
        mysql_listening=$(netstat -tlnp | grep ":3306 " | wc -l)
        backend_listening=$(netstat -tlnp | grep ":8080 " | wc -l)
        frontend_listening=$(netstat -tlnp | grep ":80 " | wc -l)
    fi
    
    if [ "$mysql_listening" -eq 0 ]; then
        echo "❌ 问题: MySQL端口3306未监听"
        echo "   修复建议: docker compose up -d mysql"
        issues_found=true
    fi
    
    # 检查后端
    if [ "$backend_listening" -eq 0 ]; then
        echo "❌ 问题: 后端端口8080未监听"
        echo "   修复建议: 检查Java进程，重新启动JAR文件"
        issues_found=true
    fi
    
    # 检查前端
    if [ "$frontend_listening" -eq 0 ]; then
        echo "❌ 问题: 前端端口80未监听"
        echo "   修复建议: docker compose up -d nginx"
        issues_found=true
    fi
    
    # 检查Docker
    if ! docker info &> /dev/null; then
        echo "❌ 问题: Docker服务未运行"
        echo "   修复建议: sudo systemctl start docker"
        issues_found=true
    fi
    
    if [ "$issues_found" = false ]; then
        log_success "未发现明显问题，系统运行正常"
    fi
}

# 主函数
main() {
    case "${1:-status}" in
        "status"|"")
            log_info "🔍 开始部署状态监控..."
            check_docker_services
            check_ports
            check_service_health
            check_system_resources
            suggest_fixes
            ;;
        "health")
            check_service_health
            ;;
        "logs")
            check_logs
            ;;
        "diagnostic")
            generate_diagnostic_report
            ;;
        "fix")
            suggest_fixes
            ;;
        *)
            echo "用法: $0 [status|health|logs|diagnostic|fix]"
            echo "  status     - 完整状态检查 (默认)"
            echo "  health     - 仅检查服务健康状态"
            echo "  logs       - 检查日志"
            echo "  diagnostic - 生成诊断报告"
            echo "  fix        - 修复建议"
            exit 1
            ;;
    esac
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi