#!/bin/bash

# FinancialSystem 集成部署脚本
# 整合所有CI/CD增强功能的主部署脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 导入配置
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
LINUX_SERVER="admin@**********"
DEPLOY_LOG="/tmp/financial-deploy-$(date +%Y%m%d-%H%M%S).log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$DEPLOY_LOG"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$DEPLOY_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$DEPLOY_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$DEPLOY_LOG"
}

log_section() {
    echo -e "\n${PURPLE}========== $1 ==========${NC}\n" | tee -a "$DEPLOY_LOG"
}

# 预检查
pre_deployment_check() {
    log_section "部署前检查"
    
    # 检查Git状态
    cd "$PROJECT_ROOT"
    if [ ! -d ".git" ]; then
        log_error "不是Git仓库"
        return 1
    fi
    
    # 检查分支
    local current_branch=$(git branch --show-current)
    if [ "$current_branch" != "main" ]; then
        log_warning "当前不在main分支: $current_branch"
        read -p "是否继续？(y/n) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi
    
    # 检查SSH连接
    if ! ssh -o ConnectTimeout=5 "$LINUX_SERVER" "echo '连接正常'" &>/dev/null; then
        log_error "无法连接到Linux服务器"
        return 1
    fi
    
    log_success "预检查通过"
    return 0
}

# 执行部署流程
execute_deployment() {
    log_section "执行集成部署流程"
    
    # 1. 优化Docker镜像
    log_info "步骤1: 优化Docker镜像管理"
    if [ -f "$SCRIPT_DIR/manage-local-images.sh" ]; then
        ssh "$LINUX_SERVER" "bash -s" < "$SCRIPT_DIR/manage-local-images.sh" full
    fi
    
    # 2. 执行增强版部署（支持回滚）
    log_info "步骤2: 执行增强版自动部署"
    if [ -f "$SCRIPT_DIR/auto-deploy-with-rollback.sh" ]; then
        bash "$SCRIPT_DIR/auto-deploy-with-rollback.sh"
        if [ $? -ne 0 ]; then
            log_error "部署失败"
            return 1
        fi
    else
        log_warning "增强版部署脚本不存在，使用标准部署"
        if [ -f "$SCRIPT_DIR/auto-deploy-trigger.sh" ]; then
            bash "$SCRIPT_DIR/auto-deploy-trigger.sh"
        fi
    fi
    
    # 3. 启动健康监控
    log_info "步骤3: 配置健康监控"
    ssh "$LINUX_SERVER" << 'EOF'
        # 复制监控脚本
        if [ -f "/opt/FinancialSystem/current/ci-cd/deploy/enhanced-health-monitor.sh" ]; then
            chmod +x /opt/FinancialSystem/current/ci-cd/deploy/enhanced-health-monitor.sh
            
            # 安装系统服务
            if [ -f "/opt/FinancialSystem/current/ci-cd/systemd/financial-health-monitor.service" ]; then
                sudo cp /opt/FinancialSystem/current/ci-cd/systemd/financial-health-monitor.service /etc/systemd/system/
                sudo systemctl daemon-reload
                sudo systemctl enable financial-health-monitor.service
                sudo systemctl restart financial-health-monitor.service
                echo "✅ 健康监控服务已启动"
            fi
        fi
EOF
    
    # 4. 执行部署后验证
    log_info "步骤4: 执行部署后验证"
    ssh "$LINUX_SERVER" << 'EOF'
        cd /opt/FinancialSystem/current
        if [ -f "ci-cd/deploy/enhanced-health-monitor.sh" ]; then
            ./ci-cd/deploy/enhanced-health-monitor.sh check
        fi
EOF
    
    log_success "集成部署流程完成"
    return 0
}

# 部署后操作
post_deployment_tasks() {
    log_section "部署后任务"
    
    # 生成部署报告
    log_info "生成部署报告"
    
    cat > "/tmp/deploy-report-$(date +%Y%m%d-%H%M%S).md" << EOF
# FinancialSystem 部署报告

**部署时间**: $(date)
**部署分支**: $(cd "$PROJECT_ROOT" && git branch --show-current)
**部署版本**: $(cd "$PROJECT_ROOT" && git rev-parse HEAD)

## 部署功能清单

### ✅ 已实现功能
1. **自动部署触发**: Git push到main分支自动触发部署
2. **失败自动回滚**: 部署失败时自动回滚到上一个稳定版本
3. **Docker镜像优化**: 本地镜像缓存和管理
4. **健康监控**: 持续健康检查和自动恢复
5. **多级容错**: 完全Docker、混合模式、最小化服务三级后备

### 📊 系统状态
- 后端服务: $(ssh "$LINUX_SERVER" "curl -s http://localhost:8080/actuator/health" &>/dev/null && echo "✅ 运行中" || echo "❌ 停止")
- 前端服务: $(ssh "$LINUX_SERVER" "curl -s http://localhost/" &>/dev/null && echo "✅ 运行中" || echo "❌ 停止")
- 数据库服务: $(ssh "$LINUX_SERVER" "docker ps | grep mysql" &>/dev/null && echo "✅ 运行中" || echo "❌ 停止")

### 🔧 配置信息
- Linux服务器: $LINUX_SERVER
- 部署路径: /opt/FinancialSystem/current
- 监控服务: financial-health-monitor.service

### 📝 使用说明
1. 提交代码到main分支自动触发部署
2. 查看部署日志: $DEPLOY_LOG
3. 查看健康状态: ssh $LINUX_SERVER "/opt/FinancialSystem/current/ci-cd/deploy/enhanced-health-monitor.sh check"
4. 查看监控日志: ssh $LINUX_SERVER "sudo journalctl -u financial-health-monitor -f"

### ⚠️ 注意事项
- 确保Docker镜像使用本地缓存以加快部署速度
- 部署失败会自动回滚，无需手动干预
- 健康监控会自动重启失败的服务
EOF
    
    log_success "部署报告已生成"
    
    # 清理临时文件
    log_info "清理临时文件"
    rm -f /tmp/financial-deploy-*.tar.gz
    
    log_success "部署后任务完成"
}

# 显示部署摘要
show_deployment_summary() {
    echo ""
    echo -e "${GREEN}=================================${NC}"
    echo -e "${GREEN}🎉 部署成功完成！${NC}"
    echo -e "${GREEN}=================================${NC}"
    echo ""
    echo "📋 部署摘要:"
    echo "  - 部署时间: $(date)"
    echo "  - 部署日志: $DEPLOY_LOG"
    echo "  - 服务地址:"
    echo "    - 前端: http://**********/"
    echo "    - 后端API: http://**********:8080/"
    echo "    - 健康检查: http://**********:8080/actuator/health"
    echo ""
    echo "🔍 常用命令:"
    echo "  - 查看部署状态: ssh $LINUX_SERVER 'cd /opt/FinancialSystem/current && ./ci-cd/deploy/enhanced-health-monitor.sh check'"
    echo "  - 查看监控日志: ssh $LINUX_SERVER 'sudo journalctl -u financial-health-monitor -f'"
    echo "  - 手动触发部署: cd $PROJECT_ROOT && ./ci-cd/deploy/integrated-deploy.sh"
    echo ""
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem集成部署"
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 执行部署流程
    if pre_deployment_check && execute_deployment; then
        post_deployment_tasks
        
        # 计算部署时间
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "部署完成，耗时: ${duration}秒"
        show_deployment_summary
        
        return 0
    else
        log_error "部署失败"
        echo "查看详细日志: $DEPLOY_LOG"
        return 1
    fi
}

# 确保脚本可执行
chmod +x "$SCRIPT_DIR"/*.sh

# 运行主函数
main "$@"