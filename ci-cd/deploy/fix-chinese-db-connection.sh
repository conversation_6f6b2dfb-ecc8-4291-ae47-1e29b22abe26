#!/bin/bash
# 修复中文数据库名连接问题的部署脚本

set -e

echo "=== 修复中文数据库名连接问题 ==="
echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 项目路径
PROJECT_DIR="/opt/FinancialSystem/current"
cd "$PROJECT_DIR"

# 1. 备份当前配置
echo -e "${YELLOW}1. 备份当前配置文件...${NC}"
if [ -f "docker-compose.prod.yml" ]; then
    cp docker-compose.prod.yml docker-compose.prod.yml.backup.$(date +%Y%m%d_%H%M%S)
fi

# 2. 创建修复后的docker-compose配置
echo -e "${YELLOW}2. 创建修复后的Docker Compose配置...${NC}"
cat > docker-compose.prod-fixed.yml << 'EOF'
# FinancialSystem 生产环境 Docker Compose 配置 - 修复版
# 解决中文数据库名连接问题

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: financial-mysql
    pull_policy: never
    environment:
      MYSQL_ROOT_PASSWORD: Zlb&198838
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
      # 添加环境变量支持中文
      LANG: C.UTF-8
      LC_ALL: C.UTF-8
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --skip-character-set-client-handshake
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 后端运行服务
  backend:
    image: openjdk:21-jdk
    platform: linux/amd64
    container_name: financial-backend
    pull_policy: never
    ports:
      - "8080:8080"
    volumes:
      - ./api-gateway/target:/app
      - ./api-gateway/logs:/logs
    working_dir: /app
    command: >
      sh -c "
        echo '等待数据库完全启动...' &&
        sleep 30 &&
        echo '启动Spring Boot应用...' &&
        java -Dfile.encoding=UTF-8 -jar api-gateway-1.0-SNAPSHOT.jar --spring.profiles.active=production,docker-fix
      "
    environment:
      - SPRING_PROFILES_ACTIVE=production,docker-fix
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
      # 使用英文数据库名
      - SPRING_DATASOURCE_PRIMARY_URL=***********************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_PRIMARY_USERNAME=root
      - SPRING_DATASOURCE_PRIMARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_SECONDARY_URL=***************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_SECONDARY_USERNAME=root
      - SPRING_DATASOURCE_SECONDARY_PASSWORD=Zlb&198838
      - SPRING_DATASOURCE_USER_SYSTEM_URL=*************************************************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USER_SYSTEM_USERNAME=root
      - SPRING_DATASOURCE_USER_SYSTEM_PASSWORD=Zlb&198838
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx Web服务器
  nginx:
    image: nginx:alpine
    platform: linux/amd64
    container_name: financial-nginx
    pull_policy: never
    ports:
      - "80:80"
    volumes:
      - ./FinancialSystem-web/build:/usr/share/nginx/html:ro
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      backend:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local

networks:
  default:
    name: financial-network
    driver: bridge
EOF

# 3. 检查并创建数据库映射脚本
echo -e "${YELLOW}3. 确保数据库映射脚本存在...${NC}"
if [ ! -f "init-scripts/00-create-db-mapping.sql" ]; then
    echo "创建数据库映射脚本..."
    # 这里应该复制上面创建的脚本内容
fi

# 4. 停止当前服务
echo -e "${YELLOW}4. 停止当前Docker服务...${NC}"
docker-compose -f docker-compose.prod.yml down || true

# 5. 备份数据库数据（如果需要）
echo -e "${YELLOW}5. 备份数据库数据...${NC}"
if docker volume ls | grep -q "financialsystem_mysql_data"; then
    echo "发现现有数据卷，创建备份..."
    docker run --rm -v financialsystem_mysql_data:/data -v $(pwd)/backup:/backup alpine tar czf /backup/mysql_data_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /data .
fi

# 6. 启动修复后的服务
echo -e "${YELLOW}6. 启动修复后的服务...${NC}"
docker-compose -f docker-compose.prod-fixed.yml up -d

# 7. 等待MySQL启动并执行数据库同步
echo -e "${YELLOW}7. 等待MySQL启动...${NC}"
sleep 30

# 8. 执行数据库同步
echo -e "${YELLOW}8. 执行数据库同步...${NC}"
docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e "
-- 检查中文数据库是否有数据
SELECT COUNT(*) INTO @chinese_db_tables FROM information_schema.TABLES WHERE TABLE_SCHEMA = '逾期债权数据库';
SELECT CONCAT('中文数据库表数量: ', @chinese_db_tables) AS info;

-- 如果中文数据库有表，执行同步
CALL IF(@chinese_db_tables > 0, sync_chinese_to_english_db(), 'SELECT \"中文数据库为空，跳过同步\" AS info');
"

# 9. 检查服务状态
echo -e "${YELLOW}9. 检查服务状态...${NC}"
sleep 10

# 检查后端健康状态
if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 后端服务启动成功${NC}"
else
    echo -e "${RED}✗ 后端服务启动失败${NC}"
    echo "查看后端日志："
    docker logs financial-backend --tail 50
fi

# 10. 创建永久修复
echo -e "${YELLOW}10. 应用永久修复...${NC}"
mv docker-compose.prod-fixed.yml docker-compose.prod.yml

echo -e "${GREEN}=== 中文数据库名连接问题修复完成 ===${NC}"
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "注意事项："
echo "1. 应用现在使用 'overdue_debt_db' 作为主数据库"
echo "2. 中文数据库 '逾期债权数据库' 仍然保留，可通过存储过程同步"
echo "3. 如需回滚，可使用备份文件: docker-compose.prod.yml.backup.*"