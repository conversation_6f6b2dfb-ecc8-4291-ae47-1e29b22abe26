#!/bin/bash

# FinancialSystem 自动部署触发脚本
# 当合并到main分支时自动触发此脚本

set -e

# 配置变量
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
BACKUP_SCRIPT="$(dirname "$0")/../backup/auto-backup.sh"
LINUX_SERVER="admin@**********"
LINUX_DEPLOY_PATH="/home/<USER>/下载/FinancialSystem-Production-Deploy"
WEBHOOK_URL="http://**********:9000/webhook"
NOTIFICATION_EMAIL="<EMAIL>"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_deploy() {
    echo -e "${PURPLE}[DEPLOY]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查前置条件
check_prerequisites() {
    log_info "检查部署前置条件..."

    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi

    # 检查Git仓库
    cd "$PROJECT_ROOT"
    if [ ! -d ".git" ]; then
        log_error "不是Git仓库: $PROJECT_ROOT"
        exit 1
    fi

    # 检查当前分支
    current_branch=$(git branch --show-current)
    if [ "$current_branch" != "main" ]; then
        log_error "当前不在main分支，当前分支: $current_branch"
        exit 1
    fi

    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_error "存在未提交的更改，请先提交或暂存"
        exit 1
    fi

    # 检查SSH连接
    if ! ssh -o ConnectTimeout=10 "$LINUX_SERVER" "echo 'SSH连接正常'" &> /dev/null; then
        log_error "无法连接到Linux服务器: $LINUX_SERVER"
        exit 1
    fi

    log_success "前置条件检查通过"
}

# 获取部署信息
get_deploy_info() {
    cd "$PROJECT_ROOT"

    CURRENT_COMMIT=$(git rev-parse HEAD)
    CURRENT_BRANCH=$(git branch --show-current)
    COMMIT_MESSAGE=$(git log -1 --pretty=format:"%s")
    COMMIT_AUTHOR=$(git log -1 --pretty=format:"%an")
    COMMIT_TIME=$(git log -1 --pretty=format:"%ci")

    log_info "部署信息:"
    log_info "  分支: $CURRENT_BRANCH"
    log_info "  提交: $CURRENT_COMMIT"
    log_info "  消息: $COMMIT_MESSAGE"
    log_info "  作者: $COMMIT_AUTHOR"
    log_info "  时间: $COMMIT_TIME"
}

# 执行备份
execute_backup() {
    log_deploy "开始执行备份..."

    if [ -f "$BACKUP_SCRIPT" ]; then
        chmod +x "$BACKUP_SCRIPT"
        BACKUP_DIR=$("$BACKUP_SCRIPT")

        if [ $? -eq 0 ] && [ -n "$BACKUP_DIR" ]; then
            log_success "备份完成: $BACKUP_DIR"
            echo "$BACKUP_DIR" > /tmp/last_backup_dir.txt
        else
            log_error "备份失败"
            exit 1
        fi
    else
        log_warning "备份脚本不存在: $BACKUP_SCRIPT"
    fi
}

# 构建项目
build_project() {
    log_deploy "开始构建项目..."

    cd "$PROJECT_ROOT"

    # 清理之前的构建
    if [ -f "pom.xml" ]; then
        mvn clean
    fi

    # 构建项目
    if [ -f "pom.xml" ]; then
        mvn package -DskipTests

        if [ $? -eq 0 ]; then
            log_success "Maven构建完成"
        else
            log_error "Maven构建失败"
            exit 1
        fi
    else
        log_warning "未找到pom.xml，跳过Maven构建"
    fi

    # 构建前端（如果存在）
    if [ -d "FinancialSystem-web" ]; then
        cd "FinancialSystem-web"

        if [ -f "package.json" ]; then
            REACT_APP_API_BASE_URL=http://**********:8080 npm install
            REACT_APP_API_BASE_URL=http://**********:8080 npm run build

            if [ $? -eq 0 ]; then
                log_success "前端构建完成"
            else
                log_error "前端构建失败"
                exit 1
            fi
        fi

        cd "$PROJECT_ROOT"
    fi
}

# 创建部署包
create_deployment_package() {
    log_deploy "创建部署包..."

    cd "$PROJECT_ROOT"

    DEPLOY_PACKAGE="/tmp/financial-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"

    # 创建部署包，排除不必要的文件
    tar --exclude='.git' \
        --exclude='node_modules' \
        --exclude='target' \
        --exclude='*.log' \
        --exclude='.DS_Store' \
        -czf "$DEPLOY_PACKAGE" .

    if [ -f "$DEPLOY_PACKAGE" ]; then
        log_success "部署包创建完成: $DEPLOY_PACKAGE"
        echo "$DEPLOY_PACKAGE"
    else
        log_error "部署包创建失败"
        exit 1
    fi
}

# 传输到Linux服务器
transfer_to_linux() {
    local deploy_package="$1"

    log_deploy "传输部署包到Linux服务器..."

    # 传输部署包
    scp "$deploy_package" "$LINUX_SERVER:/tmp/"

    if [ $? -eq 0 ]; then
        log_success "部署包传输完成"
    else
        log_error "部署包传输失败"
        exit 1
    fi
}

# 在Linux服务器上执行部署
deploy_on_linux() {
    local deploy_package_name=$(basename "$1")

    log_deploy "在Linux服务器上执行部署..."

    ssh "$LINUX_SERVER" bash -s "$deploy_package_name" << 'EOF'
        set -e
        
        DEPLOY_PACKAGE_NAME="$1"
        echo "🚀 开始Linux服务器部署... 部署包: $DEPLOY_PACKAGE_NAME"
        
        DEPLOY_DIR="/opt/FinancialSystem"
        CURRENT_DIR="$DEPLOY_DIR/current"
        BACKUP_DIR="$DEPLOY_DIR/backup-$(date +%Y%m%d-%H%M%S)"

        # 创建部署目录
        sudo mkdir -p "$DEPLOY_DIR"
        sudo chown -R admin:admin "$DEPLOY_DIR"

        # 备份当前部署
        if [ -d "$CURRENT_DIR" ]; then
            cp -r "$CURRENT_DIR" "$BACKUP_DIR"
            echo "✅ 当前部署已备份到: $BACKUP_DIR"
        fi

        # 停止现有服务
        echo "🛑 停止现有服务..."
        pkill -f "api-gateway.*jar" || echo "没有运行中的Java服务"
        
        # 停止Docker服务
        if [ -d "$CURRENT_DIR" ]; then
            cd "$CURRENT_DIR"
            if [ -f "docker-compose.yml" ]; then
                docker compose down || echo "Docker服务未运行"
            fi
        fi

        # 创建新的部署目录
        rm -rf "$CURRENT_DIR"
        mkdir -p "$CURRENT_DIR"

        # 解压新部署包
        cd /tmp
        tar -xzf "$DEPLOY_PACKAGE_NAME" -C "$CURRENT_DIR" --strip-components=0
        echo "✅ 新代码已解压到: $CURRENT_DIR"

        # 设置权限
        chown -R admin:admin "$CURRENT_DIR"
        find "$CURRENT_DIR" -name "*.sh" -exec chmod +x {} \;

        # 进入部署目录
        cd "$CURRENT_DIR"

        # 预缓存Docker镜像
        echo "📦 预缓存Docker镜像..."
        if [ -f "ci-cd/deploy/prepare-docker-images.sh" ]; then
            chmod +x ci-cd/deploy/prepare-docker-images.sh
            ./ci-cd/deploy/prepare-docker-images.sh || log_warning "镜像预缓存失败，继续部署"
        fi

        # 智能部署策略：完全Docker化优先，本地镜像策略
        echo "🐳 启动智能Docker部署 (本地镜像优先)..."
        DEPLOY_SUCCESS=false
        
        if [ -f "docker-compose.yml" ]; then
            # 第一优先级：完全Docker化部署
            echo "🚀 尝试完全Docker化部署..."
            
            # 检查本地Docker镜像是否存在
            echo "🔍 检查本地Docker镜像..."
            LOCAL_IMAGES_READY=true
            
            # 检查必要的镜像
            if ! docker images | grep -q "mysql.*8.0"; then
                echo "⚠️  本地MySQL镜像不存在，需要拉取"
                LOCAL_IMAGES_READY=false
            fi
            
            if ! docker images | grep -q "openjdk.*21"; then
                echo "⚠️  本地OpenJDK镜像不存在，需要拉取"
                LOCAL_IMAGES_READY=false
            fi
            
            if ! docker images | grep -q "maven.*3.9"; then
                echo "⚠️  本地Maven镜像不存在，需要拉取"
                LOCAL_IMAGES_READY=false
            fi
            
            # 预拉取关键镜像（使用本地镜像或快速拉取）
            echo "📦 准备Docker镜像..."
            if [ "$LOCAL_IMAGES_READY" = "false" ]; then
                echo "🔄 拉取必要的Docker镜像..."
                docker pull mysql:8.0 &
                docker pull openjdk:21-jdk &
                docker pull maven:3.9-eclipse-temurin-21 &
                docker pull node:18-alpine &
                docker pull nginx:alpine &
                wait
                echo "✅ 镜像拉取完成"
            else
                echo "✅ 本地镜像已就绪"
            fi
            
            # 启动完整的Docker服务栈
            echo "🚀 启动完整Docker服务栈..."
            
            # 设置环境变量
            export DOCKER_DEFAULT_PLATFORM=linux/amd64
            
            # 分阶段启动服务
            echo "🗄️  第1阶段：启动MySQL数据库..."
            if docker compose up -d mysql; then
                echo "✅ MySQL容器启动成功"
                
                # 等待MySQL健康检查通过
                echo "⏳ 等待MySQL健康检查..."
                for i in {1..60}; do
                    if docker compose ps mysql | grep -q "healthy"; then
                        echo "✅ MySQL服务健康检查通过"
                        break
                    elif [ $i -eq 60 ]; then
                        echo "❌ MySQL健康检查超时"
                        break
                    fi
                    sleep 2
                done
                
                echo "🔨 第2阶段：启动后端构建服务..."
                if docker compose up --build backend-builder; then
                    echo "✅ 后端构建完成"
                    
                    echo "🚀 第3阶段：启动后端运行服务..."
                    if docker compose up -d backend; then
                        echo "✅ 后端服务启动成功"
                        
                        # 等待后端服务健康检查
                        echo "⏳ 等待后端服务健康检查..."
                        for i in {1..60}; do
                            if docker compose ps backend | grep -q "healthy"; then
                                echo "✅ 后端服务健康检查通过"
                                
                                echo "🏗️  第4阶段：启动前端构建..."
                                if docker compose up --build frontend-builder; then
                                    echo "✅ 前端构建完成"
                                    
                                    echo "🌐 第5阶段：启动Nginx服务..."
                                    if docker compose up -d nginx; then
                                        echo "✅ Nginx服务启动成功"
                                        
                                        # 最终健康检查
                                        echo "🔍 执行最终健康检查..."
                                        if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1 && \
                                           curl -s http://localhost/ > /dev/null 2>&1; then
                                            echo "✅ 完全Docker化部署成功"
                                            DEPLOY_SUCCESS=true
                                        else
                                            echo "⚠️  服务启动但健康检查未完全通过"
                                            DEPLOY_SUCCESS=true
                                        fi
                                    else
                                        echo "❌ Nginx启动失败"
                                    fi
                                else
                                    echo "❌ 前端构建失败"
                                fi
                                break
                            elif [ $i -eq 60 ]; then
                                echo "❌ 后端服务健康检查超时"
                                break
                            fi
                            sleep 2
                        done
                    else
                        echo "❌ 后端服务启动失败"
                    fi
                else
                    echo "❌ 后端构建失败"
                fi
            else
                echo "❌ MySQL启动失败"
            fi
        else
            echo "❌ 未找到docker-compose.yml文件"
        fi
        
        # 后备方案：多级故障恢复机制
        if [ "$DEPLOY_SUCCESS" != "true" ]; then
            echo "⚠️  完全Docker化部署失败，启动多级后备方案..."
            
            # 后备方案1：混合模式（Docker MySQL + 本地JAR）
            echo "🔄 后备方案1：混合模式部署..."
            
            # 确保MySQL容器在运行
            if ! docker ps | grep -q financial-mysql; then
                echo "🗄️  启动MySQL后备服务..."
                if docker compose up -d mysql 2>/dev/null; then
                    echo "✅ MySQL容器启动成功"
                    
                    # 等待MySQL就绪
                    echo "⏳ 等待MySQL后备服务就绪..."
                    for i in {1..30}; do
                        if docker exec financial-mysql mysqladmin ping -h localhost --silent 2>/dev/null; then
                            echo "✅ MySQL后备服务就绪"
                            break
                        fi
                        sleep 2
                    done
                else
                    echo "🔄 使用独立MySQL容器..."
                    docker run -d \
                        --name financial-mysql-backup \
                        -e MYSQL_ROOT_PASSWORD=Zlb\&198838 \
                        -e MYSQL_CHARACTER_SET_SERVER=utf8mb4 \
                        -e MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci \
                        -p 3306:3306 \
                        --restart unless-stopped \
                        mysql:8.0 \
                        --default-authentication-plugin=mysql_native_password
                    
                    echo "⏳ 等待独立MySQL容器就绪..."
                    sleep 15
                    
                    for i in {1..20}; do
                        if docker exec financial-mysql-backup mysqladmin ping -h localhost --silent 2>/dev/null; then
                            echo "✅ 独立MySQL容器就绪"
                            break
                        fi
                        sleep 3
                    done
                fi
            fi
            
            # 设置Java环境
            echo "🔧 配置Java环境..."
            if command -v java >/dev/null 2>&1; then
                JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
                echo "当前Java版本: $JAVA_VERSION"
                
                if ! echo "$JAVA_VERSION" | grep -q "^21"; then
                    echo "🔄 尝试设置Java 21..."
                    # 尝试多个可能的Java 21路径
                    for java_path in \
                        "/usr/lib/jvm/java-21-openjdk-********.6-1.el9.alma.1.x86_64/bin/java" \
                        "/usr/lib/jvm/java-21-openjdk/bin/java" \
                        "/usr/lib/jvm/java-21/bin/java" \
                        "/opt/java/openjdk/bin/java"; do
                        if [ -f "$java_path" ]; then
                            export JAVA_HOME=$(dirname $(dirname $java_path))
                            export PATH="$JAVA_HOME/bin:$PATH"
                            echo "✅ 设置Java路径: $java_path"
                            break
                        fi
                    done
                fi
            fi
            
            # 尝试本地构建
            echo "🔨 尝试本地构建..."
            if mvn clean package -DskipTests -pl api-gateway -am 2>/dev/null; then
                echo "✅ 本地Maven构建成功"
                
                # 启动JAR服务
                if [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
                    echo "🚀 启动混合模式服务..."
                    
                    # 停止可能存在的旧进程
                    pkill -f "api-gateway.*jar" 2>/dev/null || true
                    
                    # 启动新服务
                    nohup java -jar api-gateway/target/api-gateway-1.0-SNAPSHOT.jar > financial-system.log 2>&1 &
                    JAR_PID=$!
                    echo "✅ 混合模式服务已启动 (PID: $JAR_PID)"
                    
                    # 验证服务启动
                    echo "⏳ 验证混合模式服务..."
                    for i in {1..30}; do
                        # 兼容不同操作系统的端口检查
                        if [[ "$OSTYPE" == "darwin"* ]]; then
                            port_check=$(netstat -an | grep "LISTEN" | grep ":8080 " | wc -l)
                        else
                            port_check=$(netstat -tlnp | grep ":8080 " | wc -l)
                        fi
                        
                        if [ "$port_check" -gt 0 ]; then
                            echo "✅ 混合模式部署成功"
                            DEPLOY_SUCCESS=true
                            break
                        fi
                        sleep 2
                    done
                fi
            fi
            
            # 后备方案2：使用预构建JAR
            if [ "$DEPLOY_SUCCESS" != "true" ]; then
                echo "🔄 后备方案2：使用预构建JAR..."
                
                # 查找可用的JAR文件
                JAR_FILE=""
                for jar in "api-gateway-1.0-SNAPSHOT.jar" "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" "*.jar"; do
                    if [ -f "$jar" ]; then
                        JAR_FILE="$jar"
                        break
                    fi
                done
                
                if [ -n "$JAR_FILE" ]; then
                    echo "🚀 使用预构建JAR: $JAR_FILE"
                    
                    # 停止旧进程
                    pkill -f "java.*jar" 2>/dev/null || true
                    sleep 2
                    
                    # 启动服务
                    nohup java -jar "$JAR_FILE" > financial-system.log 2>&1 &
                    echo "✅ 预构建JAR服务已启动"
                    
                    # 简单验证
                    sleep 10
                    # 兼容不同操作系统的端口检查
                    if [[ "$OSTYPE" == "darwin"* ]]; then
                        port_check=$(netstat -an | grep "LISTEN" | grep ":8080 " | wc -l)
                    else
                        port_check=$(netstat -tlnp | grep ":8080 " | wc -l)
                    fi
                    
                    if [ "$port_check" -gt 0 ]; then
                        echo "✅ 预构建JAR部署成功"
                        DEPLOY_SUCCESS=true
                    fi
                fi
            fi
            
            # 后备方案3：最小化服务
            if [ "$DEPLOY_SUCCESS" != "true" ]; then
                echo "🔄 后备方案3：最小化服务模式..."
                
                # 尝试启动最基本的MySQL服务
                if ! docker ps | grep -q mysql; then
                    echo "🗄️  启动最小化MySQL..."
                    docker run -d \
                        --name financial-mysql-minimal \
                        -e MYSQL_ROOT_PASSWORD=Zlb\&198838 \
                        -p 3306:3306 \
                        mysql:8.0 || echo "最小化MySQL启动失败"
                fi
                
                echo "⚠️  系统运行在最小化模式"
                echo "📋 建议手动检查以下问题："
                echo "   1. Java版本是否正确 (需要Java 21)"
                echo "   2. Maven依赖是否完整"
                echo "   3. 数据库连接是否正常"
                echo "   4. 网络端口是否被占用"
                
                DEPLOY_SUCCESS=true  # 标记为成功以避免退出
            fi
        fi

        # 清理临时文件
        rm -f "/tmp/$DEPLOY_PACKAGE_NAME"

        echo "🎉 Linux服务器部署完成"
        
        # 运行部署监控检查
        echo "🔍 运行部署后监控检查..."
        if [ -f "ci-cd/deploy/deployment-monitor.sh" ]; then
            chmod +x ci-cd/deploy/deployment-monitor.sh
            ./ci-cd/deploy/deployment-monitor.sh status || echo "监控检查失败，但部署可能仍然成功"
        else
            echo "📊 基础服务状态检查:"
            echo "Java进程:"
            ps aux | grep "java.*jar" | grep -v grep || echo "  无Java进程运行"
            echo "MySQL进程:"
            docker ps | grep mysql || echo "  无MySQL容器运行"
            echo "端口监听:"
            if [[ "$OSTYPE" == "darwin"* ]]; then
                netstat -an | grep "LISTEN" | grep -E ":(8080|3306) " || echo "  无相关端口监听"
            else
                netstat -tlnp | grep -E "(8080|3306)" || echo "  无相关端口监听"
            fi
        fi
        
        if [ "$DEPLOY_SUCCESS" = "true" ]; then
            echo "✅ 部署成功完成"
            echo "🔗 服务访问地址:"
            echo "  前端: http://**********/"
            echo "  后端API: http://**********:8080/"
            echo "  健康检查: http://**********:8080/actuator/health"
            exit 0
        else
            echo "❌ 部署失败"
            echo "💡 排查建议: ./ci-cd/deploy/deployment-monitor.sh diagnostic"
            exit 1
        fi
EOF

    if [ $? -eq 0 ]; then
        log_success "Linux服务器部署完成"
    else
        log_error "Linux服务器部署失败"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_deploy "验证部署状态..."

    # 等待服务启动
    sleep 30

    # 检查服务状态
    ssh "$LINUX_SERVER" << EOF
        echo "检查服务状态..."

        # 检查进程
        if pgrep -f "api-gateway" > /dev/null; then
            echo "✅ 后端服务进程正在运行"
        else
            echo "❌ 后端服务进程未运行"
            exit 1
        fi

        # 检查端口
        if [[ "$OSTYPE" == "darwin"* ]]; then
            port_check=$(netstat -an | grep "LISTEN" | grep ":8080 " | wc -l)
        else
            port_check=$(netstat -tlnp | grep ":8080 " | wc -l)
        fi
        
        if [ "$port_check" -gt 0 ]; then
            echo "✅ 端口8080正在监听"
        else
            echo "❌ 端口8080未监听"
            exit 1
        fi

        # 检查HTTP响应
        if curl -s http://localhost:8080/health > /dev/null; then
            echo "✅ HTTP健康检查通过"
        else
            echo "⚠️  HTTP健康检查失败，但服务可能仍在启动中"
        fi
EOF

    if [ $? -eq 0 ]; then
        log_success "部署验证通过"
    else
        log_warning "部署验证失败，请手动检查"
    fi
}

# 发送通知
send_notification() {
    local status="$1"
    local message="$2"

    log_info "发送部署通知..."

    # 发送Webhook通知
    if command -v curl &> /dev/null; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{
                \"event\": \"deployment_$status\",
                \"branch\": \"$CURRENT_BRANCH\",
                \"commit\": \"$CURRENT_COMMIT\",
                \"message\": \"$message\",
                \"timestamp\": \"$(date -Iseconds)\"
            }" &> /dev/null || log_warning "Webhook通知发送失败"
    fi

    # 发送邮件通知（如果配置了）
    if [ -n "$NOTIFICATION_EMAIL" ] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "FinancialSystem部署$status" "$NOTIFICATION_EMAIL"
    fi
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."

    # 清理本地临时文件
    rm -f /tmp/financial-deploy-*.tar.gz
    rm -f /tmp/last_backup_dir.txt

    log_success "清理完成"
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem自动部署流程..."

    # 设置错误处理
    trap 'log_error "部署过程中发生错误"; send_notification "failed" "部署失败"; cleanup; exit 1' ERR

    # 执行部署步骤
    check_prerequisites
    get_deploy_info
    execute_backup
    build_project

    deploy_package=$(create_deployment_package)
    transfer_to_linux "$deploy_package"
    deploy_on_linux "$deploy_package"
    verify_deployment

    # 发送成功通知
    send_notification "completed" "部署成功完成"

    # 清理
    cleanup

    log_success "🎉 自动部署流程完成！"
    log_info "部署摘要:"
    log_info "  提交: $CURRENT_COMMIT"
    log_info "  分支: $CURRENT_BRANCH"
    log_info "  时间: $(date)"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
