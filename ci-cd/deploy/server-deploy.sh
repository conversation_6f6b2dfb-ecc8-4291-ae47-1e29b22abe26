#!/bin/bash

# FinancialSystem 服务器端自动部署脚本
# 专门在Linux服务器上运行，使用本地已有镜像

set -e

# 配置变量
DEPLOY_DIR="/opt/FinancialSystem/current"
BACKUP_DIR="/opt/FinancialSystem/backup-$(date +%Y%m%d-%H%M%S)"
PROJECT_ROOT="$(pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 服务器端部署
deploy_on_server() {
    log_info "🚀 开始服务器端部署..."
    
    # 1. 备份现有部署
    if [ -d "$DEPLOY_DIR" ]; then
        log_info "📦 备份现有部署到: $BACKUP_DIR"
        cp -r "$DEPLOY_DIR" "$BACKUP_DIR"
        log_success "备份完成"
    fi
    
    # 2. 停止现有服务
    log_info "🛑 停止现有服务..."
    
    # 停止特定容器
    for container in financial-mysql financial-backend financial-nginx; do
        if docker ps -q -f name=$container | grep -q .; then
            log_info "停止容器: $container"
            docker stop $container || true
            docker rm $container || true
        fi
    done
    
    # 停止Java进程
    pkill -f "api-gateway.*jar" || true
    
    # 清理端口占用
    for port in 8080 3306 80; do
        pid=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$pid" ]; then
            log_warning "杀死占用端口 $port 的进程 $pid"
            kill -9 $pid 2>/dev/null || true
        fi
    done
    
    # 3. 准备新部署
    log_info "📁 准备新部署目录..."
    rm -rf "$DEPLOY_DIR"
    mkdir -p "$DEPLOY_DIR"
    
    # 复制文件到部署目录
    cp -r "$PROJECT_ROOT"/* "$DEPLOY_DIR"/
    cd "$DEPLOY_DIR"
    
    # 4. 确保必要文件存在
    log_info "🔍 检查必要文件..."
    
    # 检查JAR文件
    if [ ! -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
        log_error "JAR文件不存在，需要先构建"
        return 1
    fi
    
    # 确保前端build目录存在
    if [ ! -d "FinancialSystem-web/build" ]; then
        log_warning "前端build目录不存在，创建默认页面..."
        mkdir -p FinancialSystem-web/build
        cat > FinancialSystem-web/build/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>财务系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .status { color: #0066cc; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💼 财务系统</h1>
        <p class="status">系统正在启动中，请稍候...</p>
        <p>如果页面长时间未更新，请联系系统管理员。</p>
    </div>
    <script>
        setTimeout(function() { location.reload(); }, 30000);
    </script>
</body>
</html>
EOF
    fi
    
    # 5. 使用最小化配置
    if [ -f "docker-compose.minimal.yml" ]; then
        log_info "🔧 使用最小化Docker配置..."
        cp docker-compose.minimal.yml docker-compose.yml
    fi
    
    # 6. 启动服务
    log_info "🚀 启动Docker服务..."
    if docker compose up -d; then
        log_success "Docker服务启动成功"
    else
        log_warning "Docker启动失败，尝试备用方案..."
        
        # 备用方案：直接启动容器
        log_info "🔄 启动MySQL..."
        docker run -d --name financial-mysql \
            -p 3306:3306 \
            -e MYSQL_ROOT_PASSWORD=Zlb&198838 \
            mysql:8.0
        
        sleep 15
        
        log_info "🔄 启动后端服务..."
        cd api-gateway/target
        nohup java -jar api-gateway-1.0-SNAPSHOT.jar \
            --spring.profiles.active=production \
            --spring.datasource.primary.url="**********************************************************************************************************************" \
            > ../financial-system.log 2>&1 &
        
        log_info "🔄 启动Nginx..."
        docker run -d --name financial-nginx \
            -p 80:80 \
            -v "$DEPLOY_DIR/FinancialSystem-web/build:/usr/share/nginx/html:ro" \
            nginx:alpine
    fi
    
    log_success "✅ 服务器端部署完成"
    return 0
}

# 验证部署
verify_deployment() {
    log_info "🔍 验证部署..."
    
    # 等待服务启动
    sleep 30
    
    # 检查容器状态
    log_info "📊 检查Docker容器:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    # 检查服务
    local services_ok=0
    
    # 检查MySQL
    if docker ps | grep -q financial-mysql; then
        log_success "✅ MySQL容器运行正常"
        services_ok=$((services_ok + 1))
    fi
    
    # 检查后端进程
    if pgrep -f "api-gateway.*jar" > /dev/null; then
        log_success "✅ 后端Java进程运行正常"
        services_ok=$((services_ok + 1))
    fi
    
    # 检查Nginx
    if docker ps | grep -q financial-nginx; then
        log_success "✅ Nginx容器运行正常"
        services_ok=$((services_ok + 1))
    fi
    
    if [ $services_ok -eq 3 ]; then
        log_success "🎉 所有服务验证通过！"
        return 0
    else
        log_warning "⚠️  部分服务可能仍在启动中"
        return 1
    fi
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem服务器端自动部署..."
    
    # 检查环境
    if [ ! -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
        log_error "JAR文件不存在，请先在开发环境构建项目"
        exit 1
    fi
    
    # 执行部署
    if deploy_on_server; then
        log_success "🎉 服务器端部署成功！"
        
        # 验证部署
        if verify_deployment; then
            log_success "🎉 部署验证成功！"
        else
            log_warning "⚠️  请检查服务状态"
        fi
        
        # 显示访问信息
        SERVER_IP=$(hostname -I | awk '{print $1}')
        log_info "🌐 访问地址:"
        log_info "   - 前端: http://$SERVER_IP/"
        log_info "   - 后端API: http://$SERVER_IP:8080/"
        log_info "   - 健康检查: http://$SERVER_IP:8080/actuator/health"
        
        exit 0
    else
        log_error "❌ 服务器端部署失败"
        exit 1
    fi
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi