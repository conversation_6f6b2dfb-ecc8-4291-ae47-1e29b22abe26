#!/bin/bash

# FinancialSystem 修复版自动部署脚本
# 专门解决镜像缺失问题，实现真正的自动化部署

set -e

# 配置变量
PROJECT_ROOT="${PROJECT_ROOT:-$(pwd)}"
LINUX_SERVER="${LINUX_SERVER:-admin@10.25.1.85}"
DEPLOY_DIR="${DEPLOY_DIR:-/opt/FinancialSystem}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 本地构建项目
build_project_locally() {
    log_info "📦 本地构建项目..."
    cd "$PROJECT_ROOT"
    
    # 构建后端
    if mvn clean package -DskipTests -pl api-gateway -am; then
        log_success "后端构建成功"
    else
        log_error "后端构建失败"
        return 1
    fi
    
    # 构建前端 (如果存在package.json)
    if [ -f "FinancialSystem-web/package.json" ]; then
        log_info "构建前端..."
        cd FinancialSystem-web
        if [ ! -d "node_modules" ]; then
            log_info "安装前端依赖..."
            npm install
        fi
        if npm run build; then
            log_success "前端构建成功"
        else
            log_warning "前端构建失败，将使用现有文件"
        fi
        cd ..
    fi
    
    # 确保前端build目录存在
    if [ ! -d "FinancialSystem-web/build" ]; then
        log_warning "前端build目录不存在，创建默认页面..."
        mkdir -p FinancialSystem-web/build
        cat > FinancialSystem-web/build/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>财务系统</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>财务系统正在启动中...</h1>
    <p>系统正在初始化，请稍后刷新页面。</p>
    <script>
        setTimeout(function() { location.reload(); }, 30000);
    </script>
</body>
</html>
EOF
    fi
    
    return 0
}

# 部署到服务器
deploy_with_minimal_setup() {
    log_info "🚀 使用最小化配置部署..."
    
    # 1. 创建部署包
    log_info "📦 创建部署包..."
    DEPLOY_PACKAGE="/tmp/financial-fixed-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    tar --exclude='.git' \
        --exclude='node_modules' \
        --exclude='FinancialSystem-web/node_modules' \
        --exclude='*.log' \
        --exclude='.DS_Store' \
        -czf "$DEPLOY_PACKAGE" . || {
        log_error "创建部署包失败"
        return 1
    }
    
    log_success "部署包创建成功: $DEPLOY_PACKAGE"
    
    # 2. 传输到服务器
    log_info "📤 传输到服务器..."
    scp "$DEPLOY_PACKAGE" "$LINUX_SERVER:/tmp/" || {
        log_error "传输失败"
        return 1
    }
    
    # 3. 在服务器上部署
    log_info "🚀 在服务器上部署..."
    ssh "$LINUX_SERVER" bash -s "$DEPLOY_PACKAGE" << 'EOF'
        set -e
        
        DEPLOY_PACKAGE="$1"
        DEPLOY_DIR="/opt/FinancialSystem"
        CURRENT_DIR="$DEPLOY_DIR/current"
        BACKUP_DIR="$DEPLOY_DIR/backup-$(date +%Y%m%d-%H%M%S)"
        
        echo "🔧 准备部署环境..."
        
        # 停止现有服务
        echo "🛑 停止现有服务..."
        # 停止并删除特定的容器
        for container in financial-mysql financial-backend financial-nginx; do
            docker stop $container 2>/dev/null || true
            docker rm $container 2>/dev/null || true
        done
        
        # 停止Java进程
        pkill -f "api-gateway.*jar" || true
        
        # 清理端口占用
        echo "🔧 清理端口占用..."
        for port in 8080 3306 80; do
            pid=$(lsof -ti:$port 2>/dev/null || true)
            if [ -n "$pid" ]; then
                echo "⚠️  杀死占用端口 $port 的进程 $pid"
                kill -9 $pid 2>/dev/null || true
            fi
        done
        
        # 备份现有部署
        if [ -d "$CURRENT_DIR" ]; then
            echo "📦 备份现有部署..."
            mv "$CURRENT_DIR" "$BACKUP_DIR"
        fi
        
        # 创建新的部署目录
        mkdir -p "$CURRENT_DIR"
        
        # 解压部署包
        echo "📦 解压部署包..."
        tar -xzf "/tmp/$(basename $DEPLOY_PACKAGE)" -C "$CURRENT_DIR" --strip-components=0
        
        # 进入部署目录
        cd "$CURRENT_DIR"
        
        # 检查必要文件
        echo "🔍 检查必要文件..."
        if [ ! -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
            echo "❌ JAR文件不存在"
            exit 1
        fi
        
        # 使用最小化配置
        if [ -f "docker-compose.minimal.yml" ]; then
            echo "🔧 使用最小化Docker配置..."
            cp docker-compose.minimal.yml docker-compose.yml
        fi
        
        # 启动服务
        echo "🚀 启动服务..."
        if docker compose up -d; then
            echo "✅ 服务启动成功"
        else
            echo "❌ 服务启动失败，尝试备用方案..."
            
            # 备用方案：只启动必要的服务
            echo "🔄 启动MySQL..."
            docker run -d --name financial-mysql \
                -p 3306:3306 \
                -e MYSQL_ROOT_PASSWORD=Zlb&198838 \
                mysql:8.0
            
            sleep 10
            
            echo "🔄 启动后端服务..."
            cd api-gateway/target
            nohup java -jar api-gateway-1.0-SNAPSHOT.jar \
                --spring.profiles.active=production \
                --spring.datasource.primary.url="**********************************************************************************************************************" \
                > ../financial-system.log 2>&1 &
            
            echo "✅ 备用方案部署完成"
        fi
        
        echo "✅ 部署完成"
EOF
    
    local deploy_result=$?
    
    # 清理临时文件
    rm -f "$DEPLOY_PACKAGE"
    
    if [ $deploy_result -eq 0 ]; then
        log_success "✅ 部署成功完成"
        return 0
    else
        log_error "❌ 部署失败"
        return 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "🔍 验证部署..."
    
    # 等待服务启动
    log_info "⏳ 等待服务启动..."
    sleep 30
    
    # 检查Docker容器状态
    ssh "$LINUX_SERVER" << 'EOF'
        echo "📊 检查Docker容器状态..."
        docker ps
        
        echo "📄 检查进程状态..."
        ps aux | grep -v grep | grep -E "(java|mysql)" || echo "⚠️  相关进程未找到"
EOF
    
    # 健康检查
    local max_attempts=10
    
    for attempt in $(seq 1 $max_attempts); do
        log_info "健康检查尝试 $attempt/$max_attempts..."
        
        # 检查后端
        if curl -sf "http://10.25.1.85:8080/actuator/health" > /dev/null; then
            log_success "✅ 后端服务健康检查通过"
            return 0
        else
            log_warning "⚠️  后端服务健康检查失败"
        fi
        
        if [ $attempt -lt $max_attempts ]; then
            log_info "等待10秒后重试..."
            sleep 10
        fi
    done
    
    log_warning "⚠️  健康检查未完全通过，检查日志..."
    ssh "$LINUX_SERVER" "tail -20 /opt/FinancialSystem/current/api-gateway/financial-system.log 2>/dev/null || echo '日志文件不存在'"
    
    return 1
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem修复版自动部署..."
    
    # 检查依赖
    for cmd in mvn curl ssh scp tar; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要的命令: $cmd"
            exit 1
        fi
    done
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 构建项目
    if ! build_project_locally; then
        log_error "项目构建失败"
        exit 1
    fi
    
    # 执行部署
    if deploy_with_minimal_setup; then
        log_success "🎉 修复版部署成功完成！"
        
        # 验证部署
        if verify_deployment; then
            log_success "🎉 部署验证成功！"
        else
            log_warning "⚠️  部署验证未完全通过，但服务可能正在启动中"
        fi
        
        # 显示访问信息
        log_info "🌐 访问地址:"
        log_info "   - 后端API: http://10.25.1.85:8080/"
        log_info "   - 健康检查: http://10.25.1.85:8080/actuator/health"
        log_info "   - 日志查看: ssh admin@10.25.1.85 'tail -f /opt/FinancialSystem/current/api-gateway/financial-system.log'"
        
        exit 0
    else
        log_error "❌ 修复版部署失败"
        exit 1
    fi
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi