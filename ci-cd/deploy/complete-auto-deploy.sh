#!/bin/bash

# FinancialSystem 完整自动部署脚本
# 支持本地构建 + 传输 + 服务器部署的完整流程

set -e

# 配置变量
PROJECT_ROOT="${PROJECT_ROOT:-$(pwd)}"
LINUX_SERVER="${LINUX_SERVER:-admin@**********}"
DEPLOY_DIR="/opt/FinancialSystem"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 本地构建
local_build() {
    log_info "📦 本地构建项目..."
    cd "$PROJECT_ROOT"
    
    # 构建后端
    if mvn clean package -DskipTests -pl api-gateway -am; then
        log_success "后端构建成功"
    else
        log_error "后端构建失败"
        return 1
    fi
    
    # 检查JAR文件
    if [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
        log_success "JAR文件已生成"
    else
        log_error "JAR文件未找到"
        return 1
    fi
    
    return 0
}

# 传输和部署
deploy_to_server() {
    log_info "🚀 传输和部署到服务器..."
    
    # 1. 创建临时部署目录
    TEMP_DEPLOY_DIR="/tmp/financial-auto-deploy-$(date +%Y%m%d-%H%M%S)"
    
    # 2. 在服务器上准备环境
    ssh "$LINUX_SERVER" "mkdir -p $TEMP_DEPLOY_DIR"
    
    # 3. 传输项目文件
    log_info "📤 传输项目文件..."
    rsync -avz --exclude='.git' \
              --exclude='node_modules' \
              --exclude='*.log' \
              --exclude='.DS_Store' \
              "$PROJECT_ROOT/" "$LINUX_SERVER:$TEMP_DEPLOY_DIR/"
    
    # 4. 在服务器上执行部署
    log_info "🚀 在服务器上执行部署..."
    ssh "$LINUX_SERVER" bash -s "$TEMP_DEPLOY_DIR" << 'EOF'
        set -e
        
        TEMP_DEPLOY_DIR="$1"
        DEPLOY_DIR="/opt/FinancialSystem"
        CURRENT_DIR="$DEPLOY_DIR/current"
        BACKUP_DIR="$DEPLOY_DIR/backup-$(date +%Y%m%d-%H%M%S)"
        
        # 颜色定义
        BLUE='\033[0;34m'
        GREEN='\033[0;32m'
        YELLOW='\033[1;33m'
        NC='\033[0m'
        
        log_info() {
            echo -e "${BLUE}[INFO]${NC} $1"
        }
        
        log_success() {
            echo -e "${GREEN}[SUCCESS]${NC} $1"
        }
        
        log_warning() {
            echo -e "${YELLOW}[WARNING]${NC} $1"
        }
        
        log_info "🚀 开始服务器端部署..."
        
        # 备份现有部署
        if [ -d "$CURRENT_DIR" ]; then
            log_info "📦 备份现有部署到: $BACKUP_DIR"
            cp -r "$CURRENT_DIR" "$BACKUP_DIR"
        fi
        
        # 停止现有服务
        log_info "🛑 停止现有服务..."
        for container in financial-mysql financial-backend financial-nginx; do
            docker stop $container 2>/dev/null || true
            docker rm $container 2>/dev/null || true
        done
        pkill -f "api-gateway.*jar" || true
        
        # 清理端口占用
        for port in 8080 3306 80; do
            pid=$(lsof -ti:$port 2>/dev/null || true)
            if [ -n "$pid" ]; then
                kill -9 $pid 2>/dev/null || true
            fi
        done
        
        # 部署新版本
        log_info "📁 部署新版本..."
        rm -rf "$CURRENT_DIR"
        mkdir -p "$CURRENT_DIR"
        cp -r "$TEMP_DEPLOY_DIR"/* "$CURRENT_DIR"/
        cd "$CURRENT_DIR"
        
        # 确保前端build目录存在
        if [ ! -d "FinancialSystem-web/build" ]; then
            log_warning "创建默认前端页面..."
            mkdir -p FinancialSystem-web/build
            cat > FinancialSystem-web/build/index.html << 'HTMLEOF'
<!DOCTYPE html>
<html>
<head>
    <title>财务系统</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .status { color: #0066cc; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💼 财务系统</h1>
        <p class="status">系统已成功部署并启动！</p>
        <p>如果您看到此页面，说明自动部署功能正常工作。</p>
        <p>后端API服务正在初始化中，请稍候...</p>
    </div>
    <script>
        setTimeout(function() { location.reload(); }, 30000);
    </script>
</body>
</html>
HTMLEOF
        fi
        
        # 使用最小化配置
        if [ -f "docker-compose.minimal.yml" ]; then
            log_info "🔧 使用最小化Docker配置..."
            cp docker-compose.minimal.yml docker-compose.yml
        fi
        
        # 启动服务
        log_info "🚀 启动Docker服务..."
        if docker compose up -d; then
            log_success "Docker服务启动成功"
        else
            log_warning "Docker启动失败，使用备用方案..."
            
            # 备用方案
            docker run -d --name financial-mysql \
                -p 3306:3306 \
                -e MYSQL_ROOT_PASSWORD=Zlb&198838 \
                mysql:8.0
            
            sleep 15
            
            cd api-gateway/target
            nohup java -jar api-gateway-1.0-SNAPSHOT.jar \
                --spring.profiles.active=production \
                > ../financial-system.log 2>&1 &
            
            docker run -d --name financial-nginx \
                -p 80:80 \
                -v "$CURRENT_DIR/FinancialSystem-web/build:/usr/share/nginx/html:ro" \
                nginx:alpine
        fi
        
        # 等待服务启动
        sleep 30
        
        # 检查服务状态
        log_info "📊 检查服务状态:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        # 清理临时目录
        rm -rf "$TEMP_DEPLOY_DIR"
        
        log_success "✅ 自动部署完成！"
        
        # 显示访问信息
        SERVER_IP=$(hostname -I | awk '{print $1}')
        echo ""
        log_info "🌐 访问地址:"
        log_info "   - 前端: http://$SERVER_IP/"
        log_info "   - 后端API: http://$SERVER_IP:8080/"
        log_info "   - 健康检查: http://$SERVER_IP:8080/actuator/health"
EOF
    
    local deploy_result=$?
    
    if [ $deploy_result -eq 0 ]; then
        log_success "✅ 完整自动部署成功"
        return 0
    else
        log_error "❌ 完整自动部署失败"
        return 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "🔍 验证部署..."
    
    # 等待服务完全启动
    sleep 60
    
    # 检查前端
    if curl -sf "http://**********/" > /dev/null; then
        log_success "✅ 前端服务正常"
    else
        log_warning "⚠️  前端服务检查失败"
    fi
    
    # 检查后端（可能需要更长时间启动）
    local backend_ok=false
    for i in {1..10}; do
        if curl -sf "http://**********:8080/actuator/health" > /dev/null; then
            log_success "✅ 后端API服务正常"
            backend_ok=true
            break
        fi
        log_info "等待后端服务启动... ($i/10)"
        sleep 30
    done
    
    if [ "$backend_ok" = false ]; then
        log_warning "⚠️  后端服务可能仍在启动中"
    fi
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem完整自动部署..."
    
    # 检查依赖
    for cmd in mvn curl ssh rsync; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要的命令: $cmd"
            exit 1
        fi
    done
    
    # 执行本地构建
    if ! local_build; then
        log_error "本地构建失败"
        exit 1
    fi
    
    # 执行部署
    if deploy_to_server; then
        log_success "🎉 完整自动部署成功！"
        
        # 验证部署
        verify_deployment
        
        log_success "🎉 FinancialSystem自动部署完成！"
        log_info "🌐 系统访问地址: http://**********/"
        
        exit 0
    else
        log_error "❌ 完整自动部署失败"
        exit 1
    fi
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi