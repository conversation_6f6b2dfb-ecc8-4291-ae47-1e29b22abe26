#!/bin/bash
# 服务器端专用部署脚本 - 不包含本地构建

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
DEPLOY_DIR="/opt/FinancialSystem/current"

# 主部署流程
main() {
    log "🚀 开始服务器端部署..."
    
    cd "$DEPLOY_DIR"
    
    # 检查必要文件
    log "📋 检查必要文件..."
    if [ ! -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
        error "JAR文件不存在，请先执行本地构建并同步"
        exit 1
    fi
    
    if [ ! -d "FinancialSystem-web/build" ]; then
        error "前端构建文件不存在，请先执行本地构建并同步"
        exit 1
    fi
    
    success "文件检查通过"
    
    # 检查Docker服务
    log "🐳 检查Docker服务..."
    if ! docker info >/dev/null 2>&1; then
        error "Docker服务未运行"
        exit 1
    fi
    success "Docker服务正常"
    
    # 停止现有服务
    log "🛑 停止现有服务..."
    if [ -f "docker-compose.prod.yml" ]; then
        docker compose -f docker-compose.prod.yml down || true
    else
        docker compose down || true
    fi
    
    # 清理
    docker network prune -f || true
    
    # 检查Docker镜像
    log "📦 检查Docker镜像..."
    MISSING_IMAGES=()
    for image in mysql openjdk nginx; do
        if ! docker images | grep -q "^$image"; then
            MISSING_IMAGES+=($image)
        fi
    done
    
    if [ ${#MISSING_IMAGES[@]} -gt 0 ]; then
        error "缺少以下Docker镜像: ${MISSING_IMAGES[*]}"
        error "请确保服务器上有必要的Docker镜像"
        exit 1
    fi
    success "Docker镜像检查通过"
    
    # 启动服务
    log "🚀 启动服务..."
    if [ -f "docker-compose.prod.yml" ]; then
        docker compose -f docker-compose.prod.yml up -d
    else
        docker compose up -d
    fi
    
    # 等待服务启动
    log "⏳ 等待服务启动..."
    sleep 30
    
    # 检查服务状态
    log "📊 检查服务状态..."
    docker compose ps
    
    # 健康检查
    log "🏥 执行健康检查..."
    
    # 检查MySQL
    log "检查MySQL服务..."
    if docker exec financial-mysql mysqladmin ping -h localhost -uroot -p'Zlb&198838' >/dev/null 2>&1; then
        success "MySQL服务正常"
        
        # 确保数据库存在
        docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e "
            CREATE DATABASE IF NOT EXISTS \`逾期债权数据库\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            CREATE DATABASE IF NOT EXISTS \`user_system\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            CREATE DATABASE IF NOT EXISTS \`kingdee\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        " 2>/dev/null || true
        
        # 执行初始化脚本
        if [ -f "init-scripts/01-init-databases.sql" ]; then
            log "执行数据库初始化脚本..."
            docker exec -i financial-mysql mysql -uroot -p'Zlb&198838' < init-scripts/01-init-databases.sql 2>/dev/null || true
            success "数据库初始化完成"
        fi
    else
        warning "MySQL服务未就绪"
    fi
    
    # 检查后端
    log "检查后端服务..."
    MAX_RETRIES=10
    RETRY_COUNT=0
    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if curl -sf http://localhost:8080/actuator/health >/dev/null 2>&1; then
            success "后端服务正常"
            break
        else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
                warning "后端服务健康检查超时"
                docker compose logs backend --tail 20
            else
                log "后端服务未就绪，等待中... ($RETRY_COUNT/$MAX_RETRIES)"
                sleep 10
            fi
        fi
    done
    
    # 检查前端
    log "检查前端服务..."
    if curl -sf http://localhost/ >/dev/null 2>&1; then
        success "前端服务正常"
    else
        warning "前端服务检查失败"
    fi
    
    # 部署完成
    success "🎉 部署完成！"
    log "📋 访问信息:"
    log "  前端: http://**********/"
    log "  后端API: http://**********:8080/"
    log "  数据库: **********:3306"
    log ""
    log "📝 管理命令:"
    log "  查看服务状态: docker compose ps"
    log "  查看日志: docker compose logs -f"
    log "  重启服务: docker compose restart"
}

# 执行主流程
main "$@"