#!/bin/bash

# FinancialSystem 本地镜像部署脚本
# 专门为使用本地Docker镜像的环境设计

set -e

# 配置变量
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
LINUX_SERVER="admin@**********"
DEPLOY_DIR="/opt/FinancialSystem"
DOCKER_COMPOSE_FILE="docker-compose.local.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查本地镜像是否存在
check_local_images() {
    log_info "检查本地Docker镜像..."
    
    ssh "$LINUX_SERVER" << 'EOF'
        # 检查必要的镜像
        IMAGES=(
            "mysql:8.0"
            "maven:3.9-eclipse-temurin-21"
            "openjdk:21-jdk"
            "node:18-alpine"
            "nginx:alpine"
        )
        
        missing_images=()
        
        for image in "${IMAGES[@]}"; do
            if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$image$"; then
                missing_images+=("$image")
            fi
        done
        
        if [ ${#missing_images[@]} -gt 0 ]; then
            echo "⚠️  以下镜像缺失:"
            for image in "${missing_images[@]}"; do
                echo "   - $image"
            done
            echo "请运行镜像准备脚本或手动拉取镜像"
            exit 1
        else
            echo "✅ 所有必要的Docker镜像都已准备好"
        fi
EOF
}

# 准备部署环境
prepare_deploy_environment() {
    log_info "准备部署环境..."
    
    ssh "$LINUX_SERVER" << 'EOF'
        # 创建部署目录
        mkdir -p /opt/FinancialSystem
        
        # 停止现有服务
        echo "🛑 停止现有服务..."
        pkill -f "api-gateway.*jar" || true
        pkill -f "java.*jar" || true
        
        # 停止Docker服务
        echo "🐳 停止Docker服务..."
        if [ -d "/opt/FinancialSystem/current" ]; then
            cd /opt/FinancialSystem/current
            if [ -f "docker-compose.yml" ]; then
                docker compose down || true
            fi
        fi
        
        # 清理端口占用
        echo "🔧 清理端口占用..."
        for port in 8080 3306 80; do
            pid=$(lsof -ti:$port 2>/dev/null || true)
            if [ -n "$pid" ]; then
                echo "⚠️  杀死占用端口 $port 的进程 $pid"
                kill -9 $pid 2>/dev/null || true
            fi
        done
        
        # 等待端口释放
        sleep 5
        
        # 清理Docker资源
        echo "🧹 清理Docker资源..."
        docker system prune -f || true
        
        echo "✅ 环境准备完成"
EOF
}

# 主要部署函数
deploy_with_local_images() {
    log_info "🚀 开始本地镜像部署流程..."
    
    cd "$PROJECT_ROOT"
    
    # 1. 检查本地镜像
    if ! check_local_images; then
        log_error "本地镜像检查失败"
        return 1
    fi
    
    # 2. 准备部署环境
    prepare_deploy_environment
    
    # 3. 创建部署包
    log_info "📦 创建部署包..."
    DEPLOY_PACKAGE="/tmp/financial-local-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # 确保使用本地镜像的配置文件
    if [ ! -f "$DOCKER_COMPOSE_FILE" ]; then
        log_error "本地镜像配置文件不存在: $DOCKER_COMPOSE_FILE"
        return 1
    fi
    
    # 创建部署包，确保包含本地镜像配置
    tar --exclude='.git' \
        --exclude='node_modules' \
        --exclude='target' \
        --exclude='FinancialSystem-web/node_modules' \
        --exclude='FinancialSystem-web/build' \
        --exclude='*.log' \
        --exclude='.DS_Store' \
        -czf "$DEPLOY_PACKAGE" . || {
        log_error "创建部署包失败"
        return 1
    }
    
    log_success "部署包创建成功: $DEPLOY_PACKAGE"
    
    # 4. 传输到服务器
    log_info "📤 传输到服务器..."
    scp "$DEPLOY_PACKAGE" "$LINUX_SERVER:/tmp/" || {
        log_error "传输失败"
        return 1
    }
    
    # 5. 在服务器上部署
    log_info "🚀 在服务器上部署..."
    ssh "$LINUX_SERVER" bash -s "$DEPLOY_PACKAGE" "$DOCKER_COMPOSE_FILE" << 'EOF'
        set -e
        
        DEPLOY_PACKAGE="$1"
        DOCKER_COMPOSE_FILE="$2"
        DEPLOY_DIR="/opt/FinancialSystem"
        CURRENT_DIR="$DEPLOY_DIR/current"
        BACKUP_DIR="$DEPLOY_DIR/backup-$(date +%Y%m%d-%H%M%S)"
        
        echo "🔧 部署新版本..."
        
        # 备份现有部署
        if [ -d "$CURRENT_DIR" ]; then
            echo "📦 备份现有部署..."
            mv "$CURRENT_DIR" "$BACKUP_DIR"
        fi
        
        # 创建新的部署目录
        mkdir -p "$CURRENT_DIR"
        
        # 解压部署包
        echo "📦 解压部署包..."
        tar -xzf "/tmp/$(basename $DEPLOY_PACKAGE)" -C "$CURRENT_DIR" --strip-components=0
        
        # 进入部署目录
        cd "$CURRENT_DIR"
        
        # 使用本地镜像配置文件
        if [ -f "$DOCKER_COMPOSE_FILE" ]; then
            echo "🔧 使用本地镜像配置: $DOCKER_COMPOSE_FILE"
            cp "$DOCKER_COMPOSE_FILE" docker-compose.yml
        else
            echo "❌ 本地镜像配置文件不存在: $DOCKER_COMPOSE_FILE"
            exit 1
        fi
        
        # 设置执行权限
        find . -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true
        
        # 显示配置信息
        echo "🔍 Docker Compose配置信息:"
        docker compose config --services
        
        # 启动服务
        echo "🚀 启动Docker服务..."
        if docker compose up -d --remove-orphans; then
            echo "✅ Docker服务启动成功"
        else
            echo "❌ Docker服务启动失败"
            exit 1
        fi
        
        # 显示服务状态
        echo "📊 服务状态:"
        docker compose ps
        
        echo "✅ 部署完成"
EOF
    
    local deploy_result=$?
    
    # 清理临时文件
    rm -f "$DEPLOY_PACKAGE"
    
    if [ $deploy_result -eq 0 ]; then
        log_success "✅ 部署成功完成"
        return 0
    else
        log_error "❌ 部署失败"
        return 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "🔍 验证部署..."
    
    # 等待服务启动
    log_info "⏳ 等待服务启动..."
    sleep 30
    
    # 检查服务状态
    ssh "$LINUX_SERVER" << 'EOF'
        echo "📊 检查服务状态..."
        cd /opt/FinancialSystem/current
        docker compose ps
        
        echo "🔍 检查容器健康状态..."
        docker compose exec mysql mysqladmin ping -h localhost || echo "⚠️  MySQL未就绪"
EOF
    
    # 健康检查
    local health_checks=0
    local max_attempts=10
    
    for attempt in $(seq 1 $max_attempts); do
        log_info "健康检查尝试 $attempt/$max_attempts..."
        
        # 检查后端
        if curl -sf "http://**********:8080/actuator/health" > /dev/null; then
            log_success "✅ 后端服务健康检查通过"
            health_checks=$((health_checks + 1))
        else
            log_warning "⚠️  后端服务健康检查失败"
        fi
        
        # 检查前端
        if curl -sf "http://**********/" > /dev/null; then
            log_success "✅ 前端服务健康检查通过"
            health_checks=$((health_checks + 1))
        else
            log_warning "⚠️  前端服务健康检查失败"
        fi
        
        if [ $health_checks -eq 2 ]; then
            log_success "✅ 所有服务健康检查通过"
            return 0
        fi
        
        if [ $attempt -lt $max_attempts ]; then
            log_info "等待10秒后重试..."
            sleep 10
        fi
        
        health_checks=0
    done
    
    log_warning "⚠️  部分服务健康检查失败，但部署可能仍在进行中"
    return 1
}

# 主函数
main() {
    log_info "🚀 开始FinancialSystem本地镜像部署..."
    
    # 检查依赖
    for cmd in curl ssh scp tar; do
        if ! command -v $cmd &> /dev/null; then
            log_error "缺少必要的命令: $cmd"
            exit 1
        fi
    done
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    # 检查本地镜像配置文件
    if [ ! -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" ]; then
        log_error "本地镜像配置文件不存在: $PROJECT_ROOT/$DOCKER_COMPOSE_FILE"
        exit 1
    fi
    
    # 执行部署
    if deploy_with_local_images; then
        log_success "🎉 本地镜像部署成功完成！"
        
        # 验证部署
        if verify_deployment; then
            log_success "🎉 部署验证成功！"
        else
            log_warning "⚠️  部署验证未完全通过，请检查服务状态"
        fi
        
        # 显示访问信息
        log_info "🌐 访问地址:"
        log_info "   - 前端: http://**********/"
        log_info "   - 后端API: http://**********:8080/"
        log_info "   - 健康检查: http://**********:8080/actuator/health"
        
        exit 0
    else
        log_error "❌ 本地镜像部署失败"
        exit 1
    fi
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi