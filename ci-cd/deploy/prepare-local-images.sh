#!/bin/bash
# 准备本地Docker镜像脚本
# 用于在Linux服务器上预先准备所需的Docker镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 需要的镜像列表
REQUIRED_IMAGES=(
    "mysql:8.0"
    "openjdk:21-jdk"
    "maven:3.9-eclipse-temurin-21"
    "node:18-alpine"
    "nginx:alpine"
)

# 检查并保存本地镜像
log_info "开始准备本地Docker镜像..."

for image in "${REQUIRED_IMAGES[@]}"; do
    log_info "检查镜像: $image"
    
    if docker images | grep -q "$(echo $image | cut -d: -f1).*$(echo $image | cut -d: -f2)"; then
        log_success "镜像已存在: $image"
    else
        log_info "镜像不存在，尝试从本地tar文件加载: $image"
        
        # 尝试从预存的tar文件加载
        image_name=$(echo $image | sed 's/:/-/g' | sed 's/\//_/g')
        tar_file="/opt/docker-images/${image_name}.tar"
        
        if [ -f "$tar_file" ]; then
            log_info "从tar文件加载镜像: $tar_file"
            docker load -i "$tar_file"
            log_success "镜像加载成功: $image"
        else
            log_error "未找到本地镜像文件: $tar_file"
            log_info "需要手动拉取镜像: docker pull $image"
        fi
    fi
done

# 创建镜像备份目录
BACKUP_DIR="/opt/docker-images"
if [ ! -d "$BACKUP_DIR" ]; then
    log_info "创建镜像备份目录: $BACKUP_DIR"
    sudo mkdir -p "$BACKUP_DIR"
    sudo chown -R $(whoami):$(whoami) "$BACKUP_DIR"
fi

# 保存现有镜像为tar文件
log_info "保存现有镜像为tar文件以备后用..."

for image in "${REQUIRED_IMAGES[@]}"; do
    if docker images | grep -q "$(echo $image | cut -d: -f1).*$(echo $image | cut -d: -f2)"; then
        image_name=$(echo $image | sed 's/:/-/g' | sed 's/\//_/g')
        tar_file="$BACKUP_DIR/${image_name}.tar"
        
        if [ ! -f "$tar_file" ]; then
            log_info "保存镜像: $image -> $tar_file"
            docker save -o "$tar_file" "$image"
            log_success "镜像保存成功: $tar_file"
        else
            log_info "镜像备份已存在: $tar_file"
        fi
    fi
done

# 创建docker-compose配置符号链接
log_info "创建docker-compose配置链接..."
if [ -f "docker-compose.local.yml" ]; then
    if [ -f "docker-compose.yml" ]; then
        mv docker-compose.yml docker-compose.original.yml
    fi
    ln -sf docker-compose.local.yml docker-compose.yml
    log_success "已使用本地镜像配置"
fi

log_success "本地镜像准备完成！"
log_info "镜像状态："
docker images | grep -E "mysql|openjdk|maven|node|nginx" || echo "没有找到相关镜像"