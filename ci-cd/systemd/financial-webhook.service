[Unit]
Description=FinancialSystem Webhook Service
Documentation=https://github.com/your-repo/FinancialSystem
After=network.target
Wants=network.target

[Service]
Type=simple
User=admin
Group=admin
WorkingDirectory=/home/<USER>/下载/FinancialSystem-Production-Deploy
ExecStart=/usr/bin/python3 /home/<USER>/下载/FinancialSystem-Production-Deploy/ci-cd/webhook-server/webhook-server.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=financial-webhook

# 环境变量
Environment=PYTHONPATH=/home/<USER>/下载/FinancialSystem-Production-Deploy
Environment=WEBHOOK_PORT=9000
Environment=DEPLOY_SCRIPT=/home/<USER>/下载/FinancialSystem-Production-Deploy/deploy-complete.sh

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/下载/FinancialSystem-Production-Deploy
ReadWritePaths=/var/log
ReadWritePaths=/tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
