[Unit]
Description=FinancialSystem Health Monitor Service
After=network.target docker.service
Requires=docker.service

[Service]
Type=simple
User=admin
Group=admin
WorkingDirectory=/opt/FinancialSystem/current
ExecStart=/opt/FinancialSystem/current/ci-cd/deploy/enhanced-health-monitor.sh monitor
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=30
StandardOutput=append:/var/log/financial-system/monitor.log
StandardError=append:/var/log/financial-system/monitor-error.log

# 环境变量
Environment="CHECK_INTERVAL=60"
Environment="MAX_FAILURES=3"
Environment="RECOVERY_ENABLED=true"

# 资源限制
LimitNOFILE=4096
MemoryLimit=512M
CPUQuota=20%

[Install]
WantedBy=multi-user.target