#!/bin/bash

# FinancialSystem Git Post-Receive Hook
# 当代码推送到main分支时自动触发部署

set -e

echo "🔄 Git Post-Receive Hook 开始执行..."

# 配置变量
DEPLOY_BRANCH="main"
LINUX_SERVER="admin@10.25.1.85"
LINUX_DEPLOY_PATH="/home/<USER>/下载/FinancialSystem-Production-Deploy"
LOCAL_BACKUP_PATH="/Volumes/ExternalSSD-1T/08.program/FinancialSystem-Backups"
WEBHOOK_URL="http://10.25.1.85:9000/webhook"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取推送信息
while read oldrev newrev refname; do
    branch=$(git rev-parse --symbolic --abbrev-ref $refname)
    
    log_info "检测到推送: 分支 $branch"
    log_info "旧版本: $oldrev"
    log_info "新版本: $newrev"
    
    # 只处理main分支的推送
    if [ "$branch" = "$DEPLOY_BRANCH" ]; then
        log_info "🚀 检测到main分支推送，开始自动部署流程..."
        
        # 1. 创建本地备份
        log_info "📦 创建本地备份..."
        mkdir -p "$LOCAL_BACKUP_PATH"
        BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)-$newrev"
        
        # 备份当前代码
        git archive --format=tar.gz --prefix="$BACKUP_NAME/" $newrev > "$LOCAL_BACKUP_PATH/$BACKUP_NAME.tar.gz"
        log_success "本地备份创建完成: $LOCAL_BACKUP_PATH/$BACKUP_NAME.tar.gz"
        
        # 2. 推送到Linux服务器
        log_info "🔄 推送代码到Linux服务器..."
        
        # 创建部署包
        TEMP_DIR=$(mktemp -d)
        git archive --format=tar $newrev | tar -x -C "$TEMP_DIR"
        
        # 打包并传输
        cd "$TEMP_DIR"
        tar -czf "deploy-$BACKUP_NAME.tar.gz" .
        
        # 传输到Linux服务器
        scp "deploy-$BACKUP_NAME.tar.gz" "$LINUX_SERVER:/tmp/"
        
        # 3. 触发增强版自动部署（支持失败回滚）
        log_info "🚀 触发增强版自动部署（支持失败回滚）..."
        
        # 调用增强版部署脚本
        SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
        ENHANCED_DEPLOY_SCRIPT="$SCRIPT_DIR/../deploy/auto-deploy-with-rollback.sh"
        STANDARD_DEPLOY_SCRIPT="$SCRIPT_DIR/../deploy/auto-deploy-trigger.sh"
        
        if [ -f "$ENHANCED_DEPLOY_SCRIPT" ]; then
            log_info "使用增强版部署脚本（支持自动回滚）..."
            bash "$ENHANCED_DEPLOY_SCRIPT"
            DEPLOY_RESULT=$?
        elif [ -f "$STANDARD_DEPLOY_SCRIPT" ]; then
            log_info "使用标准部署脚本..."
            bash "$STANDARD_DEPLOY_SCRIPT"
            DEPLOY_RESULT=$?
        else
            log_error "部署脚本不存在"
            DEPLOY_RESULT=1
        fi
        
        # 根据部署结果发送不同的通知
        if [ $DEPLOY_RESULT -eq 0 ]; then
            log_success "✅ 部署成功完成"
            DEPLOY_STATUS="success"
        else
            log_error "❌ 部署失败，已自动回滚"
            DEPLOY_STATUS="failed"
        fi
        
        # 4. 验证部署
        log_info "🔍 验证部署状态..."
        
        ssh "$LINUX_SERVER" << EOF
            cd "$LINUX_DEPLOY_PATH"
            if [ -f "verify-deployment.sh" ]; then
                chmod +x verify-deployment.sh
                ./verify-deployment.sh
            else
                echo "⚠️  验证脚本不存在，跳过验证"
            fi
EOF
        
        # 5. 发送通知
        log_info "📧 发送部署通知..."
        
        # 发送Webhook通知（如果配置了）
        if command -v curl &> /dev/null; then
            curl -X POST "$WEBHOOK_URL" \
                -H "Content-Type: application/json" \
                -d "{
                    \"event\": \"deployment_completed\",
                    \"branch\": \"$branch\",
                    \"commit\": \"$newrev\",
                    \"timestamp\": \"$(date -Iseconds)\",
                    \"status\": \"$DEPLOY_STATUS\"
                }" || log_warning "Webhook通知发送失败"
        fi
        
        # 清理临时目录
        rm -rf "$TEMP_DIR"
        
        log_success "🎉 自动部署流程完成！"
        log_info "📊 部署摘要:"
        log_info "   - 分支: $branch"
        log_info "   - 提交: $newrev"
        log_info "   - 备份: $BACKUP_NAME"
        log_info "   - 时间: $(date)"
        
    else
        log_info "⏭️  非main分支推送，跳过自动部署"
    fi
done

echo "✅ Git Post-Receive Hook 执行完成"
