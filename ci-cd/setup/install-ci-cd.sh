#!/bin/bash

# FinancialSystem CI/CD 安装配置脚本
# 用于设置完整的自动化部署环境

set -e

# 配置变量
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
LINUX_SERVER="admin@10.25.1.85"
LINUX_DEPLOY_PATH="/home/<USER>/下载/FinancialSystem-Production-Deploy"
CI_CD_DIR="$(dirname "$(dirname "$0")")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 检查前置条件
check_prerequisites() {
    log_step "检查前置条件..."

    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi

    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安装"
        exit 1
    fi

    # 检查SSH连接
    log_info "测试SSH连接到 $LINUX_SERVER..."
    if ssh -o ConnectTimeout=10 -o BatchMode=no "$LINUX_SERVER" "echo 'SSH连接正常'" 2>/dev/null; then
        log_success "SSH连接测试通过"
    else
        log_warning "SSH连接需要密码认证，这是正常的"
    fi

    log_success "前置条件检查通过"
}

# 安装本地Git Hooks
install_git_hooks() {
    log_step "安装Git Hooks..."

    cd "$PROJECT_ROOT"

    # 创建hooks目录
    mkdir -p .git/hooks

    # 复制post-receive hook
    if [ -f "$CI_CD_DIR/git-hooks/post-receive" ]; then
        cp "$CI_CD_DIR/git-hooks/post-receive" .git/hooks/
        chmod +x .git/hooks/post-receive
        log_success "Git post-receive hook已安装"
    else
        log_warning "Git hook文件不存在: $CI_CD_DIR/git-hooks/post-receive"
    fi

    # 创建pre-push hook（可选）
    cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash
# 推送前检查
echo "🔍 执行推送前检查..."

# 检查是否在main分支
current_branch=$(git branch --show-current)
if [ "$current_branch" = "main" ]; then
    echo "⚠️  正在推送到main分支，将触发自动部署"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 推送已取消"
        exit 1
    fi
fi

echo "✅ 推送前检查通过"
EOF

    chmod +x .git/hooks/pre-push
    log_success "Git pre-push hook已安装"
}

# 配置Linux服务器
setup_linux_server() {
    log_step "配置Linux服务器..."

    # 传输CI/CD文件到Linux服务器
    log_info "传输CI/CD文件到Linux服务器..."

    ssh "$LINUX_SERVER" "mkdir -p $LINUX_DEPLOY_PATH/ci-cd"

    # 传输webhook服务器
    scp -r "$CI_CD_DIR/webhook-server" "$LINUX_SERVER:$LINUX_DEPLOY_PATH/ci-cd/"

    # 传输systemd服务文件
    scp "$CI_CD_DIR/systemd/financial-webhook.service" "$LINUX_SERVER:/tmp/"

    # 在Linux服务器上配置
    ssh "$LINUX_SERVER" << EOF
        set -e

        echo "🔧 配置Linux服务器..."

        # 安装Python依赖
        if command -v python3 &> /dev/null; then
            echo "✅ Python3已安装"
        else
            echo "❌ Python3未安装，请先安装Python3"
            exit 1
        fi

        # 设置权限
        chmod +x "$LINUX_DEPLOY_PATH/ci-cd/webhook-server/webhook-server.py"

        # 安装systemd服务
        sudo cp /tmp/financial-webhook.service /etc/systemd/system/
        sudo systemctl daemon-reload

        # 创建日志目录
        sudo mkdir -p /var/log
        sudo touch /var/log/financial-webhook.log
        sudo chown admin:admin /var/log/financial-webhook.log

        # 启用并启动webhook服务
        sudo systemctl enable financial-webhook
        sudo systemctl start financial-webhook

        # 检查服务状态
        if systemctl is-active --quiet financial-webhook; then
            echo "✅ Webhook服务已启动"
        else
            echo "❌ Webhook服务启动失败"
            sudo systemctl status financial-webhook
            exit 1
        fi

        # 配置防火墙（如果需要）
        if command -v firewall-cmd &> /dev/null; then
            sudo firewall-cmd --permanent --add-port=9000/tcp
            sudo firewall-cmd --reload
            echo "✅ 防火墙已配置"
        fi

        echo "🎉 Linux服务器配置完成"
EOF

    log_success "Linux服务器配置完成"
}

# 创建本地配置文件
create_local_config() {
    log_step "创建本地配置文件..."

    # 创建CI/CD配置文件
    cat > "$PROJECT_ROOT/.ci-cd-config" << EOF
# FinancialSystem CI/CD 配置文件
# 此文件包含自动化部署的配置信息

# 服务器配置
LINUX_SERVER="$LINUX_SERVER"
LINUX_DEPLOY_PATH="$LINUX_DEPLOY_PATH"
WEBHOOK_URL="http://$LINUX_SERVER:9000"

# 备份配置
BACKUP_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem-Backups"
BACKUP_RETENTION_DAYS=30

# 通知配置
NOTIFICATION_EMAIL=""
SLACK_WEBHOOK=""

# 部署配置
AUTO_DEPLOY_ENABLED=true
DEPLOY_BRANCH="main"
BUILD_TIMEOUT=1800
DEPLOY_TIMEOUT=1800

# 生成时间
GENERATED_AT="$(date)"
EOF

    log_success "本地配置文件已创建: $PROJECT_ROOT/.ci-cd-config"
}

# 创建快捷脚本
create_shortcuts() {
    log_step "创建快捷脚本..."

    # 创建手动部署脚本
    cat > "$PROJECT_ROOT/deploy-now.sh" << EOF
#!/bin/bash
# 手动触发部署脚本

set -e

echo "🚀 手动触发FinancialSystem部署..."

# 加载配置
if [ -f ".ci-cd-config" ]; then
    source .ci-cd-config
fi

# 执行部署触发脚本
if [ -f "$CI_CD_DIR/deploy/auto-deploy-trigger.sh" ]; then
    "$CI_CD_DIR/deploy/auto-deploy-trigger.sh"
else
    echo "❌ 部署脚本不存在"
    exit 1
fi
EOF

    chmod +x "$PROJECT_ROOT/deploy-now.sh"

    # 创建备份脚本
    cat > "$PROJECT_ROOT/backup-now.sh" << EOF
#!/bin/bash
# 手动触发备份脚本

set -e

echo "📦 手动触发FinancialSystem备份..."

# 执行备份脚本
if [ -f "$CI_CD_DIR/backup/auto-backup.sh" ]; then
    "$CI_CD_DIR/backup/auto-backup.sh"
else
    echo "❌ 备份脚本不存在"
    exit 1
fi
EOF

    chmod +x "$PROJECT_ROOT/backup-now.sh"

    # 创建状态检查脚本
    cat > "$PROJECT_ROOT/check-status.sh" << EOF
#!/bin/bash
# 检查CI/CD状态脚本

set -e

echo "🔍 检查FinancialSystem CI/CD状态..."

# 检查本地Git状态
echo "📊 本地Git状态:"
git status --porcelain
echo "当前分支: \$(git branch --show-current)"
echo "最新提交: \$(git log -1 --oneline)"
echo

# 检查Linux服务器状态
echo "🖥️  Linux服务器状态:"
ssh "$LINUX_SERVER" << 'REMOTE_EOF'
    echo "Webhook服务状态: \$(systemctl is-active financial-webhook || echo '未运行')"
    echo "部署目录: \$(ls -la $LINUX_DEPLOY_PATH | head -5)"
    echo "系统负载: \$(uptime)"
REMOTE_EOF

echo "✅ 状态检查完成"
EOF

    chmod +x "$PROJECT_ROOT/check-status.sh"

    log_success "快捷脚本已创建"
}

# 测试CI/CD系统
test_ci_cd_system() {
    log_step "测试CI/CD系统..."

    # 测试Webhook服务
    log_info "测试Webhook服务..."

    if curl -s "http://$LINUX_SERVER:9000/health" > /dev/null; then
        log_success "Webhook服务响应正常"
    else
        log_warning "Webhook服务无响应，请检查服务状态"
    fi

    # 测试SSH连接
    log_info "测试SSH连接..."

    if ssh "$LINUX_SERVER" "echo 'SSH测试成功'"; then
        log_success "SSH连接正常"
    else
        log_error "SSH连接失败"
        exit 1
    fi

    log_success "CI/CD系统测试完成"
}

# 显示使用说明
show_usage_instructions() {
    log_step "显示使用说明..."

    cat << EOF

🎉 FinancialSystem CI/CD 安装完成！

📋 使用说明:

1. 自动部署:
   - 当您将其他分支合并到main分支时，系统会自动触发部署
   - 推送到main分支也会触发自动部署

2. 手动操作:
   - 手动部署: ./deploy-now.sh
   - 手动备份: ./backup-now.sh
   - 状态检查: ./check-status.sh

3. 监控和日志:
   - Webhook日志: ssh $LINUX_SERVER "tail -f /var/log/financial-webhook.log"
   - 服务状态: ssh $LINUX_SERVER "systemctl status financial-webhook"

4. 配置文件:
   - 本地配置: .ci-cd-config
   - 修改配置后需要重新运行安装脚本

5. 备份位置:
   - 本地备份: /Volumes/ExternalSSD-1T/08.program/FinancialSystem-Backups
   - Linux备份: $LINUX_DEPLOY_PATH.backup-*

⚠️  注意事项:
- 确保main分支的代码已经过充分测试
- 部署前会自动创建备份
- 如果部署失败，可以使用备份进行回滚

🔗 相关链接:
- Webhook服务: http://$LINUX_SERVER:9000/health
- 项目目录: $PROJECT_ROOT
- Linux部署目录: $LINUX_DEPLOY_PATH

EOF
}

# 主函数
main() {
    echo "🚀 开始安装FinancialSystem CI/CD系统..."
    echo "========================================"

    check_prerequisites
    install_git_hooks
    setup_linux_server
    create_local_config
    create_shortcuts
    test_ci_cd_system
    show_usage_instructions

    log_success "🎉 CI/CD系统安装完成！"
}

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
