#!/bin/bash

# CI/CD部署测试脚本
# 用于测试完整的自动化部署流程

set -e

# 配置
PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
TEST_BRANCH="test-deploy-$(date +%Y%m%d-%H%M%S)"
TEST_LOG="/tmp/test-deployment-$(date +%Y%m%d-%H%M%S).log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_test() {
    echo -e "${BLUE}[TEST]${NC} $1" | tee -a "$TEST_LOG"
}

log_pass() {
    echo -e "${GREEN}[PASS]${NC} $1" | tee -a "$TEST_LOG"
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1" | tee -a "$TEST_LOG"
}

log_info() {
    echo -e "${YELLOW}[INFO]${NC} $1" | tee -a "$TEST_LOG"
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 记录测试结果
record_test() {
    local test_name="$1"
    local result="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "pass" ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        log_pass "$test_name"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        log_fail "$test_name"
    fi
}

# 测试1: Git钩子安装
test_git_hooks() {
    log_test "测试Git钩子安装"
    
    cd "$PROJECT_ROOT"
    
    # 检查post-receive钩子
    if [ -f ".git/hooks/post-receive" ]; then
        record_test "Git post-receive钩子存在" "pass"
    else
        # 安装钩子
        cp ci-cd/git-hooks/post-receive .git/hooks/
        chmod +x .git/hooks/post-receive
        
        if [ -f ".git/hooks/post-receive" ]; then
            record_test "Git post-receive钩子安装" "pass"
        else
            record_test "Git post-receive钩子安装" "fail"
        fi
    fi
}

# 测试2: 部署脚本权限
test_script_permissions() {
    log_test "测试部署脚本权限"
    
    local scripts=(
        "ci-cd/deploy/auto-deploy-trigger.sh"
        "ci-cd/deploy/auto-deploy-with-rollback.sh"
        "ci-cd/deploy/integrated-deploy.sh"
        "ci-cd/deploy/manage-local-images.sh"
        "ci-cd/deploy/enhanced-health-monitor.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -x "$PROJECT_ROOT/$script" ]; then
            record_test "脚本可执行: $script" "pass"
        else
            chmod +x "$PROJECT_ROOT/$script"
            record_test "脚本权限修复: $script" "pass"
        fi
    done
}

# 测试3: 模拟代码提交
test_code_commit() {
    log_test "测试代码提交触发部署"
    
    cd "$PROJECT_ROOT"
    
    # 创建测试文件
    echo "# 测试部署 $(date)" > test-deploy.md
    git add test-deploy.md
    git commit -m "test: 测试自动部署 $(date)"
    
    record_test "创建测试提交" "pass"
}

# 测试4: 部署触发验证
test_deployment_trigger() {
    log_test "测试部署触发机制"
    
    # 直接调用部署脚本进行测试
    if bash "$PROJECT_ROOT/ci-cd/deploy/integrated-deploy.sh"; then
        record_test "部署脚本执行" "pass"
    else
        record_test "部署脚本执行" "fail"
    fi
}

# 测试5: 健康检查
test_health_check() {
    log_test "测试健康检查功能"
    
    ssh admin@********** << 'EOF'
        if [ -f "/opt/FinancialSystem/current/ci-cd/deploy/enhanced-health-monitor.sh" ]; then
            /opt/FinancialSystem/current/ci-cd/deploy/enhanced-health-monitor.sh check
            exit $?
        else
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        record_test "健康检查执行" "pass"
    else
        record_test "健康检查执行" "fail"
    fi
}

# 测试6: 回滚功能
test_rollback() {
    log_test "测试自动回滚功能"
    
    # 创建一个会失败的部署来测试回滚
    cd "$PROJECT_ROOT"
    
    # 临时修改配置使部署失败
    echo "INVALID_CONFIG=true" >> .env.test
    
    # 尝试部署（应该会失败并回滚）
    if bash "$PROJECT_ROOT/ci-cd/deploy/auto-deploy-with-rollback.sh"; then
        record_test "回滚机制（预期失败）" "fail"
    else
        log_info "部署失败，检查是否已回滚"
        
        # 验证服务是否仍在运行
        if ssh admin@********** "curl -s http://localhost:8080/actuator/health" &>/dev/null; then
            record_test "自动回滚功能" "pass"
        else
            record_test "自动回滚功能" "fail"
        fi
    fi
    
    # 清理测试文件
    rm -f .env.test
}

# 测试7: Docker镜像管理
test_docker_images() {
    log_test "测试Docker镜像管理"
    
    ssh admin@********** << 'EOF'
        if [ -f "/opt/FinancialSystem/current/ci-cd/deploy/manage-local-images.sh" ]; then
            /opt/FinancialSystem/current/ci-cd/deploy/manage-local-images.sh check
            exit $?
        else
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        record_test "Docker镜像检查" "pass"
    else
        record_test "Docker镜像检查" "fail"
    fi
}

# 测试8: 监控服务
test_monitoring_service() {
    log_test "测试监控服务"
    
    ssh admin@********** << 'EOF'
        if systemctl is-active financial-health-monitor &>/dev/null; then
            exit 0
        else
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        record_test "监控服务运行状态" "pass"
    else
        record_test "监控服务运行状态" "fail"
    fi
}

# 清理测试环境
cleanup_test_env() {
    log_info "清理测试环境"
    
    cd "$PROJECT_ROOT"
    
    # 删除测试文件
    rm -f test-deploy.md
    
    # 重置到main分支
    git checkout main 2>/dev/null || true
}

# 生成测试报告
generate_test_report() {
    local report_file="/tmp/deployment-test-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>CI/CD部署测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .pass { color: green; font-weight: bold; }
        .fail { color: red; font-weight: bold; }
        .summary { background: #f0f0f0; padding: 10px; margin: 20px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #4CAF50; color: white; }
    </style>
</head>
<body>
    <h1>CI/CD部署测试报告</h1>
    <p>测试时间: $(date)</p>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <p>总测试数: $TOTAL_TESTS</p>
        <p class="pass">通过: $PASSED_TESTS</p>
        <p class="fail">失败: $FAILED_TESTS</p>
        <p>通过率: $(awk "BEGIN {printf \"%.2f\", $PASSED_TESTS/$TOTAL_TESTS*100}")%</p>
    </div>
    
    <h2>测试详情</h2>
    <pre>$(cat "$TEST_LOG")</pre>
    
    <h2>建议</h2>
    <ul>
EOF
    
    if [ $FAILED_TESTS -gt 0 ]; then
        cat >> "$report_file" << EOF
        <li>修复失败的测试项</li>
        <li>检查服务器连接和权限</li>
        <li>验证Docker环境配置</li>
EOF
    else
        cat >> "$report_file" << EOF
        <li>所有测试通过，系统运行正常</li>
        <li>建议定期运行测试以确保系统稳定性</li>
EOF
    fi
    
    cat >> "$report_file" << EOF
    </ul>
</body>
</html>
EOF
    
    log_info "测试报告已生成: $report_file"
    
    # 在macOS上自动打开报告
    if [[ "$OSTYPE" == "darwin"* ]]; then
        open "$report_file"
    fi
}

# 主函数
main() {
    echo "====================================="
    echo "CI/CD自动化部署测试"
    echo "====================================="
    echo ""
    
    log_info "开始测试，日志: $TEST_LOG"
    
    # 运行测试
    test_git_hooks
    test_script_permissions
    test_code_commit
    test_deployment_trigger
    test_health_check
    test_rollback
    test_docker_images
    test_monitoring_service
    
    # 清理
    cleanup_test_env
    
    # 生成报告
    generate_test_report
    
    # 显示结果
    echo ""
    echo "====================================="
    echo "测试结果"
    echo "====================================="
    echo "总测试数: $TOTAL_TESTS"
    echo -e "${GREEN}通过: $PASSED_TESTS${NC}"
    echo -e "${RED}失败: $FAILED_TESTS${NC}"
    echo "通过率: $(awk "BEGIN {printf \"%.2f\", $PASSED_TESTS/$TOTAL_TESTS*100}")%"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}✅ 所有测试通过！${NC}"
        echo "CI/CD系统已准备就绪，可以通过提交代码到main分支来触发自动部署。"
        exit 0
    else
        echo -e "${RED}❌ 有测试失败，请检查并修复。${NC}"
        echo "查看详细日志: $TEST_LOG"
        exit 1
    fi
}

# 确保有执行权限
chmod +x "$PROJECT_ROOT/ci-cd/deploy"/*.sh
chmod +x "$PROJECT_ROOT/ci-cd/test"/*.sh

# 运行主函数
main "$@"