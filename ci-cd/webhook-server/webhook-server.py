#!/usr/bin/env python3
"""
FinancialSystem Webhook服务器
用于接收Git推送事件并触发自动部署
"""

import json
import subprocess
import logging
import os
import sys
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

# 配置
WEBHOOK_PORT = 9000
DEPLOY_SCRIPT = "/home/<USER>/下载/FinancialSystem-Production-Deploy/deploy-complete.sh"
LOG_FILE = "/var/log/financial-webhook.log"
SECRET_TOKEN = "Zlb&198838"  # 用于验证请求的密钥

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class WebhookHandler(BaseHTTPRequestHandler):
    """Webhook请求处理器"""
    
    def log_message(self, format, *args):
        """重写日志方法"""
        logger.info(f"{self.address_string()} - {format % args}")
    
    def do_POST(self):
        """处理POST请求"""
        try:
            # 获取请求路径
            parsed_path = urlparse(self.path)
            
            if parsed_path.path == '/webhook':
                self.handle_webhook()
            elif parsed_path.path == '/deploy':
                self.handle_deploy()
            elif parsed_path.path == '/status':
                self.handle_status()
            else:
                self.send_error(404, "Not Found")
                
        except Exception as e:
            logger.error(f"处理POST请求时出错: {e}")
            self.send_error(500, f"Internal Server Error: {e}")
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_path = urlparse(self.path)
            
            if parsed_path.path == '/health':
                self.handle_health()
            elif parsed_path.path == '/status':
                self.handle_status()
            else:
                self.send_error(404, "Not Found")
                
        except Exception as e:
            logger.error(f"处理GET请求时出错: {e}")
            self.send_error(500, f"Internal Server Error: {e}")
    
    def handle_webhook(self):
        """处理Webhook请求"""
        try:
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            # 解析JSON数据
            webhook_data = json.loads(post_data.decode('utf-8'))
            
            logger.info(f"收到Webhook请求: {webhook_data}")
            
            # 验证请求（如果需要）
            if not self.verify_request(webhook_data):
                self.send_error(401, "Unauthorized")
                return
            
            # 检查是否是main分支的推送
            if self.should_deploy(webhook_data):
                # 异步执行部署
                threading.Thread(
                    target=self.execute_deployment,
                    args=(webhook_data,),
                    daemon=True
                ).start()
                
                response = {
                    "status": "success",
                    "message": "部署已触发",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                response = {
                    "status": "skipped",
                    "message": "非main分支推送，跳过部署",
                    "timestamp": datetime.now().isoformat()
                }
            
            self.send_json_response(200, response)
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            self.send_error(400, "Invalid JSON")
        except Exception as e:
            logger.error(f"处理Webhook时出错: {e}")
            self.send_error(500, f"Internal Server Error: {e}")
    
    def handle_deploy(self):
        """手动触发部署"""
        try:
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            deploy_data = json.loads(post_data.decode('utf-8'))
            
            logger.info(f"收到手动部署请求: {deploy_data}")
            
            # 异步执行部署
            threading.Thread(
                target=self.execute_deployment,
                args=(deploy_data,),
                daemon=True
            ).start()
            
            response = {
                "status": "success",
                "message": "手动部署已触发",
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_json_response(200, response)
            
        except Exception as e:
            logger.error(f"处理手动部署时出错: {e}")
            self.send_error(500, f"Internal Server Error: {e}")
    
    def handle_status(self):
        """获取部署状态"""
        try:
            # 检查部署脚本是否存在
            script_exists = os.path.exists(DEPLOY_SCRIPT)
            
            # 检查服务是否正在运行
            status = {
                "service": "running",
                "timestamp": datetime.now().isoformat(),
                "deploy_script_exists": script_exists,
                "deploy_script_path": DEPLOY_SCRIPT
            }
            
            self.send_json_response(200, status)
            
        except Exception as e:
            logger.error(f"获取状态时出错: {e}")
            self.send_error(500, f"Internal Server Error: {e}")
    
    def handle_health(self):
        """健康检查"""
        try:
            health = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "uptime": time.time() - start_time
            }
            
            self.send_json_response(200, health)
            
        except Exception as e:
            logger.error(f"健康检查时出错: {e}")
            self.send_error(500, f"Internal Server Error: {e}")
    
    def verify_request(self, data):
        """验证请求的合法性"""
        # 这里可以添加更复杂的验证逻辑
        # 例如验证签名、IP白名单等
        return True
    
    def should_deploy(self, webhook_data):
        """判断是否应该执行部署"""
        # 检查是否是main分支
        if 'branch' in webhook_data:
            return webhook_data['branch'] == 'main'
        
        # 检查Git推送事件
        if 'ref' in webhook_data:
            return webhook_data['ref'] == 'refs/heads/main'
        
        # 默认执行部署
        return True
    
    def execute_deployment(self, webhook_data):
        """执行部署脚本"""
        try:
            logger.info("开始执行部署...")
            
            # 设置环境变量
            env = os.environ.copy()
            env['WEBHOOK_DATA'] = json.dumps(webhook_data)
            
            # 执行部署脚本
            result = subprocess.run(
                [DEPLOY_SCRIPT],
                capture_output=True,
                text=True,
                env=env,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode == 0:
                logger.info("部署成功完成")
                logger.info(f"部署输出: {result.stdout}")
            else:
                logger.error("部署失败")
                logger.error(f"错误输出: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("部署超时")
        except Exception as e:
            logger.error(f"执行部署时出错: {e}")
    
    def send_json_response(self, status_code, data):
        """发送JSON响应"""
        response_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(response_data.encode('utf-8'))))
        self.end_headers()
        
        self.wfile.write(response_data.encode('utf-8'))

def main():
    """主函数"""
    global start_time
    start_time = time.time()
    
    logger.info(f"启动FinancialSystem Webhook服务器，端口: {WEBHOOK_PORT}")
    logger.info(f"部署脚本路径: {DEPLOY_SCRIPT}")
    logger.info(f"日志文件: {LOG_FILE}")
    
    # 创建HTTP服务器
    server = HTTPServer(('0.0.0.0', WEBHOOK_PORT), WebhookHandler)
    
    try:
        logger.info("Webhook服务器已启动，等待请求...")
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
        server.shutdown()
        logger.info("服务器已关闭")

if __name__ == '__main__':
    main()
