<?xml version="1.0" encoding="UTF-8"?>
<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Main" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.laoshu198838.Main" />
    <module name="api-gateway" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="VM_PARAMETERS" value="-Dspring.profiles.active=default" />
    <option name="WORKING_DIRECTORY" value="$MODULE_WORKING_DIR$" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.laoshu198838.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
