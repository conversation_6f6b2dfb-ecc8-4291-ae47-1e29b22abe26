<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="4" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1a699d71-23a6-452e-97b0-b3c76f77a225" name="Changes" comment="Changes">
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/DataCircle.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/inputform/FormDate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/inputform/FormInput.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/inputform/FormSelect.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/inputform/FormTextarea.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/ui/button.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/ui/card.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueReductionUpdate.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FinancialSystem-web/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/FinancialSystem-web/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/pages/DebtList.js" beforeDir="false" afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/pages/DebtList.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueAdd.js" beforeDir="false" afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueDebtAddUpdate.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueStatistics.js" beforeDir="false" afterPath="$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueStatistics.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FinancialSystem-web/src/routes.js" beforeDir="false" afterPath="$PROJECT_DIR$/FinancialSystem-web/src/routes.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
        <option value="Interface" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/FinancialSystem-web" />
    <option name="RESET_MODE" value="HARD" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;laoshu198838&quot;
  }
}</component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="1736603865" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/material-dashboard-react" />
            </RecentBranchesForRepo>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="main" />
                    <option name="lastUsedInstant" value="**********" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$/FinancialSystem-web" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/laoshu198838/FinancialSystem.git&quot;,
    &quot;accountId&quot;: &quot;c78e976b-eea8-45aa-80b5-e247e3436f79&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/webservice/src/main/java/com/laoshu198838/repository/OverdueDebtRepository.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="workOffline" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
    <option name="vmOptions" value="--enable-preview" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="QODANA_PROBLEMS_VIEW_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2liraodV2ynlzPIpfJ9ftXk9n00" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showExcludedFiles" value="false" />
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
    <option name="sortKey" value="BY_TIME_DESCENDING" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Application.AccountDTO.executor&quot;: &quot;Run&quot;,
    &quot;Application.AccountExportService.executor&quot;: &quot;Run&quot;,
    &quot;Application.DataSave.executor&quot;: &quot;Run&quot;,
    &quot;Application.DatabaseUtil.executor&quot;: &quot;Run&quot;,
    &quot;Application.ExcelUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.FileUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.FinancialReport (1).executor&quot;: &quot;Debug&quot;,
    &quot;Application.FinancialReport.executor&quot;: &quot;Debug&quot;,
    &quot;Application.KingdeeService.executor&quot;: &quot;Run&quot;,
    &quot;Application.Main (1).executor&quot;: &quot;Run&quot;,
    &quot;Application.Main.executor&quot;: &quot;Run&quot;,
    &quot;Application.ReportDataService.executor&quot;: &quot;Run&quot;,
    &quot;Application.Test.executor&quot;: &quot;Run&quot;,
    &quot;Application.YamlReader.executor&quot;: &quot;Run&quot;,
    &quot;Application.YamlUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.com.laoshu198838.FinancialReport.executor&quot;: &quot;Run&quot;,
    &quot;Application.com.laoshu198838.YamlUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.com.test.Test.executor&quot;: &quot;Run&quot;,
    &quot;Application.common.YamlUtils.executor&quot;: &quot;Run&quot;,
    &quot;Application.insertDataUtil.executor&quot;: &quot;Run&quot;,
    &quot;Application.kingdee.FinancialReport.executor&quot;: &quot;Debug&quot;,
    &quot;Maven.common [install].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database detector&quot;: &quot;Database detector&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Vcs Notifications&quot;: &quot;VCS notifications&quot;,
    &quot;Notification.DoNotAsk-Database detector&quot;: &quot;true&quot;,
    &quot;Notification.DoNotAsk-Vcs Notifications&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;com.intellij.ide.FileNotInSourceRootChecker.no.check&quot;: &quot;true&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;Comma-separated (CSV)_id&quot;,
    &quot;extract.method.default.visibility&quot;: &quot;private&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.preview.features.used&quot;: &quot;true&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Library/CloudStorage/OneDrive-个人/08.程序/FinancialSystem/webservice/src/main/resources&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;E:\\OneDrive\\08.程序\\FinancialSystem\\FinancialSystem-web\\node_modules\\prettier&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.4449489&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;settings.javascript.prettier&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Applications/IntelliJ IDEA.app/Contents/plugins/javascript-plugin/jsLanguageServicesImpl/external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.ExcelUtils.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.FileUtils.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.Main.executor&quot;: &quot;Debug&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;java.sql.Driver&quot;,
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/webservice/src/main/resources" />
      <recent name="$PROJECT_DIR$/mysql_data/src/main/resources" />
      <recent name="E:\OneDrive\08.程序\FinancialSystem\kingdee" />
      <recent name="E:\OneDrive\08.程序\FinancialSystem\security" />
      <recent name="E:\OneDrive\08.程序\FinancialSystem\report" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/material-dashboard-react/src/layouts/Assetmanagement/data" />
      <recent name="$PROJECT_DIR$/material-dashboard-react/src/layouts/Assetmanagement" />
      <recent name="E:\OneDrive\08.程序\FinancialSystem\src\main\java\com\laoshu198838" />
      <recent name="E:\OneDrive\08.程序\FinancialSystem\common\src\main\java" />
      <recent name="E:\OneDrive\08.程序\FinancialSystem\common\src\main\java\common" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.laoshu198838.repository" />
      <recent name="com.laoshu198838" />
      <recent name="java" />
      <recent name="main.java" />
      <recent name="common" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="" />
    </key>
  </component>
  <component name="RunManager" selected="Application.insertDataUtil">
    <configuration name="DatabaseUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.laoshu198838.util.DatabaseUtil" />
      <module name="mysql_data" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.laoshu198838.util.BatchInsertUtil" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ExcelUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.laoshu198838.ExcelUtils" />
      <module name="common" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.laoshu198838.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KingdeeService" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.laoshu198838.KingdeeService" />
      <module name="kingdee" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.laoshu198838.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Main" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.laoshu198838.Main" />
      <module name="mysql_data" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.laoshu198838.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ReportDataService" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.laoshu198838.ReportDataService" />
      <module name="kingdee" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="insertDataUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.laoshu198838.util.insertDataUtil" />
      <module name="mysql_data" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.laoshu198838.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="common" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="common" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Main" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="webservice" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.laoshu198838.Main" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.insertDataUtil" />
        <item itemvalue="Application.KingdeeService" />
        <item itemvalue="Application.ExcelUtils" />
        <item itemvalue="Application.DatabaseUtil" />
        <item itemvalue="Application.ReportDataService" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-b114ca120d71-intellij.indexing.shared.core-IU-242.21829.142" />
        <option value="bundled-js-predefined-d6986cc7102b-7c0b70fcd90d-JavaScript-IU-242.21829.142" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1a699d71-23a6-452e-97b0-b3c76f77a225" name="Changes" comment="" />
      <created>1725673803359</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1725673803359</updated>
      <workItem from="1725673804424" duration="3305000" />
      <workItem from="1725691039499" duration="37000" />
      <workItem from="1725691081179" duration="1697000" />
      <workItem from="1725692787377" duration="1127000" />
      <workItem from="1725717530168" duration="257000" />
      <workItem from="1725717963580" duration="255000" />
      <workItem from="1725718282036" duration="120000" />
      <workItem from="1725718452536" duration="11581000" />
      <workItem from="1725765591878" duration="54000" />
      <workItem from="1725765658044" duration="2679000" />
      <workItem from="1725778098790" duration="4716000" />
      <workItem from="1725783745221" duration="6653000" />
      <workItem from="1726052618137" duration="5891000" />
      <workItem from="1726058906429" duration="118000" />
      <workItem from="1726230187812" duration="3032000" />
      <workItem from="1726313830900" duration="3248000" />
      <workItem from="1726359549639" duration="4417000" />
      <workItem from="1726365447489" duration="63000" />
      <workItem from="1726365517367" duration="32000" />
      <workItem from="1726366288683" duration="628000" />
      <workItem from="1726366979498" duration="14492000" />
      <workItem from="1726459667850" duration="6359000" />
      <workItem from="1726534428304" duration="7328000" />
      <workItem from="1726646454650" duration="2538000" />
      <workItem from="1726649045110" duration="730000" />
      <workItem from="1726649816024" duration="3847000" />
      <workItem from="1726670306427" duration="2008000" />
      <workItem from="1726750552460" duration="2847000" />
      <workItem from="1726755793940" duration="472000" />
      <workItem from="1726800785230" duration="1114000" />
      <workItem from="1727001552036" duration="2380000" />
      <workItem from="1727003942187" duration="1650000" />
      <workItem from="1732326143492" duration="5307000" />
      <workItem from="1732331488417" duration="1825000" />
      <workItem from="1732333325998" duration="621000" />
      <workItem from="1732756458135" duration="1017000" />
      <workItem from="1733362774014" duration="358000" />
      <workItem from="1733790994847" duration="2983000" />
      <workItem from="1733828607014" duration="3494000" />
      <workItem from="1734048627700" duration="13024000" />
      <workItem from="1734068288223" duration="4098000" />
      <workItem from="1734138131175" duration="1484000" />
      <workItem from="1734139689321" duration="4833000" />
      <workItem from="1734225010219" duration="1310000" />
      <workItem from="1734226384662" duration="1042000" />
      <workItem from="1734227974789" duration="4278000" />
      <workItem from="1734232303466" duration="314000" />
      <workItem from="1734232647794" duration="1372000" />
      <workItem from="1734234094249" duration="464000" />
      <workItem from="1734234590633" duration="145000" />
      <workItem from="1734234763657" duration="1548000" />
      <workItem from="1734308613497" duration="1514000" />
      <workItem from="1734310181623" duration="802000" />
      <workItem from="1734392773428" duration="4070000" />
      <workItem from="1734439620823" duration="4772000" />
      <workItem from="1734479386391" duration="4133000" />
      <workItem from="1734483661794" duration="1174000" />
      <workItem from="1734484895439" duration="753000" />
      <workItem from="1734494386623" duration="691000" />
      <workItem from="1734521564338" duration="815000" />
      <workItem from="1734522426832" duration="2266000" />
      <workItem from="1734568269284" duration="2615000" />
      <workItem from="1734581074120" duration="975000" />
      <workItem from="1734582080988" duration="2225000" />
      <workItem from="1734610207418" duration="1563000" />
      <workItem from="1734611810075" duration="3057000" />
      <workItem from="1734614905298" duration="682000" />
      <workItem from="1734615618301" duration="835000" />
      <workItem from="1734616480510" duration="51000" />
      <workItem from="1734652692881" duration="2177000" />
      <workItem from="1734654937262" duration="1035000" />
      <workItem from="1734656008519" duration="1876000" />
      <workItem from="1734848620224" duration="2148000" />
      <workItem from="1734851041882" duration="3071000" />
      <workItem from="1735037474280" duration="7622000" />
      <workItem from="1735303929387" duration="501000" />
      <workItem from="1735304441848" duration="738000" />
      <workItem from="1735305186642" duration="6446000" />
      <workItem from="1735350071912" duration="1634000" />
      <workItem from="1735361409541" duration="19030000" />
      <workItem from="1735970570062" duration="6221000" />
      <workItem from="1736559523504" duration="1760000" />
      <workItem from="1736561364892" duration="1834000" />
      <workItem from="1736563653159" duration="12543000" />
      <workItem from="1736599918311" duration="4744000" />
      <workItem from="1736604810870" duration="20000" />
      <workItem from="1736644769371" duration="13491000" />
      <workItem from="1736860257732" duration="318000" />
      <workItem from="1736860586772" duration="229000" />
      <workItem from="1736862583137" duration="128000" />
      <workItem from="1736862726205" duration="3025000" />
      <workItem from="1736946423828" duration="2176000" />
      <workItem from="1736949526299" duration="202000" />
      <workItem from="1736949740000" duration="5108000" />
      <workItem from="1737156158689" duration="8761000" />
      <workItem from="1737198079977" duration="11641000" />
      <workItem from="1737294692379" duration="1739000" />
      <workItem from="1737296438210" duration="2660000" />
      <workItem from="1737423996096" duration="25698000" />
      <workItem from="1737461534466" duration="504000" />
      <workItem from="1737462046496" duration="5592000" />
      <workItem from="1737553984513" duration="3462000" />
      <workItem from="1737557454215" duration="1008000" />
      <workItem from="1737583658908" duration="94816000" />
      <workItem from="1738368129335" duration="6299000" />
      <workItem from="1738418774381" duration="624000" />
      <workItem from="1738460772817" duration="1226000" />
      <workItem from="1738541950761" duration="260000" />
      <workItem from="1738542339444" duration="33497000" />
      <workItem from="1738716872330" duration="15162000" />
      <workItem from="1738747410323" duration="350000" />
    </task>
    <task id="LOCAL-00001" summary="Changes">
      <option name="closed" value="true" />
      <created>1726053544388</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1726053544388</updated>
    </task>
    <task id="LOCAL-00002" summary="Changes">
      <option name="closed" value="true" />
      <created>1726053562458</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1726053562458</updated>
    </task>
    <task id="LOCAL-00003" summary="Changes">
      <option name="closed" value="true" />
      <created>1726646652078</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1726646652078</updated>
    </task>
    <task id="LOCAL-00004" summary="Changes">
      <option name="closed" value="true" />
      <created>1726650949843</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1726650949843</updated>
    </task>
    <task id="LOCAL-00005" summary="Changes">
      <option name="closed" value="true" />
      <created>1726650967820</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1726650967820</updated>
    </task>
    <task id="LOCAL-00006" summary="Changes">
      <option name="closed" value="true" />
      <created>1726652012299</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1726652012299</updated>
    </task>
    <task id="LOCAL-00007" summary="Changes">
      <option name="closed" value="true" />
      <created>1726653355921</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1726653355922</updated>
    </task>
    <task id="LOCAL-00008" summary="Changes">
      <option name="closed" value="true" />
      <created>1726671282700</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1726671282701</updated>
    </task>
    <task id="LOCAL-00009" summary="Changes">
      <option name="closed" value="true" />
      <created>1735308107343</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1735308107343</updated>
    </task>
    <task id="LOCAL-00010" summary="Changes">
      <option name="closed" value="true" />
      <created>1735308559886</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1735308559886</updated>
    </task>
    <task id="LOCAL-00011" summary="Changes">
      <option name="closed" value="true" />
      <created>1735351677391</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1735351677391</updated>
    </task>
    <task id="LOCAL-00012" summary="Changes">
      <option name="closed" value="true" />
      <created>1736588342651</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1736588342651</updated>
    </task>
    <task id="LOCAL-00013" summary="Changes">
      <option name="closed" value="true" />
      <created>1736588406945</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1736588406945</updated>
    </task>
    <task id="LOCAL-00014" summary="Changes">
      <option name="closed" value="true" />
      <created>1736588934905</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1736588934905</updated>
    </task>
    <task id="LOCAL-00015" summary="Changes">
      <option name="closed" value="true" />
      <created>1736589270642</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1736589270642</updated>
    </task>
    <task id="LOCAL-00016" summary="Changes">
      <option name="closed" value="true" />
      <created>1736591125384</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1736591125384</updated>
    </task>
    <task id="LOCAL-00017" summary="Changes">
      <option name="closed" value="true" />
      <created>1736593548721</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1736593548721</updated>
    </task>
    <task id="LOCAL-00018" summary="Changes">
      <option name="closed" value="true" />
      <created>1736593587737</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1736593587738</updated>
    </task>
    <task id="LOCAL-00019" summary="Changes">
      <option name="closed" value="true" />
      <created>1736593597870</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1736593597870</updated>
    </task>
    <task id="LOCAL-00020" summary="Changes">
      <option name="closed" value="true" />
      <created>1736603909055</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1736603909055</updated>
    </task>
    <task id="LOCAL-00021" summary="Changes">
      <option name="closed" value="true" />
      <created>1736603913610</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1736603913610</updated>
    </task>
    <task id="LOCAL-00022" summary="Changes">
      <option name="closed" value="true" />
      <created>1736604393363</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1736604393363</updated>
    </task>
    <task id="LOCAL-00023" summary="Changes">
      <option name="closed" value="true" />
      <created>1736604795752</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1736604795753</updated>
    </task>
    <task id="LOCAL-00024" summary="Changes">
      <option name="closed" value="true" />
      <created>1736604818667</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1736604818667</updated>
    </task>
    <task id="LOCAL-00025" summary="Changes">
      <option name="closed" value="true" />
      <created>1736692977982</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1736692977983</updated>
    </task>
    <task id="LOCAL-00026" summary="Changes">
      <option name="closed" value="true" />
      <created>1736860276890</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1736860276890</updated>
    </task>
    <task id="LOCAL-00027" summary="Changes">
      <option name="closed" value="true" />
      <created>1737295346281</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1737295346281</updated>
    </task>
    <task id="LOCAL-00028" summary="Changes">
      <option name="closed" value="true" />
      <created>1737455399504</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1737455399504</updated>
    </task>
    <task id="LOCAL-00029" summary="Changes">
      <option name="closed" value="true" />
      <created>1737763853282</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1737763853282</updated>
    </task>
    <task id="LOCAL-00030" summary="Changes">
      <option name="closed" value="true" />
      <created>1737781742261</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1737781742261</updated>
    </task>
    <task id="LOCAL-00031" summary="Changes">
      <option name="closed" value="true" />
      <created>1738300817160</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1738300817160</updated>
    </task>
    <task id="LOCAL-00032" summary="Changes">
      <option name="closed" value="true" />
      <created>1738593298820</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1738593298820</updated>
    </task>
    <task id="LOCAL-00033" summary="Changes">
      <option name="closed" value="true" />
      <created>1738594690073</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1738594690073</updated>
    </task>
    <task id="LOCAL-00034" summary="Changes">
      <option name="closed" value="true" />
      <created>1738594699334</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1738594699334</updated>
    </task>
    <task id="LOCAL-00035" summary="Changes">
      <option name="closed" value="true" />
      <created>1738724538908</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1738724538908</updated>
    </task>
    <option name="localTasksCounter" value="36" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/main" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Revert &quot;Changes&quot;&#10;&#10;This reverts commit 721f9231b403d67f55f4c139b0c1262d9f2fef48." />
    <MESSAGE value="Changes" />
    <option name="LAST_COMMIT_MESSAGE" value="Changes" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="java.lang.NoClassDefFoundError" package="java.lang" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/laoshu198838/config/src/main/java/Application.java</url>
          <line>9</line>
          <properties class="Application.Application">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/laoshu198838/common/src/main/java/com/laoshu198838/FileUtils.java</url>
          <line>77</line>
          <properties class="com.laoshu198838.FileUtils">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/src/main/java/com/laoshu198838/common/src/main/java/com/laoshu198838/FileUtils.java</url>
          <line>36</line>
          <properties class="com.laoshu198838.FileUtils">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="33" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/common/src/main/java/com/laoshu198838/FileUtils.java</url>
          <line>14</line>
          <properties class="com.laoshu198838.FileUtils" method="main">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="44" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/webservice/src/main/java/com/laoshu198838/repository/OverdueDebtRepository.java</url>
          <line>34</line>
          <properties class="com.laoshu198838.repository.OverdueDebtRepository" method="findInitialDebtReductionAmount">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/laoshu198838/kingdee/src/main/java/FinancialReport.java</url>
          <line>200</line>
          <option name="timeStamp" value="43" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/FinancialSystem-web/src/layouts/dashboard/index.js</url>
          <line>59</line>
          <option name="timeStamp" value="46" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/FinancialSystem-web/src/layouts/dashboard/index.js</url>
          <line>34</line>
          <option name="timeStamp" value="47" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/FinancialSystem-web/src/layouts/debtmanagement/components/OverdueStatisticsFilter.js</url>
          <line>7</line>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>