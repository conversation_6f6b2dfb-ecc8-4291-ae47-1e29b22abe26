<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for FinancialSystem" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.32/lombok-1.18.32.jar" />
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.32/lombok-1.18.32.jar" />
        </processorPath>
        <module name="report-management" />
        <module name="common" />
        <module name="account-management" />
        <module name="treasury" />
        <module name="data-access" />
        <module name="debt-management" />
        <module name="kingdee" />
        <module name="data-processing" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="account" target="17" />
      <module name="account (1)" target="21" />
      <module name="api-gateway" target="21" />
      <module name="audit" target="17" />
      <module name="audit (1)" target="21" />
      <module name="audit-management" target="17" />
      <module name="common (1)" target="21" />
      <module name="config" target="17" />
      <module name="config (1)" target="21" />
      <module name="consolidation" target="17" />
      <module name="consolidation (1)" target="21" />
      <module name="database" target="17" />
      <module name="database (1)" target="21" />
      <module name="exception" target="17" />
      <module name="exception (1)" target="21" />
      <module name="integration" target="17" />
      <module name="integration (1)" target="21" />
      <module name="kingdee (1)" target="21" />
      <module name="notes (1)" target="21" />
      <module name="security" target="17" />
      <module name="security (1)" target="21" />
      <module name="Treasury" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="FinancialSystem" options="-parameters" />
      <module name="Treasury" options="-parameters" />
      <module name="account" options="-parameters" />
      <module name="account-management" options="-parameters" />
      <module name="api-gateway" options="-parameters" />
      <module name="audit" options="-parameters" />
      <module name="audit-management" options="-parameters" />
      <module name="common" options="-parameters" />
      <module name="config" options="-parameters" />
      <module name="consolidation" options="-parameters" />
      <module name="data-access" options="-parameters" />
      <module name="data-processing" options="-parameters" />
      <module name="database" options="-parameters" />
      <module name="debt-management" options="-parameters" />
      <module name="exception" options="-parameters" />
      <module name="financial-system" options="-parameters" />
      <module name="integration" options="-parameters" />
      <module name="kingdee" options="-parameters" />
      <module name="notes" options="-parameters" />
      <module name="report-management" options="-parameters" />
      <module name="security" options="-parameters" />
      <module name="services" options="-parameters" />
      <module name="treasury" options="-parameters" />
      <module name="webservice" options="-parameters" />
    </option>
  </component>
</project>