<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml_副本" />
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/consolidation/pom.xml" />
        <option value="$PROJECT_DIR$/config/pom.xml" />
        <option value="$PROJECT_DIR$/report/pom.xml" />
        <option value="$PROJECT_DIR$/security/pom.xml" />
        <option value="$PROJECT_DIR$/exception/pom.xml" />
        <option value="$PROJECT_DIR$/integration/pom.xml" />
        <option value="$PROJECT_DIR$/data-access/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/api-gateway/pom.xml" />
        <option value="$PROJECT_DIR$/notes/pom.xml" />
        <option value="$PROJECT_DIR$/services/audit-management/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_21_PREVIEW" project-jdk-name="21" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>