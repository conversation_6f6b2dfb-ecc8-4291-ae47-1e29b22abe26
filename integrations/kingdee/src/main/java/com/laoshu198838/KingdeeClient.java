package com.laoshu198838;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * <AUTHOR>
 */

public class KingdeeClient {
    // 基本配置信息
    private static final String BASE_URL = "http://ierp.masonled.com:8023/ierp";
    private static final String APP_ID = "FINANCIAL_ZLB";
    private static final String APP_SECRET = "WrkjFinfsdjog.1112$%^";
    private static final String ACCOUNT_ID = "1585564454332923904";
    private static final String USER = "***********";
    private static final String PASSWORD = "Zlb&********";


    private static final HttpClient HTTP_CLIENT = HttpClient.newHttpClient();
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // 获取 appToken
    private static String getAppToken() throws Exception {
        String url = BASE_URL + "/api/getAppToken.do";
        String payload = String.format(
                "{\"appId\":\"%s\", \"appSecret\":\"%s\", \"accountId\":\"%s\"}",
                APP_ID, APP_SECRET, ACCOUNT_ID
        );

        HttpRequest request = HttpRequest.newBuilder()
                .uri(new URI(url))
                .POST(HttpRequest.BodyPublishers.ofString(payload))
                .header("Content-Type", "application/json")
                .build();

        HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
        JsonNode jsonNode = OBJECT_MAPPER.readTree(response.body());
        return jsonNode.get("data").get("app_token").asText();
    }

    // 登录获取 access_token
    private static String login(String appToken) throws Exception {
        String url = BASE_URL + "/api/login.do";
        String payload = String.format(
                "{\"user\":\"%s\", \"password\":\"%s\", \"apptoken\":\"%s\", \"accountId\":\"%s\", \"logintype\":\"2\"}",
                USER, PASSWORD, appToken, ACCOUNT_ID
        );

        HttpRequest request = HttpRequest.newBuilder()
                .uri(new URI(url))
                .POST(HttpRequest.BodyPublishers.ofString(payload))
                .header("Content-Type", "application/json")
                .build();

        HttpResponse<String> response = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
        JsonNode jsonNode = OBJECT_MAPPER.readTree(response.body());
        String accessToken = jsonNode.get("data").get("access_token").asText();
        return BASE_URL + "/kapi/app/cm/getrptdata/?access_token=" + accessToken;
    }

//    获得token
    public static String getBaseUrl() throws Exception {
        String appToken = getAppToken();
        return login(appToken);
    }
}
