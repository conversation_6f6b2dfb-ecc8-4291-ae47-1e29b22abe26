package com.laoshu198838;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;
import com.laoshu198838.util.file.YamlUtils;

/**
 * 金蝶服务类
 * 提供金蝶系统相关的数据查询和参数获取功能
 *
 * <AUTHOR>
 */
@Service
public class KingdeeService {

    /**
     * 获取kingdee中的公司信息
     */
    public List<Map<String, String>> getCompanyInfo() {
        @SuppressWarnings("unchecked")
        List<Map<String, String>> companyNames = (List<Map<String, String>>) YamlUtils.readSingleLevelYaml("params.yaml", "companies_info");
        // 获取公司数量
        if (companyNames == null) {
            throw new RuntimeException("companyNames is null");
        }
        return companyNames;
    }

    /**
     * 获取kingdee中的年份对应参数
     */
    public Map<String, String> getAllYearParams() {
        // 读取 YAML 文件并解析为 List
        @SuppressWarnings("unchecked")
        List<Map<String, String>> yearList = (List<Map<String, String>>) YamlUtils.readSingleLevelYaml("params.yaml", "years");

        // 检查是否为空
        if (yearList == null || yearList.isEmpty()) {
            throw new RuntimeException("yearList is null or empty");
        }

        // 将 List 转换为 Map
        Map<String, String> yearMap = new HashMap<>();
        for (Map<String, String> entry : yearList) {
//            合并每个 Map 的键值对到 yearMap 中
            yearMap.putAll(entry);
        }

        return yearMap;
    }

    /**
     * 获取kingdee中所有的月份信息
     */
    public Map<String, String> getAllMonthsParams() {
        // 从 YAML 文件中读取 months 数据
        @SuppressWarnings("unchecked")
        List<Map<String, String>> monthList = (List<Map<String, String>>) YamlUtils.readSingleLevelYaml("params.yaml", "months");

        // 检查是否为空
        if (monthList == null || monthList.isEmpty()) {
            throw new RuntimeException("monthList is null or empty");
        }

        // 将 List 转换为 Map
        Map<String, String> monthMap = new HashMap<>();
        for (Map<String, String> entry : monthList) {
            // 合并每个 Map 的键值对到 monthMap 中
            monthMap.putAll(entry);
        }

        return monthMap;
    }


    /**
     * 获取kingdee中的报告类型参数
     */
    public Map<String, String> getReportTypeParams() {
        // 从 YAML 文件中读取报告类型数据
        @SuppressWarnings("unchecked")
        List<Map<String, String>> reportTypeList = (List<Map<String, String>>) YamlUtils.readSingleLevelYaml("params.yaml", "报表类型代码");

        // 检查是否为空
        if (reportTypeList == null || reportTypeList.isEmpty()) {
            throw new RuntimeException("reportTypeList is null or empty");
        }

        // 将 List 转换为 Map
        Map<String, String> reportTypeMap = new HashMap<>();
        for (Map<String, String> entry : reportTypeList) {
            // 合并每个 Map 的键值对到 reportTypeMap 中
            reportTypeMap.putAll(entry);
        }

        return reportTypeMap;
    }

    public static void main(String[] args) {
        KingdeeService service = new KingdeeService();
//        获取年份参数
        Map<String, String> year = service.getAllYearParams();
        for (Map.Entry<String, String> entry : year.entrySet()) {
            System.out.println(entry.getKey() + " : " + entry.getValue());
        }
//        获取报告类型参数
        Map<String, String> reportType = service.getReportTypeParams();
        for (Map.Entry<String, String> entry : reportType.entrySet()) {
            System.out.println(entry.getKey() + " : " + entry.getValue());
        }
    }
}
