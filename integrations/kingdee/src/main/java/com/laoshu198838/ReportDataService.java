package com.laoshu198838;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import static com.laoshu198838.KingdeeClient.getBaseUrl;
import com.laoshu198838.util.file.YamlUtils;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;

/**
 * 报告数据服务类
 * 提供金蝶报告数据的获取和处理功能
 *
 * <AUTHOR>
 */
@Service
@SuppressWarnings("unchecked")
public class ReportDataService {

    @Autowired
    private KingdeeService kingdeeService;

    // 使用 Logback 原生 Logger
    private static final LoggerContext LOGGER_CONTEXT = new LoggerContext();
    private static final Logger logger = LOGGER_CONTEXT.getLogger(ReportDataService.class);


    // 根据报告类型，提取对应的报告数据
    private static Map<String, Double> extractAccountNumbers(List<List<Object>> data, String[] tmpList) {
        Map<String, Double> accountInfo = new HashMap<>();

        for (List<Object> row : data) {
            // 数据长度校验，避免索引越界
            if (row.size() < 5) {
                continue;
            }

            // 提取字段
            // "Account" 字段
            String accountCode = row.get(1).toString();
            // 科目名称
            String accountName = (String) YamlUtils.readTwoLevelYaml("params.yaml", "金蝶科目编码", accountCode);
            // "ChangeType" 字段
            String changeType = row.get(4).toString();
            // 最后一列的数字
            double number = Double.parseDouble(row.get(row.size() - 1).toString());

            // 跳过值为 0 的数据
            if (number == 0) {
                continue;
            }

            // 根据模板类型和 changeType 提取数据
            // 单体或合并资产负债表
            if ("1".equals(tmpList[0]) || "5".equals(tmpList[0])) {
                if ("EndingBalance".equals(changeType)) {
                    accountInfo.put(accountName + "期末余额", number);
                } else if ("BBOY".equals(changeType)) {
                    accountInfo.put(accountName + "期初余额", number);
                }
            } else { // 其他报表类型
                if ("CurrentPeriod".equals(changeType)) {
                    accountInfo.put(accountName + "本期数", number);
                } else if ("YTD".equals(changeType)) {
                    accountInfo.put(accountName + "本年累计数", number);
                } else if (changeType.contains("CT")) {
                    accountInfo.put(accountName + "上年同期累计数", number);
                }
            }
        }

        return accountInfo;
    }

    // 获取单个报告数据
    public static Map<String, Double> getSingleReportData(String entity,
                                                          String year, String month, String[] reportType) throws Exception {
        String baseUrl = getBaseUrl();
        try {

            // 构建请求负载数据
            Map<String, Object> payload = Map.of(
                    "model", "CUBEMason1594975639822161920",
                    "entity", entity,
                    "scene", "MRpt",
                    "year", year,
                    "period", month,
                    "currency", "DC",
                    "processList", List.of("EIRpt"),
                    "tmplList", reportType,
                    "scope", Map.of("C3", "C3None", "AuditTrail", "ATTotal")
                                                );

            // 将请求数据转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonPayload = objectMapper.writeValueAsString(payload);

            // 创建 HttpClient 实例
            @SuppressWarnings("resource")
            HttpClient httpClient = HttpClient.newHttpClient();

            // 构建 POST 请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(baseUrl))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                    .build();

            // 发送请求并获取响应
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
//            System.out.println(httpClient.send(request, HttpResponse.BodyHandlers.ofString()));

            // 解析响应数据
            // 使用 TypeReference 来明确 Map 的泛型类型
            Map<String, Object> responseData = objectMapper.readValue(response.body(), new TypeReference<>() {
            });
            // 获取第一层数据 "data"
            Map<String, Object> dataLevel1 = objectMapper.convertValue(responseData.get("data"), new TypeReference<>() {
            });
            if (dataLevel1 == null) {
                throw new RuntimeException("第一层数据 data 为空或格式不正确");
            }
            // 获取第二层数据 "data"
            Map<String, Object> dataLevel2 = objectMapper.convertValue(dataLevel1.get("data"), new TypeReference<>() {
            });
            if (dataLevel2 == null) {
                throw new RuntimeException("第二层数据 data 为空或格式不正确");
            }
            // 获取最终的数据 "data"
            List<List<Object>> finalData = objectMapper.convertValue(dataLevel2.get("data"), new TypeReference<>() {
            });
            if (finalData == null) {
                throw new RuntimeException("最终数据 data 为空或格式不正确");
            }

            // 提取会计数据 (这里假设 extractAccountNumbers 是单独的逻辑)
            return extractAccountNumbers(finalData, reportType);

        } catch (Exception e) {
            logger.error("捕获到异常：", e);
            throw new RuntimeException("获取报告数据失败", e);
        }
    }

    /**
     * 获取所有的报告参数
     */
    public List<List<String>> getAllReportParas() {
//        获取所有公司名称
        List<Map<String, String>> companyInfo = kingdeeService.getCompanyInfo();
//        获取所有的月份
        Map<String, String> allMonthsParams = kingdeeService.getAllMonthsParams();
//        获取所有的年份
        Map<String, String> allYearParams = kingdeeService.getAllYearParams();
//        获取所有的报告类型
        Map<String, String> reportTypeParams = kingdeeService.getReportTypeParams();
//        定义一个变量，将上述四个变量的值组成一个列表
        List<List<String>> allReportParas = new ArrayList<>();
        for (Map<String, String> company : companyInfo) {
            for (Map.Entry<String, String> yearEntry : allYearParams.entrySet()) {
                for (Map.Entry<String, String> monthEntry : allMonthsParams.entrySet()) {
                    for (Map.Entry<String, String> reportTypeEntry : reportTypeParams.entrySet()) {
                        // 每个组合包含：年份(中文)、月份(中文)、年份编码、月份编码、报告类型名、报告类型编码
                        List<String> combination = List.of(
//                               公司名称代码
                                company.get("entity"),
                                // 年份
                                yearEntry.getValue(),
                                // 月份
                                monthEntry.getValue(),
                                // 报告类型名
                                reportTypeEntry.getValue()
                                                          );
                        allReportParas.add(combination);
                    }
                }
            }
        }
        return allReportParas;

    }

    // 主方法
    public static void main(String[] args) {
//    测试获取所有的报告参数
        ReportDataService service = new ReportDataService();
        service.kingdeeService = new KingdeeService();
        List<List<String>> allReportParas = service.getAllReportParas();
        for (List<String> combination : allReportParas) {
            System.out.println(combination);
        }

    }

}
