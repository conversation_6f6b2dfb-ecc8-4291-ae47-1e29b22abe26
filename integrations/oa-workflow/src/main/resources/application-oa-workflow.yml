# OA工作流模块配置
spring:
  application:
    name: oa-workflow-service
  profiles:
    active: development

# OA工作流配置
oa:
  workflow:
    # OA系统基础URL
    base-url: http://10.25.1.18:8888/wui/index.html
    
    # WebDriver配置
    web-driver:
      # 是否使用无头模式
      headless: false
      # 浏览器窗口大小
      window-width: 1920
      window-height: 1080
      # 页面加载超时时间（秒）
      page-load-timeout: 30
      # 元素等待超时时间（秒）
      implicitly-wait: 10
    
    # 文件配置
    file:
      # 输出根目录
      output-dir: src/main/resources/oa-workflows
      # PDF文件子目录
      pdf-dir: pdf
      # 截图文件子目录
      screenshot-dir: screenshots
      # HTML文件子目录
      html-dir: html
    
    # 超时配置
    timeout:
      # 登录超时时间（秒）
      login-timeout: 30
      # 页面加载超时时间（秒）
      page-load-timeout: 45
      # 元素等待超时时间（秒）
      element-wait-timeout: 15
      # 打印超时时间（秒）
      print-timeout: 20
      # 验证码等待超时时间（秒）
      captcha-timeout: 300
    
    # 反检测配置
    anti-detection:
      # 启用反检测功能
      enabled: true
      # 随机等待时间（毫秒）
      random-wait-min: 500
      random-wait-max: 2000
      # 重试次数
      max-retries: 3
      # 403错误恢复
      enable-403-recovery: true

# 日志配置
logging:
  level:
    com.laoshu198838.oa: DEBUG
    org.springframework.web: INFO
    org.seleniumhq.selenium: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/oa-workflow.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 服务信息
info:
  app:
    name: OA Workflow Extractor
    description: OA工作流自动提取服务
    version: 1.0.0
    author: FinancialSystem Team

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: development
      
oa:
  workflow:
    web-driver:
      headless: false
    file:
      output-dir: src/main/resources/dev-oa-workflows

logging:
  level:
    root: INFO
    com.laoshu198838.oa: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: production
      
oa:
  workflow:
    web-driver:
      headless: true
    file:
      output-dir: /opt/financialsystem/oa-workflows
    timeout:
      login-timeout: 15
      page-load-timeout: 45
      element-wait-timeout: 15

logging:
  level:
    root: WARN
    com.laoshu198838.oa: INFO
  file:
    name: /var/log/financialsystem/oa-workflow.log

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
      
oa:
  workflow:
    web-driver:
      headless: true
    file:
      output-dir: src/main/resources/test-oa-workflows
    timeout:
      login-timeout: 5
      page-load-timeout: 15
      element-wait-timeout: 5

logging:
  level:
    root: WARN
    com.laoshu198838.oa: DEBUG