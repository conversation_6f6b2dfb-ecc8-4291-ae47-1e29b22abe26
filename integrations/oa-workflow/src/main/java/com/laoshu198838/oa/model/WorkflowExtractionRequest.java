package com.laoshu198838.oa.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;

import java.time.LocalDate;

/**
 * 工作流提取请求
 */
public class WorkflowExtractionRequest {
    
    /**
     * OA系统用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    /**
     * OA系统密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;
    
    /**
     * 工作流类型 (如: "日常留言")
     */
    private String workflowType = "日常留言";
    
    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
    
    /**
     * 输出格式 (PDF, HTML, PNG)
     */
    private String outputFormat = "PDF";
    
    /**
     * 是否包含截图
     */
    private boolean includeScreenshot = true;
    
    /**
     * 是否包含HTML源码
     */
    private boolean includeHtml = false;
    
    /**
     * 自定义输出目录
     */
    private String customOutputDir;
    
    // 构造函数
    public WorkflowExtractionRequest() {}
    
    public WorkflowExtractionRequest(String username, String password) {
        this.username = username;
        this.password = password;
    }
    
    // Getters and Setters
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getWorkflowType() {
        return workflowType;
    }
    
    public void setWorkflowType(String workflowType) {
        this.workflowType = workflowType;
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
    
    public String getOutputFormat() {
        return outputFormat;
    }
    
    public void setOutputFormat(String outputFormat) {
        this.outputFormat = outputFormat;
    }
    
    public boolean isIncludeScreenshot() {
        return includeScreenshot;
    }
    
    public void setIncludeScreenshot(boolean includeScreenshot) {
        this.includeScreenshot = includeScreenshot;
    }
    
    public boolean isIncludeHtml() {
        return includeHtml;
    }
    
    public void setIncludeHtml(boolean includeHtml) {
        this.includeHtml = includeHtml;
    }
    
    public String getCustomOutputDir() {
        return customOutputDir;
    }
    
    public void setCustomOutputDir(String customOutputDir) {
        this.customOutputDir = customOutputDir;
    }
    
    @Override
    public String toString() {
        return "WorkflowExtractionRequest{" +
                "username='" + username + '\'' +
                ", workflowType='" + workflowType + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", outputFormat='" + outputFormat + '\'' +
                ", includeScreenshot=" + includeScreenshot +
                ", includeHtml=" + includeHtml +
                '}';
    }
}