package com.laoshu198838.oa.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class WorkflowExtractionResult {
    private boolean success = false;
    private String message;
    private String errorMessage;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private int totalCount = 0;
    private int processedCount = 0;
    private int failedCount = 0;
    private List<WorkflowItem> workflows = new ArrayList<>();
    private List<String> savedFiles = new ArrayList<>();

    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    public LocalDateTime getStartTime() { return startTime; }
    public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
    public LocalDateTime getEndTime() { return endTime; }
    public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
    public int getTotalCount() { return totalCount; }
    public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
    public int getProcessedCount() { return processedCount; }
    public void setProcessedCount(int processedCount) { this.processedCount = processedCount; }
    public int getFailedCount() { 
        // Auto-calculate failed count if not explicitly set
        if (failedCount == 0 && totalCount > 0) {
            return totalCount - processedCount;
        }
        return failedCount; 
    }
    public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
    public List<WorkflowItem> getWorkflows() { return workflows; }
    public void setWorkflows(List<WorkflowItem> workflows) { this.workflows = workflows; }
    public List<String> getSavedFiles() { return savedFiles; }
    public void setSavedFiles(List<String> savedFiles) { this.savedFiles = savedFiles; }
    
    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) processedCount / totalCount * 100.0;
    }
}