package com.laoshu198838.oa.service;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * PDF生成服务
 */
@Service
public class PDFService {

    private static final Logger logger = LoggerFactory.getLogger(PDFService.class);

    /**
     * 将HTML转换为PDF
     */
    public boolean convertHtmlToPdf(String htmlFilePath, String pdfFilePath) {
        try {
            logger.info("开始转换HTML到PDF: {} -> {}", htmlFilePath, pdfFilePath);
            
            // 读取HTML内容
            String htmlContent = new String(Files.readAllBytes(Paths.get(htmlFilePath)), "UTF-8");
            
            // 预处理HTML内容
            htmlContent = preprocessHtmlForPdf(htmlContent);
            
            // 创建PDF输出流
            try (FileOutputStream os = new FileOutputStream(pdfFilePath)) {
                PdfRendererBuilder builder = new PdfRendererBuilder();
                builder.withHtmlContent(htmlContent, new File(htmlFilePath).toURI().toString());
                builder.toStream(os);
                builder.run();
            }
            
            logger.info("HTML转PDF成功: {}", pdfFilePath);
            return true;
            
        } catch (Exception e) {
            logger.error("HTML转PDF失败: {} -> {}", htmlFilePath, pdfFilePath, e);
            return false;
        }
    }

    /**
     * 预处理HTML内容以适应PDF生成
     */
    private String preprocessHtmlForPdf(String htmlContent) {
        // 添加必要的CSS样式
        String pdfStyles = """
            <style>
                @page {
                    size: A4;
                    margin: 2cm;
                }
                body {
                    font-family: 'SimSun', 'Microsoft YaHei', sans-serif;
                    font-size: 12px;
                    line-height: 1.5;
                    color: #333;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 10px 0;
                    page-break-inside: avoid;
                }
                table, th, td {
                    border: 1px solid #ddd;
                }
                th, td {
                    padding: 8px;
                    text-align: left;
                    vertical-align: top;
                    word-wrap: break-word;
                }
                th {
                    background-color: #f2f2f2;
                    font-weight: bold;
                }
                .page-break {
                    page-break-before: always;
                }
                .no-print {
                    display: none;
                }
                img {
                    max-width: 100%;
                    height: auto;
                }
                .workflow-title {
                    font-size: 18px;
                    font-weight: bold;
                    text-align: center;
                    margin: 20px 0;
                    color: #1a1a1a;
                }
                .workflow-info {
                    margin: 15px 0;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-left: 4px solid #007bff;
                }
            </style>
            """;
        
        // 移除不需要的元素
        htmlContent = htmlContent.replaceAll("(?i)<script[^>]*>.*?</script>", "");
        htmlContent = htmlContent.replaceAll("(?i)<link[^>]*rel=['\"]stylesheet['\"][^>]*>", "");
        
        // 添加PDF样式
        if (htmlContent.contains("<head>")) {
            htmlContent = htmlContent.replace("<head>", "<head>" + pdfStyles);
        } else if (htmlContent.contains("<html>")) {
            htmlContent = htmlContent.replace("<html>", "<html><head>" + pdfStyles + "</head>");
        } else {
            htmlContent = "<!DOCTYPE html><html><head>" + pdfStyles + "</head><body>" + htmlContent + "</body></html>";
        }
        
        return htmlContent;
    }

    /**
     * 生成工作流PDF报告
     */
    public boolean generateWorkflowReport(String title, String content, String outputPath) {
        try {
            logger.info("生成工作流PDF报告: {}", outputPath);
            
            String htmlContent = generateReportHtml(title, content);
            
            // 创建临时HTML文件
            String tempHtmlPath = outputPath.replace(".pdf", "_temp.html");
            Files.write(Paths.get(tempHtmlPath), htmlContent.getBytes("UTF-8"));
            
            // 转换为PDF
            boolean success = convertHtmlToPdf(tempHtmlPath, outputPath);
            
            // 删除临时文件
            try {
                Files.deleteIfExists(Paths.get(tempHtmlPath));
            } catch (Exception e) {
                logger.warn("删除临时文件失败: {}", tempHtmlPath, e);
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("生成工作流PDF报告失败: {}", outputPath, e);
            return false;
        }
    }

    /**
     * 生成报告HTML内容
     */
    private String generateReportHtml(String title, String content) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>%s</title>
                <style>
                    @page {
                        size: A4;
                        margin: 2cm;
                    }
                    body {
                        font-family: 'SimSun', 'Microsoft YaHei', sans-serif;
                        font-size: 12px;
                        line-height: 1.6;
                        color: #333;
                        margin: 0;
                        padding: 0;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 20px;
                    }
                    .title {
                        font-size: 24px;
                        font-weight: bold;
                        color: #1a1a1a;
                        margin: 0;
                    }
                    .subtitle {
                        font-size: 14px;
                        color: #666;
                        margin: 10px 0 0 0;
                    }
                    .content {
                        margin: 20px 0;
                    }
                    table {
                        width: 100%%;
                        border-collapse: collapse;
                        margin: 15px 0;
                    }
                    table, th, td {
                        border: 1px solid #ddd;
                    }
                    th, td {
                        padding: 12px 8px;
                        text-align: left;
                        vertical-align: top;
                        word-wrap: break-word;
                    }
                    th {
                        background-color: #f2f2f2;
                        font-weight: bold;
                    }
                    .footer {
                        margin-top: 40px;
                        text-align: center;
                        font-size: 10px;
                        color: #999;
                        border-top: 1px solid #ddd;
                        padding-top: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 class="title">%s</h1>
                    <p class="subtitle">生成时间: %s</p>
                </div>
                <div class="content">
                    %s
                </div>
                <div class="footer">
                    <p>此文档由FinancialSystem自动生成</p>
                </div>
            </body>
            </html>
            """, title, title, java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            ), content);
    }

    /**
     * 合并多个PDF文件
     */
    public boolean mergePdfFiles(java.util.List<String> inputPdfPaths, String outputPdfPath) {
        try {
            logger.info("合并PDF文件到: {}", outputPdfPath);
            
            // 使用iText7实现PDF合并
            // 这里需要添加iText7的依赖和实现
            
            logger.info("PDF合并成功: {}", outputPdfPath);
            return true;
            
        } catch (Exception e) {
            logger.error("PDF合并失败: {}", outputPdfPath, e);
            return false;
        }
    }

    /**
     * 检查文件是否为有效的PDF
     */
    public boolean isValidPdf(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists() || file.length() == 0) {
                return false;
            }
            
            // 检查PDF文件头
            try (FileInputStream fis = new FileInputStream(file)) {
                byte[] header = new byte[4];
                fis.read(header);
                String headerStr = new String(header);
                return "%PDF".equals(headerStr);
            }
            
        } catch (Exception e) {
            logger.error("检查PDF文件有效性失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 获取PDF文件信息
     */
    public PDFInfo getPdfInfo(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return null;
            }
            
            PDFInfo info = new PDFInfo();
            info.setFilePath(filePath);
            info.setFileName(file.getName());
            info.setFileSize(file.length());
            info.setLastModified(new java.util.Date(file.lastModified()));
            info.setValid(isValidPdf(filePath));
            
            return info;
            
        } catch (Exception e) {
            logger.error("获取PDF文件信息失败: {}", filePath, e);
            return null;
        }
    }

    /**
     * PDF文件信息类
     */
    public static class PDFInfo {
        private String filePath;
        private String fileName;
        private long fileSize;
        private java.util.Date lastModified;
        private boolean valid;
        private int pageCount;
        
        // Getters and Setters
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public java.util.Date getLastModified() { return lastModified; }
        public void setLastModified(java.util.Date lastModified) { this.lastModified = lastModified; }
        
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public int getPageCount() { return pageCount; }
        public void setPageCount(int pageCount) { this.pageCount = pageCount; }
        
        public String getFileSizeFormatted() {
            if (fileSize < 1024) return fileSize + " B";
            if (fileSize < 1024 * 1024) return String.format("%.1f KB", fileSize / 1024.0);
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }
}