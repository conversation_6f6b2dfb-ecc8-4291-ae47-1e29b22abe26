package com.laoshu198838.oa.model;

import org.openqa.selenium.WebElement;

/**
 * 工作流项目
 */
public class WorkflowItem {
    private String title;
    private String date;
    private String status;
    private String detailUrl;
    private String savedPath;
    private String errorMessage;
    private WebElement linkElement;

    // 构造函数
    public WorkflowItem() {}
    
    public WorkflowItem(String title, String date) {
        this.title = title;
        this.date = date;
    }

    // Getter和Setter方法
    public String getTitle() { 
        return title; 
    }
    
    public void setTitle(String title) { 
        this.title = title; 
    }
    
    public String getDate() { 
        return date; 
    }
    
    public void setDate(String date) { 
        this.date = date; 
    }
    
    public String getStatus() { 
        return status; 
    }
    
    public void setStatus(String status) { 
        this.status = status; 
    }
    
    public String getDetailUrl() { 
        return detailUrl; 
    }
    
    public void setDetailUrl(String detailUrl) { 
        this.detailUrl = detailUrl; 
    }
    
    public String getSavedPath() { 
        return savedPath; 
    }
    
    public void setSavedPath(String savedPath) { 
        this.savedPath = savedPath; 
    }
    
    public String getErrorMessage() { 
        return errorMessage; 
    }
    
    public void setErrorMessage(String errorMessage) { 
        this.errorMessage = errorMessage; 
    }
    
    public WebElement getLinkElement() { 
        return linkElement; 
    }
    
    public void setLinkElement(WebElement linkElement) { 
        this.linkElement = linkElement; 
    }
    
    @Override
    public String toString() {
        return "WorkflowItem{" +
                "title='" + title + '\'' +
                ", date='" + date + '\'' +
                ", status='" + status + '\'' +
                ", detailUrl='" + detailUrl + '\'' +
                ", savedPath='" + savedPath + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}