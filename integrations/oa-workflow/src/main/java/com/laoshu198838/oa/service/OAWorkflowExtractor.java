package com.laoshu198838.oa.service;

import com.laoshu198838.oa.config.OAWorkflowConfig;
import com.laoshu198838.oa.model.WorkflowExtractionRequest;
import com.laoshu198838.oa.model.WorkflowExtractionResult;
import com.laoshu198838.oa.model.WorkflowItem;
import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OA工作流提取服务
 */
@Service
public class OAWorkflowExtractor {

    private static final Logger logger = LoggerFactory.getLogger(OAWorkflowExtractor.class);

    @Autowired
    private OAWorkflowConfig config;

    @Autowired
    private PDFService pdfService;

    private WebDriver driver;
    private WebDriverWait wait;

    /**
     * 提取工作流
     */
    public WorkflowExtractionResult extractWorkflows(WorkflowExtractionRequest request) {
        WorkflowExtractionResult result = new WorkflowExtractionResult();
        result.setStartTime(LocalDateTime.now());
        result.setSuccess(false);
        
        try {
            logger.info("开始提取OA工作流，用户: {}, 时间范围: {} - {}", 
                       request.getUsername(), request.getStartDate(), request.getEndDate());
            
            // 1. 初始化WebDriver
            initializeWebDriver();
            
            // 2. 登录OA系统
            if (!login(request.getUsername(), request.getPassword())) {
                result.setErrorMessage("登录失败，请检查用户名和密码");
                return result;
            }
            
            // 3. 导航到已办事项
            if (!navigateToDoneItems()) {
                result.setErrorMessage("导航到已办事项失败");
                return result;
            }
            
            // 4. 设置筛选条件
            if (!setFilters(request)) {
                result.setErrorMessage("设置筛选条件失败");
                return result;
            }
            
            // 5. 获取工作流列表
            List<WorkflowItem> workflows = extractWorkflowList(request);
            result.setWorkflows(workflows);
            
            // 6. 处理每个工作流
            List<String> savedFiles = new ArrayList<>();
            int processed = 0;
            int total = workflows.size();
            
            for (WorkflowItem workflow : workflows) {
                try {
                    logger.info("正在处理工作流 {}/{}: {}", ++processed, total, workflow.getTitle());
                    
                    // 打开工作流详情
                    if (openWorkflowDetail(workflow)) {
                        // 保存为PDF
                        String pdfPath = saveWorkflowAsPDF(workflow);
                        if (pdfPath != null) {
                            savedFiles.add(pdfPath);
                            workflow.setSavedPath(pdfPath);
                        }
                        
                        // 返回列表页
                        driver.navigate().back();
                        Thread.sleep(2000); // 等待页面加载
                    }
                } catch (Exception e) {
                    logger.error("处理工作流失败: {}", workflow.getTitle(), e);
                    workflow.setErrorMessage(e.getMessage());
                }
            }
            
            result.setSavedFiles(savedFiles);
            result.setProcessedCount(savedFiles.size());
            result.setTotalCount(total);
            result.setSuccess(true);
            result.setMessage(String.format("成功处理 %d/%d 个工作流", savedFiles.size(), total));
            
        } catch (Exception e) {
            logger.error("提取工作流时发生错误", e);
            result.setErrorMessage("提取失败: " + e.getMessage());
        } finally {
            // 清理资源
            closeWebDriver();
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 初始化WebDriver (增强版 - 绕过403错误)
     */
    private void initializeWebDriver() {
        logger.info("初始化WebDriver (增强版反检测)");
        
        // 自动管理ChromeDriver
        WebDriverManager.chromedriver().setup();
        
        ChromeOptions options = new ChromeOptions();
        
        // 配置Chrome选项
        if (config.getWebDriver().isHeadless()) {
            options.addArguments("--headless");
        }
        
        // 基础配置
        options.addArguments("--remote-allow-origins=*");  // 修复跨域问题
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--disable-gpu");
        options.addArguments("--remote-debugging-port=9222");
        options.addArguments(String.format("--window-size=%d,%d", 
                            config.getWebDriver().getWindowWidth(), 
                            config.getWebDriver().getWindowHeight()));
        
        // 🔐 增强的反检测配置
        options.addArguments("--disable-blink-features=AutomationControlled");
        options.addArguments("--disable-web-security");
        options.addArguments("--allow-running-insecure-content");
        options.addArguments("--disable-features=TranslateUI");
        // 移除可能导致问题的选项
        // options.addArguments("--disable-iframes-sandbox");
        options.addArguments("--disable-extensions");
        // options.addArguments("--disable-plugins");  // 移除此项，某些验证码可能需要插件
        // 注意：不要禁用JavaScript和图片，否则页面无法正常渲染验证码
        // options.addArguments("--disable-images");
        // options.addArguments("--disable-javascript");
        
        // 设置真实的User-Agent
        options.addArguments("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        
        // 隐藏自动化特征
        options.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"});
        options.setExperimentalOption("useAutomationExtension", false);
        
        // 设置打印偏好
        Map<String, Object> prefs = new HashMap<>();
        prefs.put("printing.print_preview_sticky_settings.appState", 
                 "{\"version\":2,\"isColorEnabled\":false,\"isLandscapeEnabled\":false," +
                 "\"isCssBackgroundEnabled\":true,\"mediaSize\":{\"height_microns\":297000," +
                 "\"width_microns\":210000},\"marginsType\":0,\"isHeaderFooterEnabled\":false}");
        
        // 允许图片加载，确保页面正常显示（验证码需要）
        // 注意：不设置此项，保持默认允许图片加载
        
        options.setExperimentalOption("prefs", prefs);
        
        driver = new ChromeDriver(options);
        
        // 🔐 执行反检测JavaScript
        executeAntiDetectionScript();
        
        driver.manage().window().maximize();
        driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(config.getTimeout().getPageLoadTimeout()));
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(config.getTimeout().getElementWaitTimeout()));
        
        wait = new WebDriverWait(driver, Duration.ofSeconds(config.getTimeout().getElementWaitTimeout()));
    }

    /**
     * 登录OA系统（支持验证码处理，增强版）
     */
    private boolean login(String username, String password) {
        int maxRetries = 3;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info("开始登录OA系统 (第{}/{}次尝试)", attempt, maxRetries);
                
                // 🔐 访问登录页面前等待
                Thread.sleep(2000);
                
                driver.get(config.getBaseUrl());
                
                // 🔐 页面加载后再次执行反检测
                executeAntiDetectionScript();
                
                // 等待登录页面加载
                WebElement usernameField = wait.until(
                    ExpectedConditions.presenceOfElementLocated(By.id("loginid"))
                );
                
                // 🔐 模拟人工操作 - 慢速输入用户名
                usernameField.clear();
                typeSlowly(usernameField, username);
                
                // 🔐 随机等待
                Thread.sleep(500 + (int)(Math.random() * 1000));
                
                // 输入密码
                WebElement passwordField = driver.findElement(By.id("userpassword"));
                passwordField.clear();
                typeSlowly(passwordField, password);
                
                // 🔐 随机等待
                Thread.sleep(500 + (int)(Math.random() * 1000));
                
                // 检查是否有验证码
                if (handleCaptchaEnhanced()) {
                    logger.info("验证码处理完成");
                } else {
                    logger.warn("验证码处理失败，但继续尝试登录");
                }
                
                // 点击登录按钮
                WebElement loginButton = driver.findElement(By.id("submit_button"));
                
                // 🔐 模拟人工点击
                ((JavascriptExecutor) driver).executeScript("arguments[0].click();", loginButton);
                
                // 等待登录完成 - 检查是否跳转到主页面
                try {
                    wait.until(ExpectedConditions.or(
                        ExpectedConditions.urlContains("main"),
                        ExpectedConditions.urlContains("index"),
                        ExpectedConditions.presenceOfElementLocated(By.linkText("已办事项")),
                        ExpectedConditions.presenceOfElementLocated(By.xpath("//a[contains(text(), '已办事项')]"))
                    ));
                    
                    logger.info("登录成功 (第{}次尝试)", attempt);
                    return true;
                    
                } catch (TimeoutException e) {
                    logger.error("登录超时或失败 - 第{}次尝试", attempt);
                    
                    // 检查是否出现403错误
                    if (driver.getCurrentUrl().contains("403") || driver.getPageSource().contains("403")) {
                        logger.error("检测到403错误，重新初始化WebDriver");
                        closeWebDriver();
                        Thread.sleep(5000); // 等待5秒
                        initializeWebDriver();
                        continue;
                    }
                    
                    // 检查是否还在登录页面（验证码错误的情况）
                    if (isOnLoginPage()) {
                        logger.warn("仍在登录页面，可能是验证码识别失败");
                        if (attempt == maxRetries) {
                            return retryLoginWithManualCaptcha(username, password);
                        }
                    }
                    
                    // 如果不是最后一次尝试，继续重试
                    if (attempt < maxRetries) {
                        logger.info("等待{}秒后重试", attempt * 2);
                        Thread.sleep(attempt * 2000);
                    }
                }
                
            } catch (Exception e) {
                logger.error("登录过程中发生错误 (第{}次尝试)", attempt, e);
                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(attempt * 2000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        
        logger.error("登录失败，已尝试{}次", maxRetries);
        return false;
    }

    /**
     * 处理验证码 (增强版)
     */
    private boolean handleCaptchaEnhanced() {
        try {
            // 查找验证码图片元素 - 多种选择器
            List<WebElement> captchaImages = driver.findElements(By.xpath("//img[contains(@src, 'captcha') or contains(@src, 'verify') or contains(@src, 'code')]"));
            if (captchaImages.isEmpty()) {
                captchaImages = driver.findElements(By.xpath("//img[contains(@alt, '验证码') or contains(@title, '验证码')]"));
            }
            if (captchaImages.isEmpty()) {
                captchaImages = driver.findElements(By.xpath("//img[contains(@class, 'captcha') or contains(@class, 'verify')]"));
            }
            if (captchaImages.isEmpty()) {
                captchaImages = driver.findElements(By.xpath("//img[contains(@id, 'captcha') or contains(@id, 'verify')]"));
            }
            
            if (captchaImages.isEmpty()) {
                logger.info("未检测到验证码图片，可能不需要验证码");
                return true;
            }
            
            logger.info("检测到验证码，启用交互式处理模式");
            
            // 如果是非无头模式，暂停让用户手动输入验证码
            if (!config.getWebDriver().isHeadless()) {
                return waitForManualCaptchaInputEnhanced();
            } else {
                logger.warn("无头模式下检测到验证码，将尝试自动处理");
                return attemptAutoCaptchaEnhanced(captchaImages.get(0));
            }
            
        } catch (Exception e) {
            logger.error("验证码处理失败", e);
            return false;
        }
    }

    /**
     * 等待用户手动输入验证码 (增强版)
     */
    private boolean waitForManualCaptchaInputEnhanced() {
        try {
            logger.info("=== 验证码处理模式 (增强版) ===");
            logger.info("检测到验证码，请在浏览器中手动输入验证码");
            logger.info("输入完成后，程序将自动继续...");
            
            // 查找验证码输入框 - 多种选择器
            List<WebElement> captchaInputs = driver.findElements(By.xpath("//input[contains(@name, 'captcha') or contains(@name, 'verify') or contains(@name, 'code')]"));
            if (captchaInputs.isEmpty()) {
                captchaInputs = driver.findElements(By.xpath("//input[contains(@placeholder, '验证码') or contains(@title, '验证码')]"));
            }
            if (captchaInputs.isEmpty()) {
                captchaInputs = driver.findElements(By.xpath("//input[contains(@class, 'captcha') or contains(@class, 'verify')]"));
            }
            if (captchaInputs.isEmpty()) {
                captchaInputs = driver.findElements(By.xpath("//input[contains(@id, 'captcha') or contains(@id, 'verify')]"));
            }
            if (captchaInputs.isEmpty()) {
                captchaInputs = driver.findElements(By.xpath("//input[@type='text' and string-length(@name) <= 10]"));
            }
            
            if (!captchaInputs.isEmpty()) {
                WebElement captchaInput = captchaInputs.get(0);
                
                logger.info("找到验证码输入框: {}", captchaInput.getAttribute("name"));
                
                // 等待用户输入验证码（最多等待5分钟）
                for (int i = 0; i < 300; i++) {
                    Thread.sleep(1000);
                    String captchaValue = captchaInput.getAttribute("value");
                    if (captchaValue != null && captchaValue.trim().length() >= 3) {
                        logger.info("检测到验证码输入: {}", captchaValue);
                        return true;
                    }
                    
                    // 每30秒输出一次提示
                    if (i % 30 == 0 && i > 0) {
                        logger.info("等待验证码输入中... ({}/300秒)", i);
                    }
                }
                
                logger.warn("等待验证码输入超时 (5分钟)");
                return false;
            } else {
                logger.warn("未找到验证码输入框，可能不需要验证码");
                return true;
            }
            
        } catch (Exception e) {
            logger.error("等待验证码输入失败", e);
            return false;
        }
    }

    /**
     * 尝试自动识别验证码（简单数字验证码） - 增强版
     */
    private boolean attemptAutoCaptchaEnhanced(WebElement captchaImage) {
        try {
            logger.info("尝试自动识别验证码 (增强版)");
            
            // 获取验证码图片
            String base64Image = captchaImage.getScreenshotAs(OutputType.BASE64);
            
            // 尝试刷新验证码
            try {
                captchaImage.click();
                Thread.sleep(1000);
                logger.info("刷新验证码成功");
            } catch (Exception e) {
                logger.debug("无法刷新验证码: {}", e.getMessage());
            }
            
            // 这里可以集成OCR服务来识别验证码
            // 暂时返回false，表示需要手动处理
            logger.warn("自动验证码识别暂未实现，建议使用非无头模式进行手动输入");
            return false;
            
        } catch (Exception e) {
            logger.error("自动验证码识别失败", e);
            return false;
        }
    }

    /**
     * 检查是否还在登录页面
     */
    private boolean isOnLoginPage() {
        try {
            return driver.findElements(By.id("loginid")).size() > 0 ||
                   driver.findElements(By.id("userpassword")).size() > 0 ||
                   driver.getCurrentUrl().contains("login");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 重试登录（手动验证码模式）
     */
    private boolean retryLoginWithManualCaptcha(String username, String password) {
        try {
            logger.info("重试登录，启用手动验证码模式");
            
            // 如果是无头模式，切换为非无头模式
            if (config.getWebDriver().isHeadless()) {
                logger.info("切换为可视化模式以便手动输入验证码");
                driver.quit();
                
                // 临时设置为非无头模式
                boolean originalHeadless = config.getWebDriver().isHeadless();
                config.getWebDriver().setHeadless(false);
                initializeWebDriver();
                
                driver.get(config.getBaseUrl());
                
                // 重新输入用户名密码
                WebElement usernameField = wait.until(
                    ExpectedConditions.presenceOfElementLocated(By.id("loginid"))
                );
                usernameField.clear();
                usernameField.sendKeys(username);
                
                WebElement passwordField = driver.findElement(By.id("userpassword"));
                passwordField.clear();
                passwordField.sendKeys(password);
                
                // 等待用户手动处理验证码并登录
                logger.info("请在浏览器中手动输入验证码并点击登录按钮");
                logger.info("等待登录完成...");
                
                // 等待登录成功（增加等待时间）
                wait.until(ExpectedConditions.or(
                    ExpectedConditions.urlContains("main"),
                    ExpectedConditions.urlContains("index"),
                    ExpectedConditions.presenceOfElementLocated(By.linkText("已办事项")),
                    ExpectedConditions.presenceOfElementLocated(By.xpath("//a[contains(text(), '已办事项')]"))
                ));
                
                logger.info("手动登录成功");
                
                // 恢复原始设置
                config.getWebDriver().setHeadless(originalHeadless);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("重试登录失败", e);
            return false;
        }
    }

    /**
     * 导航到已办事项
     */
    private boolean navigateToDoneItems() {
        try {
            logger.info("导航到已办事项页面");
            
            // 查找并点击"已办事项"链接
            WebElement doneItemsLink = wait.until(
                ExpectedConditions.elementToBeClickable(By.linkText("已办事项"))
            );
            doneItemsLink.click();
            
            // 等待页面加载完成
            wait.until(ExpectedConditions.presenceOfElementLocated(By.className("table")));
            
            logger.info("成功导航到已办事项页面");
            return true;
            
        } catch (Exception e) {
            logger.error("导航到已办事项失败", e);
            return false;
        }
    }

    /**
     * 设置筛选条件
     */
    private boolean setFilters(WorkflowExtractionRequest request) {
        try {
            logger.info("设置筛选条件");
            
            // 如果有日期筛选需求，在这里实现
            // 由于页面结构复杂，这里可能需要根据实际页面调整
            
            return true;
            
        } catch (Exception e) {
            logger.error("设置筛选条件失败", e);
            return false;
        }
    }

    /**
     * 提取工作流列表
     */
    private List<WorkflowItem> extractWorkflowList(WorkflowExtractionRequest request) {
        List<WorkflowItem> workflows = new ArrayList<>();
        
        try {
            logger.info("提取工作流列表");
            
            // 处理分页
            int pageNumber = 1;
            boolean hasMorePages = true;
            
            while (hasMorePages) {
                logger.info("处理第 {} 页", pageNumber);
                
                // 获取当前页的工作流
                List<WorkflowItem> pageWorkflows = extractCurrentPageWorkflows(request);
                workflows.addAll(pageWorkflows);
                
                // 检查是否有下一页
                hasMorePages = goToNextPage(pageNumber);
                pageNumber++;
                
                // 防止无限循环
                if (pageNumber > 50) {
                    logger.warn("页数超过50，停止处理");
                    break;
                }
            }
            
            logger.info("共找到 {} 个工作流", workflows.size());
            
        } catch (Exception e) {
            logger.error("提取工作流列表失败", e);
        }
        
        return workflows;
    }

    /**
     * 提取当前页的工作流
     */
    private List<WorkflowItem> extractCurrentPageWorkflows(WorkflowExtractionRequest request) {
        List<WorkflowItem> workflows = new ArrayList<>();
        
        try {
            // 查找表格行
            List<WebElement> rows = driver.findElements(By.xpath("//table//tbody//tr"));
            
            for (WebElement row : rows) {
                try {
                    List<WebElement> cells = row.findElements(By.tagName("td"));
                    if (cells.size() >= 3) {
                        WorkflowItem workflow = new WorkflowItem();
                        
                        // 提取工作流信息（根据实际表格结构调整）
                        String title = cells.get(0).getText().trim();
                        String date = cells.get(1).getText().trim();
                        String status = cells.get(2).getText().trim();
                        
                        // 筛选日常留言类型的工作流
                        if (title.contains("日常留言") || 
                            title.contains(request.getWorkflowType()) ||
                            request.getWorkflowType().isEmpty()) {
                            
                            workflow.setTitle(title);
                            workflow.setDate(date);
                            workflow.setStatus(status);
                            
                            // 查找详情链接
                            WebElement linkElement = cells.get(0).findElement(By.tagName("a"));
                            workflow.setDetailUrl(linkElement.getAttribute("href"));
                            workflow.setLinkElement(linkElement);
                            
                            workflows.add(workflow);
                        }
                    }
                } catch (Exception e) {
                    logger.debug("解析表格行时出错", e);
                }
            }
            
        } catch (Exception e) {
            logger.error("提取当前页工作流失败", e);
        }
        
        return workflows;
    }

    /**
     * 转到下一页
     */
    private boolean goToNextPage(int currentPage) {
        try {
            int nextPage = currentPage + 1;
            WebElement nextPageLink = driver.findElement(
                By.xpath(String.format("//a[text()='%d']", nextPage))
            );
            
            if (nextPageLink.isDisplayed() && nextPageLink.isEnabled()) {
                nextPageLink.click();
                Thread.sleep(2000); // 等待页面加载
                return true;
            }
        } catch (Exception e) {
            logger.debug("没有更多页面或翻页失败", e);
        }
        
        return false;
    }

    /**
     * 打开工作流详情
     */
    private boolean openWorkflowDetail(WorkflowItem workflow) {
        try {
            if (workflow.getLinkElement() != null) {
                workflow.getLinkElement().click();
                Thread.sleep(3000); // 等待详情页面加载
                return true;
            } else if (workflow.getDetailUrl() != null) {
                driver.get(workflow.getDetailUrl());
                Thread.sleep(3000);
                return true;
            }
        } catch (Exception e) {
            logger.error("打开工作流详情失败: {}", workflow.getTitle(), e);
        }
        
        return false;
    }

    /**
     * 保存工作流为PDF
     */
    private String saveWorkflowAsPDF(WorkflowItem workflow) {
        try {
            // 创建输出目录
            String outputDir = createOutputDirectory();
            
            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = String.format("%s_%s.pdf", 
                                          sanitizeFileName(workflow.getTitle()), timestamp);
            String filePath = Paths.get(outputDir, fileName).toString();
            
            // 方法1: 使用浏览器打印功能
            if (saveToPDFUsingPrint(filePath)) {
                logger.info("通过打印功能保存PDF成功: {}", filePath);
                return filePath;
            }
            
            // 方法2: 使用截图 + HTML转PDF
            if (saveToHtmlThenPDF(workflow, filePath)) {
                logger.info("通过HTML转PDF保存成功: {}", filePath);
                return filePath;
            }
            
            // 方法3: 截图保存（备用方案）
            String screenshotPath = filePath.replace(".pdf", ".png");
            if (saveScreenshot(screenshotPath)) {
                logger.info("保存截图成功: {}", screenshotPath);
                return screenshotPath;
            }
            
        } catch (Exception e) {
            logger.error("保存PDF失败: {}", workflow.getTitle(), e);
        }
        
        return null;
    }

    /**
     * 使用浏览器打印功能保存PDF
     */
    private boolean saveToPDFUsingPrint(String filePath) {
        try {
            // 执行打印到PDF的JavaScript
            String script = "window.print();";
            ((JavascriptExecutor) driver).executeScript(script);
            
            // 等待打印对话框
            Thread.sleep(3000);
            
            // 这里需要处理打印对话框，保存到指定路径
            // 由于Selenium无法直接控制系统打印对话框，
            // 我们需要使用其他方法或者配置浏览器自动保存
            
            return false; // 暂时返回false，使用其他方法
            
        } catch (Exception e) {
            logger.error("使用打印功能保存PDF失败", e);
            return false;
        }
    }

    /**
     * 保存HTML然后转换为PDF
     */
    private boolean saveToHtmlThenPDF(WorkflowItem workflow, String pdfPath) {
        try {
            // 获取页面HTML
            String htmlContent = driver.getPageSource();
            
            // 保存HTML文件
            String htmlPath = pdfPath.replace(".pdf", ".html");
            Files.write(Paths.get(htmlPath), htmlContent.getBytes("UTF-8"));
            
            // 使用PDF服务转换HTML为PDF
            return pdfService.convertHtmlToPdf(htmlPath, pdfPath);
            
        } catch (Exception e) {
            logger.error("HTML转PDF失败", e);
            return false;
        }
    }

    /**
     * 保存截图
     */
    private boolean saveScreenshot(String filePath) {
        try {
            TakesScreenshot screenshot = (TakesScreenshot) driver;
            byte[] imageBytes = screenshot.getScreenshotAs(OutputType.BYTES);
            Files.write(Paths.get(filePath), imageBytes);
            return true;
        } catch (Exception e) {
            logger.error("保存截图失败", e);
            return false;
        }
    }

    /**
     * 创建输出目录
     */
    private String createOutputDirectory() throws IOException {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        Path outputPath = Paths.get(config.getFile().getOutputDir(), timestamp);
        Files.createDirectories(outputPath);
        return outputPath.toString();
    }

    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFileName(String fileName) {
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_")
                      .replaceAll("\\s+", "_")
                      .substring(0, Math.min(fileName.length(), 50));
    }

    /**
     * 🔐 执行反检测JavaScript
     */
    private void executeAntiDetectionScript() {
        try {
            String script = """
                // 移除webdriver属性
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 移除自动化相关属性
                delete window.navigator.webdriver;
                
                // 修改chrome对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };
                
                // 修改permissions
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // 修改插件信息
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // 修改语言
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
                
                console.log('反检测脚本执行完成');
                """;
            
            ((JavascriptExecutor) driver).executeScript(script);
            logger.debug("反检测JavaScript执行完成");
            
        } catch (Exception e) {
            logger.warn("执行反检测脚本失败: {}", e.getMessage());
        }
    }
    
    /**
     * 🔐 模拟人工慢速输入
     */
    private void typeSlowly(WebElement element, String text) {
        try {
            for (char c : text.toCharArray()) {
                element.sendKeys(String.valueOf(c));
                // 随机等待50-150毫秒
                Thread.sleep(50 + (int)(Math.random() * 100));
            }
        } catch (Exception e) {
            logger.warn("慢速输入失败，使用常规输入: {}", e.getMessage());
            element.sendKeys(text);
        }
    }
    
    /**
     * 🔐 模拟人工鼠标移动
     */
    private void simulateHumanMouseMovement(WebElement element) {
        try {
            // 移动到元素附近但不是正中心
            int xOffset = -5 + (int)(Math.random() * 10);
            int yOffset = -5 + (int)(Math.random() * 10);
            
            // 执行移动
            ((JavascriptExecutor) driver).executeScript(
                "var element = arguments[0]; " +
                "var event = new MouseEvent('mouseover', { " +
                "    'view': window, " +
                "    'bubbles': true, " +
                "    'cancelable': true " +
                "}); " +
                "element.dispatchEvent(event);", 
                element
            );
            
            Thread.sleep(100 + (int)(Math.random() * 200));
            
        } catch (Exception e) {
            logger.debug("模拟鼠标移动失败: {}", e.getMessage());
        }
    }
    
    /**
     * 🔐 检查和处理反爬虫机制
     */
    private boolean checkAntiCrawlerMechanisms() {
        try {
            String pageSource = driver.getPageSource().toLowerCase();
            
            // 检查常见的反爬虫标识
            String[] antiCrawlerSignals = {
                "robot", "crawler", "automation", "selenium", "webdriver",
                "captcha challenge", "access denied", "forbidden",
                "too many requests", "rate limit"
            };
            
            for (String signal : antiCrawlerSignals) {
                if (pageSource.contains(signal)) {
                    logger.warn("检测到反爬虫机制: {}", signal);
                    return true;
                }
            }
            
            // 检查HTTP状态码
            String currentUrl = driver.getCurrentUrl();
            if (currentUrl.contains("403") || currentUrl.contains("blocked")) {
                logger.warn("检测到403错误或阻止页面");
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("检查反爬虫机制失败", e);
            return false;
        }
    }
    
    /**
     * 🔐 处理403错误的恢复策略
     */
    private boolean recover403Error() {
        try {
            logger.info("尝试从403错误中恢复...");
            
            // 策略1: 清理所有Cookie和缓存
            driver.manage().deleteAllCookies();
            
            // 策略2: 等待随机时间
            int waitTime = 5000 + (int)(Math.random() * 10000); // 5-15秒
            logger.info("等待{}毫秒后重试", waitTime);
            Thread.sleep(waitTime);
            
            // 策略3: 更换User-Agent
            ((JavascriptExecutor) driver).executeScript(
                "Object.defineProperty(navigator, 'userAgent', { " +
                "get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/' + " +
                "(120 + Math.floor(Math.random() * 5)) + '.0.0.0 Safari/537.36' " +
                "});"
            );
            
            // 策略4: 重新访问首页
            driver.get(config.getBaseUrl());
            Thread.sleep(3000);
            
            // 策略5: 执行反检测脚本
            executeAntiDetectionScript();
            
            return true;
            
        } catch (Exception e) {
            logger.error("403错误恢复失败", e);
            return false;
        }
    }

    /**
     * 关闭WebDriver
     */
    private void closeWebDriver() {
        if (driver != null) {
            try {
                driver.quit();
                logger.info("WebDriver已关闭");
            } catch (Exception e) {
                logger.error("关闭WebDriver时发生错误", e);
            }
        }
    }
}