package com.laoshu198838.oa.controller;

import com.laoshu198838.oa.model.WorkflowExtractionRequest;
import com.laoshu198838.oa.model.WorkflowExtractionResult;
import com.laoshu198838.oa.service.OAWorkflowExtractor;
import com.laoshu198838.oa.service.PDFService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * OA工作流控制器
 */
@RestController
@RequestMapping("/api/oa/workflow")
public class OAWorkflowController {

    private static final Logger logger = LoggerFactory.getLogger(OAWorkflowController.class);

    @Autowired
    private OAWorkflowExtractor workflowExtractor;

    @Autowired
    private PDFService pdfService;

    /**
     * 提取工作流 - 异步处理
     */
    @PostMapping("/extract")
    public ResponseEntity<Map<String, Object>> extractWorkflows(@Valid @RequestBody WorkflowExtractionRequest request) {
        logger.info("收到工作流提取请求: {}", request);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证请求参数
            if (request.getStartDate() == null) {
                request.setStartDate(LocalDate.of(2025, 6, 1));
            }
            if (request.getEndDate() == null) {
                request.setEndDate(LocalDate.of(2025, 6, 30));
            }
            
            // 异步执行提取任务
            CompletableFuture<WorkflowExtractionResult> future = CompletableFuture.supplyAsync(() -> {
                return workflowExtractor.extractWorkflows(request);
            });
            
            // 立即返回任务已接受的响应
            response.put("success", true);
            response.put("message", "工作流提取任务已启动，正在后台处理中...");
            response.put("status", "ACCEPTED");
            response.put("requestTime", java.time.LocalDateTime.now());
            
            // 可以在这里添加任务ID和状态查询机制
            String taskId = java.util.UUID.randomUUID().toString();
            response.put("taskId", taskId);
            
            return ResponseEntity.accepted().body(response);
            
        } catch (Exception e) {
            logger.error("启动工作流提取任务失败", e);
            response.put("success", false);
            response.put("message", "启动提取任务失败: " + e.getMessage());
            response.put("status", "ERROR");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 同步提取工作流 - 用于小量数据或测试
     */
    @PostMapping("/extract-sync")
    public ResponseEntity<WorkflowExtractionResult> extractWorkflowsSync(@Valid @RequestBody WorkflowExtractionRequest request) {
        logger.info("收到同步工作流提取请求: {}", request);
        
        try {
            // 设置默认值
            if (request.getStartDate() == null) {
                request.setStartDate(LocalDate.of(2025, 6, 1));
            }
            if (request.getEndDate() == null) {
                request.setEndDate(LocalDate.of(2025, 6, 30));
            }
            
            // 执行提取
            WorkflowExtractionResult result = workflowExtractor.extractWorkflows(request);
            
            if (result.isSuccess()) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
            
        } catch (Exception e) {
            logger.error("同步提取工作流失败", e);
            WorkflowExtractionResult errorResult = new WorkflowExtractionResult();
            errorResult.setSuccess(false);
            errorResult.setErrorMessage("提取失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * 获取六月份的工作流 - 快捷方法
     */
    @PostMapping("/extract-june")
    public ResponseEntity<WorkflowExtractionResult> extractJuneWorkflows(@RequestBody Map<String, String> credentials) {
        logger.info("收到六月份工作流提取请求");
        
        try {
            String username = credentials.get("username");
            String password = credentials.get("password");
            
            if (username == null || password == null) {
                WorkflowExtractionResult errorResult = new WorkflowExtractionResult();
                errorResult.setSuccess(false);
                errorResult.setErrorMessage("用户名和密码不能为空");
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            // 创建六月份的提取请求
            WorkflowExtractionRequest request = new WorkflowExtractionRequest();
            request.setUsername(username);
            request.setPassword(password);
            request.setWorkflowType("日常留言");
            request.setStartDate(LocalDate.of(2025, 6, 1));
            request.setEndDate(LocalDate.of(2025, 6, 30));
            request.setOutputFormat("PDF");
            request.setIncludeScreenshot(true);
            
            // 执行提取
            WorkflowExtractionResult result = workflowExtractor.extractWorkflows(request);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("提取六月份工作流失败", e);
            WorkflowExtractionResult errorResult = new WorkflowExtractionResult();
            errorResult.setSuccess(false);
            errorResult.setErrorMessage("提取失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * 下载单个PDF文件
     */
    @GetMapping("/download/{fileName}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName) {
        try {
            logger.info("请求下载文件: {}", fileName);
            
            // 查找文件
            Path filePath = findFile(fileName);
            if (filePath == null || !Files.exists(filePath)) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(filePath.toFile());
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                       "attachment; filename=\"" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/pdf");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(resource.contentLength())
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("下载文件失败: {}", fileName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 批量下载PDF文件（打包为ZIP）
     */
    @PostMapping("/download-batch")
    public ResponseEntity<Resource> downloadBatch(@RequestBody List<String> fileNames) {
        try {
            logger.info("请求批量下载文件: {}", fileNames);
            
            // 创建临时ZIP文件
            String zipFileName = "oa_workflows_" + 
                               java.time.LocalDateTime.now().format(
                                   java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
                               ) + ".zip";
            
            Path zipPath = createZipFile(fileNames, zipFileName);
            
            if (zipPath == null || !Files.exists(zipPath)) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(zipPath.toFile());
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                       "attachment; filename=\"" + URLEncoder.encode(zipFileName, StandardCharsets.UTF_8) + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/zip");
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(resource.contentLength())
                    .body(resource);
                    
        } catch (Exception e) {
            logger.error("批量下载失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取可用的工作流文件列表
     */
    @GetMapping("/files")
    public ResponseEntity<Map<String, Object>> getWorkflowFiles() {
        try {
            logger.info("获取工作流文件列表");
            
            Map<String, Object> response = new HashMap<>();
            List<Map<String, Object>> files = new ArrayList<>();
            
            // 扫描输出目录
            Path outputDir = Paths.get("./oa-workflows");
            if (Files.exists(outputDir)) {
                Files.walk(outputDir)
                     .filter(Files::isRegularFile)
                     .filter(path -> path.toString().toLowerCase().endsWith(".pdf") || 
                                   path.toString().toLowerCase().endsWith(".png"))
                     .forEach(path -> {
                         try {
                             Map<String, Object> fileInfo = new HashMap<>();
                             File file = path.toFile();
                             
                             fileInfo.put("fileName", file.getName());
                             fileInfo.put("filePath", path.toString());
                             fileInfo.put("fileSize", file.length());
                             fileInfo.put("fileSizeFormatted", formatFileSize(file.length()));
                             fileInfo.put("lastModified", new java.util.Date(file.lastModified()));
                             fileInfo.put("extension", getFileExtension(file.getName()));
                             
                             // 如果是PDF，获取额外信息
                             if (file.getName().toLowerCase().endsWith(".pdf")) {
                                 PDFService.PDFInfo pdfInfo = pdfService.getPdfInfo(path.toString());
                                 if (pdfInfo != null) {
                                     fileInfo.put("valid", pdfInfo.isValid());
                                     fileInfo.put("pageCount", pdfInfo.getPageCount());
                                 }
                             }
                             
                             files.add(fileInfo);
                         } catch (Exception e) {
                             logger.warn("获取文件信息失败: {}", path, e);
                         }
                     });
            }
            
            // 按修改时间降序排序
            files.sort((a, b) -> {
                java.util.Date dateA = (java.util.Date) a.get("lastModified");
                java.util.Date dateB = (java.util.Date) b.get("lastModified");
                return dateB.compareTo(dateA);
            });
            
            response.put("success", true);
            response.put("files", files);
            response.put("totalCount", files.size());
            response.put("scanTime", java.time.LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取文件列表失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取文件列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 删除指定文件
     */
    @DeleteMapping("/files/{fileName}")
    public ResponseEntity<Map<String, Object>> deleteFile(@PathVariable String fileName) {
        try {
            logger.info("请求删除文件: {}", fileName);
            
            Path filePath = findFile(fileName);
            Map<String, Object> response = new HashMap<>();
            
            if (filePath == null || !Files.exists(filePath)) {
                response.put("success", false);
                response.put("message", "文件不存在: " + fileName);
                return ResponseEntity.notFound().build();
            }
            
            Files.delete(filePath);
            
            response.put("success", true);
            response.put("message", "文件删除成功: " + fileName);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除文件失败: {}", fileName, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "删除文件失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 健康检查 - 简化版本，不依赖任何Bean
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "OA Workflow Extractor");
        health.put("timestamp", java.time.LocalDateTime.now());
        health.put("version", "1.0.0");
        
        // 检查输出目录
        Path outputDir = Paths.get("./oa-workflows");
        health.put("outputDirectory", outputDir.toString());
        health.put("outputDirectoryExists", Files.exists(outputDir));
        
        return ResponseEntity.ok(health);
    }
    
    /**
     * 简单测试接口 - 不依赖任何注入的Bean
     */
    @GetMapping("/test")
    public ResponseEntity<String> simpleTest() {
        return ResponseEntity.ok("OA Controller is working!");
    }

    /**
     * 查找文件
     */
    private Path findFile(String fileName) throws IOException {
        Path outputDir = Paths.get("./oa-workflows");
        if (!Files.exists(outputDir)) {
            return null;
        }
        
        return Files.walk(outputDir)
                   .filter(Files::isRegularFile)
                   .filter(path -> path.getFileName().toString().equals(fileName))
                   .findFirst()
                   .orElse(null);
    }

    /**
     * 创建ZIP文件
     */
    private Path createZipFile(List<String> fileNames, String zipFileName) {
        try {
            Path zipPath = Paths.get(System.getProperty("java.io.tmpdir"), zipFileName);
            
            // 这里需要实现ZIP文件创建逻辑
            // 使用java.util.zip.ZipOutputStream
            
            return zipPath;
        } catch (Exception e) {
            logger.error("创建ZIP文件失败", e);
            return null;
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        return String.format("%.1f MB", size / (1024.0 * 1024.0));
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }
}