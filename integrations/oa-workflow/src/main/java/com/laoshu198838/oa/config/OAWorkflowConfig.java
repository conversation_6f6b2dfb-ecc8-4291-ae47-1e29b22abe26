package com.laoshu198838.oa.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * OA工作流配置类
 */
@Configuration
@ConfigurationProperties(prefix = "oa.workflow")
public class OAWorkflowConfig {
    
    /**
     * OA系统基础URL
     */
    private String baseUrl = "http://10.25.1.18:8888/wui/index.html";
    
    /**
     * Chrome WebDriver配置
     */
    private WebDriverConfig webDriver = new WebDriverConfig();
    
    /**
     * 文件保存配置
     */
    private FileConfig file = new FileConfig();
    
    /**
     * 超时配置
     */
    private TimeoutConfig timeout = new TimeoutConfig();
    
    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }
    
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
    
    public WebDriverConfig getWebDriver() {
        return webDriver;
    }
    
    public void setWebDriver(WebDriverConfig webDriver) {
        this.webDriver = webDriver;
    }
    
    public FileConfig getFile() {
        return file;
    }
    
    public void setFile(FileConfig file) {
        this.file = file;
    }
    
    public TimeoutConfig getTimeout() {
        return timeout;
    }
    
    public void setTimeout(TimeoutConfig timeout) {
        this.timeout = timeout;
    }
    
    /**
     * WebDriver配置
     */
    public static class WebDriverConfig {
        private boolean headless = false;
        private int windowWidth = 1920;
        private int windowHeight = 1080;
        private int pageLoadTimeout = 30;
        private int implicitlyWait = 10;
        
        // Getters and Setters
        public boolean isHeadless() {
            return headless;
        }
        
        public void setHeadless(boolean headless) {
            this.headless = headless;
        }
        
        public int getWindowWidth() {
            return windowWidth;
        }
        
        public void setWindowWidth(int windowWidth) {
            this.windowWidth = windowWidth;
        }
        
        public int getWindowHeight() {
            return windowHeight;
        }
        
        public void setWindowHeight(int windowHeight) {
            this.windowHeight = windowHeight;
        }
        
        public int getPageLoadTimeout() {
            return pageLoadTimeout;
        }
        
        public void setPageLoadTimeout(int pageLoadTimeout) {
            this.pageLoadTimeout = pageLoadTimeout;
        }
        
        public int getImplicitlyWait() {
            return implicitlyWait;
        }
        
        public void setImplicitlyWait(int implicitlyWait) {
            this.implicitlyWait = implicitlyWait;
        }
    }
    
    /**
     * 文件配置
     */
    public static class FileConfig {
        private String outputDir = "./oa-workflows";
        private String pdfDir = "pdf";
        private String screenshotDir = "screenshots";
        private String htmlDir = "html";
        
        // Getters and Setters
        public String getOutputDir() {
            return outputDir;
        }
        
        public void setOutputDir(String outputDir) {
            this.outputDir = outputDir;
        }
        
        public String getPdfDir() {
            return pdfDir;
        }
        
        public void setPdfDir(String pdfDir) {
            this.pdfDir = pdfDir;
        }
        
        public String getScreenshotDir() {
            return screenshotDir;
        }
        
        public void setScreenshotDir(String screenshotDir) {
            this.screenshotDir = screenshotDir;
        }
        
        public String getHtmlDir() {
            return htmlDir;
        }
        
        public void setHtmlDir(String htmlDir) {
            this.htmlDir = htmlDir;
        }
    }
    
    /**
     * 超时配置
     */
    public static class TimeoutConfig {
        private int loginTimeout = 10;
        private int pageLoadTimeout = 30;
        private int elementWaitTimeout = 10;
        private int printTimeout = 15;
        
        // Getters and Setters
        public int getLoginTimeout() {
            return loginTimeout;
        }
        
        public void setLoginTimeout(int loginTimeout) {
            this.loginTimeout = loginTimeout;
        }
        
        public int getPageLoadTimeout() {
            return pageLoadTimeout;
        }
        
        public void setPageLoadTimeout(int pageLoadTimeout) {
            this.pageLoadTimeout = pageLoadTimeout;
        }
        
        public int getElementWaitTimeout() {
            return elementWaitTimeout;
        }
        
        public void setElementWaitTimeout(int elementWaitTimeout) {
            this.elementWaitTimeout = elementWaitTimeout;
        }
        
        public int getPrintTimeout() {
            return printTimeout;
        }
        
        public void setPrintTimeout(int printTimeout) {
            this.printTimeout = printTimeout;
        }
    }
}