package com.laoshu198838.oa.service;

import com.laoshu198838.oa.config.OAWorkflowConfig;
import com.laoshu198838.oa.model.WorkflowExtractionRequest;
import com.laoshu198838.oa.model.WorkflowExtractionResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OA工作流提取器测试
 */
@ExtendWith(MockitoExtension.class)
public class OAWorkflowExtractorTest {

    @Mock
    private OAWorkflowConfig config;

    @Mock
    private PDFService pdfService;

    @InjectMocks
    private OAWorkflowExtractor extractor;

    private WorkflowExtractionRequest request;

    @BeforeEach
    void setUp() {
        // 创建测试请求
        request = new WorkflowExtractionRequest();
        request.setUsername("zhoulb");
        request.setPassword("Zlb&198838");
        request.setWorkflowType("日常留言");
        request.setStartDate(LocalDate.now().minusDays(3)); // 最近3天
        request.setEndDate(LocalDate.now());
        request.setOutputFormat("PDF");
        request.setIncludeScreenshot(true);
    }

    @Test
    void testWorkflowExtractionRequest() {
        // 验证请求对象的基本属性
        assertNotNull(request);
        assertEquals("zhoulb", request.getUsername());
        assertEquals("日常留言", request.getWorkflowType());
        assertEquals("PDF", request.getOutputFormat());
        assertTrue(request.isIncludeScreenshot());
        
        // 验证日期范围（最近3天）
        LocalDate today = LocalDate.now();
        LocalDate threeDaysAgo = today.minusDays(3);
        
        assertEquals(threeDaysAgo, request.getStartDate());
        assertEquals(today, request.getEndDate());
        
        System.out.println("✅ 测试请求参数验证通过");
        System.out.println("   - 用户名: " + request.getUsername());
        System.out.println("   - 工作流类型: " + request.getWorkflowType());
        System.out.println("   - 时间范围: " + request.getStartDate() + " 到 " + request.getEndDate());
        System.out.println("   - 输出格式: " + request.getOutputFormat());
    }

    @Test
    void testCreateRecentThreeDaysRequest() {
        // 测试创建最近3天的请求
        WorkflowExtractionRequest recentRequest = createRecentThreeDaysRequest("zhoulb", "Zlb&198838");
        
        assertNotNull(recentRequest);
        assertEquals("zhoulb", recentRequest.getUsername());
        assertEquals("日常留言", recentRequest.getWorkflowType());
        
        // 验证时间范围是最近3天
        LocalDate today = LocalDate.now();
        LocalDate threeDaysAgo = today.minusDays(3);
        
        assertEquals(threeDaysAgo, recentRequest.getStartDate());
        assertEquals(today, recentRequest.getEndDate());
        
        System.out.println("✅ 最近3天请求创建成功");
        System.out.println("   - 开始日期: " + recentRequest.getStartDate());
        System.out.println("   - 结束日期: " + recentRequest.getEndDate());
        System.out.println("   - 天数: " + java.time.temporal.ChronoUnit.DAYS.between(recentRequest.getStartDate(), recentRequest.getEndDate()) + " 天");
    }

    @Test
    void testWorkflowExtractionResultStructure() {
        // 测试结果对象的结构
        WorkflowExtractionResult result = new WorkflowExtractionResult();
        result.setSuccess(true);
        result.setMessage("测试提取完成");
        result.setTotalCount(5);
        result.setProcessedCount(4);
        
        assertTrue(result.isSuccess());
        assertEquals("测试提取完成", result.getMessage());
        assertEquals(5, result.getTotalCount());
        assertEquals(4, result.getProcessedCount());
        assertEquals(1, result.getFailedCount()); // 自动计算
        assertEquals(80.0, result.getSuccessRate(), 0.1); // 4/5 = 80%
        
        System.out.println("✅ 结果对象结构验证通过");
        System.out.println("   - 总数: " + result.getTotalCount());
        System.out.println("   - 成功: " + result.getProcessedCount());
        System.out.println("   - 失败: " + result.getFailedCount());
        System.out.println("   - 成功率: " + result.getSuccessRate() + "%");
    }

    @Test
    void testDateRangeCalculation() {
        // 测试不同的日期范围计算
        LocalDate today = LocalDate.now();
        
        // 最近1天
        WorkflowExtractionRequest oneDay = createDateRangeRequest(today.minusDays(1), today);
        assertEquals(1, calculateDaysBetween(oneDay));
        
        // 最近3天
        WorkflowExtractionRequest threeDays = createDateRangeRequest(today.minusDays(3), today);
        assertEquals(3, calculateDaysBetween(threeDays));
        
        // 最近7天
        WorkflowExtractionRequest sevenDays = createDateRangeRequest(today.minusDays(7), today);
        assertEquals(7, calculateDaysBetween(sevenDays));
        
        System.out.println("✅ 日期范围计算验证通过");
        System.out.println("   - 1天范围: " + oneDay.getStartDate() + " 到 " + oneDay.getEndDate());
        System.out.println("   - 3天范围: " + threeDays.getStartDate() + " 到 " + threeDays.getEndDate());
        System.out.println("   - 7天范围: " + sevenDays.getStartDate() + " 到 " + sevenDays.getEndDate());
    }

    @Test
    void testWorkflowTypeFiltering() {
        // 测试工作流类型筛选
        assertTrue(isMatchingWorkflowType("关于XX的日常留言", "日常留言"));
        assertTrue(isMatchingWorkflowType("日常留言-工作汇报", "日常留言"));
        assertTrue(isMatchingWorkflowType("日常留言", "日常留言"));
        assertFalse(isMatchingWorkflowType("会议纪要", "日常留言"));
        assertFalse(isMatchingWorkflowType("审批申请", "日常留言"));
        
        System.out.println("✅ 工作流类型筛选验证通过");
        System.out.println("   - '关于XX的日常留言' 匹配 '日常留言': ✓");
        System.out.println("   - '会议纪要' 匹配 '日常留言': ✗");
    }

    @Test
    void testFileNameGeneration() {
        // 测试文件名生成
        String title1 = "关于向集团报送万润科技重大资产股权";
        String title2 = "日常留言-工作汇报";
        String title3 = "包含/特殊\\字符:的*标题?";
        
        String fileName1 = generateSafeFileName(title1);
        String fileName2 = generateSafeFileName(title2);
        String fileName3 = generateSafeFileName(title3);
        
        assertFalse(fileName1.contains("/"));
        assertFalse(fileName1.contains("\\"));
        assertFalse(fileName2.contains(" "));
        assertFalse(fileName3.contains(":"));
        assertFalse(fileName3.contains("*"));
        assertFalse(fileName3.contains("?"));
        
        System.out.println("✅ 文件名生成验证通过");
        System.out.println("   - 原标题: " + title1);
        System.out.println("   - 安全文件名: " + fileName1);
        System.out.println("   - 原标题: " + title3);
        System.out.println("   - 安全文件名: " + fileName3);
    }

    // 辅助方法
    private WorkflowExtractionRequest createRecentThreeDaysRequest(String username, String password) {
        WorkflowExtractionRequest request = new WorkflowExtractionRequest();
        request.setUsername(username);
        request.setPassword(password);
        request.setWorkflowType("日常留言");
        
        LocalDate today = LocalDate.now();
        request.setStartDate(today.minusDays(3));
        request.setEndDate(today);
        
        request.setOutputFormat("PDF");
        request.setIncludeScreenshot(true);
        
        return request;
    }

    private WorkflowExtractionRequest createDateRangeRequest(LocalDate startDate, LocalDate endDate) {
        WorkflowExtractionRequest request = new WorkflowExtractionRequest();
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        return request;
    }

    private long calculateDaysBetween(WorkflowExtractionRequest request) {
        return java.time.temporal.ChronoUnit.DAYS.between(
            request.getStartDate(), 
            request.getEndDate()
        );
    }

    private boolean isMatchingWorkflowType(String title, String targetType) {
        return title.contains(targetType) || targetType.isEmpty();
    }

    private String generateSafeFileName(String title) {
        return title.replaceAll("[\\\\/:*?\"<>|]", "_")
                   .replaceAll("\\s+", "_")
                   .substring(0, Math.min(title.length(), 50));
    }
}