# 🏦 司库系统配置参数

## 📋 基础配置信息

### 客户信息
| 参数名 | 参数值 | 说明 |
|--------|--------|------|
| 客户号 | 001701393763 | 中信银行司库系统客户标识 |
| 客户名称 | 深圳万润科技股份有限公司ERP | 企业全称 |
| 直联用户代码 | SZWR003_ZL | 银企直联用户代码 |
| 直联端口 | 6767 | 司库系统接口端口 |
| 登录用户名 | *********** | 司库系统登录用户名 |

### 网络配置
| 参数名 | 参数值 | 说明 |
|--------|--------|------|
| 司库系统地址 | http://**********:6789 | 中信银行司库系统接口地址 |
| 字符编码 | GBK | 请求和响应的字符编码 |
| 连接超时 | 30秒 | HTTP连接超时时间 |
| 读取超时 | 60秒 | HTTP读取超时时间 |

## 💰 账户配置

### 主要付款账户
| 账户类型 | 账户号码 | 账户名称 | 开户网点 |
|----------|----------|----------|----------|
| 付款账号1 | 8110701012901269085 | 深圳万润科技股份有限公司ERP | 中信银行北京朝阳支行 |
| 付款账号2 | 8110701013101269086 | 深圳万润科技股份有限公司ERP | 中信银行北京朝阳支行 |
| 付款账号3 | 8110701013501269087 | 深圳万润科技股份有限公司ERP | 中信银行北京朝阳支行 |

### 测试账户
| 账户类型 | 账户号码 | 账户名称 | 开户网点 |
|----------|----------|----------|----------|
| 测试收款对公 | 8110701013501262521 | 培训机构70 | 中信银行北京分行营业部 |
| 测试收款对私 | 8110701213401262624 | 收款对私测试账户 | 中信银行北京分行营业部 |

### 跨行转账测试
| 参数 | 值 | 说明 |
|------|-----|------|
| 账户号 | 任意数字 | 用于跨行转账测试 |
| 账户名称 | 任意 | 测试用账户名称 |
| 开户网点 | 中国工商银行总行清算中心 | 跨行测试开户行 |

## 🔧 系统配置

### Spring Boot配置
```yaml
treasury:
  # 基础连接配置
  endpoint: http://**********:6789
  username: ***********
  client-code: SZWR003_ZL
  client-name: 深圳万润科技股份有限公司ERP

  # 默认账户
  default-account: 8110701012901269085

  # 连接参数
  connection:
    timeout: 30000          # 连接超时(毫秒)
    read-timeout: 60000     # 读取超时(毫秒)
    retry-count: 3          # 重试次数

  # 缓存配置
  cache:
    enabled: true
    balance-cache-duration: 300      # 余额缓存时间(秒)
    transaction-cache-duration: 600  # 交易缓存时间(秒)

  # 账户列表
  accounts:
    - account-no: 8110701012901269085
      account-name: 深圳万润科技股份有限公司ERP
      account-type: 付款账号1
      bank: 中信银行北京朝阳支行
    - account-no: 8110701013101269086
      account-name: 深圳万润科技股份有限公司ERP
      account-type: 付款账号2
      bank: 中信银行北京朝阳支行
    - account-no: 8110701013501269087
      account-name: 深圳万润科技股份有限公司ERP
      account-type: 付款账号3
      bank: 中信银行北京朝阳支行
```

### 环境变量配置
```bash
# 司库系统环境变量
export TREASURY_ENDPOINT=http://**********:6789
export TREASURY_USERNAME=***********
export TREASURY_CLIENT_CODE=SZWR003_ZL
export TREASURY_DEFAULT_ACCOUNT=8110701012901269085
```

## 📊 接口权限配置

### 查询权限
| 接口代码 | 接口名称 | 权限要求 |
|----------|----------|----------|
| SKBALQRY | 账户余额查询 | 需要账户查询权限 |
| SKTRNCOL | 当日交易明细查询 | 需要账户查询权限 |
| SKACCQRY | 账户信息查询 | 需要账户查询权限 |
| SKHISQRY | 历史明细查询申请 | 需要历史查询权限 |
| SKHISRES | 历史明细结果查询 | 需要历史查询权限 |
| SKHISBAL | 历史余额查询申请 | 需要历史查询权限 |
| SKHISBALRES | 历史余额结果查询 | 需要历史查询权限 |

### 权限说明
- **账户查询权限**: 可以查询指定账户的基本信息和实时数据
- **历史查询权限**: 可以查询账户的历史交易和余额数据
- **用户权限**: 银企直联用户需要具有相关账户的使用权限

## 🔍 数据限制

### 查询限制
| 限制类型 | 限制值 | 说明 |
|----------|--------|------|
| 余额查询账户数 | 最多20个 | 单次余额查询支持的最大账户数 |
| 交易明细分页 | 每页100条 | 交易明细查询每页最大记录数 |
| 历史查询时间范围 | 最多1年 | 历史数据查询的最大时间跨度 |
| 历史余额分页 | 每页100条 | 历史余额查询每页最大记录数 |

### 字段长度限制
| 字段类型 | 最大长度 | 编码方式 |
|----------|----------|----------|
| 账户号码 | 40字符 | GBK编码 |
| 账户名称 | 120字符 | GBK编码 |
| 开户行名称 | 300字符 | GBK编码 |
| 交易摘要 | 200字符 | GBK编码 |
| 备注信息 | 500字符 | GBK编码 |

## 🚀 部署配置

### 生产环境
```properties
# 生产环境配置
treasury.endpoint=http://**********:6789
treasury.username=***********
treasury.client-code=SZWR003_ZL
treasury.default-account=8110701012901269085
treasury.connection.timeout=30000
treasury.connection.read-timeout=60000
treasury.cache.enabled=true
```

### 测试环境
```properties
# 测试环境配置
treasury.endpoint=http://**********:6789
treasury.username=***********
treasury.client-code=SZWR003_ZL
treasury.default-account=8110701013501262521
treasury.connection.timeout=10000
treasury.connection.read-timeout=30000
treasury.cache.enabled=false
```

### 开发环境
```properties
# 开发环境配置
treasury.endpoint=http://**********:6789
treasury.username=***********
treasury.client-code=SZWR003_ZL
treasury.default-account=8110701213401262624
treasury.connection.timeout=5000
treasury.connection.read-timeout=15000
treasury.cache.enabled=false
```

## 🔐 安全配置

### 网络安全
- **防火墙配置**: 确保服务器可以访问**********:6789
- **SSL/TLS**: 如果司库系统支持，建议使用HTTPS
- **IP白名单**: 在司库系统中配置服务器IP白名单

### 认证安全
- **用户名密码**: 定期更换司库系统登录密码
- **权限最小化**: 只授予必要的账户查询权限
- **审计日志**: 记录所有司库系统访问日志

### 数据安全
- **数据加密**: 敏感数据在传输和存储时加密
- **访问控制**: 限制对司库数据的访问权限
- **备份策略**: 定期备份司库数据

## 📝 配置检查清单

### 部署前检查
- [ ] 网络连通性测试
- [ ] 用户名和密码验证
- [ ] 账户权限确认
- [ ] 接口功能测试
- [ ] 数据格式验证

### 运行时监控
- [ ] 连接状态监控
- [ ] 响应时间监控
- [ ] 错误率监控
- [ ] 数据同步状态
- [ ] 缓存命中率

### 定期维护
- [ ] 密码更新
- [ ] 权限审查
- [ ] 性能优化
- [ ] 日志清理
- [ ] 配置备份

---

**配置版本**: v1.0
**更新日期**: 2025-06-23
**维护者**: FinancialSystem开发团队
