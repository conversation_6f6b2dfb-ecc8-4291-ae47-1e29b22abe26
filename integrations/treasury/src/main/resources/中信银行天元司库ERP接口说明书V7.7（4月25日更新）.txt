 






天元司库ERP
接口说明书
版本 V7.7











修改记录
日期	版本	作者/修改者	描述	审核人
2023.3.20	V1.0	中信银行	创建文档，创建目录结构，编写报文结构、报文规则、附录等。	中信银行
2023.4.7	V5.1	中信银行	账户信息查询接口新增：是否具有使用权限字段	中信银行
2023.4.10	V5.2	中信银行	增加外部流水号和回单编号的联动关系，更新样例报文	中信银行
2023.4.13	V5.3	中信银行	历史明细查询接口list增加本方账号相关信息	中信银行
2023.4.27	V5.4	中信银行	增加付款请求批次号和银行交易流水号相关字段	中信银行
2023.5.6	V5.5	中信银行	1.对部分字段长度及日期格式进行修正；
2.回单查询接口入参新增tranType字段；	中信银行

2023.5.9	V5.6	中信银行	增加账户历史余额查询申请和账户历史余额查询接口	中信银行
2023.5.22	V5.7	中信银行	按客户反馈优化单笔支付接口、多笔支付接口	中信银行
2023.5.24	V5.8	中信银行	增加支付对账机制的说明	中信银行
2023.5.29	V5.9	中信银行	历史余额接口增加分页和查询时间限制	中信银行
2023.9.08	V6.0	中信银行	增加境内网点查询接口、单笔排款接口和排款查证接口	中信银行
2023.11.14	V6.4	中信银行	1.	账户信息查询接口按照账户中心调整进行相关字段调整；
2.	电子回单查询接口增加回单状态相关字段；
3.	单笔付款查证接口和批量付款查证接口输出增加“备注”字段；
4.	不同付款方银行附言长度增加相关银行；	中信银行
2023.12.18	V6.5	中信银行	1.增加票证中心接口：票据信息查询接口和票据背面查询接口；
2.单笔付款查证和批量付款查证扩展备注字段增加审批时间、外系统提单时间；
3.不同银行附言增加银行列表。	中信银行
2023.12.28	V6.6	中信银行	1.增加预算中心接口：
2.增加账户开户申请和账户开户状态查询接口；
3.优化账户信息查询接口；
4.新增团金宝支付、排款经办、排款查证相关接口； 
5.erp单笔付款、批量付款联动下拨、去掉收方名称字段 ；
6.erp单笔付款查证、批量付款查证增加资金下拨结果；
7.电子回单查询接口增加输出内容；	中信银行
2024.3.1	V6.7	中信银行	1.	新增薪酬代发接口；
2.	单笔付款、批量付款增加卡bin识别；
3.	“批量付款查证”接口修改为“多笔付款查证”	中信银行
2024.3.19	V6.8	中信银行	1.新增排款票据信息查询接口；
2.更新薪酬代发接口，并更新支持薪酬代发银行范围；
3.更新票证中心和预算中心接口上线时间；
4.票证中心相关接口补充票据状态和流通状态枚举值；
5.新增票据交易查询接口；
6.当日交易明细查询接口和历史交易明细查询接口优化，增加“拓展字段1”，当返回兴业银行流水明细时赋值SRVRTID；
7.批量代发接口请求报文更新；
8、票据详情查询接口输出字段以及示例报文进行调整；	中信银行
2024.3.19	V6.9	中信银行	1、跨境中心新增来账预知查询、全集团境外账户信息查询、汇入汇款列表查询、境外账户余额查询、境外账户历史余额查询；
2、单笔/批量查证接口，输出增加付款方相关信息；
3、排款查证接口输出增加付方联行号；
4、3.1.4 电子回单：SKEDDRSQ 支持申请当日回单回单（中信、招商、平安）
5、3.1.8 历史明细：申请（SKTRNHCL），查询（SKTRNHCT）可查司库已有境外银行数据（渣打、汇丰）
6、3.1.10 历史余额：申请（SKBALHSQ），查询（SKBALHCX）可查司库已有境外银行数据（渣打、汇丰）
7、票证中心新增票据背书申请接口和票据状态制单查询接口。	中信银行
2024.6.13	V7.0	中信银行	10、增加资讯中心企业工商信息查询接口；
11、增加应收应付中心企业风险查询接口；
12、批量付款接口增加明细交易处理模式；
4、排款查证接口增加返回待签收票据张数、待签收票据金额；
5、新增认领明细查询接口；
6、补充薪酬代发银行（基于业务群诉求）；
7、补充薪酬代发银行代发项目列表（基于业务群诉求）；
8、 补充团金宝交易接口对账编码字段描述；9、当日明细查询接口、电子回单查询结果和历史明细查询结果由20条改为100条；
13、优化电子回单，支持OFD格式；
14、交易明细优化，当日明细和历史明细支持返回原始的他行银行流水号；
15、电子回单增加支持T+0回单查询银行范围；
16、更新支持对账银行范围；
17、3.2.11 收款明细标签查询的接口名称调整并增加查询限制逻辑；
18、账户相关接口增加查询限制逻辑描述；	中信银行
2024.11.15	V7.1	中信银行	1、3.2.5排款接口，更新请求字段“应付日期”的字段描述（移除“不能早于当前日期”的限制），新增“排款份额”和“备用字段1-4”字段；
2、5.8不同付方银行支持附言长度，调整兴业银行附言长度为255字符，新增紫金银行、平安银行和芜湖扬子农村商业银行；
3、3.2.1单笔付款接口，新增“是否准确校验开户行行名”字段；
4、3.2.10批量代发接口，“代发月份”字段描述新增齐鲁、工商和兴业银行；“代发用途”新增民生银行；
5、5.10支持薪酬代发银行范围，新增光大银行和民生银行；
6、5.11薪酬代发银行代发项目、代发用途，新增中行、农行、民生和光大银行。	中信银行
2024.12.06	V7.2	中信银行	1、3.1.5电子回单查询，单客户一分钟访问次限制提高到30次；
2、3.1.6电子回单下载，单客户一分钟访问次限制提高到30次；
3、7.1企业工商信息查询，请求字段“企业全称”改为“企业全称/统一社会信用代码”；
4、7.1企业工商信息查询，返回字段“企业全称”改为“企业全称/统一社会信用代码”；
5、7.1企业工商信息查询，删除返回字段“高管关联企业entid”字段及股东SHAREHOLDER列表中“是否可以下探”；
6、7.1企业工商信息查询，修改FILIATION列表返回字段“对外投资”，改名为“分支机构”；
7、7.1企业工商信息查询，修改SHAREHOLDERHIS列表返回字段“企业状态”，改名为“历史股东”；
8、3.8.1企业风险查询，请求字段新增“是否保存客商”，支持将上送客商信息保存至天元司库系统；
9、新增3.8.2企业风险详情查询接口（暂未上线）。
10、票据背书申请ERP接口、票据制单查询ERP接口、票据列表查询接口、票据交易查询接口最大笔数调整至200笔。详见3.4.1、3.4.3、3.4.4、3.4.5；
11、新增票据详情批量查询接口，详见3.4.6；
12、新增票据行为签收查询接口，详见3.4.7。	
2024.12.20	V7.3	中信银行	1、增加工行代发用途、新增重庆银行代发用途（暂未上线）、新增郑州银行代发项目（暂未上线）；
2、新增支持银行附言长度；
3、“单笔付款查证”接口，“extendRemark备注”增加 4、pyTmlnsFlag 预约付款：02己取消”；	
2025.01.10	V7.4	中信银行	1、	结算中心单笔付款查证接口，响应报文“extendRemark”字段值新增“交易时间”内容；
2、	结算中心批量付款接口，请求字段新增“是否跨行”、“是否准确校验开户行行名”；
3、	结算中心排款接口，请求报文“结算方式”字段枚举值新增“银行汇票”、“银行本票”，“排款份额”字段长度、备注调整；
4、	结算中心排款查证接口，响应报文新增“备注”字段；
5、	不同付方银行支持附言长度章节，新增9家银行描述；
6、	票据交易查询接口，响应报文新增交易流水号、查询类型字段；
7、	新增票据行为查询接口。	
2025.02.05	V7.5	中信银行	1、团金宝付款增加“联动支付”字段；
2、排款及排款查证接口增加结算方式：供应链支付；
3、退汇接口说明调整，“外部请求批次号”和“外部请求流水号”的“内容说明”字段描述调整；
4、单笔付款接口增加备用字段1-4；
5、浦发费项编码在现有基础上增加1030报销款；
6、无锡农村商业银行、山西农信社、西宁农商行、徽商银行、西安银行支持代发，更新代发接口和附录；
7、不同付方银行支持附言长度附录更新；
8、单笔付款查证接口SKDLBATD，【备注】字段增加司库删除人的司库用户名称，仅在单据被删除时进行展示和返回；
9、多笔付款查证接口SKDLBATC，【备注】字段增加司库删除人的司库用户名称，仅在单据被删除时进行展示和返回；
10、新增排款供应链查询接口。	
2025.03.21	V7.6	中信银行	1、	新增多账户历史明细查询接口；
2、	当日交易明细查询接口返回报文增加“记账日期”、“退汇标识”、“交易流水号”等字段；
3、	历史交易明细结果查询接口返回报文增加“记账日期”字段；
4、	电子回单申请接口，支持工商银行当日电子回单
5、	退汇交易查询接口支持自动退汇和手工退汇交易查询，其中自动退汇交易查询银行范围包括：中信银行、招商银行、平安银行退汇；返回报文的“付款类型” -新增团金宝、联动团金宝；
6、	团金宝付款接口，参数说明【收方联行号】，说明补充文案：当收方账户为非银联卡（对公账户/存折）时，收方开户行或收方联行号选择一个必输即可；当付款金额≤100万时，收方开户行如果录入，系统不做精准校验；  当付款金额>100万时，如录入收方开户行，则需精确录入，系统做精准校验。
7、	付方银行附言长度，补充西安银行、华润银行、温州银行、江南农商行、杭州银行、绍兴银行、山西农信社、湖南银行、合肥科技/安徽肥西/安徽肥东/安徽长丰农村商业银行、芜湖杨子农商的附言；
8、	支持薪酬代发银行范围，新增徽商银行、山西农信社、西安银行。
9、	薪酬代发银行代发项目、代发用途码表-新增中信银行；
10、	票据详情查询接口，票据背面信息返回增加流水号；
11、	票据详情批量查询接口，票据背面信息返回增加流水号；
12、	票据行为查询接口，查询条件新增“对手方名称”，查询条件中“处理结果”字段支持多选；
13、	票据交易查询接口，查询条件新增“处理结果”；
14、供应链凭证登记查询接口，新增供应链凭证登记查询接口。	
2025.04.25	V7.7	中信银行	1.	薪酬代发接口，代发用途字段增加山西农信社描述；
2.	付方银行附言长度，补充宁波银行、广东发展银行、宁波通商银行、长沙银行、徽商银行等共15家银行的说明；
3.	支持薪酬代发银行范围，新增山西农信社、河北银行；
4.	薪酬代发银行代发项目、代发用途码表，新增山西农信社；
5.	单笔付款、批量付款、团金宝付款、批量代发【薪酬代发】，【rsrvtnTms 预约时间】字段，时分格式为枚举值 06:00-22:00任一时间；
6.	排款，结算方式新增-0D项目贷支付；
7.	票据行为查询接口，响应报文新增“更新时间”字段，新增“经办企业账号名称”。	

 
目 录
第一章 报文结构	1
1.1 HTTP请求报文	1
1.2 HTTP响应报文	1
第二章 报文定义规则	2
2.1 XML报文格式	2
2.2 数据项说明	2
第三章 报文接口	3
3.1 账户中心	3
3.1.1 账户余额查询	3
3.1.1.1 参数说明	3
3.1.1.2 请求报文	5
3.1.1.3 响应报文	5
3.1.2 当日交易明细查询	6
3.1.2.1 参数说明	6
3.1.2.2 请求报文	12
3.1.2.3 响应报文	12
3.1.3 账户信息查询	14
3.1.3.1 参数说明	15
3.1.3.2 请求报文	21
3.1.3.3 响应报文	22
3.1.4 电子回单申请	24
3.1.4.1 参数说明	24
3.1.4.2 请求报文	25
3.1.4.3 响应报文	26
3.1.5 电子回单查询	26
3.1.5.1 参数说明	27
3.1.5.2 请求报文	30
3.1.5.3 响应报文	30
3.1.6 电子回单文件下载	32
3.1.6.1 参数说明	32
******* 请求报文	34
******* 响应报文	35
3.1.7 历史明细查询申请	36
******* 参数说明	36
3.1.7.2 请求报文	38
3.1.7.3 响应报文	38
3.1.8 历史明细结果查询	38
3.1.8.1 参数说明	39
3.1.8.2 请求报文	44
3.1.8.3 响应报文	44
3.1.9 历史余额查询申请	46
3.1.9.1 参数说明	46
3.1.9.2 请求报文	48
3.1.9.3 响应报文	48
3.1.10 历史余额结果查询	49
3.1.10.1 参数说明	49
3.1.10.2 请求报文	52
3.1.10.3 响应报文	52
3.2 结算中心	错误!未定义书签。
3.2.1 单笔付款	错误!未定义书签。
3.2.1.1 参数说明	错误!未定义书签。
3.2.1.2 请求报文	错误!未定义书签。
3.2.1.3 响应报文	错误!未定义书签。
3.2.2 单笔付款查证	错误!未定义书签。
3.2.2.1 参数说明	错误!未定义书签。
3.2.2.2 请求报文	错误!未定义书签。
3.2.2.3 响应报文	错误!未定义书签。
3.2.3 批量付款	错误!未定义书签。
3.2.3.1 参数说明	错误!未定义书签。
3.2.3.2 请求报文	错误!未定义书签。
3.2.3.3 响应报文	错误!未定义书签。
3.2.4 多笔付款查证	错误!未定义书签。
3.2.4.1 参数说明	错误!未定义书签。
3.2.4.2 请求报文	错误!未定义书签。
3.2.4.3 响应报文	错误!未定义书签。
3.2.5 排款	错误!未定义书签。
3.2.5.1 参数说明	错误!未定义书签。
3.2.5.2 请求报文	错误!未定义书签。
3.2.5.3 响应报文	错误!未定义书签。
3.2.6 排款查证	错误!未定义书签。
3.2.6.1 参数说明	错误!未定义书签。
3.2.6.2 请求报文	错误!未定义书签。
3.2.6.3 响应报文	错误!未定义书签。
3.2.7 排款票据信息查询	错误!未定义书签。
3.2.7.1 参数说明	错误!未定义书签。
3.2.7.2 请求报文	错误!未定义书签。
3.2.7.3 响应报文	错误!未定义书签。
3.2.8 多批次排款票据查询	错误!未定义书签。
1.1.1.1. 业务规则(分场景，必填)	错误!未定义书签。
3.2.8.1 请求报文	错误!未定义书签。
3.2.8.2 响应报文	错误!未定义书签。
3.2.9 团金宝付款	错误!未定义书签。
3.2.9.1 参数说明	错误!未定义书签。
3.2.9.2 请求报文	错误!未定义书签。
3.2.9.3 响应报文	错误!未定义书签。
3.2.10 退汇交易查询	错误!未定义书签。
3.2.10.1 参数说明	错误!未定义书签。
3.2.10.2 请求报文	错误!未定义书签。
3.2.10.3 响应报文	错误!未定义书签。
3.2.11 批量代发（薪酬代发）	错误!未定义书签。
3.2.11.1 参数说明	错误!未定义书签。
3.2.11.2 请求报文	错误!未定义书签。
3.2.11.3 响应报文	错误!未定义书签。
3.2.12 收款明细标签查询（暂未上线，上线时间待定）	错误!未定义书签。
3.2.12.1 参数说明	错误!未定义书签。
3.2.12.2 请求报文	错误!未定义书签。
3.2.12.3 响应报文	错误!未定义书签。
3.3 公共中心	136
3.3.1 境内银行网点信息查询	136
3.3.1.1 参数说明	136
3.3.1.2 请求报文	138
3.3.1.3 响应报文	138
3.4 票证中心	错误!未定义书签。
3.4.1 票据列表查询	错误!未定义书签。
3.4.1.1 参数说明	错误!未定义书签。
3.4.1.2 请求报文	错误!未定义书签。
3.4.1.3 响应报文	错误!未定义书签。
3.4.2 票据详情查询	错误!未定义书签。
3.4.2.1 参数说明	错误!未定义书签。
3.4.2.2 请求报文	错误!未定义书签。
3.4.2.3 响应报文	错误!未定义书签。
3.4.3 票据交易查询	错误!未定义书签。
3.4.3.1 参数说明	错误!未定义书签。
3.4.3.2 请求报文	错误!未定义书签。
3.4.3.3 响应报文	错误!未定义书签。
3.4.4 票据背书申请	错误!未定义书签。
3.4.4.1 参数说明	错误!未定义书签。
3.4.4.2 请求报文	错误!未定义书签。
3.4.4.3 响应报文	错误!未定义书签。
3.4.5 票据制单查询	错误!未定义书签。
3.4.5.1 参数说明	错误!未定义书签。
3.4.5.2 请求报文	错误!未定义书签。
3.4.5.3 响应报文	错误!未定义书签。
3.4.6 票据详请批量查询	错误!未定义书签。
3.4.6.1 参数说明	错误!未定义书签。
3.4.6.2 请求报文	错误!未定义书签。
3.4.6.3 响应报文	错误!未定义书签。
3.4.7 票据行为签收查询	错误!未定义书签。
3.4.7.1 参数说明	错误!未定义书签。
3.4.7.2 请求报文	错误!未定义书签。
3.4.7.3 响应报文	错误!未定义书签。
3.4.8 票据行为查询	错误!未定义书签。
3.4.8.1 参数说明	错误!未定义书签。
3.4.8.2 请求报文	错误!未定义书签。
3.4.8.3 响应报文	错误!未定义书签。
3.5 预算中心	188
3.5.1 预算中心科目查询（暂未上线，上线时间待定）	188
3.5.1.1 参数说明	188
3.5.1.2 请求报文	190
3.5.1.3 响应报文	191
3.5.2 预算明细查询（暂未上线，上线时间待定）	193
3.5.2.1 参数说明	193
3.5.2.2 请求报文	195
3.5.2.3 响应报文	196
3.5.3 预算占用（暂未上线，上线时间待定）	196
3.5.3.1 参数说明	197
3.5.3.2 请求报文	201
3.5.3.3 响应报文	202
3.5.4 预算还原（暂未上线，上线时间待定）	205
3.5.4.1 参数说明	205
3.5.4.2 请求报文	207
3.5.4.3 响应报文	207
3.6 跨境中心	209
3.6.1 归集帐单-中信银行收报	209
3.6.1.1 参数说明	209
3.6.1.2 请求报文	213
3.6.1.3 响应报文	213
3.6.2 归集帐单-企业bic收报	215
3.6.2.1 参数说明	216
3.6.2.2 请求报文	220
3.6.2.3 响应报文	221
3.6.3 归集帐单原报文-中信银行收报	223
3.6.3.1 参数说明	223
3.6.3.2 请求报文	224
3.6.3.3 响应报文	224
3.6.4 归集账单原报文-企业bic收报	225
3.6.4.1 参数说明	225
3.6.4.2 请求报文	226
3.6.4.3 响应报文	226
3.6.5 全球账户支付经办	227
3.6.5.1 参数说明	227
3.6.5.2 请求报文	232
3.6.5.3 响应报文	233
3.6.6 全球账户支付交易状态查询	234
3.6.6.1 参数说明	234
******* 请求报文	235
******* 响应报文	235
3.6.7 境外资金视图-境外资金分布视图基础数据	236
3.6.7.1 参数说明	236
3.6.7.2 请求报文	238
3.6.7.3 响应报文	238
3.6.8 汇出汇款经办	240
3.6.8.1 参数说明	240
3.6.8.2 请求报文	245
3.6.8.3 响应报文	247
3.6.9 汇出汇款交易状态查询	247
3.6.9.1 参数说明	247
******* 请求报文	248
******* 响应报文	248
3.6.10 来账预知查询	248
******** 参数说明	249
3.6.10.2 请求报文	251
3.6.10.3 响应报文	251
3.6.11 全集团境外账户信息查询	252
3.6.11.1 参数说明	252
3.6.11.2 请求报文	254
3.6.11.3 响应报文	254
3.6.12 汇入汇款列表查询	255
3.6.12.1 参数说明	256
******** 请求报文	258
3.6.12.3 响应报文	259
3.6.13 汇入汇款确认经办	260
3.6.13.1 参数说明	260
3.6.13.2 请求报文	264
3.6.13.3 响应报文	265
3.6.14 境外账户余额查询	265
3.6.14.1 参数说明	266
3.6.14.2 请求报文	268
3.6.14.3 响应报文	268
3.6.15 境外账户历史余额查询	269
3.6.15.1 参数说明	269
3.6.15.2 请求报文	271
3.6.15.3 响应报文	272
3.7 资讯中心	272
3.7.1 企业工商信息查询	272
3.7.1.1 参数说明	273
3.7.1.2 请求报文	280
3.7.1.3 响应报文	280
3.8 应收应付中心	283
3.8.1 企业风险查询	283
3.8.1.1 参数说明	285
3.8.1.2 请求报文	287
3.8.1.3 响应报文	288
3.8.2 企业风险详情查询	289
3.8.2.1 参数说明	290
3.8.2.2 请求报文	300
3.8.2.3 响应报文	301
3.9 供应链中心	错误!未定义书签。
3.9.1 排款供应链凭证查询ERP接口	错误!未定义书签。
3.9.1.1参数说明	错误!未定义书签。
3.9.1.2请求报文	错误!未定义书签。
3.9.1.3响应报文	错误!未定义书签。
第四章 支付对账机制	317
第五章 附录	318
5.1 制单状态	318
5.2 交易状态	318
5.3 币种标识	319
5.4 直联银行标识	319
5.5 交易类接口请求代码	322
5.6 支持对账银行范围	322
5.7 支持历史余额银行范围	323
5.8 不同付方银行支持附言长度	错误!未定义书签。
5.9 银行编码信息和区域编码信息	327
5.10 支持薪酬代发银行范围	327
5.11 薪酬代发银行代发项目、代发用途码表	328
 
第一章 报文结构
在企业内部局域网环境内，报文的传输方式采用HTTP协议。HTTP报文包括企业内部系统向前置服务器（客户端软件）的请求报文以及前置服务器（客户端软件）向企业内部系统的响应报文，均由HTTP报文头与HTTP报文体两部分构成。
1.1 HTTP请求报文
HTTP请求报文由HTTP报文头（请求行、通用信息、请求头、实体头）、回车换行（CRLF）、HTTP报文体构成。
HTTP请求报文采用POST方式提交。
HTTP报文体为报文接口定义的请求报文数据（XML报文数据）。
1.2 HTTP响应报文
前置服务器处理HTTP请求报文后，返回HTTP响应报文至企业内部系统。HTTP响应报文由HTTP报文头（状态行、通用信息、响应头、实体头）、回车换行（CRLF）、HTTP报文体构成。
HTTP报文体为报文接口定义的响应报文数据（XML报文数据）。
第二章 报文定义规则
2.1 XML报文格式
所有XML报文均遵循以下数据格式：
<?xml version="1.0" encoding="GBK"?>
<stream>
		<key1>value1</key1>
<key2>value2</key2>
		<list name="userDataList">
			<row>
				<key3>value3</key3>
				…
			</row>
		</list>
</stream>
其中，list循环域中放置重复数据记录。
报文中的数据项标签名称必须与接口定义中的数据项标签名称一致（包括大小写）。
2.2 数据项说明
	日期数据项格式为：YYYYMMDD，例如：20080630。
	时间数据项格式为：hhmmss，例如：230000。
	报文接口中的数据项默认不能为空，若可为空的数据项将有相应说明。本文档中可根据参数说明中的要求查看输入和输出字段要求。
接口中要求的字段长度均是以GBK下字符串长度为基础，如varchar(66)最大支持66个英文及数字，或33个汉字。
第三章 报文接口
3.1 账户中心
3.1.1 账户余额查询
	请求代码： SKBALQRY
	接口说明：
	查询司库中活期账户的实时余额信息。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.账户余额查询请求每次支持不多于20个账户的查询，发起账户余额查询请求后，返回当前司库同步到的账户余额信息。如果账户余额尚未查询到，司库统一返回金额字段为空。
3.1.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
list
row
accountNo	账号	varchar(40)	是	待查询余额的账号，允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为40
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	部分账号校验失败时，失败原因展示。
List
row
accountNo	账号	varchar(40)	否	交易成功且查询到账户时返回
accountName	账户名称	varchar(120)	否	交易成功且查询到账户时返回
usableBalance	可用账户余额	decimal(15,2)	否	交易成功且查询到账户时返回，标识该账号可操作的账户余额。
Balance	账号余额	decimal(15,2)	否	交易成功且查询到账户时返回，标识该账户中全部余额，包含冻结金额、可操作余额等
fraAmt	冻结余额	decimal(15,2)	否	交易成功且查询到账户时返回，标识该账号的冻结金额。
lastUdtTms	更新时间	char(15)	否	交易成功且查询到账户时返回，更新时间
dataSrc	数据来源	varchar(16)	否	直联、非直联-人工等
currencyID	币种	varchar(5)	否	交易成功且查询到账户时返回，币种类型见附录4.3所示
date	日期	char(8)	否	交易成功且查询到账户时返回，余额更新日期
row
list

3.1.1.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKBALQRY</action>
<userName>11100114956559012768</userName>
<list name="userDataList">
<row>
<accountNo>8110901013900618088</accountNo>
</row>
</list>
</stream>
3.1.1.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<failReason></failReason>
<list name="userDataList"”>
<row>
<accountName>erp测试</accountName>
<accountNo>8110901013900618088</accountNo>
<balance>1700.00</balance>
<currencyID></currencyID>
<dataSrc>非直联-人工</dataSrc>
<date>********</date>
<fraAmt>200.00</fraAmt>
<lastUdtTms></lastUdtTms>
<usableBalance>1500.00</usableBalance>
</row>
</list>
</stream>
3.1.2 当日交易明细查询
请求代码： SKTRNCOL
接口说明：
该接口用于查询账户的当日交易明细信息。
接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.该交易使用分页查询，起始记录号从1开始，每页最多显示100条记录。报文中的交易流水号sumTranNo由司库系统产生，用于标识客户交易明细数据唯一性。

3.1.2.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
tranType	交易类型（借贷方向）	varchar(2)	是	需要查询交易的类型，01：全部交易；02：账户支出（借）；03：账户收入（贷）
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持100条记录
list
row
accountNo	账号	varchar(40)	是	待查询的账号，允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为40
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户明细数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户明细数量
list
row
sumTranNo	系统交易流水号	varchar(20)	否	交易成功且查询到交易明细时返回，该流水号是司库系统内该笔明细的唯一性标识
tranDate	交易日期	char(8)	否	交易成功且查询到交易明细时返回，交易发生日期
tranTime	交易时间	char(6)	否	交易成功且查询到交易明细时返回，交易发生时间
accountingDate
	记账日期
	char(8)
	否	该日期为银行起息记账日期，使用yyyyMMdd格式,直联接口返回则记录，若未返回，则使用交易日期。规则如上，仅供参考

accountNo	本方账号	varchar(32)	否	交易成功且查询到账户时返回
openBankName	本方开户行	varchar(300)	否	交易成功时返回，查询输入的账号对应的开户行名称
accountName	本方户名	varchar(120)	否	交易成功时返回，查询输入的账号对应的账户名称
bankName	本方所属银行	varchar(120)	否	交易成功时返回，查询输入的账号对应的所属银行名称
instName	机构名称	varchar(360)	否	交易成功时返回，查询输入的账号对应的机构名称
instCode	机构编码	varchar(20)	否	交易成功时返回，查询输入的账号对应的机构编码
oppAccountNo	对方账号	varchar(40)	否	交易成功且查询到交易明细时返回
oppAccountName	对方账户名称	varchar(120)	否	交易成功且查询到交易明细时返回
oppOpenBankName	对方开户行名	varchar(120)	否	交易成功且查询到交易明细时返回
oppOpenBankNo	对方开户行联行号	varchar(32)	否	交易成功且查询到交易明细时返回
tranType	交易类型	char(2)	否	交易成功且查询到交易明细时返回，02：账户支出；03：账户收入
tranAmount	交易金额	decimal(15,2)	否	交易成功且查询到交易明细时返回
rrtanid	退汇标识	char(1)	否	0已退汇 1非退汇 2手工退汇
balance	账户余额	decimal(15,2)	否	交易成功且查询到交易明细时返回，标识该账户中全部余额，包含冻结金额、可操作余额等
currencyID	币种	char(5)	否	交易成功且查询到账户时返回，币种类型见附录4.4所示
txnSrlnum	交易流水号	varchar(200)	否	交易匹配号，支持付款单与明细关联的银行返回，详见接口说明中支持付款与明细关联的银行列表
bnkSrlnum	银行流水号	varchar(200)	否	交易成功且查询到交易明细时返回，是司库返回的交易明细流水号
originalSrlNum	原始银行流水号	Varchar(100)	否	交易成功且查询到交易明细时返回，是对方银行返回的交易明细流水号（目前仅支持平安银行）
dataSource	数据来源	varchar(2)	否	交易成功且查询到交易明细时返回，标识该交易数据查询来源，1：接口查询（通过各行银企直联或中信网银）；2：用户导入（自行导入的交易数据）
lvmsg	附言	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明附言时返回空值
smy	摘要	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明摘要时返回空值
rmrk	备注	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明备注时返回空值
purpose	用途	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明用途时返回空值
cashTfrId	现转标识	char(1)	否	0现金 1转账
hdlTms	直联获取时间	TIMESTAMP	否	系统存储时间，格式为：yyyy-MM-dd HH:mm:ss
externalNum	外部请求流水号	varchar(50)	否	对方行支持明细对账时返回
externalBatNum	外部请求批次号	varchar(30)	否	对方行支持明细对账、且为批量支付生成时返回
accDtlId	系统交易流水号	varchar(20)	否	交易成功且查询到交易明细时返回，该流水号是司库系统内该笔明细的唯一性标识,与sumTranNo保持一致
extendRemark	拓展字段1	varchar(20)	否	仅支持兴业银行，别的银行为空
isOpnDirconId	联网方式	varchar(1)	否	标识本方账户的联网方式，
0非直联 1直联
accCgyId	账户性质	char(20)	否	标识本方账户的账户性质，
1一般账户 2基本账户 3专用账户 4临时账户 5其他
accTpId	存款类型	char(20)	否	标识本方账户的存款类型，
1活期 2定期 3通知 4活期保证金 5定期保证金 6其他
isFrgnAccId	境内/境外账户	char(1)	否	标识本方是境内还是境外账户，
0境内 1境外
accCharId	账户属性	char(20)	否	标识本方账户的账户属性，
1实账户 2登记簿

accStatId	银行账户状态	char(20)	否	标识本方账户的银行账户状态，
1正常 2销户 3司法冻结 4普通冻结 5久悬 6挂失 7冻结
row
list

3.1.2.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKTRNCOL</action>
<userName>11100114956559012768</userName>
<tranType>01</tranType>
<startRecord>1</startRecord>
<pageNumber>20</pageNumber>
<list name="userDataList">
<row>
<accountNo>8110901013900618088</accountNo>
</row>
</list>
</stream>
3.1.2.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status>AAAAAAA</status>
    <statusText>交易成功</statusText>
	<failReason></failReason>
    <totalRecords>1</totalRecords>
	<returnRecords>1</returnRecords>
	<list name="userDataList">
		<row>
            <sumTranNo>81109010139006180882201781</sumTranNo>
            <tranDate>********</tranDate>
            <tranTime>121212</tranTime>
            <accountingDate>********</accountingDate>
            <accountNo>8110901013900618088</accountNo>
            <openBankName>兴业银行股份有限公司福州五一支行</openBankName>
            <accountName>erp测试</accountName>
            <bankName>上海银行</bankName>
            <instName></instName>
            <instCode></instCode>
            <oppAccountNo>AAAGFRP1XXX</oppAccountNo>
            <oppAccountName></oppAccountName>
            <oppOpenBankName></oppOpenBankName>
            <oppOpenBankNo></oppOpenBankNo>
            <tranType>03</tranType>
            <tranAmount>222.00</tranAmount>
            <rrtanid></rrtanid>
            <balance>222.00</balance>
            <currencyID>CNY</currencyID>
            <txnSrlnum></txnSrlnum>	
            <bnkSrlnum>********AAAGFRP1XXXAMH940PMGW********0M9000003518100</bnkSrlnum>
    		<originalSrlNum>********A00003518100</originalSrlNum>
    		<dataSource>1</dataSource>
            <lvmsg></lvmsg>
            <smy></smy>
            <rmrk></rmrk>
            <purpose></purpose>
            <cashTfrId></cashTfrId>
            <hdlTms>2023-01-01 12:32:51</hdlTms>
            <externalNum></externalNum>
            <externalBatNum></externalBatNum>
            <accDtlId></accDtlId>
            <extendRemark>2323</extendRemark>
            <isOpnDirconId></isOpnDirconId>
            <accCgyId></accCgyId>
            <accTpId></accTpId>
            <isFrgnAccId></isFrgnAccId>
            <accCharId></accCharId>
            <accStatId></accStatId>
		</row>
	</list>
</stream>
3.1.3 账户信息查询
请求代码： SKBACQRY
接口说明：
该接口用于查询客户在司库系统中维护的账号信息
接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.该交易使用分页查询，起始记录号从1开始，每页最多显示20条记录。
3.1.3.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar（8）	是	标识要请求的接口
userName	银企直联用户名	varchar（50）	是	银企直联用户登陆用户名
blngBnkId	所属银行	varchar（20）	否	见附录4.4
accCgyId	账户性质（原：账户类型）	varchar（20）	否	1	一般账户
2	基本账户
3	专用账户
4	临时账户
5	其他
空   全部
accTpId	存款类型（原：账户种类）	varchar（20）	否	1	活期
2	定期
3	通知
4	活期保证金
5	定期保证金
6	结构性存款
空   全部
accStatId	账户状态	varchar（20）	否	1	正常
2	销户
3	司法冻结
4	普通冻结
5	睡眠
6	挂失
7	冻结
空   全部
isOpnDirconId	联网方式（原：是否直联）	varchar（1）	否	空：全部、
1  直联
2  非直联
3   SWIFT
accCharId	账户属性	varchar（2）	否	空：全部、
1：实账户、2：登记簿
accStyId	账户类型	varchar（2）	否	空：全部、
0：	境内普通账户
1：FTE
2：NRA
3：FTN
4：OSA
5：境外其他账户
isFrgnAccId	境内/境外	varchar（2）	否	空：全部、
0：境内
1：境外
startDate	起始日期	Date（yyyymmdd hh:mm:ss）	否	空：全部
大于该时间节点的账户
endDate	终止日期	Date（yyyymmdd hh:mm:ss）	否	空：全部
小于该时间节点的账户
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持20条记录
list
row
accountNo	账号	varchar(40)	否	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为40
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户数量
list
row
accNm	账户/分薄名称	varchar(120)	否	交易成功时返回，查询输入的账号对应的账户名称
accountNo	账号/分薄编号	varchar(40)	否	交易成功时返回，查询输入的账户号
instCode	所属机构代码	varchar(100)	否	交易成功时返回，查询输入的账号对应的机构编码
instName	所属机构名称	varchar(360)	否	交易成功时返回，查询输入的账号对应的机构名称
accChar	账户属性(原：账户性质)	varchar(20)	否	交易成功且查询到账户时返回，实账号，登记簿
isMoreCurrAccId	是否多币种账户	varchar(2)	否	交易成功且查询到账户时返回，是、否
currencyId	币种	varchar(50)	否	交易成功且查询到账户时返回，币种类型见附录4.3所示,多币种时以,分隔展示多个币种
isFrgnAcc	境内/境外账户	varchar(2)	否	交易成功且查询到账户返回，
境内、境外
accCgy	账户性质（原：账户类型）	varchar(20)	否	交易成功且查询到账户时返回，
一般账户
基本账户
专用账户
临时账户
其他
accTp	存款类型（原：账户种类）	varchar(20)	否	交易成功且查询到账户返回，
活期
定期
通知存款
活期保证金
定期保证金
结构性存款
crnAcc	关联活期账号	varchar(40)	否	交易成功且查询到账户返回，查询输入的账号对应的关联活期账号

agrmLmt	协定额度	decimal(15,2)	否	交易成功且查询到账户时返回，查询输入的账号对应的协定额度
blngBnkId	所属银行	varchar(20)	否	交易成功且查询到账户时返回，
见附录4.4
opnacctBrId	开户网点	varchar(20)	否	交易成功且查询到账户返回，查询输入的账号对应的开户网点
bnkCodeId	联行号	varchar(60)	否	交易成功且查询到账户返回，查询输入的账号对应的网点联行号
accuse	账户用途	varchar( 20 )	否	交易成功且查询到账户返回，
支出户
收入户
资本户
成本户
住房公积金类
保证金户
日常经营
收入支出户
depbnkCtyId	开户国家/地区	varchar(20)	否	交易成功且查询到账户返回，查询输入的账号对应的开户国家/地区
opnacctProvId	开户省	varchar(20)	否	交易成功且查询到账户返回，查询输入的账号对应的开户省
opnacctCityId	开户市	varchar(20)	否	交易成功且查询到账户返回，查询输入的账号对应的开户市
isOsaId	是否离岸账户	varchar(2)	否	交易成功且查询到账户返回，是、否
opnacctDt	开户日期	char(8)	否	交易成功且查询到账户返回，查询输入的账号对应的开户日期
accStatId	账户状态	varchar(20)	否	交易成功且查询到账户时返回，
正常、销户、久悬、挂失、冻结
accPayRst	支付限制	varchar(20)	否	交易成功且查询到账户返回，
不限制
定向支付
只收不付
isopndircon	联网方式（原：是否直联）	varchar(20)	否	交易成功且查询到账户返回，
直联、非直联、SWIFT
isUselmtofathrt	是否可使用	varchar(2)	否	交易成功且查询到账户返回，
是、否
actInstCode	实际使用机构编码	varchar(20)	否	交易成功时返回，查询输入的账号对应的账号使用机构编码
actInstName	实际使用机构名称	varchar(50)	否	交易成功时返回，查询输入的账号对应的账号使用机构名称
mainAccNum	主体账号	varchar(20)	否	当账户属性为登记簿时，主体账号字段有值
mainAccNm	主体账户名称	varchar(50)	否	当账户属性为登记簿时，主体账号名称根据主体账号信息展示
accSty	账户类型	varchar(50)	否	交易成功且查询到账户时返回，境内普通账户、NRA、FTE、FTN、OSA、境外其他账户
opnacctBrBic	开户网点BIC	varchar(20)	否	交易成功且查询到账户返回，查询输入境外账号对应的开户网点BIC
cnclacctDt	销户日期	Date(yyyymmdd)	否	交易成功且查询到账户返回，查询输入的账号对应的销户日期
accSrc	账户来源	varchar(50)	否	交易成功且查询到账户返回，手工导入、签约同步、账户生命周期同步
lastUdtTms	最后更新时间	Date（yyyymmdd hh:mm:ss）	否	交易成功且查询到账户返回，查询账号最后更新时间
row
list


3.1.3.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKBACQRY</action>
<userName>11100199667893870098</userName>
<blngBnkId></blngBnkId>
<accCgyId></accCgyId>
<accTpId></accTpId>
<accStatId></accStatId>
<isOpnDirconId></isOpnDirconId>
<accCharId></accCharId>
<accStyId></accStyId>
<isFrgnAccId></isFrgnAccId>
<startRecord>1</startRecord>
<pageNumber>20</pageNumber>
<list name="userDataList">
<row>
<accountNo>8110901013900618088</accountNo>
</row>
</list>
</stream>

3.1.3.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<failReason></failReason>
<returnRecords>1</returnRecords>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<totalRecords>1</totalRecords>
<list name="userDataList">
<row>
<accCgy>临时账户</accCgy>
<accChar>实账户</accChar>
<accNm>erp测试</accNm>
<accPayRst>定向支付</accPayRst>
<accStatId>正常</accStatId>
<accTp>活期保证金</accTp>
<accountNo>8110901013900618088</accountNo>
<accuse></accuse>
<agrmLmt></agrmLmt>
<blngBnkId>BOS</blngBnkId>
<bnkCodeId>1</bnkCodeId>
<crnAcc>1</crnAcc>
<currencyId>CNY,USD</currencyId>
<depbnkCtyId>中国</depbnkCtyId>
<instCode></instCode>
<instName></instName>
<isFrgnAcc>境内</isFrgnAcc>
<isMoreCurrAccId>否</isMoreCurrAccId>
<isOsaId>否</isOsaId>
<isopndircon>直联</isopndircon>
<opnacctBrId>兴业银行股份有限公司福州五一支行</opnacctBrId>
<opnacctCityId>拉萨</opnacctCityId>
<opnacctCntyId>拉萨</opnacctCntyId>
<opnacctDt></opnacctDt>
<opnacctProvId>西藏</opnacctProvId>
 <actInstCode></actInstCode>
<actInstName></actInstName>
<mainAccNum></mainAccNum>
<mainAccNm></mainAccNm>
<accSty>直联</accSty>
<opnacctBrBic></opnacctBrBic>
<cnclacctDt></cnclacctDt>
<accSrc></accSrc>
<lastUdtTms></lastUdtTms>
</row>
</list>
</stream>
3.1.4 电子回单申请
请求代码： SKEDDRSQ
接口说明：
客户可使用该接口实现司库电子回单查询申请，申请交易成功后司库系统将根据客户提交的查询条件为客户查询电子回单信息。后续客户需使用司库电子回单文件查询（SKEDDQRY）接口查询回单文件统计状态。
接口使用须知：
1.账号需提前在司库系统内维护并为直联用户赋予查询权限；
2.司库电子回单下载获取需通过三部操作完成：1. SKEDDRSQ（司库电子回单查询申请）提交所需账号的电子回单查询申请；2. SKEDDQRY（司库电子回单文件查询）查询第一步中查询的电子回单信息是否准备完成，如完成则根据查询提供的分页信息返回相应的回单编号，如未完成则继续轮训该接口（若涉及的电子回单信息较多时建议适当延长轮训间隔）；3. SKEDCDTD（司库电子回单文件下载）根据回单编号对需要的回单文件进行下载。
3.接口限流机制：每台服务同时处理2笔交易，等待时间1秒
4.接口访问限制：同客户每天1000次
5.时间间隔最大为30天
  6.SKEDDRSQ 支持申请当日回单回单：中信、招商、平安银行、浦发银行、民生银行、中国银行、工商银行；若回单所属银行暂不支持获取当日回单时，接口返回错误码SE01100（XX银行暂不支持当日回单），并默认返回T-1日回单。
3.1.4.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
clientID	记录申请编号	varchar(20)	是	客户查询自定义的流水号，用于查询结果信息，流水号需唯一，仅支持数字或字母组合
accountNo	账号	varchar(40)	是	用户有查询权限的银行账号，允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为40
startDate	起始日期	char(8)	是	开始日期与结束日期跨度限制30天
endDate	终止日期	char(8)	是	开始日期与结束日期跨度限制30天
Response
status	交易状态	varchar(7)	是	交易状态，支付类交易返回成功表示交易提交成功，具体交易状态需根据结果查询接口确认
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
clientID	记录申请编号	varchar(20)	是	客户交易自定义的流水号，用于查询结果信息，流水号需唯一

3.1.4.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKEDDRSQ</action>
<userName>citic</userName><!--登录名varchar(30)-->
<clientID>***************</clientID><!--记录申请编号 varchar(20)-->
<accountNo>8110801013201236512</accountNo><!--账号 varchar(40)-->
<startDate>********</startDate><!--起始日期char(8) 格式YYYYMMDD-->
<endDate>********</endDate><!--终止日期char(8) 格式YYYYMMDD-->
</stream>

3.1.4.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <clientID>Zp0478327504389</clientID>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
</stream>

3.1.5 电子回单查询
请求代码： SKEDDQRY
接口说明：
客户可使用该接口，查询SKEDDRSQ（司库电子回单查询申请）接口发出的交易请求的处理结果。若查询返回成功，可根据查询出的回单编号在SKEDCDTD（司库电子回单文件下载）接口下载对应的回单文件。交易将返回明确成功、处理中、失败等状态。
接口使用须知：
1.账号需提前在司库系统内维护并为直联用户赋予查询权限；
2.司库电子回单下载获取需通过三部操作完成：1. SKEDDRSQ（司库电子回单查询申请）提交所需账号的电子回单查询申请；2. SKEDDQRY（司库电子回单文件查询）查询第一步中查询的电子回单信息是否准备完成，如完成则根据查询提供的分页信息返回相应的回单编号，如未完成则继续轮训该接口（若涉及的电子回单信息较多时建议适当延长轮训间隔）；3. SKEDCDTD（司库电子回单文件下载）根据回单编号对需要的回单文件进行下载。
3.接口限流机制：每台服务同时处理2笔交易，等待时间1秒
4.接口访问限制：同客户每分钟30次

3.1.5.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
clientID	记录申请编号	varchar(20)	是	SKEDDRSQ交易申请时提交的流水号，仅支持数字或字母组合
startDate	起始日期	char(8)	否	查询明细范围的开始日期，使用yyyyMMdd格式，日期格式不能超出请求流水号对应的SKEDDRSQ（多银行电子回单查询申请）交易范围，不填写时默认取SKEDDRSQ（多银行电子回单查询申请）输入的起始日期开始日期与结束日期跨度限制30天
endDate	终止日期	char(8)	否	查询明细范围的结束日期，使用yyyyMMdd格式，日期格式不能超出请求流水号对应的SKEDDRSQ（多银行电子回单查询申请）交易范围，不填写时默认取SKEDDRSQ（多银行电子回单查询申请）输入的终止日期开始日期与结束日期跨度限制30天
tranType	交易类型	char (2)	否	需要查询交易的类型，01：全部交易；02：账户支出；03：账户收入 
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持100条记录
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
totRcptStat	总回单查询状态	char(1)	是	总回单查询状态，1:全部成功（终态）；2：部分成功；3：部分成功，部分失败（终态）
totalRecords	已查询到的回单总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户数量
list（userDataList）
row
date	回单日期	char(8)	否	
rcptNum	回单编号	varchar(200)	否	
externalNum	外部请求流水号	varchar(50)	否	对方行支持明细对账时返回
externalBatNum	外部请求批次号	varchar(30)	否	对方行支持明细对账、且为批量支付生成时返回
bnkSrlnum	银行流水号	varchar(200)	否	交易成功且查询到回单时返回
txnDt	交易时间	char(8)	是	yyyymmdd
pyAccnum	本方账号	varchar(200)	是	
opnBnkNm	本方开户行	varchar(200)	是	
pyAccnm	本方户名	varchar(200)	是	
rcvpyAccnum	对方账号	varchar(200)	否	
rcvpyAccnm	对方户名	varchar(200)	否	
cptBnkNm	对方开户行	varchar(200)	否	
txnTp	交易类型	varchar(200)	是	
txnAmt	交易金额	char(30)	是	132134.3230
curr	币种	varchar(200)	是	人民币、美元等
lvmsg	附言	varchar(200)	否	
smy	摘要	varchar(200)	否	
row
list
list（userUnsuccList）
row	
date	数据日期	char(8)	
rcptStat	回单状态	char(1)	1:查询中；2:查询失败
message	未查询数据原因	varchar(254)	
row
list

3.1.5.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
	<action>SKEDDQRY</action>
	<userName></userName>
	<clientID>Z10478327504389</clientID>
<tranType>01</tranType>
	<startDate>20230401</startDate>
	<endDate>20230402</endDate>
	<startRecord>1</startRecord>
	<pageNumber>10</pageNumber>
</stream>
3.1.5.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<returnRecords>1</returnRecords>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<list name="userDataList">
<row>
<date>20230402</date>
<rcptNum>8110701012501429490_2022102020000020221020SCMM00025359380000020001</rcptNum>
<externalNum></externalNum>
<externalBatNum></externalBatNum>
<bnkSrlnum>********AAAGFRP1XXXAMH940PMGW********0M9000003518100</bnkSrlnum>
<txnTp></txnTp>
<curr></curr>
<rcvpyAccnum>8110901013900618088</rcvpyAccnum>
<rcvpyAccnm>北京天地寰宇池</rcvpyAccnm>
<txnAmt>23.5300</txnAmt>
<lvmsg></lvmsg>
<smy></smy>
<txnDt></txnDt>
<pyAccnum>8110701012501429490</pyAccnum>
<pyAccnm>北京天地寰宇池1</pyAccnm>
<opnBnkNm>中信银行北京中信城支行</opnBnkNm>
<cptBnkNm></cptBnkNm>
</row>
</list>
<list name="userUnsuccList">
<row>
<date>20230401</date>
<message>本日期电子回单正在获取中</message>
</row>
</list>
</stream>
3.1.6 电子回单文件下载
请求代码： SKEDCDTD
接口说明：
客户可使用该接口，查询SKEDDRSQ（电子回单查询）接口发出的交易请求的处理结果。若查询返回成功，可根据查询出的回单编号在SKEDCDTD（司库电子回单文件下载）接口下载对应的回单文件。交易将返回明确成功、处理中、失败等状态。
接口使用须知：
1.账号需提前在司库系统内维护并为直联用户赋予查询权限；
2.司库电子回单下载获取需通过三部操作完成：1. SKEDDRSQ（司库电子回单查询申请）提交所需账号的电子回单查询申请；2. SKEDDQRY（司库电子回单文件查询）查询第一步中查询的电子回单信息是否准备完成，如完成则根据查询提供的分页信息返回相应的回单编号，如未完成则继续轮训该接口（若涉及的电子回单信息较多时建议适当延长轮训间隔）；3. SKEDCDTD（司库电子回单文件下载）根据回单编号对需要的回单文件进行下载。
3.接口限流机制：每台服务同时处理2笔交易，等待时间3秒
4.接口访问限制：同客户每分钟30次
5.每次数量为：20条
6.文件压缩后最大大小为：2M
7.在输入中新增“文件格式”字段，非必输，字典项为“OFD优先”、“PDF优先”。

3.1.6.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
fileType	文件格式	char(1)	否	PDF(返回PDF文件)
OFD(优先返回OFD文件，若银行不支持，则返回PDF文件)
其他-默认返回PDF文件
list
row
rcptNum	回单编号	varchar(200)	是	需要回单文件下载的回单编号/20个
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户数量
list
row
rcptNum	回单编号	varchar(200)	否	
pdfName	回单文件名称	varchar(256)	否	
row
list
fileContent	回单汇总文件内容	varchar(2097152)	否	交易成功时返回，返回所有回单文件汇总后压缩的文件内容，需使用base64解码后再进行zip解压缩
Base64使用commons-codec包的org.apache.commons.codec.binary.Base64
如查询到的文件压缩后超限，将返回文件超大的报错，需修改回单文件下载数量
fileName	回单汇总文件名称	varchar(128)	否	交易成功时返回，回单汇总文件的文件名
size	文件大小	int	否	文件大小

******* 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKEDCDTD</action>
<userName>11100153811560443580</userName>
<list name="userDataList">
<row>
<rcptNum>8110701013101434287_2022101920000020221019SCMM00024555760000010001</rcptNum>
</row>
</list>
</stream>
******* 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<failReason></failReason>
<fileContent>UEsDBBQACAgIAImKilYAAAAAAAAAAAAAAAAyAAAAMjAyMjEwMTkyMDAwMDAyMDIyMTAxOVNDTU0wMDAyNDU1NTc2MDAwMDAxMDAwMS5wZGYAGUDmvyVQREYtMS40CiXi48/TCjIgMCBvYmoKPDwvTGVuZ3RoIDIyMTA2L0xlbmd0aDEgMTc2ODc2L0ZpbHRlci9GbGF0ZURlY29kZT4+c3RyZWFtCnic7b1psG3JVR647nt3eNM9Z897n33mc6c3z0OVSlUqSaWhVJRUElJLaAAJCaEOkKoahJCwTQO2o+mwZQwEdGMU2DLItC0BsjBD2AwWJpqm7UBWFaYNPNerdjdBhW0ccoO7W9XFu525vrUyc597S5Yi+OEI592x7tlD7hxWZq5cU65NK0R0gr6LjtLs3e9/1xP3P/ttv2jufIpo7e3v/tAHZxtnjn6BaP0Y0ZFfee8T3/j+13z8B54lOva/mDSb3/jNH3nvxs5feLO5/pdEq7/2vm9413v+1e986MPm0cfM85vvMzeOHFm9Zq5/21xvv+/9H/zw9/7wF/6Cuf5/if7gyW9+/N3vOpJ9tXn3j/8ZrfzKV73/XR9+4r957ZG/REc+05j0sye+5RueWLv9un9mru8x158xsGLqaWt8ilbXHjOXj5mLDXqMvpN+gv4n+vv0C/S/0f9O/4b+iP6Y/h96jp5fWVnJV4Yr51YeWnn1yqMr37TyWyv/fOXJld9e+Z2Vf7nyeyu3V7545PVH3nXkN4780yO/e/TjR//u0c8e/c3VldXXrb5+lsyq2WA2nu3OLs+uzW7M7pt9an50vjY/Pq/m8/k3zT8y/8H5jyyOLfqLdFEu2sVssbXYWZxbXFzcWty/ePXiqxZvWLxr8Y2Lxxc/tPiRxScWv7qVbf/X2z+688jOt+58384P7Xx852d3fnnnN3b+6c7ndv5gd333+O6Duy+7+N0X/9LF7734Vy/+Dxd/8uLfu/jTF3/24i9c/EcX/9dL/9/lo5fry5PLZy4/cPkHL//Y5f/78t0rZ66euXrl6o9f+75rP3bt49d+/dpvXXvy2r949Of+5if+8If/8DeebZ6dPLv77Plnrz5737Mvf/ZNz77ni+e/+JEv/tAX//CL/+G5/nPpc8VzzXPz57afO3P3yt2H7r7h7pvvvvfuN+1/7f4f7O9b7BuM/iR9mjH6DD1L/47+L4PRL34FGP1Ng9HvP/rjR//+0X9iMJowRvNZMxvNdgxGrx7A6DvnT8z/+vyHA4yOGaN7BqPXHUbfuXjP4gMBRt+5/cEd2nls57t3fnDnb+18ZueXdv5ng9Hf2vnXu2sdjP6VAKM/zxj9wmW6vHZ5fHluMPrSyz96+eOXn7+ydnXGGP1r1z4mGP38tX9xnV5Lf/hRg9GKMXru2SuM....</fileContent>
<fileName>********172018_ReceiptFile.zip</fileName>
<returnRecords>1</returnRecords>
<size>114736</size>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<totalRecords>1</totalRecords>
<list name="resultList">
<row>
<pdfName>2022101920000020221019SCMM00024555760000010001.pdf</pdfName>
<rcptNum>8110701013101434287_2022101920000020221019SCMM00024555760000010001</rcptNum>
</row>
</list>
</stream>
3.1.7 历史明细查询申请
请求代码： SKTRNHCL
接口说明：
使用该接口提交账户历史交易明细查询请求
接口使用须知：
请求使用的银企直联用户需有相关账号的查询权限。
******* 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
clientId	记录申请编号	varchar(20)	是	客户查询自定义的流水号，用于查询结果信息，流水号需唯一，仅支持数字或字母组合
accountNo	账号	varchar(40)	是	用户有查询权限的银行账号，允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为40
startDate	起始日期	char(8)	是	查询明细范围的开始日期，使用yyyyMMdd格式
开始日期与结束日期跨度限制95天
endDate	终止日期	char(8)	是	查询明细范围的结束日期，使用yyyyMMdd格式 开始日期与结束日期跨度限制95天
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
clientId	记录申请编号	varchar(20)	是	客户查询自定义的流水号，用于查询结果信息，流水号需唯一

3.1.7.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
	<action>SKTRNHCL</action>
	<userName>11100114956559012768</userName>
	<clientId>********</clientId>
	<accountNo>1</accountNo>
	<startDate>********</startDate>
	<endDate>********</endDate>
</stream>

3.1.7.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<failReason></failReason>
<statusText>交易成功</statusText>
<clientId>********</clientId>
</stream>
3.1.8 历史明细结果查询
请求代码： SKTRNHCT
接口说明：
该接口用于查询账户的历史交易明细信息。
接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限	；
2.该交易使用分页查询，起始记录号从1开始，每页最多显示100条记录。报文中的交易流水号sumTranNo由司库系统产生，用于标识客户交易明细数据唯一性；
3.历史明细：申请（SKTRNHCL），查询（SKTRNHCT）可查司库已有境外银行数据（渣打、汇丰）
3.1.8.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
clientId	记录申请编号	varchar(20)	是	交易申请时提交的流水号 ，仅支持数字或字母组合
startDate	起始日期	char(8)	否	查询明细范围的开始日期，使用yyyyMMdd格式，日期格式不能超出请求流水号对应的SKTRNHCL（历史明细信息同步）交易范围，不填写时默认取SKTRNHCL（历史明细信息同步）输入的起始日期    开始日期与结束日期跨度不能超过95天
endDate	终止日期	char(8)	否	查询明细范围的结束日期，使用yyyyMMdd格式，日期格式不能超出请求流水号对应的SKTRNHCL（历史明细信息同步）交易范围，不填写时默认取SKTRNHCL（历史明细信息同步）输入的终止日期 开始日期与结束日期跨度不能超过95天
tranType	交易类型	char(2)	是	需要查询交易的类型，01：全部交易；02：账户支出；03：账户收入
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持100条记录
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
accountNo	账号	varchar(40)	否	交易成功时返回，查询输入的账号
accountName	账户名称	varchar(120)	否	交易成功时返回，查询输入的账号对应的账户名称
openBankName	开户行名称	varchar(300)	否	交易成功时返回，查询输入的账号对应的开户行名称
bankName	本方所属银行	varchar(120)	否	交易成功时返回，查询输入的账号对应的所属银行名称
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户数量
list
row
sumTranNo	系统交易流水号	varchar(20)	否	交易成功且查询到交易明细时返回，是对方银行返回的交易明细流水号
tranDate	交易日期	char(8)	否	交易成功且查询到交易明细时返回，交易发生日期
tranTime	交易时间	char(6)	否	交易成功时返回，交易发生时间
accountingDate
	记账日期
	char(8)
	否	该日期为银行起息记账日期，使用yyyyMMdd格式直联接口返回则记录，若未返回，则使用交易日期。规则如上，仅供参考.
accountNo	账号	varchar(40)	否	交易成功时返回，查询输入的账号
accountName	账户名称	varchar(120)	否	交易成功时返回，查询输入的账号对应的账户名称
openBankName	开户行名称	varchar(300)	否	交易成功时返回，查询输入的账号对应的开户行名称
bankName	本方所属银行	varchar(120)	否	交易成功时返回，查询输入的账号对应的所属银行名称
instName	机构名称	varchar(360)	否	交易成功时返回，查询输入的账号对应的机构名称
instCode	机构编码	varchar(20)	否	交易成功时返回，查询输入的账号对应的机构编码
oppAccountNo	对方账号	varchar(40)	否	交易成功且查询到交易明细时返回
oppAccountName	对方账户名称	varchar(120)	否	交易成功且查询到交易明细时返回
oppOpenBankName	对方开户行名	varchar(120)	否	交易成功且查询到交易明细时返回
tranType	交易类型	char(2)	否	交易成功且查询到交易明细时返回，02：账户支出；03：账户收入
tranAmount	交易金额	decimal(15,2)	否	交易成功且查询到交易明细时返回
balance	账户余额	decimal(15,2)	否	交易成功且查询到账户时返回，标识该账户中全部余额，包含冻结金额、可操作余额等
currencyID	币种	varchar(5)	否	交易成功且查询到账户时返回，币种类型见附录4.4所示
bnkSrlnum	银行流水号	varchar(200)	否	交易成功且查询到交易明细时返回，是对方银行返回的交易明细流水号
originalSrlNum	原始银行流水号	Varchar(100)	否	交易成功且查询到交易明细时返回，是对方银行返回的交易明细流水号（目前仅支持平安银行）
dataSource	数据来源	varchar(2)	否	交易成功且查询到交易明细时返回，标识该交易数据查询来源，1：接口查询（通过各行银企直联或中信网银）；2：用户导入（自行导入的交易数据）
lvmsg	附言	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明附言时返回空值
smy	摘要	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明摘要时返回空值
rmrk	备注	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明备注时返回空值
purpose	用途	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明用途时返回空值
externalNum	外部请求流水号	varchar(50)	否	对方行支持明细对账时返回
externalBatNum	外部请求批次号	varchar(30)	否	对方行支持明细对账、且为批量支付生成时返回
accDtlId	系统交易流水号	varchar(20)	否	交易成功且查询到交易明细时返回，该流水号是司库系统内该笔明细的唯一性标识,与sumTranNo保持一致
extendRemark	扩展字段1	varchar(20)	否	仅支持兴业银行，别的银行为空
row
list

3.1.8.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKTRNHCT</action>
<userName></userName>
<clientId>1234abcd321129</clientId>
<tranType>01</tranType>
<startDate></startDate>
<endDate></endDate>
<startRecord>1</startRecord>
<pageNumber>1</pageNumber>
</stream>
3.1.8.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<accountNo>8110901013900618088</accountNo>
<accountName>erp测试</accountName>
<openBankName>兴业银行股份有限公司福州五一支行</openBankName>
<bankName>上海银行</bankName>
<totalRecords>1</totalRecords>
<returnRecords>1</returnRecords>
<list name="userDataList">
	<row>
		<sumTranNo>*****************</sumTranNo>
		<tranDate>********</tranDate>
		<tranTime>123151</tranTime>
		<accountingDate>********</accountingDate>
		<accountNo>8110901013900618088</accountNo>
		<accountName>erp测试</accountName>
		<openBankName>兴业银行股份有限公司福州五一支行</openBankName>
		<bankName>上海银行</bankName>
		<instName></instName>
		<instCode></instCode>
		<oppAccountNo>AAAGFRP1XXX</oppAccountNo>
		<oppAccountName></oppAccountName>
		<oppOpenBankName></oppOpenBankName>
		<tranType>03</tranType>
		<tranAmount>222.00</tranAmount>
		<balance>222.00</balance>
		<currencyID>CNY</currencyID>
		<bnkSrlnum>********A00003518100</bnkSrlnum>
		<originalSrlNum>****************</originalSrlNum>
		<dataSource>2</dataSource>
		<lvmsg></lvmsg>
		<smy></smy>
		<rmrk></rmrk>
		<purpose></purpose>
		<externalNum></externalNum>
		<externalBatNum></externalBatNum>
		<accDtlId></accDtlId>
		<extendRemark>2323</extendRemark>
	</row>
</list>
</stream>
3.1.9 历史余额查询申请
请求代码： SKBALHSQ
接口说明：
客户可使用该接口提交账户历史余额查询申请
接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.账户历史余额查询申请每次支持不多于20个账户的查询。
3.可向前申请1年以内的历史余额数据，并且起始日期、终止日期之间的日期间隔不得大于90天。

3.1.9.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
clientID	记录申请编号	varchar(20)	是	客户查询自定义的流水号，用于查询结果信息，流水号需唯一，仅支持数字或字母组合
startDate	起始日期	char(8)	是	查询余额范围的开始日期，使用yyyyMMdd格式  近1年开始日期与结束日期跨度限制90天
endDate	终止日期	char(8)	是	查询余额范围的结束日期，使用yyyyMMdd格式近1年开始日期与结束日期跨度限制90天
list
row
accountNo	账号	varchar(40)	是	待查询的账号，允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为40
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
clientID	记录申请编号	char(20)	是	客户查询自定义的流水号，用于查询结果信息，流水号需唯一
3.1.9.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
	<action>SKBALHSQ</action>
	<userName>11100114956559012768</userName>
	<clientID>********</clientID>
<startDate>********</startDate>
		<endDate>********</endDate>
	<list name="userDataList">
<row>
<accountNo>8110901013900618088</accountNo>
</row>
</list>
</stream>
3.1.9.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<failReason></failReason>
<statusText>交易成功</statusText>
<clientID>********</clientID>
</stream>
3.1.10 历史余额结果查询
请求代码： SKBALHCX
接口说明：
客户可使用历史余额查询申请中提交的记录申请编号，来查询账户的历史余额数据。
接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.余额数据支持三种来源：01银行接口返回，即通过调用各银行的历史余额接口来获取数据；02银行明细返回，当对方行没有历史余额接口，而通过其当日最后一条银行流水明细中的余额字段来获取数据；03 明细数据计算，当对方行不支持历史余额接口、其明细也没有余额字段时，通过某日的余额基准数据，结合其间交易明细轧差计算，得出的历史余额数据；
3.数据状态01获取中，为余额数据获取的中间状态，如对方银行接口已调用，但尚未返回；02 已返回、03获取失败为余额数据获取的终态，其中返回03时，余额金额字段为空；
4.当数据来源01和02的时候，数据状态包括02或03；数据来源是03时，数据状态根据接口调用状态分别展示01、02或03.
5.历史余额：申请（SKBALHSQ），查询（SKBALHCX）可查司库已有境外银行数据（渣打、汇丰）

3.1.10.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
clientID	记录申请编号	char(20)	是	客户查询自定义的流水号，用于查询结果信息，流水号需唯一，仅支持数字或字母组合
startDate	起始日期	char(8)	否	查询余额范围的开始日期，使用yyyyMMdd格式，日期格式不能超出记录申请编号对应的SKBALHSQ（历史余额查询申请）交易范围，不填写时默认取SKBALHSQ（历史余额查询申请）输入的起始日期 近1年开始日期与结束日期跨度限制90天
endDate	终止日期	char(8)	否	查询余额范围的结束日期，使用yyyyMMdd格式，日期格式不能超出记录申请编号对应的SKBALHSQ（历史余额查询申请）交易范围，不填写时默认取SKBALHSQ（历史余额查询申请）输入的终止日期 近1年开始日期与结束日期跨度限制90天
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持20条记录
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户数量
list
row
accountNo	账号	varchar(40)	是	交易成功且查询到账户时返回
accountName	账户名称	varchar(120)	是	交易成功且查询到账户时返回
balance	历史余额	decimal(15,2)	否	交易成功且查询到余额时返回
currencyID	币种	varchar(5)	否	交易成功且查询到余额时返回，币种类型见附录4.3所示
date	日期	char(8)	否	交易成功且查询到余额时返回，余额日期
dataSource	数据来源	char(2)	否	01: 银行接口返回
02：银行明细返回
03：司库数据计算
dataStatus	数据状态	char(2)	是	01：获取中
02：已返回
03：获取失败
lastUdtTms	更新时间	char(15)	否	交易成功且查询到余额时返回，更新时间
row
list

3.1.10.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
	<action>SKBALHCX</action>
	<userName>11100114956559012768</userName>
	<clientID>********</clientID>
<startDate>********</startDate>
		<endDate>********</endDate>
<startRecord>1</startRecord>
<pageNumber>1</pageNumber>

</stream>

3.1.10.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<failReason></failReason>
<statusText>交易成功</statusText>
<returnRecords>1</returnRecords>
<totalRecords>1</totalRecords>
<list name="userDataList">
<row>
<accountName>账户01</accountName>
<accountNo>8110901013900618088</accountNo>
<balance>1700.00</balance>
<currencyID>CNY</currencyID>
<date>********</date>
<lastUdtTms>******** 152230</lastUdtTms>
<dataSource>03</dataSource>
<dataStatus>02</dataStatus>
</row>
</list>
</stream>
3.1.11 多账户历史明细查询
请求代码： SKTRNNCT
接口说明：
该接口用于查询多账户的历史交易明细信息。
接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.该交易使用分页查询，起始记录号从1开始，每页最多显示100条记录。报文中的交易流水号sumTranNo由司库系统产生，用于标识客户交易明细数据唯一性。

3.1.11.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
startDate	起始日期	char(8)	是	查询明细范围的开始日期，使用yyyyMMdd格式，开始日期与结束日期跨度不能超过31天
endDate	终止日期	char(8)	是	查询明细范围的结束日期，使用yyyyMMdd格式，开始日期与结束日期跨度不能超过31天
tranType	借贷方向
	char(2)	是	需要查询交易的类型，01：全部交易；02：借；03：贷
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持100条记录
list
row
accountNo	账号	varchar(40)	是	待查询的账号，最多支持上送10个账号
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户数量
list
row
sumTranNo	系统交易流水号	varchar(20)	否	交易成功且查询到交易明细时返回，是对方银行返回的交易明细流水号
tranDate	交易日期	char(8)	否	交易成功且查询到交易明细时返回，交易发生日期使用yyyyMMdd格式
tranTime	交易时间	char(6)	否	交易成功时返回，交易发生时间使用hhmmss格式
accountingDate
	记账日期
	char(8)
	否	该日期为银行起息记账日期，使用yyyyMMdd格式,直联接口返回则记录，若未返回，则使用交易日期。规则如上，仅供参考

accountNo	本方账号	varchar(40)	否	交易成功时返回，查询输入的账号
accountName	账户名称	varchar(120)	否	交易成功时返回，查询输入的账号对应的账户名称
openBankName	开户行名称	varchar(300)	否	交易成功时返回，查询输入的账号对应的开户行名称
bankName	本方所属银行	varchar(120)	否	交易成功时返回，查询输入的账号对应的所属银行名称
instName	机构名称	varchar(360)	否	交易成功时返回，查询输入的账号对应的机构名称
instCode	机构编码	varchar(20)	否	交易成功时返回，查询输入的账号对应的机构编码
oppAccountNo	对方账号	varchar(40)	否	交易成功且查询到交易明细时返回
oppAccountName	对方账户名称	varchar(120)	否	交易成功且查询到交易明细时返回
oppOpenBankName	对方开户行名	varchar(120)	否	交易成功且查询到交易明细时返回
oppOpenBankNo	对方开户行联行号	varchar(32)	否	交易成功且查询到交易明细时返回
tranType	借贷方向	char(2)	否	交易成功且查询到交易明细时返回，02：借；03：贷
tranAmount	交易金额	decimal(15,2)	否	交易成功且查询到交易明细时返回
rrtanid	退汇标识	char(1)	否	0已退汇 1非退汇 2手工退汇
balance	账户余额	decimal(15,2)	否	交易成功且查询到账户时返回，标识该账户中全部余额，包含冻结金额、可操作余额等
currencyID	币种	varchar(5)	否	交易成功且查询到账户时返回，币种类型见附录4.4所示
txnSrlnum	交易流水号	varchar(200)	否	交易匹配号，支持付款单与明细关联的银行返回，详见接口说明中支持付款与明细关联的银行列表
bnkSrlnum	银行流水号	varchar(200)	否	回单匹配号，系统生成的流水号，明细对应的回单该字段值一致
originalSrlnum	原始银行流水号	Varchar(100)	否	交易成功且查询到交易明细时返回，是对方银行返回的交易明细流水号（目前仅支持平安银行）
dataSource	数据来源	varchar(2)	否	交易成功且查询到交易明细时返回，标识该交易数据查询来源，
标识该交易数据查询来源，1：直联；2：非直联-人工；3：非直联-智能（RPA导入）
lvmsg	附言	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明附言时返回空值
smy	摘要	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明摘要时返回空值
rmrk	备注	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明备注时返回空值
purpose	用途	varchar(512)	否	交易成功且查询到交易明细时返回，交易未注明用途时返回空值
cashTfrId	现转标识	char(1)	否	0现金 1转账
hdlTms	直联获取时间	TIMESTAMP	否	系统存储时间，格式为：yyyy-MM-dd HH:mm:ss
externalNum	外部请求流水号	varchar(50)	否	对方行支持明细对账时返回
externalBatNum	外部请求批次号	varchar(30)	否	对方行支持明细对账、且为批量支付生成时返回
accDtlId	系统交易流水号	varchar(20)	否	交易成功且查询到交易明细时返回，该流水号是司库系统内该笔明细的唯一性标识,与sumTranNo保持一致
extendRemark	扩展字段1	varchar(20)	否	仅支持兴业银行，别的银行为空
isOpnDirconId	联网方式	varchar(1)	否	标识本方账户的联网方式，
0非直联 1直联
accCgyId	账户性质	char(20)	否	标识本方账户的账户性质，
1一般账户 2基本账户 3专用账户 4临时账户 5其他
accTpId	存款类型	char(20)	否	标识本方账户的存款类型，
1活期 2定期 3通知 4活期保证金 5定期保证金 6其他
isFrgnAccId	境内/境外账户	char(1)	否	标识本方是境内还是境外账户，
0境内 1境外
accCharId	账户属性	char(20)	否	标识本方账户的账户属性，
1实账户 2登记簿

row
list
3.1.11.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKTRNNCT</action>
<userName>xxxxx</userName>
<startDate>********</startDate>
<endDate>********</endDate>
<tranType>01</tranType>
<startRecord>1</startRecord>
<pageNumber>20</pageNumber>
<list name="userDataList">
	<row>
		<accountNo>8110901013900618088</accountNo>
	</row>
	<row>
		<accountNo>8110901013900618089</accountNo>
	</row>
</list>
</stream>
3.1.11.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status>AAAAAAA</status>
    <statusText>交易成功</statusText>
    <totalRecords>1</totalRecords>
	<returnRecords>1</returnRecords>
	<list name="userDataList">
		<row>
            <sumTranNo>81109010139006180882201781</sumTranNo>
            <tranDate>********</tranDate>
            <tranTime>121212</tranTime>
            <accountingDate>********</accountingDate>
            <accountNo>8110901013900618088</accountNo>
            <accountName>erp测试</accountName>
            <openBankName>兴业银行股份有限公司福州五一支行</openBankName>
            <bankName>上海银行</bankName>
            <instName></instName>
            <instCode></instCode>
            <oppAccountNo>AAAGFRP1XXX</oppAccountNo>
            <oppAccountName></oppAccountName>
            <oppOpenBankName></oppOpenBankName>
            <oppOpenBankNo></oppOpenBankNo>
            <tranType>03</tranType>
            <tranAmount>222.00</tranAmount>
            <rrtanid></rrtanid>
            <balance>222.00</balance>
            <currencyID>CNY</currencyID>
            <txnSrlnum></txnSrlnum>	
            <bnkSrlnum>********AAAGFRP1XM9000003518100</bnkSrlnum>
    		<originalSrlnum>********A00003518100</originalSrlnum>
    		<dataSource>1</dataSource>
            <lvmsg></lvmsg>
            <smy></smy>
            <rmrk></rmrk>
            <purpose></purpose>
            <cashTfrId></cashTfrId>
            <hdlTms>2023-01-01 12:32:51</hdlTms>
            <externalNum></externalNum>
            <externalBatNum></externalBatNum>
            <accDtlId></accDtlId>
            <extendRemark>2323</extendRemark>
            <isOpnDirconId></isOpnDirconId>
            <accCgyId></accCgyId>
            <accTpId></accTpId>
            <isFrgnAccId></isFrgnAccId>
            <accCharId></accCharId>
		</row>
	</list>
</stream>
3.2 结算中心
1.在结算中心下，单笔付款“外部请求流水号”和批量付款、排款、团金宝付款“外部请求批次号”不能重复以及同一批次下“外部请求流水号”不能重复，“外部请求批次号”和“外部排款批次号”按照如下格式：JS+集团在我行的核心客户号+年月日+8位顺序码，例如JS0017008678312024012400000001。
2.所有支付结果均以查证接口为准。
3.2.1 单笔付款
请求代码：SKDLTTRN
接口说明：
用于发起单笔付款申请，企业调用该接口推送单笔付款请求，司库系统接收该请求后生成单笔付款的申请任务，并返回受理状态。受理成功后，客户需依据司库系统配置的审批流程参数及工作流，完成司库系统的审批流转。待审批通过后，系统自动执行单笔付款交易指令，用户可通过单笔查证交易进行交易状态的查询。
接口使用须须知：
1.付方账号需提前在司库系统内维护为直联账户并为直联用户赋予单笔付款支付权限及付方单位的机构权限；
2.接口调用后立即返回司库受理状态，此状态只表示交易请求是否受理，若校验通过该笔付款将进入【司库系统】-【结算中心】-【单笔付款】功能，支付是否成功需稍后使用单笔查证交易进行查询。
3.在司库中优先级按照如下处理模式：
按照【金额】或【附言】进行审批流配置；若公共中心内，结算中心业务参数中开启按金额审批，则需同时配置按金额审批阈值。当ERP推送的单笔付款的付款金额超过设定的审批阈值时，则需落地审批，否则依据附言审批开关匹配是否需要审批。【金额】或【附言】开关均关闭时按照【业务模式参数】进行处理。
按照业务模式参数可支持三种模式：
1）审批处理，ERP传输的单据，直接到流程中心-待审批任务；
2)直接出账，只走司库接口，单据可在单笔付款查询功能查询；
3）经办处理时，需要在司库公共中心进行流程配置，单笔付款经办页面进行后续操作。
4.若该账户支持联动支付，资金会先从核心账户转入该支付账户中进行支付，资金下拨结果可在【单笔付款查证】接口中进行查看；
5.是否跨行，非必输字段，若用户上送，则以用户上送为准，若用户不上送，司库仍按照收付方银行判断是否跨行；
3.2.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
externalNum	外部请求流水号	varchar(50)	是	最大长度为50，不能重复。
linkPayFlag	联动支付	char(2)	否	00：否，01:是  默认为否
pypartyAccnum	付方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
rcvpyAccnum	收方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
rcvpyAccnm	收方户名	varchar(300)	是	最大支持长度300（每汉字/占3长度；每非汉字/占1长度）
isCheckRcvpartyDepbnkId	是否准确校验开户行行名	char（2）	否	00:是  01：否     默认为是
当该字段为00是时：同时传入开户行行名和联行号，以联行号为准；
当该字段为01否时：同时传入开户行行名和联行号，以联行号为准；只传入开户行行名直接返显开户行，不再校验开户行行名在境内网点表是否存在，若用户上送的开户行行名匹配不上所属银行，则【收方银行】展示--，【是否跨行】字段，若用户上送，则以用户上送的为准，若用户未上送，收方银行为空，则无法按照收付方银行判断出是否跨行，则是否跨行默认为是；若用户上送的开户行行名匹配不出联行号时，则【收方联行号】、【收方地区】展示为--；只传入联行号，以联行号为准。
rcvpartyDepbnkId	收方开户行	varchar(80)	否	收方开户行和收方联行号选择一个输入即可
付款种类对私付款且账户为银联卡时，可根据卡BIN号识别收方开户行、收方联行号字段，两字段不必输，非空情况以上送值为准
rcvpartyBnkgId	收方联行号	varchar(40)	否	
txnCntprTp	交易对手类型	varchar(20)	否	客户:01供应商:02
经销商:03其他:04
pyAccTp	付款种类	char(2)	是	00：对公 01：对私 
amt	付款金额	decmial（15，2）	是	整数最长13位，2位小数
currencyID	币种	varchar(5)	是	币种仅支持人民币（CNY）
isInterbnk	是否跨行	char（2）	否	00：不跨行  01：跨行
当用户上送该字段时，以用户上送为准；若用户未上送该字段时，保持原判断逻辑，判断收付方银行是否跨行
urgntAprvFlag	加急审批	char（2）	否	00：否，01:是 默认为否
pyTmlnsFlag	预约付款	char（2）	否	00：否 01:是 默认为否
rsrvtnTms	预约时间	varchar(19)	否	是否预约付款状态为01，预约时间必填；预约时间年月日格式为 yyyy-MM-dd  时分格式为枚举值 06:00-22:00任一时间
pscpt	附言	varchar(300)	是	银行附言，最大支持长度300（每汉字占3长度；每非汉字占1长度），不同付方银行支持附言长度不同，详见附录5.8
rmrk	备注	varchar(120)	否	最大长度为40
rmrk1	备用字段1	varchar(600)	否	最大长度为200
rmrk2	备用字段2	varchar(600)	否	最大长度为200
rmrk3	备用字段3	varchar(600)	否	最大长度为200
rmrk4	备用字段4	varchar(600)	否	最大长度为200
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
externalNum	外部请求流水号	varchar(50)	是	入参流水号返回
dealMode	处理模式	char(1)	是	1.审批处理
2.直接出账
3.经办处理
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
 
3.2.1.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLTTRN</action>
    <userName>11100177806072284560</userName>
    <externalNum>20230525003</externalNum>
    <linkPayFlag>00</linkPayFlag>
    <pypartyAccnum>8110701013001434341</pypartyAccnum>
 <isCheckRcvpartyDepbnkId>00</isCheckRcvpartyDepbnkId>
    <rcvpyAccnum>8110701013801434323</rcvpyAccnum>
    <rcvpyAccnm>测试收方户名</rcvpyAccnm>
    <rcvpartyDepbnkId></rcvpartyDepbnkId>
    <rcvpartyBnkgId>302100011106</rcvpartyBnkgId>
    <txnCntprTp>01</txnCntprTp>
    <amt>88</amt>
    <currencyID>CNY</currencyID>
<isInterbnk>00</isInterbnk>
    <urgntAprvFlag></urgntAprvFlag>
    <pyTmlnsFlag>01</pyTmlnsFlag>
    <rsrvtnTms>2023-08-30 10:00</rsrvtnTms>
    <pscpt>附言</pscpt>
    <rmrk>备注</rmrk>
  <rmrk1>备用字段1</rmrk1>
  <rmrk2>备用字段2</rmrk2>
  <rmrk3>备用字段3</rmrk3>
  <rmrk4>备用字段4</rmrk4>
    <pyAccTp>00</pyAccTp>
</stream>
3.2.1.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<dealMode>1</dealMode>
<externalNum>2023060700003</externalNum>
<failReason></failReason>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
</stream>

3.2.2 单笔付款查证
请求代码：SKDLBATD
接口说明：
企业ERP等系统调用该接口查询单笔付款执行情况
接口使用须须知：
1.直联用户需在司库系统配置付方单位的查询权限；
2.返回的支付任务状态若为处理中，不代表该笔交易失败，请勿重复提交，防止重复动账。
3.2.2.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
externalNum	外部请求流水号	varchar(50)	是	最大长度为50
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
pyStat	支付任务状态	char(2)	是	01：待提交：待提交审批
02：待付款：已提交审批,流程运行中
03：处理中：审批通过，正在付款
04：付款成功：支付终态，付款成功
05：付款失败：支付终态，付款失败
06：已拒绝：审批不同意
alocStat	资金下拨结果	char(2)	否	该字段在单据为联动支付时展示：
00-待处理；01-处理中；02-差额为0,无需下拨；03-成功；04-失败
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
bnkRetCode	银行交易结果	varchar(512)	是	银行返回结果
（当单据为联动支付时，该字段包括资金下拨失败的信息）
extendRemark	备注	varchar(254)	否	JSON格式字符串，示例
{"approvalTime":"2023-12-06 11:11:05","createTime":"2023-11-21 13:50:39","rcvBnkTime":"2023-11-22 11:11:11","rejectReason":"不同意","pyTmlnsFlag"："02"，"optUserNm："张三"}
字段释义：
1.approvalTime：终审通过/拒绝时间(yyyy-MM-dd HH:mm:ss) 
2.createTime：外系统提单时间(yyyy-MM-dd HH:mm:ss) 
3.rcvBnkTime：交易时间(yyyy-MM-dd HH:mm:ss) 
4.rejectReason：拒绝原因（当支付任务状态为06已拒绝时，该字段展示审批最终节点的审批意见）
5.pyTmlnsFlag预约付款：00：否 01:是 02:已取消
6.optUserNm删除人用户名称：XX
pypartyAccnum	付方账号	varchar(32)	是	
pypartyAccnm	付方户名	varchar(300)	是	
pypartyDepBnkNm	付方开户行	varchar(300)	是	
pypartyBnkgId	付方开户行联行号	varchar(11)	是	

3.2.2.2 请求报文

<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLBATD</action>
    <userName>11100177806072284560</userName>
    <externalNum>20230525003</externalNum>
</stream>

3.2.2.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<bnkRetCode></bnkRetCode>
<failReason></failReason>
<pyStat>02</pyStat>
<alocStat>00</alocStat>
<extendRemark></extendRemark>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
</stream>
3.2.3 批量付款
请求代码：SKDLPAAT
接口说明：
该接口用于发起多笔付款申请，调用该接口推送多笔付款请求，司库系统接收该请求后生成多笔付款的申请任务，并返回受理状态。受理成功后，客户需依据司库系统配置的审批流程参数及工作流，完成司库系统的审批流转。待审批通过后，系统自动执行付款交易指令，用户可通过批量查证交易进行交易状态的查询。
接口使用须须知：
1.本交易为一借多贷模式，1个付方账号，多个收方账号；
2.付方账号需提前在司库系统内维护为直联账户并为直联用户赋予批量付款支付权限及付方单位的机构权限；
3.接口调用后立即返回司库受理状态，此状态只表示交易请求是否受理，若校验通过该笔付款将进入【司库系统】-【结算中心】-【批量付款】功能，支付是否成功需稍后使用多笔查证交易进行查询。
4.在司库中按照如下业务模式参数进行处理：
1）审批处理，ERP传输的单据，直接到流程中心-待审批任务；
2)直接出账，只走司库接口，单据可在批量付款查询功能查询；
3）经办处理时，需要在司库公共中心进行流程配置，批量付款经办页面进行后续操作。
5.若该账户支持联动支付，资金会先从核心账户转入该支付账户中进行支付，资金下拨结果可在【批量付款查证】接口中进行查看；
6.接口请求参数中提供明细校验模式，“明细校验处理模式”。处理模式包含两种，整批验证模式、明细验证模式，为空时默认为单条明细异常整批失败。
整批验证模式 01：即现有处理模式，当批次内存在一条明细验证不通过时，整批状态失败，不入库，接口返回批次失败及错误信息。
明细验证模式02：新模式，当批次内存在一条明细验证不通过时，跳过该异常明细继续执行后续明细验证，验证通过的明细进行入库，根据外部请求批次号、外部请求流水号进行保存，同时接口返回部分成功及部分失败明细的条目信息，当采用明细验证模式下，单条明细校验不通过时交易状态返回“BBBBBBB”交易状态信息返回“交易部分成功！”。
7.是否跨行，非必输字段，若用户上送，则以用户上送为准，若用户不上送，司库仍按照收付方银行判断是否跨行；
3.2.3.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
externalBatNum	外部请求批次号	varchar(30)	是	最大长度为30，不能重复。
linkPayFlag	联动支付	char(2)	否	00：否，01:是  默认为否
pypartyAccnum	付方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
currencyID	币种	varchar(5)	是	币种只支持人民币（CNY）
totNbr	付款总笔数	char(4)	是	付款总笔数为整数，最大1000
amt	付款总金额	decmial（15，2）	是	整数最长13位，2位小数
urgntAprvFlag	加急审批	char（2）	是	00：否，01:是 默认为否
rsrvtnFlag	预约付款	char（2）	是	00：否 01:是 默认为否
rsrvtnTms	预约时间	varchar(19)	否	是否预约付款状态为01，预约时间必填；预约时间年月日格式为 yyyy-MM-dd  时分格式为枚举值 06:00-22:00任一时间
checkMode	明细校验处理模式	char（2）	否		整批验证模式 01：即现有处理模式，当批次内存在一条明细验证不通过时，整批状态失败，不入库，接口返回批次失败及错误信息。
	明细验证模式02：新模式，当批次内存在一条明细验证不通过时，跳过该异常明细继续执行后续明细验证，验证通过的明细进行入库，根据外部请求批次号、外部请求流水号进行保存，同时接口返回部分成功及部分失败明细的条目信息，后续操作逻辑、业务规则未发生变化
未上传时，默认为01
list
row
externalNum	外部请求流水号	varchar(50)	是	最大长度为50，同批次内不能重复。
rcvpyAccnum	收方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
rcvpyAccnm	收方户名	varchar(300)	是	最大支持长度300（每汉字/占3长度；每非汉字/占1长度）
isInterbnk	是否跨行	char（2）	否	00：不跨行  01：跨行
当用户上送该字段时，以用户上送为准；若用户未上送该字段时，保持原判断逻辑，判断收付方银行是否跨行
isCheckRcvpartyDepbnkId	是否准确校验开户行行名	char（2）	否	00:是  01：否
默认为是，
当该字段为00是时：
同时传入开户行行名和联行号，以联行号为准；只传入开户行行名时精确匹配；只传入联行号，以联行号为准。
当该字段为01否时：
同时传入开户行行名和联行号，以联行号为准；只传入开户行行名直接返显开户行，不再校验开户行行名在境内网点表是否存在；若用户上送的开户行行名匹配不上所属银行，则【收方银行】展示--，【是否跨行】字段，若用户上送，则以用户上送的为准，若用户未上送，收方银行为空，则无法按照收付方银行判断出是否跨行，则是否跨行默认为是；若用户上送的开户行行名匹配不出联行号时，则【收方联行号】、【收方地区】展示为--；只传入联行号，以联行号为准。
rcvpartyDepbnkId	收方开户行	varchar(80)	否	收方开户行和收方联行号选择一个输入即可；当同时传入收款账号开户行和联行网点号时，默认使用联行网点号信息。
付款种类对私付款且账户为银联卡时，可根据卡BIN号识别收方开户行、收方联行号字段，两字段不必输，非空情况以上送值为准
rcvpartyBnkgId	收方联行号	varchar(40)	否	
txnCntprTp	交易对手类型	varchar(20)	否	客户:01供应商:02
经销商:03其他:04
pyAccTp	付款种类	char(2)	是	00：对公 01：对私 
debitAmt	付款金额	decmial（15，2）	是	整数最长13位，2位小数
pscpt	附言	varchar(300)	是	银行附言，最大支持长度300（每汉字占3长度；每非汉字占1长度），不同付方银行支持附言长度不同，详见附录5.8
rmrk	备注	varchar(120)	否	至多支持40个汉字或字符
row
list
Response
status	交易状态	varchar(7)	是	交易状态checkMode：02且部分明细入库成功，返回“BBBBBBB”
statusText	交易状态信息	varchar(254)	是	交易状态结果描述checkMode：02且部分明细入库成功，返回“交易部分成功”
externalBatNum	外部请求批次号	varchar(30)	是	外部请求批次号
dealMode	处理模式	char(1)	是	1.审批处理
2.直接出账
3.经办处理
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
list（校验结果明细）
row
externalNum	明细流水号	varchar(50)	是	最大长度为50
rowStat
	校验状态	varchar(7)	是	校验状态返回码
AAAAAAA - 校验成功
rowStatMsg
	校验状态信息	varchar（50）	否	校验状态结果描述
row
list

3.2.3.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLPAAT</action>
    <userName>11100177806072284560</userName>
    <externalBatNum>2022215121512312</externalBatNum>
    <linkPayFlag>00</linkPayFlag>
    <pypartyAccnum>8110701013001434341</pypartyAccnum>
    <currencyID>CNY</currencyID>
    <totNbr>1</totNbr>
    <amt>1</amt>
    <urgntAprvFlag>00</urgntAprvFlag>
    <rsrvtnFlag>00</rsrvtnFlag>
    <rsrvtnTms></rsrvtnTms>
<checkMode></checkMode>
    <list name="debitList">
        <row>
            <externalNum>2022215121512312</externalNum>
            <rcvpyAccnum>1</rcvpyAccnum>
            <rcvpyAccnm>2</rcvpyAccnm>
<isInterbnk>00</isInterbnk>
  <isCheckRcvpartyDepbnkId>00</isCheckRcvpartyDepbnkId>
            <rcvpartyDepbnkId>3</rcvpartyDepbnkId>
            <rcvpartyBnkgId>105148900025</rcvpartyBnkgId>
            <txnCntprTp>02</txnCntprTp>
            <pyAccTp>01</pyAccTp>
            <debitAmt>1</debitAmt>
            <pscpt>8</pscpt>
            <rmrk></rmrk>
        </row>
    </list>
</stream>
3.2.3.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<dealMode>1</dealMode>
<externalBatNum>2022215121512312</externalBatNum>
<failReason></failReason>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<list name="batchInfoList">
<row>
<externalNum>2022215121512312</externalNum>
<rowStat>AAAAAAA</rowStat>
<rowStatMsg></rowStatMsg>
</row>
</list>
</stream>

3.2.4 多笔付款查证
请求代码：SKDLBATC
接口说明：
调用该接口查询批量付款执行情况。
接口使用须须知：
1.直联用户需在司库系统配置付方单位的查询权限；
2.返回的批次支付任务状态若为处理中，不代表整批次交易失败，请勿重复提交，防止重复动账；
3.返回的批次支付任务状态若为部分成功，代表该批次中存在交易失败的支付明细。
3.2.4.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
externalBatNum	外部请求批次号	varchar(30)	是	最大长度为30
list
row
externalNum	外部请求流水号	varchar(50)	否	最大长度为50
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
pyStat	批次支付任务状态	char(2)	是	01：待提交：待提交审批
02：待付款：已提交审批，流程运行中
03：处理中：审批通过，正在付款或批次内包含处理中的支付明细任务
04：付款成功：支付终态，批次付款成功
05：付款失败：支付终态，批次付款失败
06：已拒绝：批次审批不同意
07：部分成功：支付终态，批次内同时包含付款失败和付款成功的支付明细任务
alocStat	资金下拨结果	char(2)	否	该字段在单据为联动支付时展示：
00-待处理；01-处理中；02-差额为0,无需下拨；03-成功；04-失败
externalBatNum	外部请求批次号	varchar(30)	是	外部请求批次号
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
extendRemark	备注	varchar(254)	否	JSON格式字符串，示例
{"approvalTime":"2023-12-06 11:11:05","createTime":"2023-11-21 13:50:39","rejectReason":"不同意","optUserNm："张三"}
字段释义：
1.approvalTime：终审通过/拒绝时间(yyyy-MM-dd HH:mm:ss) 
2.createTime：外系统提单时间(yyyy-MM-dd HH:mm:ss) 
3.rejectReason：拒绝原因（当支付任务状态为06已拒绝时，该字段展示审批最终节点的审批意见）
4.optUserNm删除人用户名称：XX
pypartyAccnum	付方账号	varchar(32)	是	
pypartyAccnm	付方户名	varchar(300)	是	
pypartyDepBnkNm	付方开户行	varchar(300)	是	
pypartyBnkgId	付方开户行联行号	varchar(11)	是	
list
row
bnkRetCode	银行处理结果	varchar(512)	是	银行返回结果
（当单据为联动支付时，该字段包括资金下拨失败的信息）
pyStat	明细支付任务状态	char(2)	是	01：待提交：同批次，待提交审批
02：待付款：同批次，已提交审批，流程运行中
03：处理中：明细支付任务正在付款
04：付款成功：明细支付终态，付款成功
05：付款失败：明细支付终态，付款失败
externalNum	外部请求流水号	varchar(50)	是	最大长度为50
row
list
 
3.2.4.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLBATC</action>
    <userName>11100177806072284560</userName>
    <externalBatNum>2022215121512312</externalBatNum>
    <list name="extNumList">
        <row>
            <externalNum>2022215121512312</externalNum>
        </row>
    </list>
</stream>
3.2.4.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<externalBatNum>2022215121512312</externalBatNum>
<failReason></failReason>
<pyStat>02</pyStat>
<alocStat>00</alocStat>
<extendRemark></extendRemark>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<list name="batchInfoList">
<row>
<bnkRetCode></bnkRetCode>
<externalNum>2022215121512312</externalNum>
<pyStat>02</pyStat>
</row>
</list>
</stream>
3.2.5 排款
请求代码：SKDLFNMA
接口说明：
企业ERP等系统调用该接口推送排款请求，司库系统接收该请求后在【司库系统】-【结算中心】-【排款管理】功能生成排款任务，并返回受理状态。
接口使用须知：
1.接口调用后返回司库受理状态，此状态只表示排款请求是否受理；后续的排款及支付情况需使用排款查证交易进行查询；
2.付方账号需提前在司库系统内维护为直联账户并按需为直联用户赋予单笔付款或批量付款的支付权限及付方单位的机构权限；
3.收方明细信息数量最大支持1000笔；
4.若推送的明细单据数量为单条时，系统默认生成单笔付款类型的排款任务；当推送的明细单据数量为多条时，系统默认生成批量付款类型的排款任务。
5.若联动支付为是时，系统对应生成单笔或批量类型的联动支付排款任务。若未通过校验，则接口报错并提示“该账户无法支持联动支付”。
6.预算占用校验规则：若该排款请求根据配置需占用预算，则对预算占用事项进行校验，若未通过校验则返回错误及提示“无预算占用记录”，进行校验时，预算占用事项流水号（预算占用接口中“交易流水号”）需与本接口“外部排款流水号”保持一致。
3.2.5.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
fndarBatNum	外部排款批次号	varchar(30)	是	最大长度为30，不能重复。
校验规则：如果占预算需要有预算占用记录，同预算占用接口中“外部占用预算编号”
pypartyAccnum	付方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
businessType	业务类型	varchar(90)	否	最大长度为30
settlementMode	结算方式	char(2)	否	结算方式：01支付转账 02 银承开票 03 商承开票 04银承转让 05商承转让 06 现金支票 07转账支票 08 银行保函 09信用证 0A 银行汇票 0B银行本票0C供应链凭证支付0D项目贷支付10其他，不传默认01支付转账
payRat	排款份额	varchar（1000）	否	Json格式[
{“pcode”:”01”,
“pamt”:”100”
"payAccNum": "123456789"},
{“pcode”:”02”,
“pamt”:”200”
"payAccNum": "123456789"}   ]
校验排款份额json字段中不同支付转账方式对应的金额不能大于付款金额；json中最大key，value值最大支持10对；（结算方式可重复），当排款份额里金额总计大于付款金额时，返回报错，报错提示为：“排款份额总计不能大于付款金额”
payDt	应付日期	varchar(10)	是	格式：yyyy-MM-dd 示例：2023-08-25
linkPayFlag	联动支付	char(2)	否	00：否，01:是  默认为否
list
row
fndarNum	外部排款流水号	varchar(50)	是	最大长度50，同批次内不能重复。
校验规则：如果占预算需要有预算占用记录，同预算占用接口中“交易流水号”
rcvpyAccnum	收方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
rcvpyAccnm	收方户名	varchar(300)	是	最大支持长度300（每汉字/占3长度；每非汉字/占1长度）
rcvpartyDepbnkId	收方开户行	varchar(80)	否	1.收方开户行和收方联行号选择一个输入即可；当同时传入收款账号开户行和联行网点号时，默认使用联行网点号信息;
2.对私付款且账户为银联卡时，可根据卡BIN号识别收方开户行、收方联行号字段，两字段不必输
rcvpartyBnkgId	收方联行号	varchar(40)	否	
txnCntprTp	交易对手类型	char(2)	否	客户:01供应商:02经销商:03其他:04 ,不传默认其他04
pyAccTp	付款种类	char(2)	是	00：对公 01：对私 
amt	付款金额	decmial（15，2）	是	整数最长13位，2位小数
currencyID	币种	varchar(5)	是	只支持人民币CNY
pscpt	附言	varchar(300)	是	银行附言，最大支持长度300（每汉字占3长度；每非汉字占1长度），不同付方银行支持附言长度不同，详见附录
rmrk	备注	varchar(120)	否	最大长度为40
rmrk1	备用字段1	varchar(600)	否	最大长度为200
rmrk2	备用字段2	varchar(600)	否	最大长度为200
rmrk3	备用字段3	varchar(600)	否	最大长度为200
rmrk4	备用字段4	varchar(600)	否	最大长度为200
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
fndarBatNum	排款请求批次号	varchar(50)	是	入参流水号返回
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
list（校验结果明细）
row
fndarNum	外部排款流水号	varchar(50)	是	最大长度为50
rowStat
	校验状态	varchar(7)	是	校验状态返回码
AAAAAAA - 校验成功
rowStatMsg	校验状态信息	varcha（50）	否	校验状态结果描述
row
list

3.2.5.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLFNMA</action>
    <userName>11100177806072284560</userName>
    <fndarBatNum>2022215121512312</fndarBatNum>
    <pypartyAccnum>8110701013001434341</pypartyAccnum>
    <businessType>业务类型</businessType>
    <settlementMode>10</settlementMode>
 <payRat>[{“pcode”:”01”,“pamt”:”100”},{“pcode”:”02”,“pamt”:”200”} ]</payRat>
    <payDt>2024-01-02</payDt>
    <linkPayFlag>00</linkPayFlag>
    <list name="fndarList">
        <row>
            <fndarNum>2022215121512312-1</fndarNum>
            <rcvpyAccnum>1</rcvpyAccnum>
            <rcvpyAccnm>2</rcvpyAccnm>
            <rcvpartyDepbnkId>3</rcvpartyDepbnkId>
            <rcvpartyBnkgId>105148900025</rcvpartyBnkgId>
            <txnCntprTp>02</txnCntprTp>
            <pyAccTp>01</pyAccTp>
            <amt>1</amt>
            <currencyID>CNY</currencyID>
            <pscpt>附言</pscpt>
            <rmrk>备注</rmrk>
  <rmrk1>备用字段1 <rmrk1>
  <rmrk2>备用字段2<rmrk2>
  <rmrk3>备用字段3<rmrk3>
  <rmrk4>备用字段4 <rmrk4>
        </row>
    </list>
</stream>
3.2.5.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status>AAAAAAA</status>
    <statusText>交易成功</statusText>
    <fndarBatNum>2022215121512312</fndarBatNum>
    <failReason></failReason>
    <list name="fndarList">
        <row>
            <fndarNum>2022215121512312-1</fndarNum>
            <rowStat>AAAAAAA</rowStat>
            <rowStatMsg></rowStatMsg>
        </row>
    </list>
</stream>

3.2.6 排款查证
请求代码：SKDLBAFN
接口说明：
企业ERP等系统调用该接口查询排款任务的执行情况。
接口使用须知：
1.直联用户需在司库系统配置付方单位的查询权限。
2.使用排款申请接口返回的原外部排款批次号来进行查证，将返回该笔排款任务拆分出的各笔付款任务状态。
2.1 排款-单笔付款情况下进行查证，查证接口rows标签下不根据未支付金额返回付款金额与之相同的未排期明细数据。
2.2 排款-批量付款情况下进行查证，查证接口rows标签下不会返回未排期的付款数据。
3.当结算方式为银承开票、商承开票、银承转让或商承转让时，返回相关的票据汇总信息。
4.当结算方式为现金支票、转账支票、银行保函、信用证、其他时，子任务状态为“支付成功”时，返回线下付款日期。
3.2.6.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
fndarBatNum	外部排款批次号	varchar(30)	是	最大长度为30
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
fndarBatNum	外部排款批次号	varchar(30)	是	最大长度为0
docId	排款业务编号/批次号	varchar(50)	是	单笔付款类型为排款业务编号；批量付款类型为批次号
fndarStat	排款业务状态	char(2)	是	同结算中心“批次/单据状态”:
02	已排期
03	未排期
04	处理中
05	处理成功
12	部分排期
13	终止排期
totAmt	申请金额	decmial（15，2）	是	
currencyID	币种	varchar(5)	是	
paidAmt	已付金额	decmial（15，2）	是	
unpaidAmt	未付金额	decmial（15，2）	是	
payingAmt	在途金额	decmial（15，2）	是	
payDt	应付日期	varchar(10)	是	格式：yyyy-MM-dd 示例：2023-08-25
extendRemark	备注	varchar(500)	否	JSON格式字符串，示例
{
"tmtEndarTime: 2024-12-17 11:11:05”
"timtEndarReason":"不同意"
}
宁段释义
1.	tmtFndarTime：終止排期时间间(yyyy-MM-dd HH:mmss)
tmtFndarReason：终止排期原因

list
row
fndarNum	外部排款流水号		varchar(50)	是	推送的原单据外部排款流水号
subTaskNum	子任务编号	varchar(50)	否	在司库结算做排期操作后生成的子任务编号
externalBatNum	外部请求批次号	varchar(30)	否	批量付款时的外部请求批次号，单笔付款时为空
在排期操作后，对应司库系统的外部请求批次号
externalNum	外部请求流水号	varchar(50)	否	单笔、批量付款中支付事项的外部请求流水号
在排期操作后，对应司库系统的外部请求流水号
pypartyOrgNm	付方单位	varchar(500)	是	
pypartyAccnum	付方账号	varchar(32)	是	
pypartyAccnm	付方户名	varchar(300)	是	
pypartyDepBnkNm	付方开户行	varchar(300)	是	
pypartyBnkgId	付方开户行联行号	varchar(11)	是	
rcvpyAccnm	收方户名	varchar(300)	是	
rcvpyAccnum	收方账号	varchar(32)	是	
rcvpartyDepBnkNm	收方开户行	varchar(300)	是	
amt	付款金额	decmial（15，2）	是	
pyStat	子任务单据状态	char(2)	是	02	已排期
03	未排期（作废）
04	处理中
05	处理成功
06	处理失败
pcsStat	子任务流程状态	char(2)	否	DF	- 草稿
IP	- 运行中
FN	- 已完成
TM	- 已作废
pyTp	付款类型	char(4)	是	S1 - 单笔对外付款
S2 - 联动单笔付款
B1 - 批量对外付款
B2 - 联动批量付款
settlementMode	结算方式	char(2)	是	结算方式：01支付转账 02 银承开票 03 商承开票 04银承转让 05商承转让 06 现金支票 07转账支票 08 银行保函 09信用证 10其他 0A银行汇票0B银行本票 0C供应链凭证支付
fndarTms	排款时间	varchar(19)	否	格式：yyyy-MM-dd HH:mm:ss
示例：2023-08-25 14:42:00
rsrvtnTms	预约时间	varchar(19)	否	格式：yyyy-MM-dd HH:mm:ss
示例：2023-08-25 14:42:00
rcvBnkTms
	转账结果获取时间	varchar(19)	否	格式：yyyy-MM-dd HH:mm:ss
示例：2023-08-25 14:42:00
结算方式为“支付转账”子任务状态为“处理成功”、“处理失败”时非空，填充时间为从付款行获取该笔转账结果的时间。
offlinePayDate	线下付款日期	varchar(10)	否	格式：yyyy-MM-dd 示例：2024-08-01
结算方式为 06 现金支票 07转账支票 08 银行保函 09信用证 10其他时，子任务状态为“支付成功”时非空，返回线下付款日期
offlineBnkJrnlNum	流水号	varchar(120)	否	结算方式为 06 现金支票 07转账支票 08 银行保函 09信用证 10其他时，子任务状态为“支付成功”时非空，才返回流水号
offlineRmrk	线下付款说明	varchar(120)	否	算方式为 06 现金支票 07转账支票 08 银行保函 09信用证 10其他时，子任务状态为“支付成功”时非空，才返回流水号
billInfoCount	票据总张数	varchar(4)	否	该排款单据下有票据信息返回票据总张数
billTotAmt	票据总金额	decmial（15，2）	否	该排款单据下有票据信息返回票据总金额
signBillInfoCount
	已收票张数	varchar(4)	否	该排款单据下票据信息的已收票票据张数
signBillTotAmt
	已收票金额	decmial（15，2）	否	该排款单据下票据信息的已收票票据金额
waitSignBillInfoCount	待签收票据张数	varchar(4)	否	该排款子任务单据下票据状态为“已收票-已锁定”“已承兑-已锁定”票据数量
waitSignBillTotAmt	待签收票据金额	decmial（15，2）	否	该排款子任务单据下票据状态为“已收票-已锁定”“已承兑-已锁定”票据金额
row
list

3.2.6.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLBAFN</action>
    <userName>11100177806072284560</userName>
    <fndarBatNum>2022215121512312</fndarBatNum>
</stream>
3.2.6.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status>AAAAAAA</status>
    <statusText>交易成功</statusText>
    <failReason></failReason>
    <fndarBatNum>22215121512312</fndarBatNum>
    <docId>00</docId>
<fndarStat>05</fndarStat>
    <totAmt>10</totAmt>
    <currencyID>CNY</currencyID>
    <paidAmt>10</paidAmt>
    <unpaidAmt>0</unpaidAmt>
    <payingAmt>0</payingAmt>
    <payDt>2024-01-02</payDt>
    <list name="fndarList">
        <row>
            <fndarNum>22215121512312-1</fndarNum>
            <subTaskNum>ZITA22215121512312</subTaskNum>
            <externalBatNum>111</externalBatNum>
            <externalNum>111-1</externalNum>
            <pypartyOrgNm></pypartyOrgNm>
            <pypartyAccnum></pypartyAccnum>
            <pypartyAccnm></pypartyAccnm>
            <pypartyDepBnkNm></pypartyDepBnkNm>
            <rcvpyAccnm></rcvpyAccnm>
            <rcvpyAccnum></rcvpyAccnum>
            <rcvpartyDepBnkNm></rcvpartyDepBnkNm>
            <amt>10</amt>
            <pyStat>05</pyStat>
            <pcsStat>FN</pcsStat>
            <pyTp>S1</pyTp>
            <settlementMode>10</settlementMode>
            <fndarTms>2024-01-02 14:42:00</fndarTms>
            <rsrvtnTms>2024-01-02 15:00:00</rsrvtnTms>
              <rcvBnkTms>2024-01-02 15:00:00</rcvBnkTms>
<offlinePayDate>2024-01-02</offlinePayDate>
<offlineBnkJrnlNum></offlineBnkJrnlNum>
<offlineRmrk></offlineRmrk>
            <billInfoCount></billInfoCount>
            <billTotAmt></billTotAmt>
            <signBillInfoCount></signBillInfoCount>
            <signBillTotAmt></signBillTotAmt>
            <waitSignBillInfoCount></waitSignBillInfoCount>
              <waitSignBillTotAmt></waitSignBillTotAmt>

        </row>
    </list>
</stream>
3.2.7 排款票据信息查询
请求代码： SKDLBLNF
接口说明：
用于查询排款结算方式为银承开票、商承开票、银承转让或商承转让时，司库系统接收该请求。
接口使用须须知： 
1.直联用户需在司库系统配置付方单位的查询权限,[公共中心]-[用户权限管理]-[机构权限设置]进行维护。
2.接口支持一次最大20条；
3.使用排款申请接口返回的原外部排款批次号、子任务编号来进行查证，将按子任务号或外部排款批次号得查询结果返回票据信息。
4.当调用排款查证接口返回子任务的结算方式为银承开票、商承开票、银承转让或商承转让时使用。
3.2.7.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKDLBLNF
userName	登录用户名	varchar(30)	是	银企直联用户名
fndarBatNum	外部排款批次号	varchar(30)	是	
subTaskNum	子任务编号	varchar(100)	否	
startRecord	起始记录号	varchar(4)	是	起始记录号
pageNumber	请求记录条数	varchar(4)	是	请求记录条数，最大20
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
fndarBatNum	外部排款批次号	varchar(2)	是	
docId	排款业务编号/批次号	varchar(50)	是	单笔付款类型为排款业务编号；批量付款类型为批次号
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的排款票据数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的排款票据数量
list
row
fndarNum	外部排款流水号		varchar(50)	否	推送的原单据外部排款流水号
subTaskNum	子任务编号	varchar(50)	否	在司库结算做排期操作后生成的子任务编号
settlementMode	结算方式	varchar(50)	否	结算方式：02 银承开票 03 商承开票 04银承转让 05商承转让
signAcc	签约账号	varchar(40)	否	排款付款账号
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
isSupprDt	出票日期	char(10)	否	格式yyyy-MM-dd
billRcvDt	票据到期日期	char(10)	否	格式yyyy-MM-dd
isPrmtSubpge	是否允许分包	char(1)	否	Y:可分包；N：不可分包
billStat	票据状态	char(6)	否	按码值存储，按码值返回
crclFlag	流通标志	char(6)	否	
billTp	票据类型	cahr(4)	否	AC01：银承； AC02：商承
billFaceAmt	票面金额	decimal(15,2)	否	
tfrMark	转让标记	char(4)	否	EM00：可再转让 ；EM01不得转让
bankDockingMode	银行对接模式	char(1)	否	Y:直联；N：非直联
billFaceMemo	票面备注	varchar(180)	否	
rmtrNm	出票人名称	varchar(300)	否	
rmtrIsSupprMemo	出票人备注	varchar(384)	否	
rmtrDepBnkBrCode	出票人开户行行号	char(20)	否	
rmtrDepBnkNm	出票人开户行行名	varchar(300)	否	
rmtrAccNum	出票人账户	varchar(40)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrDepBnkNm	承兑人开户行名称	varchar(300)	否	
acptrDepBnkBrCode	承兑人开户行行号	char(20)	否	
acptrAccNum	承兑人账户	varchar(40)	否	
acptrDt	承兑日期	char(10)	否	格式yyyy-MM-dd
payeeDepBnkNm	收款人开户行名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeDepBnkBrCpde	收款人开户行行号	char(20)	否	
payeeAccNum	收款人账号	varchar(40)	否	
signForDt	签收结果获取日期	char(10)	否	格式yyyy-MM-dd

row
list

3.2.7.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action> SKDLBLNF </action>
    <userName>11100177806072284560</userName>
<fndarBatNum>2022215121512312</fndarBatNum>
< subTaskNum>2022215121512312</ subTaskNum>
<startRecord>1</startRecord>
<pageNumber>1</pageNumber>
</stream>
3.2.7.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status></status>
    <statusText></statusText>
    <fndarBatNum>22215121512312</fndarBatNum>
    <docId>00</docId>
    <totalRecords></totalRecords>
    <returnRecords></returnRecords>
    <list name="billInfoList">
        <row>
            <fndarNum></fndarNum>
            <subTaskNum></subTaskNum>
            <settlementMode></settlementMode>
            <signAcc></signAcc>
            <billPkgId></billPkgId>
            <subBillRng></subBillRng>
            <isSupprDt></isSupprDt>
            <billRcvDt></billRcvDt>
            <isPrmtSubpge></isPrmtSubpge>
            <billStat></billStat>
            <crclFlag></crclFlag>
            <billTp></billTp>
            <billFaceAmt></billFaceAmt>
            <tfrMark></tfrMark>
            <bankDockingMode></bankDockingMode>
            <billFaceMemo></billFaceMemo>
            <rmtrNm></rmtrNm>
            <rmtrIsSupprMemo></rmtrIsSupprMemo>
            <rmtrDepBnkBrCode></rmtrDepBnkBrCode>
            <rmtrDepBnkNm></rmtrDepBnkNm>
            <rmtrAccNum></rmtrAccNum>
            <acptrNm></acptrNm>
            <acptrDepBnkNm></acptrDepBnkNm>
            <acptrDepBnkBrCode></acptrDepBnkBrCode>
            <acptrAccNum></acptrAccNum>
            <acptrDt></acptrDt>
            <payeeDepBnkNm></payeeDepBnkNm>
            <payeeNm></payeeNm>
            <payeeDepBnkBrCpde></payeeDepBnkBrCpde>
            <payeeAccNum></payeeAccNum>
            <signForDt></signForDt>
        </row>
    </list>
</stream>
3.2.8 多批次排款票据查询
请求代码： SKDLBLNG
接口说明：
用于查询排款结算方式为银承开票、商承开票、银承转让或商承转让时，司库系统接收该请求。
接口使用须须知： 
1.直联用户需在司库系统配置付方单位的查询权限,[公共中心]-[用户权限管理]-[机构权限设置]进行维护。
2.接口支持一次最大100条；
3.使用排款申请接口返回的原外部排款批次号、子任务编号来进行查证，将按子任务号或外部排款批次号得查询结果返回票据信息。
4.当调用排款查证接口返回子任务的结算方式为银承开票、商承开票、银承转让或商承转让时使用。
1.1.1.1. 业务规则(分场景，必填)
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKDLBLNG
userName	登录用户名	varchar(30)	是	银企直联用户名
fndarBatNum	外部排款批次号	varchar(30)	否	
subTaskNum	子任务编号	varchar(100)	否	
startRecord	起始记录号	varchar(4)	是	起始记录号
pageNumber	请求记录条数	varchar(4)	是	请求记录条数，最大100
startDate	起始日期	char(8)	是	查询范围的开始日期，使用YYYYMMDD格式，开始日期与结束日期跨度限制30天
endDate	终止日期	char(8)	是	查询范围的结束日期，使用YYYYMMDD格式，开始日期与结束日期跨度限制30天
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的排款票据数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的排款票据数量
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示
list
row
fndarBatNum	外部排款批次号	varchar(2)	否	
docId	排款业务编号/批次号	varchar(50)	是	单笔付款类型为排款业务编号；批量付款类型为批次号
fndarNum	外部排款流水号		varchar(50)	否	推送的原单据外部排款流水号
subTaskNum	子任务编号	varchar(50)	否	在司库结算做排期操作后生成的子任务编号
settlementMode	结算方式	varchar(50)	否	结算方式：02 银承开票 03 商承开票 04银承转让 05商承转让
signAcc	签约账号	varchar(40)	否	排款付款账号
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
isSupprDt	出票日期	char(10)	否	格式yyyy-MM-dd
billRcvDt	票据到期日期	char(10)	否	格式yyyy-MM-dd
isPrmtSubpge	是否允许分包	char(1)	否	Y:可分包；N：不可分包
billStat	票据状态	char(6)	否	按码值存储，按码值返回
crclFlag	流通标志	char(6)	否	
billTp	票据类型	cahr(4)	否	AC01：银承； AC02：商承
billFaceAmt	票面金额	decimal(15,2)	否	
tfrMark	转让标记	char(4)	否	EM00：可再转让 ；EM01不得转让
bankDockingMode	银行对接模式	char(1)	否	Y:直联；N：非直联
billFaceMemo	票面备注	varchar(180)	否	
rmtrNm	出票人名称	varchar(300)	否	
rmtrIsSupprMemo	出票人备注	varchar(384)	否	
rmtrDepBnkBrCode	出票人开户行行号	char(20)	否	
rmtrDepBnkNm	出票人开户行行名	varchar(300)	否	
rmtrAccNum	出票人账户	varchar(40)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrDepBnkNm	承兑人开户行名称	varchar(300)	否	
acptrDepBnkBrCode	承兑人开户行行号	char(20)	否	
acptrAccNum	承兑人账户	varchar(40)	否	
acptrDt	承兑日期	char(10)	否	格式yyyy-MM-dd
payeeDepBnkNm	收款人开户行名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeDepBnkBrCpde	收款人开户行行号	char(20)	否	
payeeAccNum	收款人账号	varchar(40)	否	
signForDt	签收结果获取日期	char(10)	否	格式yyyy-MM-dd

row
list
3.2.8.1 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action> SKDLBLNG </action>
    <userName>11100177806072284560</userName>
<fndarBatNum>2022215121512312</fndarBatNum>
< subTaskNum>2022215121512312</ subTaskNum>
<startRecord>1</startRecord>
<pageNumber>1</pageNumber>
<startDate>20240901</startDate>
<endDate>20240930</endDate>
</stream>
3.2.8.2 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status></status>
    <statusText></statusText>
    <totalRecords></totalRecords>
<returnRecords></returnRecords>
<failReason></failReason>
    <list name="billInfoList">
        <row>
           <fndarBatNum></fndarBatNum>
            <docId></docId>
            <fndarNum></fndarNum>
            <subTaskNum></subTaskNum>
            <settlementMode></settlementMode>
            <signAcc></signAcc>
            <billPkgId></billPkgId>
            <subBillRng></subBillRng>
            <isSupprDt></isSupprDt>
            <billRcvDt></billRcvDt>
            <isPrmtSubpge></isPrmtSubpge>
            <billStat></billStat>
            <crclFlag></crclFlag>
            <billTp></billTp>
            <billFaceAmt></billFaceAmt>
            <tfrMark></tfrMark>
            <bankDockingMode></bankDockingMode>
            <billFaceMemo></billFaceMemo>
            <rmtrNm></rmtrNm>
            <rmtrIsSupprMemo></rmtrIsSupprMemo>
            <rmtrDepBnkBrCode></rmtrDepBnkBrCode>
            <rmtrDepBnkNm></rmtrDepBnkNm>
            <rmtrAccNum></rmtrAccNum>
            <acptrNm></acptrNm>
            <acptrDepBnkNm></acptrDepBnkNm>
            <acptrDepBnkBrCode></acptrDepBnkBrCode>
            <acptrAccNum></acptrAccNum>
            <acptrDt></acptrDt>
            <payeeDepBnkNm></payeeDepBnkNm>
            <payeeNm></payeeNm>
            <payeeDepBnkBrCpde></payeeDepBnkBrCpde>
            <payeeAccNum></payeeAccNum>
            <signForDt></signForDt>
        </row>
    </list>
</stream>

3.2.9 团金宝付款
请求代码：SKDLITLG
接口说明：
用于发起团金宝交易，支持落地经办、提交审批、直接付款三种模式，用户可以根据设置信息选择业务处理模式。
接口使用须须知：
1.外部请求批次号不能重复，包括批量付款、团金宝交易
2.操作人必须有ERP团金宝业务权限、付款账户操作权限
3.收款信息最大支持1000笔
4.仅支持人民币交易
5.在司库中按照如下业务模式参数进行处理：
1）审批处理，ERP传输的单据，直接到流程中心-待审批任务；
2) 直接出账，只走司库接口，单据可在团金宝/薪酬代发查询功能查询；
3）经办处理时，团金宝经办页面进行后续操作
6.若选择联动支付，增加权限校验，校验付方账号必须是资金池成员账号且开通联动下拨功能，若该账户支持联动支付，资金会先从核心账户转入该支付账户中进行支付，资金下拨结果可在【多笔付款查证】接口中进行查看；
3.2.9.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
externalBatNum	外部请求批次号	varchar(30)	是	最大长度为30，不能重复。
linkPayFlag	联动支付	char(2)	否	00：否，01:是  默认为否
pypartyAccnum	付方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
currencyID	币种	varchar(5)	是	币种类型见附录5.3所示，暂仅支持人民币支付
totNbr	付款总笔数	char(4)	是	付款总笔数为整数，最大1000
amt	付款总金额	decmial（15，2）	是	整数最长13位，2位小数
itlgPayrollType	是否代发	char(2)	是	00：否，01:是
itlgPreparType	记账方式	char(1)	是	是否代发为是时，仅支持合笔记账；是否代发为否时，可输入合笔记账或单笔记账
0-单笔记账，1-合笔记账
itlgRefundFlag	退款方式	char(1)	是	0-单笔退款，1-合并退款
itlgChkNum	对账编号	char(20)	否	不可重复，基于对账功能优化此字段7月以后不再使用，ERP端仍可上送司库系统不再接收，对账使用账号明细外部请求批次号及外部请求流水号
urgntAprvFlag	加急审批	char（2）	否	00：否，01:是 默认为否
rsrvtnFlag	预约付款	char（2）	否	00：否 01:是 默认为否
rsrvtnTms	预约时间	varchar(19)	否	是否预约付款状态为01，预约时间必填；预约时间年月日格式为 yyyy-MM-dd  时分格式为枚举值 06:00-22:00任一时间
batPscpt	批次附言	varchar(60)	是	付方附言

batRmrk	批次备注	varchar(50)	否	付方备注
list
row
externalNum	外部请求流水号	varchar(50)	是	最大长度为50，同批次内不能重复。
rcvpyAccnum	收方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
rcvpyAccnm	收方户名	varchar(300)	是	最大支持长度300（每汉字/占3长度；每非汉字/占1长度）
rcvpartyDepbnkId	收方开户行	varchar(80)	否	填写说明：
1.当收方账户为银联卡时，收方开户行或收方联行号字段非必填；
2. 当收方账户为非银联卡（对公账户/存折）时，收方开户行或收方联行号选择一个必输即可：
	当付款金额≤100万时，收方开户行如果录入，系统不做精准校验。  
	当付款金额>100万时，如录入收方开户行，则需精确录入，系统做精准校验。
	
3.当同时传入收方开户行和收方联行号时，默认使用的是收方联行号信息 
rcvpartyBnkgId	收方联行号	varchar(40)	否	
debitAmt	付款金额	decmial（15，2）	是	整数最长13位，2位小数
pscpt	附言	varchar(300)	是	银行附言，最大支持长度300（每汉字占3长度；每非汉字占1长度），不同付方银行支持附言长度不同，详见附录5.8
rmrk	备注	varchar(120)	否	最大长度为40
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
externalBatNum	外部请求批次号	varchar(30)	是	外部请求批次号
dealMode	处理模式	char(1)	是	1.审批处理
2.直接出账
3.经办处理
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
list（校验结果明细）
row
externalNum	明细流水号	varchar(50)	是	最大长度为50
rowStat
	校验状态	varchar(7)	是	校验状态返回码
AAAAAAA - 校验成功
rowStatMsg
	校验状态信息	varchar（50）	否	校验状态结果描述
row
list


3.2.9.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLITLG</action>
    <userName></userName>
    <externalBatNum>15726985932281037</externalBatNum>
    <pypartyAccnum>8110701013701511777</pypartyAccnum>
    <totNbr>1</totNbr>
    <amt>1</amt>
    <currencyID>185</currencyID>
    <itlgPayrollType>01</itlgPayrollType>
    <itlgPreparType>0</itlgPreparType>
    <itlgRefundFlag>0</itlgRefundFlag>
    <itlgChkNum>123123</itlgChkNum>
<batPscpt>123123</batPscpt>
<batRmrk>123123</batRmrk>
    <urgntAprvFlag></urgntAprvFlag>
    <rsrvtnFlag>00</rsrvtnFlag>
    <rsrvtnTms>2023-01-01 11:00</rsrvtnTms>
    <list name="debitList">
        <row>
            <externalNum>11100177806072284560</externalNum>
            <rcvpyAccnum>111001284560</rcvpyAccnum>
            <rcvpyAccnm>辽源农村商业</rcvpyAccnm>
            <rcvpartyDepbnkId></rcvpartyDepbnkId>
            <rcvpartyBnkgId>103227058594</rcvpartyBnkgId>
            <debitAmt>1</debitAmt>
            <pscpt>11100177806072284560</pscpt>
            <rmrk>11100177806072284560</rmrk>
        </row>
    </list>
</stream>
3.2.9.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <dealMode>3</dealMode>
   <externalBatNum>15726985932281037</externalBatNum>
   <failReason></failReason>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <list name="batchInfoList">
      <row>
         <externalNum>11100177806072284560</externalNum>
         <rowStat>AAAAAAA</rowStat>
         <rowStatMsg>处理成功</rowStatMsg>
      </row>
   </list>
</stream>

3.2.10 退汇交易查询
请求代码：SKQRRTAN
接口说明：
用于查询状态已标识为“已退汇”的支付单据，包括本地制单、ERP来源、排款来源以及各渠道来源的单笔付款、批量付款、薪酬支付、费用报销、付款计划、用款申请交易。
接口使用须知：
1.接口支持一次最大20条，日期最大范围为当日T-1前90天内的交易；
2.请求使用的银企直联用户必须有ERP退汇业务权限、付款账户查询权限；
3.退汇交易查询支持自动退汇和手工退汇交易查询，其中自动退汇交易查询银行范围包括：中信银行、招商银行、平安银行退汇情况的交易查询。
4.付款类型是“团金宝”业务，仅支持单笔记账方式的退汇明细查询。
3.2.10.1 参数说明
字段标识	数据项	类型	是否必输	内容说明
输入字段
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
startRecord	起始记录号	varchar(4)	是	起始记录号
pageNumber	请求记录条数	varchar(4)	是	请求记录条数，最大20
<row>
accountNo	账号	varchar(32)	否	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
</row>
startDate	退汇日期起始日期	varchar(8)	是	退汇日期起始日期,格式：yyyyMMdd
endDate	退汇日期结束日期	varchar(8)	是	退汇日期结束日期,格式：yyyyMMdd
退汇日期结束日期不能为当日及以后，查询范围90天
输出字段
totalNum	总笔数	varchar(8)	是	
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
<row>
externalBatNum	外部请求批次号	varchar(30)	否	单笔付款时为空
externalNum	外部请求流水号	varchar(50)	否	
docBatNum	付款批次号/业务编号	varchar(50)	是	司库内部编号
docSrlNum	付款明细编号	varchar(50)	否	司库内部编号，单笔付款时为空
pypartyAccnum	付方账号	varchar(32)	是	
pypartyAccnm	付方户名	varchar(200)	是	
pypartyDepbnk	付方开户行	varchar(80)	是	
pypartyBnk	付方所属银行	varchar(40)	是	
pypartyInstNum	付方机构编码	varchar(40)	是	
pypartyInstNm	付方机构名称	varchar(200)	是	
rcvpyAccnum	收方账号	varchar(32)	是	
rcvpyAccnm	收方账户名称	varchar(200)	是	
rcvpartyDepbnk	收方开户行名	varchar(80)	是	
pyAccTp	付款种类	varchar(2)	是	00：对公 01：对私 
debitAmt	付款金额	decmial（15，2）	是	
currencyID	币种	varchar(5)	是	交易成功且查询到账户时返回，币种类型见附录5.3所示，暂时只支持人民币CNY
pyTp	付款类型	varchar(4)	是	S1 - 单笔对外付款
S2 - 联动单笔付款
S4 - 单笔集团内转账
B1 - 批量对外付款
B2 - 联动批量付款
SP1 - 薪酬代发
SP2 - 联动薪酬代发
FE1 - 费用报销
T1-团金宝 
T2-联动团金宝
pscpt	附言	varchar(300)	是	单笔取付款附言，批量取收方附言
pyTm	原付款时间	varchar(20)	是	格式：yyyyMMdd HH:mm:ss
rrtanDt	退汇日期	varchar(8)	是	格式：yyyyMMdd
txnSrlNum	原付款系统交易流水号	varchar(80)	否	司库交易明细流水号
bnkSrlNum	原付款银行流水号	varchar(80)	否	银行交易流水号
rrtanTxnSrlNum	退汇系统交易流水号	varchar(80)	否	司库交易明细流水号
rrtanBnkSrlNum	退汇银行流水号	varchar(80)	否	银行交易流水号
</row>

3.2.10.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKQRRTAN</action>
<userName></userName>
<startRecord>1</startRecord>
<pageNumber>10</pageNumber>
<startDate>********</startDate>
<endDate>********</endDate>
<list name="debitList">
    <row>
        <accountNo>8110701013401434234</accountNo>
    </row>
</list>
</stream>
3.2.10.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <failReason></failReason>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
<totalNum>1</totalNum>
   <list name="batchInfoList">
      <row>
         <bnkSrlNum>SKSEC17026228429220063</bnkSrlNum>
         <currencyID>CNY</currencyID>
         <debitAmt>8.76</debitAmt>
         <docBatNum>SKSEC17026228429220063</docBatNum>
         <externalBatNum></externalBatNum>
         <externalNum></externalNum>
         <pscpt>测试退汇</pscpt>
         <pyAccTp>01</pyAccTp>
         <pyTm>2023-12-15 14:47:33</pyTm>
         <pyTp></pyTp>
         <pypartyAccnm>天赋模拟开户汉赋啊知和所</pypartyAccnm>
         <pypartyAccnum>8110701013401434234</pypartyAccnum>
         <pypartyBnk>中信银行</pypartyBnk>
         <pypartyDepbnk>中信银行北京分行营业部</pypartyDepbnk>
         <pypartyInstNm>结算中心测试司库专用-万木兰勿动</pypartyInstNm>
         <pypartyInstNum>09219258</pypartyInstNum>
         <rcvpartyDepbnk>绍兴银行股份有限公司柯桥支行</rcvpartyDepbnk>
         <rcvpyAccnm>王伟羊</rcvpyAccnm>
         <rcvpyAccnum>1163489741000023</rcvpyAccnum>
         <rrtanBnkSrlNum></rrtanBnkSrlNum>
         <rrtanDt>2023-12-15</rrtanDt>
         <rrtanTxnSrlNum></rrtanTxnSrlNum>
         <txnSrlNum></txnSrlNum>
      </row>
   </list>
</stream>
3.2.11 批量代发（薪酬代发）
请求代码：SKDLPYCD
接口说明：
该接口用于发起薪酬代发申请，调用该接口推送请求，司库系统接收该请求后生成薪酬代发的申请任务，并返回受理状态。受理成功后，客户需依据司库系统配置的审批流程参数及工作流，完成司库系统的审批流转。待审批通过后，系统自动执行付款交易指令，用户可通过批量付款/团金宝查证交易进行交易状态的查询。
接口使用须须知：
1.本交易为一借多贷模式，1个付方账号，多个收方账号；
2.付方账号需提前在司库系统内维护为直联账户并为直联用户赋予薪酬代发支付权限及付方单位的机构权限；
3.接口调用后立即返回司库受理状态，此状态只表示交易请求是否受理，若校验通过该笔付款将进入【司库系统】-【结算中心】-【薪酬代发】功能，支付是否成功需稍后使用多笔查证交易进行查询。
4.在司库中按照如下业务模式参数进行处理：
1）审批处理，ERP传输的单据，直接到流程中心-待审批任务；
2)直接出账，只走司库接口，单据可在薪酬代发查询功能查询；
3）经办处理时，需要在司库公共中心进行流程配置，薪酬代发经办页面进行后续操作。
5.若该账户支持联动支付，资金会先从核心账户转入该支付账户中进行支付，资金下拨结果可在【批量付款查证】接口中进行查看；

3.2.11.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
externalBatNum	外部请求批次号	varchar(30)	是	最大长度为30，不能重复。
linkPayFlag	联动支付	char(2)	否	01：是；00：否
默认:00 否
pypartyAccnum	付方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
currencyID	币种	varchar(5)	是	币种类型见附录5.3所示，暂仅支持人民币支付
totNbr	付款总笔数	char(4)	是	付款总笔数为整数，最大1000
amt	付款总金额	decmial（15，2）	是	整数最长13位，2位小数
payrollDt	代发月份	char(6）	否	使用中信银行代发、中原银行、齐鲁银行代发、工商银行代发、兴业银行代发代发时必填
格式：yyyyMM
urgntAprvFlag	加急审批	char（2）	否	00：否，01:是 默认为否
rsrvtnFlag	预约付款	char（2）	否	00：否 01:是 默认为否
rsrvtnTms	预约时间	varchar(19)	否	是否预约付款状态为01，预约时间必填；预约时间年月日格式为 yyyy-MM-dd  时分格式为枚举值 06:00-22:00任一时间
batPscpt	批次附言	varchar(60)	是	代发批次附言
payrollUse	代发用途	varchar(60)	否	付款行为交行时，必输，传入交行签约类型编码；
付款行为建行、浦发银行、兴业银行、工商银行、招商银行、平安银行、中国银行、农行、民生、光大、广发、渤海、华夏、无锡农村商业银行、山西农信社、西宁农商行、徽商银行、西安银行、中信银行等时，必输，传入代发用途；(其中中信银行增加默认值1：代发工资)
付款行为威海银行时，代发用途默认为“0002-代发工资”
上述字典码值信息必须在司库系统-公共中心-数据字典-结算中心数据字典功能中可查询到详见附录薪酬代发银行代发项目、代发用途码表

extField1	扩展字段1	varchar(60)	否	付款行为交行时，必输，传入交行协议编号；
付款行为平安时，非必输，传入平安协议编号；
付款行为建行时，必输，传入代发项目；
上述字典码值信息必须在司库系统-公共中心-数据字典-结算中心数据字典功能可查询到详见附录薪酬代发银行代发项目、代发用途码表

list
row
externalNum	外部请求流水号	varchar(50)	是	最大长度为50
rcvpyAccnum	收方账号	varchar(32)	是	允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为32
rcvpyAccnm	收方户名	varchar(300)	是	最大支持长度300（每汉字/占3长度；每非汉字/占1长度）
rcvpartyDepbnkId	收方开户行	varchar(80)	否	1.	可根据银联卡BIN号匹配收方开户行、收方联行号字段，两字段不必输；
2.	对于非银联卡.收方开户行和收方联行号选择一个输入即可；当同时传入收款账号开户行和联行网点号时，默认使用联行网点号信息。
rcvpartyBnkgId	收方联行号	varchar(40)	否	
debitAmt	付款金额	decmial（15，2）	是	整数最长13位，2位小数
pscpt	附言	varchar(300)	是	银行附言，最大支持长度20（每汉字占1长度；每非汉字占1长度）
rmrk	备注	varchar(120)	否	最大长度为40
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
externalBatNum	外部请求批次号	varchar(30)	是	外部请求批次号
dealMode	处理模式	varchar(1)	是	1.审批处理
2.直接出账
3.经办处理
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
list（校验不通过明细）
row
externalNum	明细流水号	varchar(50)	是	最大长度为50
rowStat
	校验状态	varchar(7)	是	校验状态返回码
rowStatMsg
	校验状态信息	varchar（50）		详见附录4.2制单状态所示
row
list

3.2.11.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKDLPYCD</action>
    <userName>SK-001-XX</userName>
    <externalBatNum>PYCD001</externalBatNum>
    <linkPayFlag>00</linkPayFlag>
    <pypartyAccnum>6228209638527410</pypartyAccnum>
    <currencyID>CNY</currencyID>
    <totNbr>2</totNbr>
    <amt>2</amt>
    <urgntAprvFlag>00</urgntAprvFlag>
    <rsrvtnFlag>00</rsrvtnFlag>
    <rsrvtnTms></rsrvtnTms>
    <payrollDt>202403</payrollDt>
<batPscpt>批次附言</batPscpt>
<payrollUse>代发用途</payrollUse>
<extField1>扩展字段1</extField1>
    <list name="debitList">
        <row>
            <externalNum>PL-1</externalNum>
            <rcvpyAccnum>6228381234567890</rcvpyAccnum>
            <rcvpyAccnm>某某人1</rcvpyAccnm>
            <rcvpartyDepbnkId></rcvpartyDepbnkId>
            <rcvpartyBnkgId></rcvpartyBnkgId>
            <debitAmt>1</debitAmt>
            <pscpt>明细附言1</pscpt>
            <rmrk>备注1</rmrk>
        </row>
        <row>
            <externalNum>PL-2</externalNum>
            <rcvpyAccnum>6251709638527410</rcvpyAccnum>
            <rcvpyAccnm>某某人2</rcvpyAccnm>
            <rcvpartyDepbnkId></rcvpartyDepbnkId>
            <rcvpartyBnkgId></rcvpartyBnkgId>
            <debitAmt>1</debitAmt>
            <pscpt>明细附言2</pscpt>
            <rmrk>备注2</rmrk>
        </row>
    </list>
</stream>

3.2.11.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <dealMode>3</dealMode>
    <externalBatNum>PYCD001</externalBatNum>
    <failReason></failReason>
    <status>AAAAAAA</status>
    <statusText>交易成功</statusText>
    <list name="debitInfoList">
        <row>
            <externalNum>PL-1</externalNum>
            <rowStat>AAAAAAA</rowStat>
            <rowStatMsg></rowStatMsg>
        </row>
        <row>
            <externalNum>PL-2</externalNum>
            <rowStat>AAAAAAA</rowStat>
            <rowStatMsg></rowStatMsg>
        </row>
    </list>
</stream>

3.2.12 收款明细标签查询（暂未上线，上线时间待定）
请求代码：SKDLRPQR
接口说明：
该接口用于ERP系统查询银行账号明细中认领标签信息以及认领时被拆分的认领明细数据,每一个sumTranNo认领明细列表rldetail上限为50条记录。
接口使用须须知：
1.请求使用的银企直联用户需有相关账号的所属机构、实际使用机构查询权限	；
2.该交易使用分页查询，起始记录号从1开始，每页最多显示20条记录。报文中的交易流水号sumTranNo由司库系统产生，用于标识客户交易明细数据唯一性；
3.接口调用后未查询到数据返回空列表，输入账号不在授权范围内不返回对应账号的认领明细数据。
4.ERP系统优先查询银行账号明细并根据银行账号明细sumTranNo与本接口sumTranNo进行匹配并关联认领明细及认领标签信息。



3.2.12.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
dbcrDrcId	明细类型	varchar(2)	否	需要查询交易的类型，02：账户支出；03：账户收入，空值默认全部类型
startTxnDt	明细日期起始日	char(8)	是	查询明细范围的开始日期，使用yyyyMMdd格式
明细日期开始日期-截止日期最大不能超过30天
endTxnDt	明细日期截止日	char(8)	是	查询明细范围的截止日期，使用yyyyMMdd格式
明细日期截止日期最大为t-1日
minAmt	最小金额	decimal(15,2)	否	查询明细范围的最小金额
MaxAmt	最大金额	decimal(15,2)	否	查询明细范围的最大金额
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持20条记录
list
row
accountNo	账号	varchar(40)	是	待查询的账号，允许输入0-9a-zA-Z空格-?:().,'+/ 字符，空格不能为首尾字符，不能全为特殊字符，至少一个数字，最大长度为40
列表上限50条记录
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户明细数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户明细数量
list
row
sumTranNo	系统交易流水号	varchar(20)	否	交易成功且查询到交易明细时返回，该流水号是司库系统内该笔明细的唯一性标识
label	明细标签		否	Json格式	 [{
			"pcode": "LB001",
			"pname": "款项性质",
			"code": "KX001",
			"name ": "公共款项"
		},
		{
			"pcode": "LB002",
			"pname": "收付款类型",
			"code": "SF001",
			"name": "现汇付款"
		}
	]

clmStat	认领状态	char(1)	否	1：未认领；
2：已认领；
3：部分认领
4：不用认领
cliamAmt	已认领金额	decimal(15,2)	否	
clmMode	认领方式	char(1)	否	1:手工单笔认领；
2:手工批量认领；
3:系统规则认领；
rldetail	认领列表			[{
	"rlcode": "认领对象编码",
	"pname": "认领对象名称",
	"gscode": "归属关系编码",
	"gsname": "归属关系名称",
	"rltime": "认领时间",
格式：yyyy-MM-dd HH:mm:ss
示例：2023-08-25 14:42:00

	"rlamt": "认领金额",
	"rluser": "认领操作人名称"
	},
	{
	"rlcode": "认领对象编码",
	"pname": "认领对象名称",
	"gscode": "归属关系编码",
	"gsname": "归属关系名称",
	"rltime": "认领时间",
	"rlamt": "认领金额",
	"rluser": "认领操作人名称"
}]
crtUsr	经办人		否	
row
list

3.2.12.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKDLRPQR</action>
	<userName>11100112097329261435</userName>
<startRecord>1</startRecord>
<pageNumber>10</pageNumber>
<startTxnDt>********</startTxnDt>
<endTxnDt>********</endTxnDt>
<minAmt>1.00</minAmt>
<dbcrDrcId>02</dbcrDrcId>
<MaxAmt>*********.00</MaxAmt>
<list name="List"> 
<row>
<accountNo>8110701013301511765</accountNo>
</row>
<row>
<accountNo>8110701013701511777</accountNo>
</row>
</list>
</stream>
3.2.12.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <failReason></failReason>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
 <totalRecords></totalRecords>
   <returnRecords></returnRecords>
   <list name="List">
      <row>
         <accDtlId>90210222162746023371</accDtlId>
         <cliamAmt>200.00</cliamAmt>
         <clmMode>2</clmMode>
         <clmStat>2</clmStat>
         <crtUsr>TRA9523-04</crtUsr>
         <label>
[
{"code":"LB001","name":"款项性质","pcode":"01","pname":"标签1"},
{"code":"LB002","name":"收付款类型","pcode":"1","pname":"测试默认数据"},
{"code":"LB003","name":"认领对象","pcode":"00002","pname":"对象1"},
{"code":"LB004","name":"归属关系","pcode":"11","pname":"测试默认1数据"},
{"code":"LB005","name":"收付款科目","pcode":"11","pname":"标签A2"}
]
</label>
         <rldetail>
[
{"gscode":"11","gsname":"测试默认1数据","pname":"对象1",
"rlamt":"200.00","rlcode":"00002","rltime":"2024-05-20 11:17:26","rluser":"TRA9523-04"}]		
</rldetail>
      </row>
     </list>
</stream>

3.3 公共中心
3.3.1 境内银行网点信息查询
请求代码：SKPUBQLH
接口说明：
用于查询境内银行网点信息查询，企业调用该接口查询网点请求，司库系统接收该请求后返回境内银行网点信息，支持分页查询，参数为银行编号和区域编码ID（详见附件），银行编码和区域编码不能同时都为空。
接口使用须须知：
1.当前支持境内银行网点信息查询，含外资行境内网点；
2.银行编码和区域码详见【附录5.9-银行编码信息和区域信息V1.0】，查询时这两个参数不能同时为空；
3.支持分页查询；
3.3.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	是	银企直联用户名
bankCode	银行类别编码	varchar(10)	是	银行类别编码，最大长度为10，有标准数据见附件1信息（本字段与区域编码字段至少必输其一）
areaId	区域编码	varchar(4)	是	有标准数据见附件1信息（本字段与银行类别编码字段至少必输其一）
sk_recordNum	分页条数	varchar(4)	是	分页条数，最大长度为4
sk_startNo	分页起始序号	varchar(4)	是	分页起始序号，最大长度为4
Response
sk_recordNum	分页条数	varchar(4)	是	标识要请求的接口，交易代码
sk_startNo	分页起始序号	varchar(4)	是	最大长度4
sk_totalNum	总条数	varchar(4)	是	最大长度4
status	交易状态	varchar(7)	是	交易状态7个A是成功
statusText	交易状态说明	varchar(254)	是	交易状态说明
List
Row
bankCode	银行类别编码	varchar(10)	是	银行类别编码，最大长度为10，有标准数据见附件1信息
BankName	银行名称	varchar(50)	是	银行名称
areaId	区域编码	varchar(4)	是	有标准数据见附件1信息
areaName	区域名称	Varchar(200)	是	省+市信息
OpenBankName	开户行行名	Varchar(200)	是	开户行名称信息
bankFirmCode	开户行名联行号	varchar(12)	是	长度固定为12
Row
List
3.3.1.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKPUBQLH</action>
<bankCode>BCM</bankCode>
<areaId>10056</areaId>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
</stream>
3.3.1.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<sk_recordNum>5</sk_recordNum>
<sk_startNo>1</sk_startNo>
<sk_totalNum>20</sk_totalNum>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<list name="bnkFirmModelList">
<row>
<areaId>10056</areaId>
<areaName>黑龙江省大庆市</areaName>
<bankCode>BCM</bankCode>
<bankFirmCode>************</bankFirmCode>
<bankName>交通银行</bankName>
<openBankName>交通银行股份有限公司大庆龙岗支行</openBankName>
</row>
<row>
<areaId>10056</areaId>
<areaName>黑龙江省大庆市</areaName>
<bankCode>BCM</bankCode>
<bankFirmCode>************</bankFirmCode>
<bankName>交通银行</bankName>
<openBankName>交通银行股份有限公司大庆龙凤支行</openBankName>
</row>
<row>
<areaId>10056</areaId>
<areaName>黑龙江省大庆市</areaName>
<bankCode>BCM</bankCode>
<bankFirmCode>************</bankFirmCode>
<bankName>交通银行</bankName>
<openBankName>交通银行股份有限公司大庆东风支行</openBankName>
</row>
<row>
<areaId>10056</areaId>
<areaName>黑龙江省大庆市</areaName>
<bankCode>BCM</bankCode>
<bankFirmCode>************</bankFirmCode>
<bankName>交通银行</bankName>
<openBankName>交通银行股份有限公司大庆大同支行</openBankName>
</row>
<row>
<areaId>10056</areaId>
<areaName>黑龙江省大庆市</areaName>
<bankCode>BCM</bankCode>
<bankFirmCode>************</bankFirmCode>
<bankName>交通银行</bankName>
<openBankName>交通银行股份有限公司大庆开发区支行</openBankName>
</row>
</list>
</stream>
3.4 票证中心
3.4.1 票据列表查询
请求代码：SKBILINF
接口说明：
企业ERP等系统调用该接口发起票据信息查询，支持持有票据、票据行为申请、票据行为签收三类场景，司库系统接收该请求后返回对应的票据信息，查询结果按照【账号】+【票据包号】+【子票区间】进行去重。
持有票据：选择该条件进行查询可以列表展示指客户当前持有的票据
票据行为申请：选择该条件（支持全选或复选）可以查询客户发起过对应申请行为的票据
票据行为签收：选择该条件（支持全选或复选）可以查询客户发起过对应签收行为的票据
接口使用须须知：
1.请求使用的银企直联用户需有相关账号的查询权限	；
2.该交易使用分页查询，起始记录号从1开始，每页最多显示200条记录。
3.4.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILINF
userName	登录用户名	varchar(30)	是	银企直联用户名
queryType	查询类型	varchar(2)	是	00持有01申请02签收
bsnTp	业务种类	varchar(100)	否	17-提示付款,18-供应链票据付款,51-出票登记,52-提示承兑,53-提示收票,54-未用退回,55-保证申请,56-背书转让,57-贴现申请,58-回购式贴现赎回,59-质押,60-质押解除,61-追索通知,62-同意清偿,63-线下追偿登记,64-不得转让标记撤销,65-票据查验,24-到期前提示付款,72-到期后质押解除，支持多选用“，”隔开
List
Row 
signAcc	签约账号	varchar(40)	否	最大支持200个
billPkgId	票据包号	varchar(30)	否	最大支持200个
Row 
List
minAmt	最小金额	decimal(15,2)	否	金额数据项格式为：15，2，即：小数部分为2位，整数部分为13位，最小值为0.00，最大值为*************.99
maxAmt	最大金额	decimal(15,2)	否	
expDtStart	到期日起始日期	char(10	否	格式yyyy-MM-dd 非必输  queryType 为01、02时候必输
expDtEnd	到期日截止日期	char(10)	否	格式yyyy-MM-dd 非必输  queryType 为01、02时候必输
startRecord	起始记录号	char(10)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持200条记录
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有票据数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的票据数量
List
Row
signAcc	签约账号	varchar(40)	否	
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
isSupprDt	出票日期	char(10)	否	格式yyyy-MM-dd
billRcvDt	票据到期日期	char(10)	否	格式yyyy-MM-dd
isPrmtSubpge	是否允许分包	char(1)	否	Y:可分包；N：不可分包
billStat	票据状态	char(6)	否	CS01	已出票
CS02	已承兑
CS03	已收票
CS04	已到期
CS05	已终止
CS06	已结清
crclFlag	流通标志	char(6)	否	TF0101	待收票
TF0301	可流通
TF0302	已锁定
TF0303	不可转让
TF0304	已质押
TF0305	待赎回
TF0401	托收在途
TF0402	追索中
TF0501	已结束
billTp	票据类型	cahr(4)	否	AC01：银承； AC02：商承
billFaceAmt	票面金额	decimal(15,2)	否	
tfrMark	转让标记	char(4)	否	EM00：可再转让 ；EM01不得转让
bankDockingMode	银行对接模式	char(1)	否	Y:直联；N：非直联
billFaceMemo	票面备注	varchar(180)	否	
rmtrNm	出票人名称	varchar(300)	否	
rmtrIsSupprMemo	出票人备注	varchar(384)	否	
rmtrDepBnkBrCode	出票人开户行行号	char(20)	否	
rmtrDepBnkNm	出票人开户行行名	varchar(300)	否	
rmtrAccNum	出票人账户	varchar(40)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrDepBnkNm	承兑人开户行名称	varchar(300)	否	
acptrDepBnkBrCode	承兑人开户行行号	char(20)	否	
acptrAccNum	承兑人账户	varchar(40)	否	
acptrDt	承兑日期	char(10)	否	格式yyyy-MM-dd
payeeDepBnkNm	收款人开户行名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeDepBnkBrCpde	收款人开户行行号	char(20)	否	
payeeAccNum	收款人账号	varchar(40)	否	
Row
List
3.4.1.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBILINF</action>
    <userName>11100779717942176794</userName>    <!--登录名 varchar(30) 必输-->
    <queryType>00</queryType>    <!--查询类型 00持有 01申请 02签收 必输-->
    <bsnTp></bsnTp>    <!--业务种类 非必输
    17-提示付款,18-供应链票据付款,51-出票登记,52-提示承兑,53-提示收票,54-未用退回,55-保证申请,56-背书转让,57-贴现申请,58-回购式贴现赎回,59-质押,60-质押解除,61-追索通知,62-同意清偿,63-线下追偿登记,64-不得转让标记撤销,
    65-票据查验,24-到期前提示付款,72-到期后质押解除
    支持多选用“，”隔开  -->
<list name="queryBillParam">
<row>
       <signAcc>8110777013901818820</signAcc>    <!--签约账号 char(40) 必输-->
       <billPkgId>6302107777199520230829000059149</billPkgId>    <!--票据包号 varchar(30) 必输-->
</row>
    </list>
    <minAmt>0.00</minAmt>
    <maxAmt>99999999999.99</maxAmt>
    <expDtStart>2022-12-01</expDtStart>    <!--到期日起始日期 char(10) 格式yyyy-MM-dd 非必输  queryType 为01、02时候必输 -->
    <expDtEnd>2024-12-01</expDtEnd>    <!--到期日截止日期 char(10) 格式yyyy-MM-dd 非必输   queryType 为01、02时候必输-->
    <startRecord>1</startRecord>    <!--起始记录数 char（4）必输-->
    <pageNumber>10</pageNumber>    <!--每页条数 char（4）必输-->
3.4.1.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
 <status></status>    <!--交易状态 char(7)-->
    <statusText></statusText>    <!--交易状态信息 varchar(254)-->
    <sk_totalNum></sk_totalNum>    <!--总记录数-->
    <sk_recordNum></sk_recordNum>    <!--每页条数-->
    <sk_startNo></sk_startNo>    <!--起始记录数-->
    <list name="dataList">
        <row>
            <billPkgId></billPkgId>            <!--票据包号 varchar (30)-->
            <subBillRng></subBillRng>            <!--子票区间 varchar (25)-->
            <isSupprDt></isSupprDt>            <!--出票日期 varchar (10) yyyy-MM-dd-->
            <billRcvDt></billRcvDt>            <!--票据到期日期 varchar (10) yyyy-MM-dd-->
            <isPrmtSubpge></isPrmtSubpge>            <!--是否允许分包 char (1)-->
            <billStat></billStat>            <!--票据状态 char (6)-->
            <crclFlag></crclFlag>            <!--流通标志 char (6)-->
            <billTp></billTp>            <!--票据类型 cahr (4)-->
            <billFaceAmt></billFaceAmt>            <!--票面金额 decimal (17,2)-->
            <tfrMark></tfrMark>            <!--转让标记 char (4)-->
            <bankDockingMode></bankDockingMode>            <!--银行对接模式 char (1)-->
            <billFaceMemo></billFaceMemo>            <!--票面备注 varchar (180)-->
            <rmtrNm></rmtrNm>            <!--出票人名称 varchar (300)-->
            <rmtrIsSupprMemo></rmtrIsSupprMemo>            <!--出票人备注 varchar (384)-->
            <rmtrDepBnkBrCode></rmtrDepBnkBrCode>            <!--出票人开户行行号 char (20)-->
            <rmtrDepBnkNm></rmtrDepBnkNm>            <!--出票人开户行行名 varchar (300)-->
            <rmtrAccNum></rmtrAccNum>            <!--出票人账户 varchar (40)-->
            <acptrNm></acptrNm>            <!--承兑人名称 varchar (300)-->
            <acptrDepBnkNm></acptrDepBnkNm>            <!--承兑人开户行名称 varchar (300)-->
            <acptrDepBnkBrCode></acptrDepBnkBrCode>            <!--承兑人开户行行号 char (20)-->
            <acptrAccNum></acptrAccNum>            <!--承兑人账户 varchar (40)-->
            <acptrDt></acptrDt>            <!--承兑日期 varchar (10) yyyy-MM-dd-->
            <payeeDepBnkNm></payeeDepBnkNm>            <!--收款人开户行名称 varchar (300)-->
            <payeeNm></payeeNm>            <!--收款人名称 varchar (300)-->
            <payeeDepBnkBrCpde></payeeDepBnkBrCpde>            <!--收款人开户行行号 char (20)-->
            <payeeAccNum></payeeAccNum>            <!--收款人账号 varchar (40)-->
            <appActNm></appActNm>            <!--请求方账户名称 varchar (300)-->
<appAct></appAct>            <!--请求方账号 varchar (40)-->
            <appCode></appCode>            <!--请求方信用代码 varchar (64)-->
            <appBankNo></appBankNo>            <!--请求方开户行行号 varchar (64)-->
            <appBankNm></appBankNm>            <!--请求方开户行行名 varchar (300)-->            
<rcvActNm></rcvActNm>            <!--接收方账户名称 varchar (300)-->
<rcvAct></rcvAct>            <!--接收方账号 varchar (40)-->
<rcvCode></rcvCode>            <!--接收方信用代码 varchar (64)-->
            <rcBankNo></rcBankNo>            <!--接收方开户行行号 varchar (64)-->
            <rcBankNm></rcBankNm>            <!--接收方开户行行名 varchar (300)-->            
<signDate></signDate>            <!--签收日期 varchar (10) yyyy-MM-dd-->
            <fixSignFlag></fixSignFlag>            <!--签收标识 varchar (50)-->
            <stlmthd></stlmthd>            <!--清算标志 varchar (50)-->
            <applyDate></applyDate>            <!--申请日期 varchar (10) yyyy-MM-dd-->
            <tfrFlagNm></tfrFlagNm>            <!--转让标记描述 varchar (30)-->
        </row>
    </list>
</stream>

3.4.2 票据详情查询
请求代码：SKBILBAK
接口说明：
企业ERP等系统调用该接口发起票面信息查询，查询票据完整票面信息及流转记录。
接口使用须须知：
1.当输入账号进行查询时，系统需校验用户对票据所属的账号具有查询权限；
3.4.2.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILBAK
userName	登录用户名	varchar(30)	是	银企直联用户名
signAcc	签约账号	varchar(40)	是	
billPkgId	票据包号	varchar(30)	是	
subBillRng	子票区间	varchar(25)	否	新一代电票必输，格式为************,000001010110
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
isSupprDt	出票日期	char(10)	否	格式yyyy-MM-dd
billRcvDt	票据到期日期	char(10)	否	格式yyyy-MM-dd
isPrmtSubpge	是否允许分包	char(1)	否	Y:可分包；N：不可分包
billStat	票据状态	char(6)	否	CS01	已出票
CS02	已承兑
CS03	已收票
CS04	已到期
CS05	已终止
CS06	已结清
crclFlag	流通标志	char(6)	否	TF0101	待收票
TF0301	可流通
TF0302	已锁定
TF0303	不可转让
TF0304	已质押
TF0305	待赎回
TF0401	托收在途
TF0402	追索中
TF0501	已结束
billTp	票据类型	cahr(4)	否	AC01：银承； AC02：商承
billFaceAmt	票面金额	decimal(15,2)	否	
tfrMark	转让标记	char(4)	否	EM00：可再转让 ；EM01不得转让
bankDockingMode	银行对接模式	char(1)	否	Y:直联；N：非直联
billFaceMemo	票面备注	varchar(180)	否	
rmtrNm	出票人名称	varchar(300)	否	
rmtrIsSupprMemo	出票人备注	varchar(384)	否	
rmtrDepBnkBrCode	出票人开户行行号	char(20)	否	
rmtrDepBnkNm	出票人开户行行名	varchar(300)	否	
rmtrAccNum	出票人账户	varchar(40)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrDepBnkNm	承兑人开户行名称	varchar(300)	否	
acptrDepBnkBrCode	承兑人开户行行号	char(20)	否	
acptrAccNum	承兑人账户	varchar(40)	否	
acptrDt	承兑日期	char(10)	否	格式yyyy-MM-dd
payeeDepBnkNm	收款人开户行名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeDepBnkBrCpde	收款人开户行行号	char(20)	否	
payeeAccNum	收款人账号	varchar(40)	否	
List name="fixEbillBackInfoTransportList"
Row
backId	背面信息交易流水号	varchar(20)	否	数据会刷新，请勿做唯一性校验
tfrFlagNm	转让标记	varchar(30)	否	
appActNm	请求方账号名称	varchar(300)	否	
appAct	请求方账号	varchar(40)	否	
appCode	请求方信用代码	varchar(64)	否	
appBankNo	请求方开户行行号	varchar(64)	否	
appBankNm	请求方开户行行名	varchar(300)	否	
rcvActNm	接收方账户名称	varchar(300)	否	
rcvAct	接收方账号	varchar(40)	否	
rcvCode	接收方信用代码	varchar(64)	否	
rcBankNo	接收方开户行行号	varchar(64)	否	
rcBankNm	接收方开户行行名	varchar(300)	否	
applyDate	申请日期	char(10)	否	
fixSignFlag	签收标识	varchar(50)	否	
signDate	签收日期	char(10)	否	格式yyyy-MM-dd
rate	利率	decimal(15,2)	否	
rpdOpdt	赎回截止日	char(10)	否	格式yyyy-MM-dd
rpdDudt	赎回开放日	char(10)	否	
prsnTpyAmt	付款金额	decimal(17,2)	否	格式yyyy-MM-dd
dshnRcd	拒付代码	varchar(50)	否	格式yyyy-MM-dd
vstddShnrcdl	拒付理由代码	varchar(300)	否	格式yyyy-MM-dd
dshnRsn	拒付备注信息	varchar(900)	否	格式yyyy-MM-dd
rcrsTyp	追索类型	varchar(50)	否	
agrrDat	清偿日期	char(10)	否	
backMem	背面备注	varchar(900)	否	
wareAdr	保证人地址	varchar(900)	否	
pleDgeDt	解质押日期	char(10)	否	
stlmthd	清算标志	varchar(50)	否	
Row
List
List name="fixEbillAddInfoList"
Row
riskBillStatus	风险票据状态描述	varchar(64)	否	
rmtrNm	出票人名称	varchar(300)	否	
rmtrSoccrcode	出票人社会信用代码	varchar(100)	否	
rmtrActNm	出票人账户名称	varchar(300)	否	
rmtrMemno	出票人办理渠道	varchar(300)	否	
rmtrBillact	出票人票据账号	varchar(40)	否	
rmtrDepBnkBrCode	出票人开户行行号	varchar(50)	否	
rmtrDepBnkNm	出票人开户行名	varchar(300)	否	
rmtrAccNum	出票人账号	varchar(50)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrSoccrcode	承兑人社会信用代码	varchar(64)	否	
acptrActNm	承兑人账户名称	varchar(300)	否	
acptrMemno	承兑人办理渠道	varchar(300)	否	
acptrBillact	承兑人票据账号	varchar(40)	否	
acptrDepBnkBrCode	承兑人开户行行号	varchar(30)	否	
acptrDepBnkNm	承兑人开户行名	varchar(300)	否	
acptrAccNum	承兑人账号	varchar(40)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeSoccrcode	收款人社会信用代码	varchar(64)	否	
pyeeActNm	收款人账户名称	varchar(300)	否	
pyeeMemno	收款人办理渠道	varchar(300)	否	
pyeeBillact	收款人票据账号	varchar(40)	否	
payeeDepBnkBrCpde	收款人开户行行号	varchar(30)	否	
payeeDepBnkNm	收款人开户行名	varchar(300)	否	
payeeAccNum	收款人账号	varchar(40)	否	
Row
List
List name="fixEbillAddInfoDeposList"
Row
ebillInfoType	历史行为种类
character(4)	否	
appNm	请求方名称	varchar(128)	否	
appCode	请求方信用代码	varchar(64)	否	
appActNm	请求方账户名称	varchar(300)	否	
appMemno	请求方办理渠道	varchar(300)	否	
appBillact	请求方票据账号	varchar(50)	否	
appBankNo	请求方开户行行号	varchar(50)	否	
appBankNm	请求方开户行行名	varchar(300)	否	
appAct	请求方账号	varchar(50)	否	
rcvNm	接收名称	varchar(300)	否	
rcvCode	接收信用代码	varchar(64)	否	
rcvActNm	接收账户名称	varchar(300)	否	
rcvMemno	接收方办理渠道	varchar(300)	否	
rcvBillact	接收方票据账号	varchar(50)	否	
rcBankNo	接收方开户行行号	varchar(64)	否	
rcBankNm	接收方开户行行名	varchar(300)	否	
rcvAct	接收方账号	varchar(50)	否	
wareAdr	保证人地址	varchar(300)	否	
Row
List

3.4.2.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBILBAK</action>
    <userName>11100179717942176777</userName>    <!--登录名 varchar(30) 必输-->
    <signAcc>8110701013901818877</signAcc>    <!--签约账号 char(40) 必输-->
    <billPkgId>6************20230829007759149</billPkgId>    <!--票据包号 varchar(30) 必输-->
    <subBillRng>************,************</subBillRng>    <!--子票区间 varchar(25) 可空,新一代票据必输-->
</stream>
3.4.2.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <statusText></statusText>
    <billPkgId></billPkgId>
    <subBillRng></subBillRng>
    <isSupprDt></isSupprDt>
    <billRcvDt></billRcvDt>
    <isPrmtSubpge></isPrmtSubpge>
    <billStat></billStat>
    <crclFlag></crclFlag>
    <billTp></billTp>
    <billFaceAmt></billFaceAmt>
    <tfrMark></tfrMark>
    <bankDockingMode></bankDockingMode>
    <billFaceMemo></billFaceMemo>
    <rmtrNm></rmtrNm>
    <rmtrDepBnkBrCode></rmtrDepBnkBrCode>
    <rmtrDepBnkNm></rmtrDepBnkNm>
    <rmtrAccNum></rmtrAccNum>
    <acptrNm></acptrNm>
    <acptrDepBnkNm></acptrDepBnkNm>
    <acptrDepBnkBrCode></acptrDepBnkBrCode>
    <acptrAccNum></acptrAccNum>
    <acptrDt></acptrDt>
    <payeeDepBnkNm></payeeDepBnkNm>
    <payeeNm></payeeNm>
    <payeeDepBnkBrCpde></payeeDepBnkBrCpde>
    <payeeAccNum></payeeAccNum>
    <List name="fixEbillBackInfoTransportList">
        <Row>
<backId ></backId >
            <ebillInfoType></ebillInfoType>
            <tfrFlagNm></tfrFlagNm>
            <appActNm></appActNm>
            <appAct></appAct>
            <appCode></appCode>
            <appBankNo></appBankNo>
            <appBankNm></appBankNm>
            <rcvActNm></rcvActNm>
            <rcvAct></rcvAct>
            <rcvCode></rcvCode>
            <rcvBankNo></rcvBankNo>
            <rcvBankNm></rcvBankNm>
            <applyDate></applyDate>
            <fixSignFlag></fixSignFlag>
            <signDate></signDate>
            <rate></rate>
            <rpdOpdt></rpdOpdt>
            <rpdDudt></rpdDudt>
            <prsnTpyAmt></prsnTpyAmt>
            <dshnRcd></dshnRcd>
            <vstddShnrcdl></vstddShnrcdl>
            <dshnRsn></dshnRsn>
            <rcrsTyp></rcrsTyp>
            <agrrDat></agrrDat>
            <backMem></backMem>
            <wareAdr></wareAdr>
            <pleDgeDt></pleDgeDt>
            <stlmthd></stlmthd>
        </Row>
    </List>
    <List name="fixEbillAddInfoList">
        <Row>
            <riskBillStatus></riskBillStatus>
            <rmtrNm></rmtrNm>
            <rmtrSoccrcode></rmtrSoccrcode>
            <rmtrActNm></rmtrActNm>
            <rmtrMemno></rmtrMemno>
            <rmtrBillact></rmtrBillact>
            <rmtrDepBnkBrCode></rmtrDepBnkBrCode>
            <rmtrDepBnkNm></rmtrDepBnkNm>
            <rmtrAccNum></rmtrAccNum>
            <acptrNm></acptrNm>
            <acptrSoccrcode></acptrSoccrcode>
            <acptrActNm></acptrActNm>
            <acptrMemno></acptrMemno>
            <acptrBillact></acptrBillact>
            <acptrDepBnkBrCode></acptrDepBnkBrCode>
            <acptrDepBnkNm></acptrDepBnkNm>
            <acptrAccNum></acptrAccNum>
            <payeeNm></payeeNm>
            <payeeSoccrcode></payeeSoccrcode>
            <pyeeActNm></pyeeActNm>
            <pyeeMemno></pyeeMemno>
            <pyeeBillact></pyeeBillact>
            <payeeDepBnkBrCpde></payeeDepBnkBrCpde>
            <payeeDepBnkNm></payeeDepBnkNm>
            <payeeAccNum></payeeAccNum>
        </Row>
    </List>
    <List name="fixEbillAddInfoDeposList">
        <Row>
            <ebillInfoType></ebillInfoType>
            <appNm></appNm>
            <appCode></appCode>
            <appActNm></appActNm>
            <appMemno></appMemno>
            <appBillact></appBillact>
            <appBankNo></appBankNo>
            <appBankNm></appBankNm>
            <appAct></appAct>
            <rcvNm></rcvNm>
            <rcvCode></rcvCode>
            <rcvActNm></rcvActNm>
            <rcvMemno></rcvMemno>
            <rcvBillact></rcvBillact>
            <rcBankNo></rcBankNo>
            <rcBankNm></rcBankNm>
            <rcvAct></rcvAct>
            <wareAdr></wareAdr>
        </Row>
    </List>
</stream>
3.4.3 票据交易查询
请求代码：SKBILTRD
接口说明：
支持按照交易类型（票据行为申请、票据行为签收）查询相应票据记录（含新老电票、直联与非直联票据），并展示申请行为完成的后一手的最新签收状态或签收行为的状态，并展示与状态相匹配的流转记录。
接口使用须须知：
1.当输入账号进行查询时，系统需校验用户对票据所属的账号具有查询权限；
3.4.3.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILTRD
userName	登录用户名	varchar(30)	是	银企直联用户名
Row 
signAcc	签约账号	varchar(40)	否	最大支持200个
billPkgId	票据包号	varchar(30)	否	最大支持200个
Row
List
queryType	查询类型	varchar(2)	是	01申请02签收
bsnTp	业务种类	varchar(100)	否	17-提示付款,18-供应链票据付款,51-出票登记,52-提示承兑,53-提示收票,54-未用退回,55-保证申请,56-背书转让,57-贴现申请,58-回购式贴现赎回,59-质押,60-质押解除,61-追索通知,62-同意清偿,63-线下追偿登记,64-不得转让标记撤销,65-票据查验,24-到期前提示付款,72-到期后质押解除，支持多选用“，”隔开
transStatus	交易状态/处理结果	varchar(100)	否	支持多选，枚举值逗号分隔
当queryType=01,处理结果展示
01 已录入02 已发送03 已确认成功04 已确认失败05 已签收
06 已拒绝07 已清算08 清算失败09 清分失败10 对方未签收,日终退回11 票交所申请清退,日终退回12 已作废
当queryType=02,处理结果展示01 未签收02 已签收03 已拒绝
04 签收已确认05 驳回已确认06 已清算07 清算失败08 清分失败09 未签收,日终退回10 票交所申请清退,日终退回。
fixBsnLaunchType	业务发起方式	varchar(2)		业务发起方式，01-客户发起，02-中信银行代客发起，03-票交所自动发起 可空，为空时默认查询全部
minAmt	最小金额	decimal(15,2)	否	金额数据项格式为：15，2，即：小数部分为2位，整数部分为13位，最小值为0.00，最大值为*************.99
maxAmt	最大金额	decimal(15,2)	否	
createDtStart	交易日起始日期	char(10）	是	格式yyyy-MM-dd 
createDtEnd	交易日截止日期	char(10)	是	格式yyyy-MM-dd 
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持200条记录
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
failReason	错误信息展示	varchar(254)	否	
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有票据数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的票据数量
List
Row
signAcc	签约账号	varchar(40)	是	
billPkgId	票据包号	varchar(30)	是	
subBillRng	子票区间	varchar(25)	否	
billTp	票据类型	cahr(4)	是	AC01：银承； AC02：商承
billFaceAmt	票面金额	decimal(15,2)	是	
bsnTp	业务种类	char(2)	是	
billModality	票据形态	char（1）	是	1:传统电票；2：新一代电票
bankDockingMode	银行对接模式	char(1)	否	Y:直联；N：非直联
fixBsnLaunchType	业务发起方式	char(2)	否	01-客户发起；02-司库系统外发起；03-票交所自动发起；SK-司库代客发起
fixBsnLaunchTypeDesc	业务发起方式描述	varchar(30)	否	
isCnclFlag	是否已撤销	char(2)	否	00-否；01-是
transDt	交易日期	char(10）	是	格式yyyy-MM-dd
aplNm	发起人名称	varchar(300)	否	
aplDepbnkBrcode	发起人开户行行号	char(20）	否	
aplDepbnkNm	发起人开户行行名	varchar(300)	否	
rcverNm	接收人名称	varchar(300)	否	
rcverDepbnkBrcode	接收人开户行行号	char(20）	否	
rcverDepbnkNm	接收人开户行行名	varchar(300)	否	
billStat	票据状态	char(6)	是	CS01	已出票
CS02	已承兑
CS03	已收票
CS04	已到期
CS05	已终止
CS06	已结清
crclFlag	票据流通状态	char(6)	是	TF0101	待收票
TF0301	可流通
TF0302	已锁定
TF0303	不可转让
TF0304	已质押
TF0305	待赎回
TF0401	托收在途
TF0402	追索中
TF0501	已结束
transStatus	交易状态／处理结果	char(2)	是	当queryType=01,处理结果展示
01 已录入02 已发送03 已确认成功04 已确认失败05 已签收
06 已拒绝07 已清算08 清算失败09 清分失败10 对方未签收,日终退回11 票交所申请清退,日终退回12 已作废
当queryType=02,处理结果展示01 未签收02 已签收03 已拒绝
04 签收已确认05 驳回已确认06 已清算07 清算失败08 清分失败09 未签收,日终退回10 票交所申请清退,日终退回
realPayAmt	实付金额	decimal(15,2)		贴现实付金额
businessSerialNo	交易流水号	varchar(64)	否	交易流水号
queryType	查询类型	varchar(2)	是	01申请 02签收
Row
List

3.4.3.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBILTRD</action>
    <userName></userName>
    <queryType></queryType>
    <bsnTp></bsnTp>
    <List name="queryBillParam">
        <Row>
            <signAcc></signAcc>
            <billPkgId></billPkgId>
        </Row>
    </List>
    <queryType></queryType>
<bsnTp></bsnTp>
< transStatus ></transStatus >
    <fixBsnLaunchType></fixBsnLaunchType>
    <minAmt></minAmt>
    <maxAmt></maxAmt>
    <createDtStart></createDtStart>
    <createDtEnd></createDtEnd>
    <startRecord></startRecord>
    <pageNumber></pageNumber>
</stream>
3.4.3.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status></status>
    <statusText></statusText>
    <failReason></failReason>
    <totalRecords></totalRecords>
    <returnRecords></returnRecords>
    <List name="dataList">
        <Row>
            <signAcc></signAcc>
            <billPkgId></billPkgId>
            <subBillRng></subBillRng>
            <billTp></billTp>
            <billFaceAmt></billFaceAmt>
            <bsnTp></bsnTp>
            <billModality></billModality>
            <bankDockingMode></bankDockingMode>
            <fixBsnLaunchType></fixBsnLaunchType>
            <fixBsnLaunchTypeDesc></fixBsnLaunchTypeDesc>
            <isCnclFlag></isCnclFlag>
            <transDt></transDt>
            <aplNm></aplNm>
            <aplDepbnkBrcode></aplDepbnkBrcode>
            <aplDepbnkNm></aplDepbnkNm>
            <rcverNm></rcverNm>
            <rcverDepbnkBrcode></rcverDepbnkBrcode>
            <rcverDepbnkNm></rcverDepbnkNm>
            <billStat></billStat>
            <crclFlag></crclFlag>
            <transStatus></transStatus>
            <realPayAmt></realPayAmts>
<businessSerialNo></businessSerialNo>
<queryType></queryType>
        </Row>
    </List>
</stream>
3.4.4 票据背书申请
请求代码：SKBILEDS
接口说明：
1.企业通过ERP、OA等第三方系统调用该接口后，发起背书转让申请，司库系统支持审批、不审批两种模式。
接口使用须须知：
1.当输入账号进行查询时，系统需校验用户对票据所属的账号具有经办权限；
3.4.4.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILEDS
userName	登录用户名	varchar(30)	是	银企直联用户名
list name="reqList"
row 最大支持200个
signAcc	签约账号	varchar(40)	是	
billPkgId	票据包号	varchar(30)	是	
subBillRng	子票区间	varchar(25)	是	新一代电票，可分包票据子票区间格式为************,000001010110，不可分包为0
aplyAmt	申请金额	decimal(15,2)	是	金额数据项格式为：15，2，即：小数部分为2位，整数部分为13位，最小值为0.00，最大值为*************.99
endrsAccNum	被背书人账户	varchar(40)	是	
endrsNm	被背书人名称	varchar(300)	是	
endrsDepBnkBrCode	被背书人开户行行号	char(20)	是	
endrsDepBnkNm	被背书人开户行行名	varchar(300)	是	
tfrMark	转让标记	char(4)	是	EM00：可转让 ；EM01不得转让
Row
List
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
batOrderId	批次交易流水号	char(20)	否	
nextApproverId	下一节点审批人id	char(20)	否	
nextApproverNm	下一节点审批人名称	varchar(300)	否	
list name="fixBillErpEndsRspList"
Row
signAcc	签约账号	varchar(40)	是	
billPkgId	票据包号	varchar(30)	是	
subBillRng	子票区间	varchar(25)	是	
orderId	制单流水号	char(20)	否	
checkCode	校验信息码	varchar(8)	否	
checkMsg	校验信息描述	varchar(254)	否	
Row
List

3.4.4.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBILEDS</action>
    <userName>11100102769067235087</userName>    <!--登录名 varchar(30) 必输-->
    <list name="reqList">
        <row>
            <billPkgId>610460204628620231219077002889</billPkgId>            <!--票据包号 varchar(30) 必输-->
            <subBillRng>************,000001219177</subBillRng>            <!--子票区间 varchar(25) 可空,新一代票据必输-->
            <signAcc>660057723377</signAcc>            <!--签约账号 char(40) 必输-->
            <aplyAmt>1.00</aplyAmt>            <!-- 申请金额 decimal(15,2)  必输-->
            <endrsAccNum>8110701013101487477</endrsAccNum>            <!--被背书人账户 varchar(40) 必输-->
            <endrsNm>天津小鸟车业有限公司</endrsNm>            <!--被背书人名称  varchar(300)    必输-->
            <endrsDepBnkBrCode>302100011775</endrsDepBnkBrCode>            <!--被背书人开户行行号 char(20)    必输-->
            <endrsDepBnkNm>中信银行北京分行账务中心</endrsDepBnkNm>            <!--被背书人开户行行名 varchar(300)    必输-->
            <tfrMark>EM00</tfrMark>            <!--转让标记 char(4) 必输  EM00：可转让 ；EM01不得转让-->
        </row>
    </list>
</stream>
3.4.4.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <batOrderId>20240419103645447977</batOrderId>
   <nextApproverId>20230306144134595377</nextApproverId>
   <nextApproverNm>名人食品经办</nextApproverNm>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <list name="fixBillErpEndsRspList">
      <row>
         <billPkgId>610460204628620231219000007789</billPkgId>
         <checkCode>SKCDC00</checkCode>
         <checkMsg>校验通过</checkMsg>
         <orderId>20240419103645447770</orderId>
         <signAcc>660057723377</signAcc>
         <subBillRng>************,000001219177</subBillRng>
      </row>
   </list>
</stream>
3.4.5 票据制单查询
请求代码：SKBILQOS
接口说明：
1.企业ERP系统、OA系统等第三方系统对接司库票证中心，通过该接口查询票据申请及签收操作的交易进度及结果。
接口使用须须知：
1.当输入账号进行查询时，系统需校验用户对票据所属的账号具有查看权限；
3.4.5.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILQOS
userName	登录用户名	varchar(30)	是	银企直联用户名
batOrderId	批次交易流水号	char(20)	是	
list name="reqList"
row 最大支持200个
orderId	制单流水号	char(20)	否	
billPkgId	票据包号	varchar(30)	否	
Row
List
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
batOrderId	批次交易流水号	char(20)	否	
list name="dataList"
Row
orderId	制单流水号	char(20)	否	
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
stt	制单状态	char(2)	否	A0-审核通过，已发送
A1-审核拒绝 
A2-等待审核
S1-发送失败
S2-发送成功
S3-交易成功
S4-交易失败
sttInf	制单信息	varchar(500)	否	
pbcPcsStat	票交所状态	char(3)	否	
pbcStatInf	票交所状态信息	varchar(500)	否	
Row
List

3.4.5.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBILQOS</action>
    <userName>11100102769067235087</userName>    <!--登录名 varchar(30) 必输-->
    <batOrderId>20240419103645447909</batOrderId>    <!--批次交易流水号	char(20)必输-->
    <list name="reqList">
        <row>
            <billPkgId>6************20240315000019235</billPkgId>            <!--票据包号 varchar(30) 非必输-->
            <orderId>20240419103645447909</orderId>            <!--制单流水号	char(20) 非必输-->
        </row>
</list>
</stream>
3.4.5.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status>AAAAAAA</status>
    <statusText>交易成功</statusText>
    <batOrderId>20240419103645447909</batOrderId>
    <list name="dataList">
        <row>
            <orderId>20240419103645447910</orderId>            <!--订单id -->
            <billPkgId>610460204628620231219000002889</billPkgId>            <!--票据包号  -->
            <subBillRng>************,000001219100</subBillRng>            <!--子票区间  -->
            <stt>S2</stt>            <!--制单状态 -->
            <pbcPcsStat>A3</pbcPcsStat>            <!--人行状态 -->
            <pbcStatInf>发送人行成功</pbcStatInf>            <!--人行状态描述 -->
        </row>
</list>
</stream>
3.4.6 票据详请批量查询
请求代码：SKBILBIF
接口说明：
企业ERP等系统调用该接口发起批量票面信息查询，查询票据完整票面信息及流转记录。
接口使用须须知：
1.	当输入账号进行查询时，系统需校验用户对票据所属的账号具有查询权限；
2.	查询笔数最大支持10笔
3.4.6.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILBIF
userName	登录用户名	varchar(30)	是	银企直联用户名
Row 
signAcc	签约账号	varchar(40)	是	
billPkgId	票据包号	varchar(30)	是	
subBillRng	子票区间	varchar(25)	是	格式为************,000001010110或0（不可分包）
Row 
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
failReason	错误信息展示	varchar(254)	否	
List
Row
				
				
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
isSupprDt	出票日期	char(10)	否	格式yyyy-MM-dd
billRcvDt	票据到期日期	char(10)	否	格式yyyy-MM-dd
isPrmtSubpge	是否允许分包	char(1)	否	Y:可分包；N：不可分包
billStat	票据状态	char(6)	否	CS01	已出票
CS02	已承兑
CS03	已收票
CS04	已到期
CS05	已终止
CS06	已结清
crclFlag	流通标志	char(6)	否	TF0101	待收票
TF0301	可流通
TF0302	已锁定
TF0303	不可转让
TF0304	已质押
TF0305	待赎回
TF0401	托收在途
TF0402	追索中
TF0501	已结束
billTp	票据类型	cahr(4)	否	AC01：银承； AC02：商承
billFaceAmt	票面金额	decimal(15,2)	否	
tfrMark	转让标记	char(4)	否	EM00：可再转让 ；EM01不得转让
bankDockingMode	银行对接模式	char(1)	否	Y:直联；N：非直联
billFaceMemo	票面备注	varchar(180)	否	
rmtrNm	出票人名称	varchar(300)	否	
rmtrIsSupprMemo	出票人备注	varchar(384)	否	
rmtrDepBnkBrCode	出票人开户行行号	char(20)	否	
rmtrDepBnkNm	出票人开户行行名	varchar(300)	否	
rmtrAccNum	出票人账户	varchar(40)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrDepBnkNm	承兑人开户行名称	varchar(300)	否	
acptrDepBnkBrCode	承兑人开户行行号	char(20)	否	
acptrAccNum	承兑人账户	varchar(40)	否	
acptrDt	承兑日期	char(10)	否	格式yyyy-MM-dd
payeeDepBnkNm	收款人开户行名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeDepBnkBrCpde	收款人开户行行号	char(20)	否	
payeeAccNum	收款人账号	varchar(40)	否	
List name="fixEbillBackInfoTransportList"
Row
backId	背面信息交易流水号	varchar(20)	否	数据会刷新，请勿做唯一性校验
tfrFlagNm	转让标记	varchar(30)	否	
appActNm	请求方账号名称	varchar(300)	否	
appAct	请求方账号	varchar(40)	否	
appCode	请求方信用代码	varchar(64)	否	
appBankNo	请求方开户行行号	varchar(64)	否	
appBankNm	请求方开户行行名	varchar(300)	否	
rcvActNm	接收方账户名称	varchar(300)	否	
rcvAct	接收方账号	varchar(40)	否	
rcvCode	接收方信用代码	varchar(64)	否	
rcBankNo	接收方开户行行号	varchar(64)	否	
rcBankNm	接收方开户行行名	varchar(300)	否	
applyDate	申请日期	char(10)	否	
fixSignFlag	签收标识	varchar(50)	否	
signDate	签收日期	char(10)	否	格式yyyy-MM-dd
rate	利率	decimal(15,2)	否	
rpdOpdt	赎回截止日	char(10)	否	格式yyyy-MM-dd
rpdDudt	赎回开放日	char(10)	否	
prsnTpyAmt	付款金额	decimal(17,2)	否	格式yyyy-MM-dd
dshnRcd	拒付代码	varchar(50)	否	格式yyyy-MM-dd
vstddShnrcdl	拒付理由代码	varchar(300)	否	格式yyyy-MM-dd
dshnRsn	拒付备注信息	varchar(900)	否	格式yyyy-MM-dd
rcrsTyp	追索类型	varchar(50)	否	
agrrDat	清偿日期	char(10)	否	
backMem	背面备注	varchar(900)	否	
wareAdr	保证人地址	varchar(900)	否	
pleDgeDt	解质押日期	char(10)	否	
stlmthd	清算标志	varchar(50)	否	
Row
List
List name="fixEbillAddInfoList"
Row
riskBillStatus	风险票据状态描述	varchar(64)	否	
rmtrNm	出票人名称	varchar(300)	否	
rmtrSoccrcode	出票人社会信用代码	varchar(100)	否	
rmtrActNm	出票人账户名称	varchar(300)	否	
rmtrMemno	出票人办理渠道	varchar(300)	否	
rmtrBillact	出票人票据账号	varchar(40)	否	
rmtrDepBnkBrCode	出票人开户行行号	varchar(50)	否	
rmtrDepBnkNm	出票人开户行名	varchar(300)	否	
rmtrAccNum	出票人账号	varchar(50)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrSoccrcode	承兑人社会信用代码	varchar(64)	否	
acptrActNm	承兑人账户名称	varchar(300)	否	
acptrMemno	承兑人办理渠道	varchar(300)	否	
acptrBillact	承兑人票据账号	varchar(40)	否	
acptrDepBnkBrCode	承兑人开户行行号	varchar(30)	否	
acptrDepBnkNm	承兑人开户行名	varchar(300)	否	
acptrAccNum	承兑人账号	varchar(40)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeSoccrcode	收款人社会信用代码	varchar(64)	否	
pyeeActNm	收款人账户名称	varchar(300)	否	
pyeeMemno	收款人办理渠道	varchar(300)	否	
pyeeBillact	收款人票据账号	varchar(40)	否	
payeeDepBnkBrCpde	收款人开户行行号	varchar(30)	否	
payeeDepBnkNm	收款人开户行名	varchar(300)	否	
payeeAccNum	收款人账号	varchar(40)	否	
Row
List
List name="fixEbillAddInfoDeposList"
Row
ebillInfoType	历史行为种类
character(4)	否	
appNm	请求方名称	varchar(128)	否	
appCode	请求方信用代码	varchar(64)	否	
appActNm	请求方账户名称	varchar(300)	否	
appMemno	请求方办理渠道	varchar(300)	否	
appBillact	请求方票据账号	varchar(50)	否	
appBankNo	请求方开户行行号	varchar(50)	否	
appBankNm	请求方开户行行名	varchar(300)	否	
appAct	请求方账号	varchar(50)	否	
rcvNm	接收名称	varchar(300)	否	
rcvCode	接收信用代码	varchar(64)	否	
rcvActNm	接收账户名称	varchar(300)	否	
rcvMemno	接收方办理渠道	varchar(300)	否	
rcvBillact	接收方票据账号	varchar(50)	否	
rcBankNo	接收方开户行行号	varchar(64)	否	
rcBankNm	接收方开户行行名	varchar(300)	否	
rcvAct	接收方账号	varchar(50)	否	
wareAdr	保证人地址	varchar(300)	否	
Row
List
Row
List
3.4.6.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBILBAK</action>
<userName>1110017971794217677</userName>    <!--登录名 varchar(30) 必输-->
<list name="queryBillParam">
    		<signAcc>8110701013901818877</signAcc>    <!--签约账号 char(40) 必输-->
   		 <billPkgId>6************20230829000059177</billPkgId>    <!--票据包号 varchar(30) 必输-->
<subBillRng>************,************</subBillRng>    <!--子票区间 varchar(25) 必输-->
</row>
</stream>
3.4.6.3 响应报文

<?xml version="1.0" encoding="GBK"?>
<stream>
<failReason>
</failReason>
<jsonStr>{"fixBillInfoRspList":[{"billPkgId":"6************20240423000658835","subBillRng":"************,************","getSubbillRng":"2024-04-23","billRcvDt":"2024-10-23","isPrmtSubpge":"Y","billStat":"CS05","crclFlag":"TF0501","billTp":"AC02","billFaceAmt":"19800.00","tfrMark":"EM00","bankDockingMode":"Y","billFaceMemo":"","rmtrNm":"广东狮子涂料有限公司","rmtrDepBnkBrCode":"************","rmtrDepBnkNm":"中信银行北京奥运村支行","rmtrAccNum":"8110701011901849967","acptrNm":"广东狮子涂料有限公司","acptrDepBnkNm":"银行名称","acptrDepBnkBrCode":"************","acptrAccNum":"8110701011901849967","acptrDt":"2024-05-16","payeeDepBnkNm":"测试银行总行营业部账务中心","payeeNm":"斑马信息科技有限公司","payeeDepBnkBrCpde":"************","payeeAccNum":"8110701013601818069","fixEbillBackInfoTransportList":[{"backId":"20240512158475515846","appActNm":"广东狮子涂料有限公司","appAct":"8110701011901849967","appCode":"91440605791219171C","appBankNo":"************","appBankNm":"","rcvAct":"8110701013701818257","rcvCode":"91330110MA2AY8QQ5L","rcvBankNo":"************","rcvBankNm":"","rcvActNm":"杭州企鹅科技有限公司","signDate":"2024-04-26","fixSignFlag":"SU00","rate":"0.00","stlmthd":"","applyDate":"","tfrFlagNm":"可再转让","rpdOpdt":"","rpdDudt":"","dshnRcd":"","prsnTpyAmt":"0.00","dshnRsn":"","rcrsTyp":"","agrrDat":"","backMem":"","wareAdr":"699999","vstddShnrcdl":"","pleDgeDt":"","ebillInfoType":"00"}],"fixEbillAddInfoList":[{"riskBillStatus":"非风险票据","rmtrNm":"广东狮子涂料有限公司","rmtrSoccrcode":"91440605791219171C","rmtrActNm":"广东狮子涂料有限公司","rmtrMemno":"测试银行股份有限公司","rmtrBillact":"","rmtrDepBnkBrCode":"************","rmtrDepBnkNm":"测试银行北京奥运村支行","rmtrAccNum":"8110701011901849967","acptrNm":"广东狮子涂料有限公司","acptrSoccrcode":"91440605791219171C","acptrActNm":"广东狮子涂料有限公司","acptrMemno":"测试银行股份有限公司","acptrBillact":"","acptrDepBnkBrCode":"************","acptrDepBnkNm":"测试银行北京奥运村支行","acptrAccNum":"8110701011901849967","payeeNm":"斑马信息科技有限公司","payeeSoccrcode":"91310114MA1GT67G33","pyeeActNm":"斑马信息科技有限公司","pyeeMemno":"测试银行股份有限公司","pyeeBillact":"","payeeDepBnkBrCpde":"************","payeeDepBnkNm":"测试银行总行营业部账务中心","payeeAccNum":"8110701013601818069"}],"fixEbillAddInfoDeposList":[{"riskBillStatus":"非风险票据","rmtrNm":"广东狮子涂料有限公司","rmtrSoccrcode":"91440605791219171C","rmtrActNm":"广东狮子涂料有限公司","rmtrMemno":"中测试银行股份有限公司","rmtrBillact":"","rmtrDepBnkBrCode":"************","rmtrDepBnkNm":"测试银行北京奥运村支行","rmtrAccNum":"8110701011901849967","acptrNm":"广东狮子涂料有限公司","acptrSoccrcode":"91440605791219171C","acptrActNm":"广东狮子涂料有限公司","acptrMemno":"测试银行股份有限公司","acptrBillact":"","acptrDepBnkBrCode":"************","acptrDepBnkNm":"测试银行北京奥运村支行","acptrAccNum":"8110701011901849967","payeeNm":"斑马信息科技有限公司","payeeSoccrcode":"91310114MA1GT67G33","pyeeActNm":"斑马信息科技有限公司","pyeeMemno":"中信银行股份有限公司","pyeeBillact":"","payeeDepBnkBrCpde":"************","payeeDepBnkNm":"测试银行总行营业部账务中心","payeeAccNum":"8110701013601818069"}]}]}
</jsonStr>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
</stream>


3.4.7 票据行为签收查询
请求代码：SKBILSGN
接口说明：
需求背景：企业ERP等系统调用该接口发起行为签收查询
接口使用须须知：
当输入账号进行查询时，系统需校验用户对票据所属的账号具有查询权限；
3.4.7.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILSGN
userName	登录用户名	varchar(30)	是	银企直联用户名
rcverAccnum	签收方账号	varchar(40)	否	
billTp	票据类型	varchar(4)	否	AC01银承 AC02 商承
sgninTp	签收类型	varchar(2)	否	01提示承兑签收02提示收票签收
03 背书转让签收 04质押签收
05质押解除签收06提示付款签收
07保证签收 08同意清偿签收
09不得转让撤销签收
aplNm	签收方名称	varchar(300)	否	
billAplDtStart	申请日期
（提示签收日期）	char(10）	是	格式yyyy-MM-dd 非必输
billAplDtEnd	申请日期	char(10）	是	格式yyyy-MM-dd 非必输
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持200条记录
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
failReason	错误信息展示	varchar(254)	否	
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有票据数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的票据数量
List
Row
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
sgninTp	签收类型	varchar(2)	否	01提示承兑签收02提示收票签收
03 背书转让签收 04质押签收
05质押解除签收06提示付款签收
07保证签收 08同意清偿签收
09不得转让撤销签收10供应链票据付款签收
billModality	票据形态	char（1）	否	1:传统电票；2：新一代电票
billTp	票据类型	cahr(4)	否	AC01：银承； AC02：商承
instNm	签收方名称	varchar(300)	否	
rcverAccnum	签收方账号	varchar(40)	否	
accBelongBank	签收方所属行	varchar(300)	否	
bankDockingMode	银行对接模式	char(1)	否	Y:直联；N：非直联
billFaceAmt	票面金额	decimal(15,2)	否	
isSupprDt	出票日期	char(10)	否	格式yyyy-MM-dd
billRcvDt	到期日期	char(10)	否	格式yyyy-MM-dd
tfrMark	转让标记	char(4)	否	EM00：可再转让 ；EM01不得转让
rmtrNm	出票人名称	varchar(300)	否	
rmtrBankNo	出票人开户行行号	char(20)	否	
rmtrBank	出票人开户行行名	varchar(300)	否	
acptrNm	承兑人名称	varchar(300)	否	
acptrBank	承兑人开户行名称	varchar(300)	否	
acptrBankNo	承兑人开户行行号	char(20)	否	
payeeDepBnkNm	收款人开户行名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
payeeBankNo	收款人开户行行号	char(20)	否	
billFaceRmrk	票面备注	varchar(180)	否	
cptlDdctfrMode	资金划扣方式	char(4)	否	

agrClroffAmt	同意清偿金额	decimal(15,2)	否	
aplAccnum	申请人账号	char(20)	否	
aplNm	申请人名称	varchar(300)	否	
Row
List
 
3.4.7.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBILSGN</action>
<userName>11100179717942176794</userName>    
<!--登录名 varchar(30) 必输-->
<rcverAccnum>8110701013901818820</rcverAccnum> 
<!--签收方账号 varchar(30) 非必输-->
<billTp></billTp>  
<!--票据类型 varchar(4) 非必输须为AC01银承 AC02商承--> 
<sgninTp></sgninTp>
<!--签收类型 varchar(2) 非必输需为01提示承兑签收 02提示收票签收 03背书转让签收 04质押签收 05质押解除签收 06提示付款签收 07保证签收 08同意清偿签收 09不得转让撤销签收 10供应链票据付款签收-->
<aplNm></aplNm>
<!--签收方名称 varchar(300) 非必输-->
<billAplDtStart>2024-01-24</billAplDtStart>
<!--申请日期起始日期 varchar(10) 格式yyyy-MM-dd 非必输-->
<billAplDtEnd>2024-05-24</billAplDtEnd>
<!--申请日期结束日期 varchar(10) 格式yyyy-MM-dd 非必输-->
<startRecord>1</startRecord>
<!--起始记录号 char(4) 必输-->
<pageNumber>200</pageNumber>
<!--请求记录条数 char(4) 必输-->   
</stream>
3.4.7.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status></status> <!--交易返回码 varchar(8)-->
<statusText></statusText> <!--交易返回信息 varchar(254) -->
<failReason></failReason> <!--错误信息展示 varchar(254) -->
<totalRecords></totalRecords> <!--总记录条数 int-->
<returnRecords></returnRecords> <!--返回记录条数 int -->
 <list name="dataList">
      <row>
  <billPkgId></billPkgId> <!--票据包号 varchar(30)-->
  <subBillRng></subBillRng> <!--子票区间 varchar(25)-->
  <sgninTp></sgninTp> <!--签收类型 varchar(2)01提示承兑签收02提示收票签收03 背书转让签收 04质押签收05质押解除签收06提示付款签收07保证签收 08同意清偿签收09不得转让撤销签收10供应链票据付款签收-->
  <billModality></billModality> <!--票据形态 char（1）2：新一代电票-->
  <billTp></billTp> <!--票据类型 cahr(4)AC01：银承； AC02：商承-->
  <instNm></instNm> <!--签收方名称 varchar(300)-->
  <rcverAccnum></rcverAccnum> <!--签收方账号 varchar(40)-->
  <accBelongBank></accBelongBank> <!--签收方所属行 varchar(300)-->
  <bankDockingMode></bankDockingMode> <!--银行对接模式 char(1) Y:直联；N：非直联-->
  <billFaceAmt></billFaceAmt> <!--票面金额 decimal(15,2)-->
  <isSupprDt></isSupprDt> <!--出票日期 char(10)格式yyyy-MM-dd-->
  <billRcvDt></billRcvDt> <!--到期日期 char(10)格式yyyy-MM-dd-->
  <tfrMark></tfrMark> <!--转让标记 char(4)EM00：可再转让 ；EM01不得转让-->
  <rmtrBankNo></rmtrBankNo> <!--出票人开户行行号 char(20)-->
  <rmtrBank></rmtrBank> <!--出票人开户行行名 varchar(300)-->
  <acptrNm></acptrNm> <!--承兑人名称 varchar(300)-->
  <acptrBank></acptrBank> <!--承兑人开户行名称 varchar(300)-->
  <acptrBankNo></acptrBankNo> <!--承兑人开户行行号 char(20)-->
  <payeeDepBnkNm></payeeDepBnkNm> <!--收款人开户行名称 varchar(300)-->
  <payeeNm></payeeNm> <!--收款人名称 varchar(300)-->
  <payeeBankNo></payeeBankNo> <!--收款人开户行行号 char(20)-->
  <billFaceRmrk></billFaceRmrk> <!--票面备注 varchar(180)-->
  <cptlDdctfrMode></cptlDdctfrMode> <!--资金划扣方式 char(4) SM00-线上清算SM01-线下清算-->
     <agrClroffAmt></agrClroffAmt> <!--同意清偿金额 decimal(15,2)-->
  <aplAccnum></aplAccnum> <!--申请人账号 char(20)-->
  <aplNm></aplNm> <!--申请人名称 varchar(300)-->
  </row>
</list>
</stream>
3.4.8 票据行为查询
请求代码：SKBILCNT
接口说明：票据行为查询接口
接口使用须须知：
当输入账号进行查询时，系统需校验用户对票据所属的账号具有查询权限；
3.4.8.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码	varchar(8)	是	标识要请求的接口，交易代码：SKBILCNT
userName	登录用户名	varchar(30)	是	银企直联用户名
accNum	企业账号	varchar(40)	否	
bsnType	业务类型	varchar(2)	否	01 出票登记
02 提示承兑申请 
03 提示收票申请
04 未用退回 
05 保证申请 
06 背书转让申请 
07 贴现申请 
08 质押申请 
09 质押解除申请 
10 提示付款申请 
11 追索通知 
12 同意清偿申请 
13 不得转让标记撤销申请 
71 手工票登记 
72 手工票收票
31 提示承兑签收
32 提示收票签收 
33 背书转让签收
34 质押签收 
35 质押解除签收 
36 提示付款签收 
37 保证签收 
38 同意清偿签收 
39 不得转让撤销签收 
40 供应链票据付款签收 
41 手工收票签收 
52 提示承兑撤销 
53 提示收票撤销 
56 背书转让撤销 
59 质押申请撤销 
60 质押解除申请撤销 
57 贴现申请撤销 
55 保证撤销 
17 提示付款撤销 
61 追索撤销
62 同意清偿撤销 
64 不得转让标记撤销申请撤销 
orderId	交易流水号	char(20)	否	
bankDockingMode	银行对接模式	varchar(1)	否	Y 直联 N 非直连
hdlDtStart	经办日期	char(10）	是	格式yyyy-MM-dd 必输
hdlDtEnd	经办日期	char(10）	是	格式yyyy-MM-dd 必输
billfaceAmtMin	付款最小金额	decimal(15,2)	否	金额数据项格式为：15，2，即：小数部分为2位，整数部分为13位，最小值为0.00，最大值为*************.99
billfaceAmtMax	付款最大金额	decimal(15,2)	否	金额数据项格式为：15，2，即：小数部分为2位，整数部分为13位，最小值为0.00，最大值为*************.99
billPkgid	票据包号	varchar(30)	否	
billTp	票据类型	varchar(4)	否	AC01银承 AC02 商承
rmtrNm	出票人名称	varchar(300)	否	
acptrNm	承兑人名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
optInstNm	对手方名称	varchar(300)	否	
stt	处理结果	varchar(100)	否	A0 审批通过，已发送 
A1 审批拒绝 
A2 审批中 
A3 审批驳回至经办人 
S1 发送失败 
S2 发送成功 
S3 交易成功 
S4 交易失败 
A4 审批通过，待发送
支持多选用“，”隔开
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持200条记录
Response
status	交易返回码	varchar(8)	是	
statusText	交易返回信息	varchar(254)	是	
failReason	错误信息展示	varchar(254)	否	
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有票据数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的票据数量
List
Row
orderId	交易流水号	char(20)	否	
stt	处理结果	char(2)	否	A0 审批通过，已发送 
A1 审批拒绝 
A2 审批中 
A3 审批驳回至经办人 
S1 发送失败 
S2 发送成功 
S3 交易成功 
S4 交易失败 
A4 审批通过，待发送
pbcStatNm	票交所处理状态	varchar(500)	否	
pbcStatErrInf	处理信息	varchar(1000)	否	
bsnType	业务种类
		否	
billPkgId	票据包号	varchar(30)	否	
subBillRng	子票区间	varchar(25)	否	
billTp	票据类型	cahr(4)	否	AC01：银承； AC02：商承
billFaceAmt	票面金额	decimal(15,2)	否	
aplymt	申请金额	decimal(15,2)	否	
entInstNm	经办企业名称	varchar(500)	否	
enterpriseAccNum	经办企业账号	varchar(40)	否	
hdlUsrDepbnkNm	经办账号所属行	varchar(300)	否	
optInstNm	对手方名称	varchar(300)	否	
issupprDt	出票日期	char(10）	否	格式yyyy-MM-dd
billRcvDt	到期日期	char(10）	否	格式yyyy-MM-dd
tfrMark	转让标记	cahr(4)	否	EM00可再转让
EM01不得转让
rmtrNm	出票人名称	varchar(300)	否	
acptrNm	承兑人名称	varchar(300)	否	
payeeNm	收款人名称	varchar(300)	否	
oppntAccnum	对手方账号	varchar(40)	否	
oppntDepbnkNm	对手方开户行名称	varchar(300)	否	
bankDockingMode	银行对接模式	varchar(1)	否	Y 直联 N 非直连
hdlDt	经办日期	char(10）	否	格式yyyy-MM-dd
crtUsrNm	经办人	varchar(180)	否	
enterpriseAccNm	经办企业账号名称	varchar(500)	否	
lastUdtTms	更新时间	varchar(20)	否	格式yyyy-MM-dd HH:mm;ss
Row
List
 
3.4.8.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
	<action>SKBILCNT</action>
	<userName></userName>
	<accNum></accNum>
	<bsnType></bsnType>
	<orderId></orderId>
	<bankDockingMode></bankDockingMode>
	<hdlDtStart></hdlDtStart>
	<hdlDtEnd></hdlDtEnd>
	<billfaceAmtMin></billfaceAmtMin>
	<billfaceAmtMax></billfaceAmtMax>
	<billPkgid></billPkgid>
	<billTp></billTp>
	<rmtrNm></rmtrNm>
	<acptrNm></acptrNm>
	<payeeNm></payeeNm>
< optInstNm ></ optInstNm >
<stt></stt>
	<startRecord></startRecord>
	<pageNumber></pageNumber>
</stream>
3.4.8.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status></status>
    <statusText></statusText>
    <failReason></failReason>
    <totalRecords></totalRecords>
    <returnRecords></returnRecords>
 <list name="fixBillCntlList">
      <row>
    <orderId></orderId>
<stt></stt>
<pbcStatNm></pbcStatNm>
<pbcStatErrInf></pbcStatErrInf>
<bsnType></bsnType>
<billPkgId></billPkgId>
<subBillRng></subBillRng>
<billTp></billTp>
<billFaceAmt></billFaceAmt>
<aplymt></aplymt>
<entInstNm></entInstNm>
<enterpriseAccNum></enterpriseAccNum>
<hdlUsrDepbnkNm></hdlUsrDepbnkNm>
<optInstNm></optInstNm>
<issupprDt></issupprDt>
<billRcvDt></billRcvDt>
<tfrMark></tfrMark>
<rmtrNm></rmtrNm>
<acptrNm></acptrNm>
<payeeNm></payeeNm>
<oppntAccnum></oppntAccnum>
<oppntDepbnkNm></oppntDepbnkNm>
<bankDockingMode></bankDockingMode>
<hdlDt></hdlDt>
<crtUsrNm></crtUsrNm>
     < enterpriseAccNm >< enterpriseAccNm > 
< lastUdtTms >< lastUdtTms >
 </row>
</list>
 
</stream>

3.5 预算中心
3.5.1 预算中心科目查询（暂未上线，上线时间待定）
请求代码： SKBU6A01
接口说明：
企业ERP等系统调用该接口查询预算科目信息 。
接口使用须知：
1.请求使用的银企直连用户需要有相关账号的查询权限。
2.须有已生效的主控预算,预算编制信息查询状态必须为调整中或已生效状态。
3.业务异常信息不限于下列：
3.5.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码：SKBU6A01
userName	登录名	varchar(50)	是	erp签约司库用户代码，用于校验司库权限
bsnTp	业务类型编号	char(20)	是	司库系统内业务类型关联预算配置存在EXTBLEND
orgNo	预算执行机构编码	varchar(20)	是	
bdgtDvltInstId	预算编制机构编码	varchar(20)	否	不传默认orgNo
currencyID	币种	varchar(5)	否	不传默认CNY
bdgtOcpDt	预算占用日期	varchar (10）	否	yyyy-MM-dd不传默认当前日期
sbjNo	预算科目编号	varchar(50)	否	
sbjNm	预算科目名称	varchar(400)	否	
sbjHrch	科目层级	char(2)	否	最大50层
sbjChar	预算科目性质	varchar(10)	否	1、支出项2、收入项3、其他项，逗号隔开
sbjSupMode	预算科目上报方式	char(4)	否	1、明细上报2、汇总上报3、不限制
isPrmtOverBdgtAprv	是否允许超预算审批	char(2)	否	1、是 0、否
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持20条记录
输出字段
status	交易状态	varchar(7)	是	交易状态
AAAAAAA 交易成功
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
isRequired	业务类型是否必填预算	char(2)	是	0：选占预算
1：占预算
2：不占预算
currencyID	币种	varchar(5)	是	
cptlBdgtNo	资金预算编号	varchar(20)	否	
cptlBdgtNm	资金预算名称	varchar(360)	否	
cptlPlnTP	资金计划类型	varchar(30)	否	YN 年预算 HN 半年预算 SN 季预算 MN 月计划 WN 周计划 TN 旬计划 DN 日计划 MR 月滚动 SR 季滚动
sbjDetailList	预算科目列表	数组	否	
List
Row
sbjNo	预算科目编号	varchar(50)	否	
sbjNm	预算科目名称	varchar(400)	否	
sbjHrch	科目层级	char(2)	否	
bdgtAvlBlnc	预算科目可用余额	decimal（15，2）	否	
rmrk	预算辅助核算信息	varchar(300)	否	
sbjChar	预算科目性质	varchar(10)	否	
isDltMode	是否明细上报	char(2)	否	0否1是
bdgtStartDate	预算周期起期	varchar(4)	否	
bdgtEndDate	预算周期止期	varchar(4)	否	
sbjSupMode	预算科目上报方式	char(4)	否	1、明细上报2、汇总上报3、不限制
sbtCntlTp	预算科目控制类型	char(4)	否	1、刚性2、弹性（按比例）3、弹性（按金额）4、柔性（提醒）5、不控制
sbtElasLmtAmt	预算科目弹性限额	decimal（15，2）	否	
isPrmtOverBdgtAprv	是否允许超预算审批	char(2)	否	0否1是
List
Row
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户明细数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户明细数量

3.5.1.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKBU6A01</action>
<userName>登录名</userName>
<bsnTp>EXTBLEND</bsnTp>

<orgNo>ERP1234567892222</orgNo>
<bdgtDvltInstId>ERP12345698756156</bdgtDvltInstId>
<currencyID>CNY</currencyID>
<bdgtOcpDt>2023-12-27</bdgtOcpDt>
<sbjNo>ERP1234567892222</sbjNo>
<sbjNm>预算科目名称</sbjNm>
<sbjHrch>1</sbjHrch>
<sbjChar>1</sbjChar>
<sbjSupMode>1</sbjSupMode>
<isPrmtOverBdgtAprv>1</isPrmtOverBdgtAprv>
<startRecord>1</startRecord>
<pageNumber>10</pageNumber>
</stream>

3.5.1.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易状态信息</statusText>
<failReason>错误信息展示</failReason>
  		<isRequired>0</isRequired>
 		<currencyID>CNY</currencyID>
  		<cptlBdgtNo>ERP1234567892222</cptlBdgtNo>
  		<cptlBdgtNm>资金预算名称</cptlBdgtNm>
<cptlPlnTP>YN</cptlPlnTP>
<list name="sbjDetailList">
<row>
<sbjNo>ERP1234567892222</sbjNo>
<sbjNm>预算科目名称</sbjNm>
<sbjHrch>1</sbjHrch>
<bdgtAvlBlnc>15000.00</bdgtAvlBlnc>
<rmrk>预算辅助核算信息</rmrk>
<sbjChar>预算科目性质</sbjChar>
<isDltMode>1</isDltMode>
<bdgtStartDate>2023</bdgtStartDate>
<bdgtEndDate>2024</bdgtEndDate>
<sbjSupMode>1</sbjSupMode>
<sbtCntlTp>1</sbtCntlTp>
<sbtElasLmtAmt>150000.00</sbtElasLmtAmt>
<isPrmtOverBdgtAprv>1</isPrmtOverBdgtAprv>
</row>
</list>
<totalRecords>1</totalRecords>
<returnRecords>1</returnRecords>
</stream>
异常案例：
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>SE04017</status>
<statusText>本次占用金额格式应为(15,2)</statusText>
</stream>
错误码：
SE04004:预算科目编号不能为空
SE04005:预算执行机构编码不能为空
SE04006:该机构不存在
SE04007:未查询到机构数据
SE04008:预算业务类型编号不能为空
SE04009:预算执行机构编码不能为空
SE04010:预算科目层级不能大于10层
SE04033:未查询到资金预算编制科目信息
SE04034:日期格式有误
SE04036:获取预算编制信息为空
SE04037:预算科目选择查询失败
SE04038:该机构不是预算单位,请先维护预算单位
SE04039:科目选择查询数据为空
SE04040:未查询到主控预算信息
SE04041:币种信息不存在

3.5.2 预算明细查询（暂未上线，上线时间待定）
请求代码：SKBU6A04
接口说明：
企业ERP等系统调用该接口查询预算明细。
接口使用须须知：
1.请求使用的银企直连用户需要有相关账号的查询权限。
2.若预算科目选择接口中【用户已选的科目以及“是否明细上报”字段，判断是否必填明细事项】，若【是否明细上报】返回为是，则必须调用预算明细接口。
3.5.2.1 参数说明
	输入输出
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
SKBU6A04
userName	用户代码	varchar(50)	是	erp签约司库用户代码，用于校验司库权限
cptlBdgtNo	资金预算编号	varchar(20)	是	最大长度为50
sbjNo	预算科目编号	varchar(200)	是	
orgNo	预算执行机构编码	varchar(20)	是	
bdgtDvltInstId	预算编制机构编码	varchar(20)	否	不传默认orgNo
matter	预算明细事项	varchar(400)	否	
startRecord	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
pageNumber	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持20条记录
Response	Response
status	交易状态	varchar(7)	是	交易状态
AAAAAAA 交易成功
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
sbjDltList	预算科目明细列表	字符型	是	
List
Row
dtlNo	预算明细编号	varchar(200)	是	
matter	预算明细事项	varchar(400)	是	
bdgtAvlBlnc	预算明细可用余额	decimal（15，2）	是	
List
Row
totalRecords	总记录条数	int	否	交易成功时返回，返回该登陆用户具有查询权限的所有账户明细数量
returnRecords	返回记录条数	int	否	交易成功时返回，返回该登陆用户本次查询获取到的账户明细数量

3.5.2.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBU6A04</action>
    <userName>11100177806072284560</userName>
    <cptlBdgtNo>YSBZ20221228140640</cptlBdgtNo>
    <sbjNo>1001</sbjNo>
    <orgNo>80000001</orgNo>
    <bdgtDvltInstId>80000001</bdgtDvltInstId>
    <matter>...</matter>
    <startRecord>1</startRecord>
    <pageNumber>10</pageNumber>
</stream>
错误报文：
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>SE04005</status>
<statusText>预算执行机构编码不能为空</statusText>
</stream>
错误码:
SE04006:该机构不存在
SE04007:未查询到机构数据
SE04041:币种信息不存在
SE04005:预算执行机构编码不能为空
SE04004:预算科目编号不能为空

3.5.2.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<failReason></failReason>
<list name="sbjDltList">
<row>
   <dtlNo>...</dtlNo>
   <matter>...</matter>
   <bdgtAvlBlnc>666666.66</bdgtAvlBlnc>
<row>
</list> 
<totalRecords>100</totalRecords>
<returnRecords>10</returnRecords>
</stream>
3.5.3 预算占用（暂未上线，上线时间待定）
请求代码：SKBU6A11
接口说明：
企业ERP等系统调用该接口占用预算；
接口使用须须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.外部占用流水号唯一，且作为每笔请求唯一流水号，同一外部占用流水号不可多次使用；
3.根据预算明细、预算科目信息，调用预算占用，需与已有数据相关联且方向一致等；
4.预算明细不得超出1000条。
3.5.3.1 参数说明
	输入输出
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码：SKBU6A11
userName	用户代码	varchar(50)	是	erp签约司库用户代码，用于校验司库权限
bsnTp	业务类型编号	varchar(20)	是	
externalNum	外部占用预算编号	varchar(30)	是	最大长度为30，不能重复。
orgNo	预算执行机构编码	varchar(20)	是	
currencyID	币种	varchar(5)	否	不传默认CNY
bdgtOcpDt	预算占用日期	varchar(10)	否	yyyyMMdd不传默认当前日期
cptlBdgtNo	资金预算编号	varchar(20)	是	
dbcrDrc	借贷方向	varchar(1)	是	0借1贷
sbjNo	预算科目编号	varchar(50)	是	
dtlNo	预算明细编号	varchar(50)	否	占明细时必传
execAmt	本次占用金额	decimal(15,2)	是	
isOverBdgt	是否已经过超预算审批	varchar(1)	否	超预算必传 0否1是
overBdgtCmnt	超预算说明	varchar(300)	否	超预算必传
mark	备注	varchar(300)	否	
asstBdgtIgCmnt	辅助预算核算信息	varchar(300)	否	
bdgtDvltInstId	预算编制机构编码	varchar(20)	否	不传默认orgNo
trdDtlList	交易明细列表	List	是	
List
Row(单次请求最多1000条)
trdSrlNum	交易流水号	varchar(50)	否	每一笔交易明细的流水号，单笔时该字段默认外部占用预算编号，多笔时该字段必输
txnDt	交易日期	varchar(10)	否	yyyyMMdd多笔时必输
cptInstId	对方机构编码	varchar(20)	否	
cptInstNm	对方机构名称	varchar(360)	否	
cptAccNum	对方账号	varchar(40)	否	
cptAccNm	对方账号名称	varchar(300)	否	
cptBnkNm	对方银行名称	varchar(300)	否	
txnAmt	交易金额	decimal(15,2)	否	多笔时必输
rmrk	备注	varchar(512)	否	
smy	摘要	varchar(512)	否	
lvmsg	附言	varchar(512)	否	
execAmt	交易明细占用金额	decimal(15,2)	否	多笔时必输
instId	本方单位编码	varchar(20)	否	
instNm	本方单位名称	varchar(360)	否	
accNum	本方账号	varchar(40)	否	
accNm	本方账号名称	varchar(300)	否	
List
Row
Response
status	交易状态	varchar(7)	是	交易状态
AAAAAAA 交易成功
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
overBdgtSt	超预算场景	varchar(1)	是	1、未超预算可执行
2、超预算不可执行
3、超预算可执行
4、超预算可执行，需页面录入超预算说明
externalNum	外部占用预算编号	varchar(20)	是	最大长度为20，不能重复。
orgNo	预算执行机构编码	varchar(20)	是	
orgNm	预算执行机构名称	varchar(360)	是	
cptlBdgtNo	资金预算编号	varchar(30)	是	
cptlBdgtNm	资金预算名称	varchar(100)	是	
sbjNo	预算科目编号	varchar(50)	是	
sbjNm	预算科目名称	varchar(400)	是	
dtlNo	预算明细编号	varchar(50)	是	
matter	预算明细事项	varchar(400)	是	
rmrk	辅助预算核算信息	varchar(300)	是	
execAmt	本次占用金额	decimal(15,2)	是	
acmAmt	已被占用金额	decimal(15,2)	是	
amt	预算可用金额	decimal(15,2)	是	
currencyID	币种	varchar(5)	是	
bsnTp	业务类型编号	varchar(50)	是	
sbtCntlTp	预算科目控制类型	varchar(4)	是	科目控制类型 1-刚性 2-弹性（按比例）3-弹性（按金额）4-柔性（提醒）5-不控制
sbtElasLmtAmt	预算科目弹性限额	decimal(15,2)	是	
occStatus	是否占用成功	varchar(4)	是	0否1是
isOverBdgt	是否超预算	varchar(4)	是	0否1是
overBdgtAmt	超额金额	decimal(15,2)	是	
bdgtStartDate	预算周期起期	varchar(10)	是	yyyyMMdd
bdgtEndDate	预算周期止期	varchar(10)	是	yyyyMMdd
iniAmt	初始上报金额	decimal(15,2)	是	
sbjChar	预算科目性质	char(2)	是	
currentAmt	调整后预算金额	decimal(15,2)	是	
cptlPlanTp	资金计划类型	char(2)	是	YN 年预算 HN 半年预算 SN 季预算 MN 月计划 WN 周计划 TN 旬计划 DN 日计划 MR 月滚动 SR 季滚动
isPrmtOverBdgtAprv	是否允许超预算审批	char(2)	是	0否1是
isOccupyBdgt	是否可执行	char(2)	是	0否1是
overBdgtInf	超预算提示信息	varchar(300)	是	

3.5.3.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBU6A11</action>
    <userName>11100177806072284560</userName>
    <bsnTp>YSBZ20221228140640</bsnTp>
    <externalNum>198903098888</externalNum>
    <orgNo>800000001</orgNo>
    <currencyID>CNY</currencyID>
    <bdgtOcpDt>20231227</bdgtOcpDt>
    <cptlBdgtNo>资金占用编号</cptlBdgtNo>
    <dbcrDrc>1</dbcrDrc>
	<sbjNo>1001</sbjNo>
	<dtlNo>A001</dtlNo>
	<execAmt>10.00</execAmt>
	<isOverBdgt>0</isOverBdgt>
	<overBdgtCmnt>...</overBdgtCmnt>
	<mark>...</mark>
	<asstBdgtIgCmnt>...</asstBdgtIgCmnt>
	<bdgtDvltInstId>10000000</bdgtDvltInstId>
	 <list name="trdDtlList">
	<row>
	  <trdSrlNum>SKSEC16953628700090014</trdSrlNum>
	  <txnDt>20231224</txnDt>
	  <cptInstId>80000001</cptInstId>
	  <cptInstNm>xxx集团</cptInstNm>
	  <cptAccNum>6227000148560005</cptAccNum>
	  <cptAccNm>xxx股份有限公司</cptAccNm>
	  <cptBnkNm>中信银行</cptBnkNm>
	  <txnAmt>15.00</txnAmt>
	  <rmrk>...</rmrk>
	  <smy>...</smy>
	  <lvmsg>...</lvmsg>
	  <execAmt>52.12</execAmt>
	  <instId>60000001</instId>
	  <instNm>xxx集团</instNm>
	  <accNum>6227000110000005</accNum>
	  <accNm>xxx股份有限公司</accNm>
	</row>
</list>
</stream>
3.5.3.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<failReason></failReason>
<overBdgtSt>1</overBdgtSt>
<externalNum>XXXXXXXXXX</externalNum>
<orgNo>80000021</orgNo>
<orgNm>xxx集团</orgNm>
<cptlBdgtNo>YSBZ20231220100001</cptlBdgtNo>
<cptlBdgtNm>资金预算名称</cptlBdgtNm>
<sbjNo>1001</sbjNo>
<sbjNm>预算科目</sbjNm>
<dtlNo>A0001</dtlNo>
<matter>商业贷款</matter>
<rmrk>辅助预算核算信息</rmrk>
<execAmt>10.00</execAmt>
<accAmt>5.00</accAmt>
<amt>10</amt>
<currencyID>CNY</currencyID>
<bsnTp>EXTBLEND</bsnTp>
<sbtCntlTp>1</sbtCntlTp>
<sbtElasLmtAmt>2.0</sbtElasLmtAmt>
<occStatus>1</occStatus>
<isOverBdgt>0</isOverBdgt>
<overBdgtAmt>0.00</overBdgtAmt>
<bdgtStartDate>20231228</bdgtStartDate>
<bdgtEndDate>20231229</bdgtEndDate>
<iniAmt>10.00</totalRecords>
<sbjChar>1</sbjChar>
<currentAmt>10.00</currentAmt>
<cptlPlanTp>YN</cptlPlanTp>
<isPrmtOverBdgtAprv>0</isPrmtOverBdgtAprv>
<isOccupyBdgt>1</isOccupyBdgt>
<overBdgtInf>超预算提示信息</overBdgtInf>
</stream>
异常案例：
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>SE04017</status>
<statusText>本次占用金额格式应为(15,2)</statusText>
</stream>
错误码：
SE04001:外部占用预算编号不能为空
SE04003:交易明细列表不能超过1000条
SE04004:预算科目编号不能为空
SE04005:预算执行机构编码不能为空
SE04006:该机构不存在
SE04007:未查询到机构数据
SE04008:预算业务类型编号不能为空
SE04009:预算执行机构编码不能为空
SE04010:预算科目层级不能大于10层
SE04011:预算占用失败
SE04012:业务类型外部占用类型错误
SE04013:资金预算编号不能为空
SE04014:借贷方向不能为空
SE04015:预算明细编号不能为空
SE04016:本次占用金额不能为空
SE04017:本次占用金额格式应为(15,2)
SE04018:交易明细交易流水号不能为空
SE04019:交易明细交易日期不能为空
SE04020:交易明细交易金额不能为空
SE04021:交易明细占用金额不能为空
SE04022:所占科目不存在
SE04023:该笔业务已占用预算，请先全部还原后再进行重新占用
SE04024:支出仅支持占支出项科目
SE04025:收入仅支持占收入项或其他项科目
SE04032:科目明细下可还原金额查询异常，请确认该科目明细下可还原金额
SE04033:未查询到资金预算编制科目信息
SE04034:日期格式有误
SE04035:外部占用流水号重复
SE04036:获取预算编制信息为空
SE04037:预算科目选择查询失败
SE04038:该机构不是预算单位,请先维护预算单位
SE04039:科目选择查询数据为空
SE04040:未查询到主控预算信息
SE04041:币种信息不存在

3.5.4 预算还原（暂未上线，上线时间待定）
请求代码：SKBU6A12
接口说明：
企业ERP等系统调用该接口查询预算占用还原，企业用户做废付款单等相关场景时，要还原之前占用得预算，则调用该接口。
接口使用须须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.本次还原金额不能超过该科目/该明细事项可还原金额，否则还原失败；
3.若上送了【外部占用预算编号】，在系统中均需唯一，否则认定为重复请求，应拒绝请求。
3.5.4.1 参数说明
	输入输出
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
externalNum	外部占用预算编号	varchar(30)	是	最大长度为30，不能重复。外部erp系统占用的唯一标识
rmrk	还原备注	varchar(300)	否	本次还原的原因或说明
restType	还原类型	varchar(4)	否	1、手动还原2、自动还原（不传默认自动还原）3、定时还原，高并发场景需要定时还原（定时还原应用于批量付款失败，多条子明细合并生成一笔还原单据，失败的存一张表中，每5分钟去查一次，同一笔单据号的生成一笔还原单据）
isWhlRest	是否全部还原	varchar(4)	否	1是0否，不传默认否，判断规则：如果交易明细列表为空，并且全部还原为是，则全部还原
trdDtlList	交易明细列表	List	是	单次不超过1000笔
list
row
trdSrlNum	交易流水号	字符型	是	每一笔交易明细的流水号
thsRestAmt	本次还原金额	字符型	是	
row
list
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
isRest	还原是否成功	varchar(4)	是	0否 1是
externalNum	外部占用预算编号	varchar(30)	是	最大长度为20，不能重复。外部erp系统占用的唯一标识
list
row
trdSrlNum	交易流水号	字符型	是	每一笔交易明细的流水号(还原成功)
row
list

3.5.4.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <action>SKBU6A12</action>
    <userName>11100177806072284560</userName>
    <externalNum>XXXXXXXXXXXXXXXXX</externalNum>
    <rmrk>...</rmrk>
    <restType>1</restType>
    <isWhlRest>1</isWhlRest>
    <list name="trdDtlList">
	<row>
	  <trdSrlNum>SKSEC16953628700090014</trdSrlNum>
	  <thsRestAmt>88.00</thsRestAmt>
	</row>
</list>
</stream>

3.5.4.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<failReason></failReason>
<isRest>1</isRest>
<externalNum>XXXXXXXXXX</externalNum>
<list name="resDltList">
<row>
     <trdSrlNum>SKSEC16953628700090014</trdSrlNum>
<row>
</list> 
</stream>
异常案例：
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>SE04017</status>
<statusText>本次占用金额格式应为(15,2)</statusText>
</stream>
异常码：
SE04001:外部占用预算编号不能为空
SE04002:交易明细列表不能为空
SE04003:交易明细列表不能超过1000条
SE04026:预算还原失败
SE04027:未查询到勾兑信息记录
SE04028:该笔单据已在还原中，不允许重复还原
SE04029:勾兑状态不允许还原
SE04030:该笔单据已有定时还原任务，不允许再手动或自动还原
SE04031:本次还原金额大于可还原金额，不支持还原
SE04032:科目明细下可还原金额查询异常，请确认该科目明细下可还原金额
SE04033:未查询到资金预算编制科目信息

3.6 跨境中心
3.6.1 归集帐单-中信银行收报
请求代码： SKCBCBLB
	接口说明：
	查询海外归集对账单，获取MT940和MT950的数据。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.海外归集对账单查询请求每次支持不多于20条数据的查询，发起请求后，返回该机构下的数据信息。如果查询不到数据，司库统一返回金额字段为空。
3.6.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
accNo	账号	varchar(20)	否	查询账号	
curr	币种	varchar(50)	否	查询币种，传值3位币种码
begDt	起始日期	char(8)	否	查询开始日期 yyyyMMdd
endDt	终止日期	char(8)	否	查询结束日期 yyyyMMdd
refNum	关联报文参考号	char(80)	否	查询相关关联报文参考号
tranDrc	交易方向	char(10)	否	查询交易方向：D，C，RC，RD（D代表借 C代表贷 RC代表以借冲贷 RD代表以贷冲借）
acctBankNum	对账单发报行BIC	char(80)	否	查询对账单发报行BIC
sk_startNo	起始记录号	char(4)	否	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
sk_recordNum	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持100条记录
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
sk_startNo	起始记录号	int	是	查询开始的记录编号
sk_recordNum	请求记录条数	int	是	每次查询请求的记录数量
totalNum	总条数	int	是	总条数
thisNum	当前条数	Int	是	当前条数
list
row
oseasBillId	归集账单表主键	varchar(20)	否	归集账单表主键
instNm	机构名称	varchar(500)	否	交易成功时返回，机构名称
instNum	机构编码	varchar(100)	否	交易成功时返回，机构编码
instMngTp	机构性质	varchar(120)	否	交易成功时返回，机构性质 I01代表实体机构I02代表管理机构
accNo	账号	varchar(40)	否	交易成功时返回，账号
curr	币种	varchar(40)	否	交易成功时返回，币种
transDate	对账日期	varchar(20)	否	交易成功时返回，对账日期
msgType	对账单类型	varchar(20)	否	交易成功时返回，对账单类型
bsnCode	业务类型	varchar(20)	否	交易成功时返回，业务类型
tranDrc	交易方向(借贷标志)	char(4)	否	交易成功时返回，交易方向(借贷标志)借-D 贷-C 以借冲贷-RC 以贷冲借-RD
tranAmt	交易金额	decimal(17，2)	否	交易成功时返回，交易金额
accountDate	记账日期	varchar(20)	否	交易成功时返回，记账日期
acctBankNum	对账单发报行BIC	varchar(40)	否	交易成功时返回，对账单发报行BIC
depbnkRgon	开户行国别与地区	varchar(500)	否	交易成功时返回，开户行国别与地区
openBalanceDCFlag	期初余额方向	varchar(40)	否	交易成功且查询到账户时返回，期初余额方向
openBalance	期初余额	decimal(17，2)	否	交易成功时返回，期初余额
openBalanceDate	期初余额日期	varchar(20)	否	交易成功且查询到交易明细时返回，期初余额日期
finalBalanceDCFlag	期末余额方向	char(4)	否	交易成功且查询到交易明细时返回,期末余额方向
finalBalance	期末余额	decimal(17，2)	否	交易成功时返回，期末余额
finalBalanceDate	期末余额日期	varchar(20)	否	交易成功且查询到交易明细时返回，期末余额日期
valueDate	起息日	varchar(20)	否	交易成功时返回，起息日
ebkNo	账户服务机构参考号(端到端标识号)	varchar(40)	否	交易成功且查询到交易明细时返回，账户服务机构参考号(端到端标识号)
mesgAccNo	补充信息(报文账号)	varchar(40)	否	交易成功且查询到交易明细时返回，补充信息(报文账号)
refNum	关联报文参考号	varchar(40)	否	交易成功且查询到交易明细时返回，关联报文参考号
pscpt	附言	varchar(1200)	否	交易成功时返回，附言
currNm	币种名称	varchar(20)	否	交易成功时返回，币种名称
currentPage	当前页号	varchar(10)	否	交易成功时返回，当前页号
bsnIdCodeNm	业务类型说明中文	varchar(1200)	否	交易成功时返回，业务类型说明中文
bsnIdCode	业务类型说明英文	varchar(1200)	否	交易成功时返回，业务类型说明英文
totalPage	总页数	varchar(10)		交易成功时返回，总页数
islastPage	最后一页指示符	char(1)		交易成功时返回，最后一页指示符 Y-是 N-不是
actualBal	实际余额	decimal(28，4)	否	交易成功时返回，实际余额
actualBalDate	实际余额日期	varchar(20)	否	交易成功时返回，实际余额日期
fowAvlBalcDomm	其余有效余额信息	varchar(1050)	否	交易成功时返回，其余有效余额信息
row
list

3.6.1.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCBLB</action>
<userName>11100114956559012768</userName>
<accNo>*********22</accNo>
<curr>JPY</curr>
<begDt>********</begDt>
<endDt>********</endDt>
<refNum>********1602</refNum>
<tranDrc>D</tranDrc>
<acctBankNum>********XXX</acctBankNum>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
</stream>

3.6.1.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
<sk_totalNum>100</sk_totalNum>
<list name="osBillList">
<row>
<oseasBillId>********142508179284</oseasBillId>
<instNm>杭州马猴烧韭科技有限公司</instNm>
<instNum>********</instNum>
<instMngTp>I01</instMngTp>
<accNo>*******************</accNo>
<curr>HKD</curr>
<transDate>********</transDate>
<msgType>日终-MT950</msgType>
<bsnCode>NOPT</bsnCode>
<tranDrc>D</tranDrc>
<tranAmt>1500.00</tranAmt>
<accountDate>********</accountDate>
<acctBankNum>********XXX</acctBankNum>
<depbnkRgon>美利坚合众国</depbnkRgon>
<openBalanceDCFlag>C</openBalanceDCFlag>
<openBalance>********</openBalance>
<openBalanceDate>********</openBalanceDate>
<finalBalanceDCFlag>C</finalBalanceDCFlag>
<finalBalance>1500.00</finalBalance>
<finalBalanceDate>********</finalBalanceDate>
<valueDate>********</valueDate>
<ebkNo>************</ebkNo>
<mesgAccNo>asda</mesgAccNo>
<refNum>************</refNum>
<pscpt></pscpt>
<currNm>港币</currNm>
<currentPage>1</currentPage>
<bsnIdCodeNm>有价证券相关项目一选择权</bsnIdCodeNm>
<bsnIdCode>Securities Related Item - Options</bsnIdCode>
<totalPage>1</totalPage>
<islastPage>Y</islastPage>
<actualBal>20.00</actualBal>
<actualBalDate>********</actualBalDate>
<fowAvlBalcDomm>你才是大傻子</fowAvlBalcDomm>
</row>
</list>
</stream>

3.6.2 归集帐单-企业bic收报
请求代码： SKCBCBLQ
	接口说明：
	查询海外归集对账单数据，获取AMH940和AMH942的帐单数据。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.海外归集对账单查询请求每次支持不多于20条数据的查询，发起请求后，返回该机构下的数据信息。如果查询不到数据，司库统一返回金额字段为空。
3.6.2.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
accNum	账号	varchar(20)	否	查询账号
curr	币种	varchar(50)	否	查询币种，传值3位币种码
txnDtStart	起始日期	varchar(50)	否	查询开始日期 yyyyMMdd
txnDtEnd	终止日期	varchar(50)	否	查询结束日期 yyyyMMdd
rcnclTp	对账单类型	varchar(20)	否	查询对账单类型 AMH942 AMH940(AMH942代表对账单日间报文 AMH940代表对账单日终报文)
refMsgNum	关联报文参考号	char(1600)	否	查询相关关联报文参考号
dbcrDrcId	交易方向	char(2)	否	查询交易方向：1， 0， 2， 3(1代表借 0代表贷 2代表以借冲贷 3代表以贷冲借 )
bnkBic	对账单发报行BIC	char(80)	否	查询对账单发报行BIC
startNo	起始记录号	char(4)	否	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
recordNum	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持100条记录
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
startNo	起始记录号	int	是	查询开始的记录编号
recordNum	请求记录条数	int	是	每次查询请求的记录数量
totalNum	总条数	int	是	总条数
thisNum	当前条数	Int	是	当前条数
list
row
instNm	机构名称	varchar(500)	否	交易成功时返回，机构名称
instNum	机构编码	varchar(100)	否	交易成功时返回，机构编码
instTp	机构类型	char(1)	否	交易成功时返回，机构类型
instChar	机构性质(旧)	char(1)	否	交易成功时返回，机构性质(旧)
instMngTp	机构性质	varchar(120)	否	交易成功时返回，机构性质I01代表实体机构I02代表管理机构
accNum	账号	varchar(40)	否	交易成功时返回，账号
curr	币种	varchar(40)	否	交易成功时返回，币种
bnkBic	所属银行BIC	varchar(40)	否	交易成功时返回，所属银行BIC
dctr	所属银行国别与地区	varchar(40)	否	交易成功时返回，所属银行国别
txnDt	对账日期	Date	否	交易成功时返回，对账日期
rcnclTp	对账单类型	varchar(20)	否	交易成功时返回，对账单类型
bsnId	业务类型	varchar(20)	否	交易成功时返回，业务类型
rcrdBillDt	记账日期	varchar(10)	否	交易成功时返回，记账日期
dbcrDrcId	交易方向	char(4)	否	交易成功时返回，交易方向(借-1 贷-0 以借冲贷-2 以贷冲借-3
debitHpnAmt	借方发生额	decimal(28，4)	否	交易成功时返回，借方发生额
crHpnAmt	贷方发生额	decimal(28，4)	否	交易成功时返回，贷方发生额
tranAmt	交易金额	decimal(28，4)	否	交易成功时返回，交易金额
bgnBalDrc	期初余额方向	char(2)	否	交易成功且查询到账户时返回，期初余额方向
bgnBal	期初余额	decimal(28，4)	否	交易成功时返回，期初余额
bgnBalDt	期初余额日期	varchar(20)	否	交易成功且查询到交易明细时返回，期初余额日期
endgBalDrc	期末余额方向	char(2)	否	交易成功且查询到交易明细时返回,期末余额方向
endgBal	期末余额	decimal(28，4)	否	交易成功时返回，期末余额
endgBalDt	期末余额日期	varchar(20)	否	交易成功且查询到交易明细时返回，期末余额日期
valDt	起息日	varchar(10)	否	交易成功时返回，起息日
refMsgNum	关联报文参考号	varchar(40)	否	交易成功且查询到交易明细时返回，关联报文参考号
lvmsg	附言	varchar(1200)	否	交易成功时返回，附言
accDtlId	对账单ID	varchar(20)	否	交易成功时返回，对账单ID
instBic	企业bic	varchar(40)	否	交易成功时返回，企业bic
currentPage	当前页号	varchar(10)	否	交易成功时返回，当前页号
bsnIdCodeNm	业务类型说明中文	varchar(1600)	否	交易成功时返回，业务类型说明中文
bsnIdCode	业务类型说明英文	varchar(1600)	否	交易成功时返回，业务类型说明英文
totalPage	总页数	varchar(10)	否	交易成功时返回，总页数
islastPage	最后一页指示符	char(1)	否	交易成功时返回，最后一页指示符 Y-是 N-不是
ebkNo	账户服务机构参考号	varchar(40)	否	交易成功时返回，账户服务机构参考号
mesgAccNo	补充信息(报文账号)	varchar(40)	否	交易成功时返回，补充信息(报文账号)
currNm	中文币种	varchar(100)	否	交易成功时返回，中文币种
actualBal	实际余额	decimal(28，4)	否	交易成功时返回，实际余额
actualBalDate	实际余额日期	varchar(20)	否	交易成功时返回，实际余额日期
fowAvlBalcDomm	其余有效余额信息	varchar(1050)	否	交易成功时返回，其余有效余额信息
row
list

3.6.2.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action> SKCBCBLQ</action>
<userName>11100114956559012768</userName>
<accNum>******222</accNum>
<curr>JPY</curr>
<txnDtStart>2023-08-01</txnDtStart>
<txnDtEnd>2023-08-10</txnDtEnd>
<rcnclTp>AMH942</rcnclTp>
<refMsgNum>112222</refMsgNum>
<dbcrDrcId>1</dbcrDrcId>
<bnkBic>*******XXZ</bnkBic>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
</stream>

3.6.2.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
<sk_totalNum>100</sk_totalNum>
<list name="accBillList">
<row>
<instNm>杭州马猴烧韭科技有限公司</instNm>
<instNum>********</instNum>
<instTp></instTp>
<instChar></instChar>
<instMngTp>I01</instMngTp>
<accNum>*******************</accNum>
<curr>HKD</curr>
<bnkBic>********XXX</bnkBic>
<dctr>法兰西共和国</dctr>
<txnDt>2023-08-14</txnDt>
<rcnclTp>AMH940</rcnclTp>
<bsnId>FSLE</bsnId>
<rcrdBillDt>2023-08-14</rcrdBillDt>
<dbcrDrcId>0</dbcrDrcId>
<debitHpnAmt>10.0000</debitHpnAmt>
<crHpnAmt>10.0000</crHpnAmt>
<tranAmt></tranAmt>
<bgnBalDrc>0</bgnBalDrc>
<bgnBal>15000.0000</bgnBal>
<bgnBalDt>1520.0000</bgnBalDt>
<endgBalDrc>0</endgBalDrc>
<endgBal>1500.0000</endgBal>
<endgBalDt>********</endgBalDt>
<valDt>2023-08-14</valDt>
<refMsgNum>********0914</refMsgNum>
<lvmsg>asda</lvmsg>
<accDtlId>********142640730819</accDtlId>
<instBic>********XXX</instBic>
<currentPage>1</currentPage>
<bsnIdCodeNm>有价证券相关项目一选择权</bsnIdCodeNm>
<bsnIdCode>Securities Related Item - Options</bsnIdCode>
<totalPage>1</totalPage>
<islastPage>Y</islastPage>
<ebkNo>SSZHCNS0XXX</ebkNo>
<mesgAccNo>aasd</mesgAccNo>
<currNm>美元</currNm>
<actualBal>20.00</actualBal>
<actualBalDate>********</actualBalDate>
<fowAvlBalcDomm>我是大傻子</fowAvlBalcDomm>
</row>
</list>
</stream>

3.6.3 归集帐单原报文-中信银行收报
请求代码： SKCBCMGB
	接口说明：
	查询海外归集对账单MT940和MT950的源报文信息。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.源报文信息查询请求每次根据归集帐单id查询原报文，发起请求后，返回该id下的原报文信息。
3.6.3.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
oseasBillId	归集帐单id	varchar(20)	是	查询原报文，传值归集帐单id
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
oseasBillId	归集帐单id	varchar(20)	是	传值归集帐单id
msgData	报文	varchar(1600)	是	报文体
failReason	详细错误信息	varchar(254)	是	详细错误信息

3.6.3.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCMGB</action>
<userName>11100114956559012768</userName>
<oseasBillId>********162329220048</oseasBillId>
</stream>
3.6.3.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<oseasBillId>********162329220048<oseasBillId>
<msgData>[{"Receiver":"********XXX"},{"Sender":"********XXX"},{"20/Transaction Reference Number：":"************"},{"25/Account Identification：":"Abcd:112233()+?"},{"28C/Statement Number/Sequence Number：":"1/1"},{"60F/Opening Balance：":"C230814USD15000,"},{"61/Statement Line：":"2308140814C1000,FSEC************//************"},{"86/Information to Account Owner：":"asdadas123"},{"62F/Closing Balance(Booked Funds)：":"C230814USD16000,"}]</msgData>
<failReason>账号无权限</failReason>

</stream>

3.6.4 归集账单原报文-企业bic收报
请求代码： SKCBCMGQ
	接口说明：
	查询海外归集对账单AMH940和AMH9942的源报文信息。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.源报文信息查询，原报文信息已于列表信息一并返回，srcMsg字段为报文信息，从列表能取到？
3.6.4.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
accDtlId	对帐单id	varchar(20)	是	查询原报文，传值对帐单id
Response
status	交易状态	varchar(7)	是	交易状态	
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
accDtlId	对帐单id	varchar(20)	是	对帐单id
srcMsg	报文	varchar(1600)	是	报文体
failReason	详细错误信息	varchar(254)	是	详细错误信息

3.6.4.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCMGQ</action>
<userName>11100114956559012768</userName>
<accDtlId>20230411142811225632</accDtlId>
</stream>

3.6.4.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<accDtlId>********142640730819</accDtlId>
<srcMsg>[{"Receiver":"********XXX"},{"Sender":"********XXX"}}]</srcMsg>
<failReason>账号无权限</failReason>
</stream>

3.6.5 全球账户支付经办
请求代码： SKCBCAM1
	接口说明：
	AMH支付经办支付。
	接口使用须知：
1.	请求使用的银企直联用户需有相关账号的经办权限；
3.6.5.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(30)	是	银企直联用户登陆用户名
clientID	客户流水号	varchar(20)	是	唯一不重复，最好是前四位能代表自己的业务，固定长度20
rcvbnkBic	收报行BIC	varchar(11)	是（第三种模式可空）	由英文或数字组成的11位字符串
rcvbnk	收报行名称	varchar(60)	否	由数字字母空格和-?:().,'+组成
pypartyDepbnkBic	付款人开户行BIC	varchar(11)	否	由英文或数字组成的11位字符串 
pypartyDepbnk	付款人开户行名称	varchar(60)	否	当付款人开户行BIC为空时，则此项可为空；当付款人开户行BIC有信息时，则此项为必输项
由数字字母空格和-?:().,'+组成
pypartyAccnum	付款人账号	varchar(34)	是	付款人账号与收款人账户不能相等
支持输入0-9a-zA-Z空格-?:().,'+/
首尾字母不能为空格，不能全部为特殊字符，至少包含一个数字
txnCurrId	交易币种编码	varchar(3)	是	三位币种编码
txnAmt	交易金额	Decimal(14,2)	是	当币种为“日元，韩元”时，金额不允许有小数，只能是整数
accbrPyTms	希望账户行付款时间	varchar(8)	是（第三种模式可空）	格式：YYYYMMDD 
rmtPscpt	汇款附言	varchar(120)	否	由数字字母空格和-?:().,'+组成
feeChrgtoMode	费用承担方式	varchar(3) 	是（第三种模式可空）	BEN：收款人承担
OUR：汇款人承担
SHA：共同承担
payeeNm	收款人名称	varchar(33*X)	是（第三种模式可空）	收款人名称varchar(33*X)，其中(X+Y)<=3 且 X<=2 且 Y<=2
由数字字母空格和-?:().,'+组成
payeeAddr	收款人地址	varchar(33*Y)	否	收款人地址varchar(33*Y)，其中(X+Y)<=3 且 X<=2 且 Y<=2可空
由数字字母空格和-?:().,'+组成
payeeLctCty	收款人所在国家或地区编码/代码	varchar(2)	是	两位编码/代码示例：CN
payeeLctUrbn	收款人所在城市	varchar(30)	否	由数字字母空格和-?:().,'+组成
payeeAccnum	收款人账号	varchar(34)	是	付款人账号与收款人账户不能相等
支持输入0-9a-zA-Z空格-?:().,'+/
首尾字母不能为空格，不能全部为特殊字符，至少包含一个数字
mdlbnkBic	中间行BIC	varchar(11)	否	由英文或数字组成的11位字符串
pypartyEngNm	付款人英文名称	varchar(65)	否	可空 如果付款企业是金融BIC，付款人名称，付款人地址需同时必输
由数字字母空格和-?:().,'+组成
pypartyAddr	付款人地址	varchar(65)	否	可空 如果付款企业是金融BIC，付款人名称，付款人地址需同时必输
由数字字母空格和-?:().,'+组成
insrCode	指示代码	varchar(35)	否	CHQB、CMSW、CMTO、CMZB、CORT、EQUI、INTC、NETS、OTHR、PHON、REPA、RTGS、URGP
请录入与收报行约定一致的指示代码
insrAdlInf	指示代码附加信息	varchar(30)	否	默认不显示该字段，当指示代码选项为“CMTO、PHON、OTHR、REPA”时，才显示该字段、且非必输。
rcvbnkInptMode	收款行BIC填写方式	char(1)	是（第三种模式可空）	(1)A：已有收款行BIC
(2)C：无收款行BIC，填写清算系统代码、清算系统识别号
(3)D：无收款行BIC，填写清算系统代码、清算系统识别号、名称及地址
payeeDepbnkBic	收款行BIC	varchar(11)	是（第三种模式可空）	由英文或数字组成的11位字符串
rcvbnkNm	收款行名称	varchar(140)	否	可空，收款行名称和地址必须同时存在。
收款行名称和地址长度相加不能超过140。
支持输入0-9a-zA-Z空格-?:().,'+/
首尾字母不能为空格，不能全部为特殊字符，至少包含一个数字。
rcvbnkAddr	收款行地址	varchar(140)	否	可空，收款行名称和地址必须同时存在。
收款行名称和地址长度相加不能超过140。
支持输入0-9a-zA-Z空格-?:().,'+/
首尾字母不能为空格，不能全部为特殊字符，至少包含一个数字。
rcvbnkClrgSysCode	收款行清算系统代码	varchar(2)	否	可空，固定2位且仅允许大写字母。
收款行清算系统代码和标识必须同时存在。
rcvbnkClrgSysFlag	收款行清算系统标识	varchar(30)	否	可空，长度不能超过30且仅允许大写字母和数字。
收款行清算系统代码和标识必须同时存在。
Response
status	交易状态	varchar(7)	是	交易状态	 “AAAAAAA”表示成功，其余码值失败
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误描述	Varchar（254）		
clientID	客户流水号	varchar(20)	是	唯一不重复，最好是前四位能代表自己的业务
srcMode	Erp上送数据处理模式	varchar(1)	是	1：审批处理；2：直接出账；3：经办处理

3.6.5.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCAM1</action>
<userName>11100*******768</userName>
<clientID>****20******42648</clientID>
<rcvbnkBic>SSZ*****XXX</rcvbnkBic>
<rcvbnk>xxx</rcvbnk>
<pypartyDepbnkBic>SSZ*****XXX</pypartyDepbnkBic>
<pypartyDepbnk>xxx</pypartyDepbnk>
<pypartyAccnum>**10**********303</pypartyAccnum>
<txnCurrId>CNY</txnCurrId>
<txnAmt>123.56</txnAmt>
<accbrPyTms>20230418</accbrPyTms>
<rmtPscpt>1231242</rmtPscpt>
<feeChrgtoMode>BEN</feeChrgtoMode>
<payeeNm>zhangsan</payeeNm>
<payeeAddr>8973 Jianguo street,berlin</payeeAddr>
<payeeLctCty>DE</payeeLctCty>
<payeeLctUrbn>berlin</payeeLctUrbn>
<payeeAccnum>**10**********303</payeeAccnum>
<mdlbnkBic>SSZ*****XXX</mdlbnkBic>
<pypartyEngNm>ssds</pypartyEngNm>
<pypartyAddr>ererere</pypartyAddr>
<insrCode></insrCode>
<insrAdlInf></insrAdlInf>
<rcvbnkInptMode>C</rcvbnkInptMode>
<payeeDepbnkBic>SSZ*****XXX</payeeDepbnkBic>
<rcvbnkNm>1</rcvbnkNm>
<rcvbnkAddr>2</rcvbnkAddr>
<rcvbnkClrgSysCode>CC</rcvbnkClrgSysCode>
<rcvbnkClrgSysFlag>A1</rcvbnkClrgSysFlag>
</stream>

3.6.5.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<clientID>XXXX2023090200542648</clientID>
<failReason>账号无权限</failReason>
<srcMode>1</srcMode>
</stream>
3.6.6 全球账户支付交易状态查询
请求代码： SKCBCAM2
	接口说明：
	全球账户支付经办交易状态查询。
	接口使用须知：
1.请求使用的银企直联用户需有相关查询权限；
3.6.6.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(30)	是	银企直联用户登陆用户名
clientID	客户流水号	varchar(20)	是	唯一，不重复，最好是前四位能代表自己的业务
Response
status	交易状态	varchar(7)	是	交易状态	
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
clientID	客户流水号	varchar(20)	是	客户流水号
srcMode	erp上送数据处理模式	varchar(1)	是	1：审批处理；2：直接出账；3：经办处理
stt	指令状态	varchar(2)	否	TS:暂存,DF:草稿,IP:审批中,TM:审批拒绝，1：报文发送中-银企,2：发送失败-银企,3：报文发送中-AMH,4：报文发送成功,5：报文发送失败
failReason	错误信息描述	varchar(254)	否	

******* 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCAM2</action>
<userName>1110******12768</userName>
<clientID>****20******42648</clientID>
</stream>

******* 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<clientID>****20******42648</clientID>
<failReason>账号无权限</failReason>
<srcMode>1</srcMode>
<stt>0</stt>
<pcsStat>TS</pcsStat> 
</stream>
3.6.7 境外资金视图-境外资金分布视图基础数据
请求代码： SKCBCJWZ
	接口说明：
	查询境外资金分布基础数据报文信息。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.境外资金分布每次最多支持20条数据的查询，发起请求后，响应对应的境外资金分布基础数据。如果查询不到数据，司库统一返回字段为空。
3.6.7.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	否	银企直联用户登陆用户名，不填写则默认当前直连登录用户
startBalDt	查询开始日期	varchar(8)	是	日期格式YYYYMMDD	
endgBalDt	查询结束日期	varchar(8)	是	日期格式YYYYMMDD
sk_startNo	起始页	int 	是	查询开始的记页数，从1开始，超过最大记录数将返回空列表
sk_recordNum	请求记录条数	int	是	每次查询请求的数量最多支持20条记录
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
sk_startNo	起始页数	int	是	查询开始页数
sk_recordNum	请求记录条数	int	是	每次查询请求的记录数量
sk_totalNum	总条数	int	是	总条数
thisNum	当前条数	Int	是	当前条数
list
row
custId	客户id	char(20)	否	交易成功时返回，客户id
instId	机构id	char(20)	否	交易成功时返回，机构id
instNm	机构名称	varchar(200)	否	交易成功时返回，机构名称
accNum	账号	varchar(40)	否	账户号
dctr	国别或地区代码	varchar(3)	否	账户号开户时所属的国别或地区代码
dctrNm	国别或地区代码（中文名称）	varchar(100)	否	账户号开户时所属的国别或地区代码（中文名称）
curr	币种	varchar(3)	否	交易成功时返回，币种
currNm	币种（中文名称）	varchar(100)	否	交易成功时返回，币种（中文名称）
endgBal	期末余额	decimal(17,2)	否	交易成功时返回，期末余额
endgBalDt	期末余额日期	varchar(8)	否	交易成功时返回，期末余额日期
rmbBal	折人民币金额	decimal(17,2)	否	交易成功时返回，折人民币金额
rmbExgRate	折人民币汇率	decimal(17,4)	否	交易成功时返回，折人民币汇率
bnkBic	银行bic	varchar(40)	否	交易成功时返回，银行bic
bnkNm	银行名称	varchar(100)	否	交易成功时返回，银行名称
crtTms	创建时间	Time	否	交易成功时返回，创建时间（资金视图数据创建时间）
row
list

3.6.7.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCJWZ</action>
<userName></userName>
<startBalDt>********</startBalDt>
<endgBalDt>********</endgBalDt>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
</stream>

3.6.7.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status>AAAAAAA</status>
<statusText>交易成功</statusText>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
<sk_totalNum>100</sk_totalNum>
<thisNum>20</thisNum>
<list name="jszList">
<row>
<accNum>******8179284</accNum>
<instNm>杭州马猴烧韭科技有限公司</instNm>
<dctr>USA</dctr>
<curr>CNY</curr>
<endgBal>期末余额</endgBal>
<endgBalDt>期末余额日期</endgBalDt>
<rmbBal>1000</rmbBal>
<rmbExgRate>0.7</rmbExgRate>
<bnkBic>********ACC</bnkBic>
<bnkNm>huifrng</bnkNm>
<instId>203694155422125</instId>
<custId>023151125421152</custId>
<crtTms>2023-09-19 16:48:49.708</crtTms>
</row>
</list>
</stream>
3.6.8 汇出汇款经办
请求代码： SKKJ2131
	接口说明：
	汇出汇款经办支付。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的经办权限；
3.6.8.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(30)	是	银企直联用户登陆用户名
bsnSrlnum	客户流水号	varchar(20)	是	唯一不重复，最好是前四位能代表自己的业务，固定长度20
bsnTp	业务类型	CHAR(1)	是	1：贸易；2：服务贸易；3：资本；4：同名划转
csbdFrgn	境内外标识	CHAR(1)	是	2:境内;1:境外
业务类型为同名划转时,境内外标识必须为境内
pyforexcChar	付汇性质	CHAR(1)	否	可空，X：保税区；E：出口加工区；D钻石交易所；A：其他特殊经济区；M：深加工转；O：其他；内外标识为境外时此字段必须为空，境内外标识为境内时必输
pyTp	付款类型	CHAR(1)	是	1:预付货款2:货到付款3:退款4:其他。
业务类型为同名划转时，付款类型必须为其他；业务类型为服务贸易或资本时，付款类型必须为退款或其他
thisFndIsUdbdgds	本笔款项是否为保税货物项下付款	CHAR(1)	否	1:是;2:否
业务类型为贸易时必输，其他业务类型时可空
rmtCurr	汇出款币种	CHAR(3)	是	汇出款币种
rmtAmt	汇出款金额	DECIMAL(15,2)	是	汇出款金额
若折交易币别金额2不为空时：汇出款金额等于折交易币别金额1与折交易币别金额2相加之和，否则汇出款金额等于折交易币别金额1
汇出款币种为日元，韩元时，汇出款金额必须是整数
remDate	报文起息日	CHAR(8)	是	格式yyyyMMdd，不少于当日
rmtAccnum1	汇款账号1	VARCHAR(19)	否	用户授权机构下的账号；
人民币账号；
rmtCurr1	汇款币种1	CHAR(3)	否	汇款账号1、汇款币种1、折交易币种金额1必须同时存在
cnvrTxnCurrAmt1	折交易币别金额1	DECIMAL(15,2)	否	汇款账号1、汇款币种1、折交易币种金额1必须同时存在
rmtAccnum2	汇款账号2	VARCHAR(19)	否	用户授权机构下的账号；
外币账号；
不能与汇款账号1相同；
汇款账号1和汇款账号2必输其一
rmtCurr2	汇款币种2	CHAR(3)	否	汇款账号2、汇款币种2、折交易币种金额2必须同时存在
cnvrTxnCurrAmt2	折交易币别金额2	DECIMAL(15,2)	否	汇款账号2、汇款币种2、折交易币种金额2必须同时存在
payeeNm	收款人名称	VARCHAR(70)	是	收款人名称
payeeAccnum	收款人账号	VARCHAR(35)	是	收款人账号
payeePermCty	收款人常驻国家与地区	CHAR(3)	是	收款人常驻国家与地区
当业务类型为同名划转、境内外标识为境内时，收款人常驻国家必须为CHN-中华人民共和国；境内外标识为境外时，收款人常驻国家不能是CHN-中华人民共和国
payeeAddr	收款人地址	VARCHAR(140)	否	收款人地址
payeeBnkBic	收款人银行BIC	VARCHAR(11)	否	收款人银行BIC与收款人银行名称和地址两者间录入其中任何一项即可
payeeBnkNm	收款人银行名称	VARCHAR(70)	否	收款人银行名称和地址长度相加不超过140个字符
payeeBnkAddr	收款人银行地址	VARCHAR(70)	否	收款人银行名称和地址长度相加不超过140个字符
payeeBnkAgncBic	收款银行之代理行BIC	VARCHAR(11)	否	收款人银行之代理行BIC与收款人银行之代理行名称和代理行地址两者间录入其中任何一项即可
payeeDepbnkAgncbnkAccnum	收款人开户银行在其代理行账号	VARCHAR(35)	否	收款人开户银行在其代理行账号
payeeBnkAgncNm	收款银行之代理行名称	VARCHAR(70)	否	收款银行代理行名称和地址长度相加不超过140个字符
payeeBnkAgncAddr	收款银行之代理行地址	VARCHAR(70)	否	收款银行代理行名称和地址长度相加不超过140个字符
rmtPscpt	汇款附言	VARCHAR(140)	否	汇款附言
dmstFgnFeeUndrtk	国内外费用承担	CHAR(3)	是	OUR:汇款人、SHA:共同、BEN:收款人
isWhldFlag	是否需要做全额到账预扣费	CHAR(1)	否	1：是、2：否；
国内外费用承担选择OUR，汇出款币种为USD或EUR时，该字段必输，其他情况必须为空
ctrNum	合同号	VARCHAR(50)	是	合同号
lnvNum	发票号	VARCHAR(95)	否	发票号
业务类型为贸易时必输，其他业务类型时必须为空
rcrdNum	备案表号	VARCHAR(15)	否	备案表号
oldDclNum	原申报号	VARCHAR(22)	否	付款类型选择退款时该字段必输
txnId1	交易编码1	RCHAR(6)	是	根据业务类型选择，贸易：1开头、999999、999998；服务贸易：2/3/4开头、999999、999998；资本：5/6/7/8/9开头；同名划转：929010
corpdAmt1	相应金额1	DECIMAL(15,2)	是	最多13位整数
相应金额1和相应金额2之和须等于汇出款金额小数点后一位四舍五入后取整的值
txnPscpt1	交易附言1	VARCHAR(180)	是	交易附言1
txnId2	交易编码2	RCHAR(6)	否	与交易编码1不能相同；
交易编码2、交易附言2、相应金额2必须同时存在
业务类型为同名划转时，汇出汇款交易编码必须为“929010”且汇出汇款交易编码2必须为空
corpdAmt2	相应金额2	DECIMAL(15,2)	否	最多13位整数；
相应金额1，2存在，应该相加等于汇款金额取整；
交易编码2、交易附言2、相应金额2必须同时存在
非空时相应金额1和相应金额2之和须等于汇出款金额小数点后一位四舍五入后取整的值
当业务类型为同名划转时必须为空
txnPscpt2	交易附言2	VARCHAR(180)	否	交易编码2、交易附言2、相应金额2必须同时存在
svcfeeChrgfeeAccnum	手续费扣费账号	VARCHAR(19)	是	手续费扣费账号
svcfeeChrgfeeAccnumCurr	手续费扣费币种	RCHAR(3)	是	手续费扣费账号与汇款账号一样时币种也必须一样
isAgncSubsPyId	是否代理子公司付款标识	CHAR(1)	是	1 是 2 否
agncSubsNmChn	代理子公司名称（中文）	VARCHAR(70)	否	代理汇款必输
agncSubsNmEng	代理子公司名称（英文）	VARCHAR(70)	否	代理汇款必输
agncSubsAccnum	代理子公司账号	VARCHAR(19)	否	代理汇款必输
agncSubsAddr	代理子公司地址	VARCHAR(70)	否	代理汇款必输
grpSubsCrspOrgcode	集团子公司对应组织机构代码	CHAR(18)	否	代理汇款必输
rturnoldDclTxnId	还原申报交易编码	CHAR(6)	否	代理汇款必输
rturnoldCorpdAmt	还原相应金额	DECIMAL(15,2)	否	代理汇款必输
rturnoldTxnPscpt	还原申报交易附言	VARCHAR(256)	否	代理汇款必输
rturnoldTxnId2	还原交易编码2	CHAR(6)	否	还原交易编码2、还原交易附言2、还原相应金额2必须同时存在
rturnoldCorpdAmt2	还原相应金额2	DECIMAL(15,2)	否	还原交易编码2、还原交易附言2、还原相应金额2必须同时存在
rturnoldTxnPscpt2	还原交易附言2	VARCHAR(256)	否	还原交易编码2、还原交易附言2、还原相应金额2必须同时存在
rturnoldDclApl	还原申报申请人	VARCHAR(20)	否	代理汇款必输
rturnoldDclAplTel	还原申报申请人电话	VARCHAR(20)	否	代理汇款必输
rturnoldPyTp	还原付款类型	CHAR(1)	否	A:预付货款;P:货到付款;R:退款;O:其他；
代理汇款必输
rturnoldSafeApvlNum	还原外汇局批件号/备案表号	VARCHAR(15)	否	代理汇款必输
rturnoldIsUdbdgdsPyforexc	还原是否保税货物项下付汇	CHAR(1)	否	Y：是；N：否；
代理汇款必输；
rturnoldFndSrc	还原款项来源	CHAR(1)	否	1：贸易项下；2：服务贸易；3：资本；
代理汇款必输
ctctpsn	联系人	VARCHAR(24)	是	联系人
ctctTel	联系电话	VARCHAR(20)	是	联系电话
Response
status	交易状态	varchar(7)	是	交易状态	 “AAAAAAA”表示成功，其余码值失败
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误描述	Varchar（254）		

3.6.8.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKKJ2131</action>
<userName>111001********19543</userName>
<bsnSrlnum>****202********2972</bsnSrlnum>
<bsnTp>1</bsnTp>
<csbdFrgn>1</csbdFrgn>
<pyforexcChar>X</pyforexcChar>
<pyTp>1</pyTp>
<thisFndIsUdbdgds>1</thisFndIsUdbdgds>
<rmtCurr>CNY</rmtCurr>
<rmtAmt>23</rmtAmt>
<remDate>20231026</remDate>
<rmtAccnum1></rmtAccnum1>
<rmtCurr1></rmtCurr1>
<cnvrTxnCurrAmt1></cnvrTxnCurrAmt1>
<rmtAccnum2>**10**********303</rmtAccnum2>
<rmtCurr2>USD</rmtCurr2>
<cnvrTxnCurrAmt2>23</cnvrTxnCurrAmt2>
<payeeNm>pntoge</payeeNm>
<payeeAccnum>**10**********000</payeeAccnum>
<payeePermCty>USA</payeePermCty>
<payeeAddr>beijing</payeeAddr>
<payeeBnkBic>SSZ*****XXX</payeeBnkBic>
<payeeBnkNm></payeeBnkNm>
<payeeBnkAddr></payeeBnkAddr>
<payeeBnkAgncBic>********CXF</payeeBnkAgncBic>
<payeeDepbnkAgncbnkAccnum></payeeDepbnkAgncbnkAccnum>
<payeeBnkAgncNm></payeeBnkAgncNm>
<payeeBnkAgncAddr></payeeBnkAgncAddr>
<rmtPscpt>测试附言</rmtPscpt>
<dmstFgnFeeUndrtk>OUR</dmstFgnFeeUndrtk>
<isWhldFlag>1</isWhldFlag>
<ctrNum>*********</ctrNum>
<lnvNum>541254</lnvNum>
<rcrdNum></rcrdNum>
<oldDclNum></oldDclNum>
<txnId1>121010</txnId1>
<corpdAmt1>1</corpdAmt1>
<txnPscpt1>一般贸易</txnPscpt1>
<txnId2>121020</txnId2>
<corpdAmt2>22</corpdAmt2>
<txnPscpt2>进料加工贸易</txnPscpt2>
<svcfeeChrgfeeAccnum>**10**********753</svcfeeChrgfeeAccnum>
<svcfeeChrgfeeAccnumCurr>CNY</svcfeeChrgfeeAccnumCurr>
<isAgncSubsPyId>1</isAgncSubsPyId>
<agncSubsNmChn>代理子公司中文名称</agncSubsNmChn>
<agncSubsNmEng>company</agncSubsNmEng>
<agncSubsAccnum>**10**********111</agncSubsAccnum>
<agncSubsAddr>shanghai</agncSubsAddr>
<grpSubsCrspOrgcode>379***896</grpSubsCrspOrgcode>
<rturnoldDclTxnId>121010</rturnoldDclTxnId>
<rturnoldCorpdAmt>85</rturnoldCorpdAmt>
<rturnoldTxnPscpt>一般贸易</rturnoldTxnPscpt>
<rturnoldTxnId2></rturnoldTxnId2>
<rturnoldCorpdAmt2></rturnoldCorpdAmt2>
<rturnoldTxnPscpt2></rturnoldTxnPscpt2>
<rturnoldDclApl>tom</rturnoldDclApl>
<rturnoldDclAplTel>18********</rturnoldDclAplTel>
<rturnoldPyTp>A</rturnoldPyTp>
<rturnoldSafeApvlNum>1</rturnoldSafeApvlNum>
<rturnoldIsUdbdgdsPyforexc>Y</rturnoldIsUdbdgdsPyforexc>
<rturnoldFndSrc>1</rturnoldFndSrc>
<ctctpsn>联系人</ctctpsn>
<ctctTel>18********</ctctTel>
</stream>
3.6.8.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <failReason>成功</failReason>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
</stream>
3.6.9 汇出汇款交易状态查询
请求代码： SKKJ2132
	接口说明：
	汇出汇款经办交易状态查询。
	接口使用须知：
1.请求使用的银企直联用户需有相关查询权限；
3.6.9.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(30)	是	银企直联用户登陆用户名
bsnSrlnum	客户流水号	varchar(20)	是	唯一，不重复，最好是前四位能代表自己的业务
Response
status	交易状态	varchar(7)	是	交易状态	
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
bsnSrlnum	客户流水号	varchar(20)	是	客户流水号
pcsStat	审批状态	varchar(2)	否	TS:暂存,DF:草稿,IP:审批中,FN:审批通过,TM:审批拒绝
txnStat	汇款状态	char(1)	否	0:处理中; 1:处理成功; 2:处理失败;
failReason	错误信息描述	varchar(254)	否	

******* 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKKJ2132</action>
<userName>1110******19543</userName>
<bsnSrlnum>****202********2972</bsnSrlnum>
</stream>

******* 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <bsnSrlnum>****202********2972</bsnSrlnum>
   <failReason>成功</failReason>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <txnStat>0</txnStat>
</stream>
3.6.10 来账预知查询
请求代码： SKCBCLZY
	接口说明：
	来账预知列表查询。
	接口使用须知：
1.请求使用的银企直联用户需有相关查询权限；
******** 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名，不填写则默认当前直连登录用户
beginDate	汇款开始日期	varchar(10)	否	日期格式YYYY-MM-DD	
endDate	汇款结束日期	varchar(10)	否	日期格式YYYY-MM-DD
startRmtAmt	汇款最小金额	decimal(18,3)	否	查询汇款最小金额
endRmtAmt	汇款最大金额	decimal(18,3)	否	查询汇款最大金额
rmterNm	汇款人名称	varchar(210)	否	汇款人名称支持模糊查询
aplyOrg	收款人	varchar(500)	否	收款人支持模糊查询
payeeNum	收款人账号	varchar(40)	否	收款人账号支持模糊查询
rmtCurr	汇款币种	varchar(3)	否	户款币种简码
sk_startNo	起始页	int 	是	查询开始的记页数，从1开始，超过最大记录数将返回空列表
sk_recordNum	请求记录条数	int	是	每次查询请求的数量最多支持20条记录
Response
failReason	错误信息描述	varchar(254)	否	
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
sk_startNo	起始页数	int	是	查询开始页数
sk_recordNum	请求记录条数	int	是	每次查询请求的记录数量
sk_totalNum	总条数	int	是	总条数
thisNum	当前条数	int	是	当前条数
list
row
instNm	收款人名称	varchar(500)	否	交易成功时返回，收款人名称
payeeNum	收款人账号	varchar(40)	否	交易成功时返回,收款人账号
rmtAmt	汇款金额	decimal(18,3)	否	交易成功时返回,汇款金额
rmtCurr	币种简码	varchar(3)	否	交易成功时返回，币种简码
rmtCurrNm	币种名称	varchar(50)	否	交易成功时返回，币种名称
rmtDt	汇款日期	varchar(10)	否	交易成功时返回，汇款日期
rmterNm	汇款人名称	varchar(210)	否	交易成功时返回，汇款人名称
crnBnkStat	来账状态	varchar(40)	否	交易成功时返回，来账状态
row
list

3.6.10.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCLZY</action>
<userName></userName>
<aplyOrg>分</aplyOrg>
<rmtCurr>USD</rmtCurr>
<payeeNum></payeeNum>
<startRmtAmt></startRmtAmt>
<endRmtAmt></endRmtAmt>
<rmterNm></rmterNm>
<beginDate></beginDate>
<endDate></endDate>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
</stream>
3.6.10.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <failReason>成功</failReason>
   <sk_recordNum>20</sk_recordNum>
   <sk_startNo>1</sk_startNo>
   <sk_totalNum>2</sk_totalNum>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <thisNum>2</thisNum>
   <list name="list">
      <row>
         <crnBnkBic>CIBKCNBJXXX</crnBnkBic>
         <crnBnkStat>收款行已入账</crnBnkStat>
         <instNm>分机构02</instNm>
         <payeeNum>8110714012801811345</payeeNum>
         <rmtAmt>121212121212121.000</rmtAmt>
         <rmtCtyRgon>USA</rmtCtyRgon>
         <rmtCtyRgonNm>美利坚合众国</rmtCtyRgonNm>
         <rmtCurr>USD</rmtCurr>
         <rmtCurrNm>美元</rmtCurrNm>
      </row>
   </list>
</stream>

3.6.11 全集团境外账户信息查询
	请求代码： SKCBCJWA
	接口说明：
	查询司库中全集团境外账户信息。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的查询权限；
2.发起全集团境外账户信息查询请求后，返回当前司库同步到的账户信息。
3.6.11.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码
userName	登录名	varchar(50)	否	银企直联用户名
instNm	机构名称	varchar(168)	否	机构名称
billNum	预查账号	varchar(40)	否	预查账号
qryDtStart	查询开始日期	varchar(8)	是	查询开始日期,格式YYYYMMDD
qryDtEnd	查询结束日期	varchar(8)	是	查询结束日期,格式YYYYMMDD
billCurr	预查账号币种	varchar(3)	否	预查账号币种
billBic	预查账号发报行BIC	varchar(11)	否	预查账号发报行BIC(帐户行行号)
stat	处理状态	varchar(1)	否	1 待处理 2 已分发 3 已维护
sk_startNo	页码	Int	是	当前页
sk_recordNum	页数	Int	是	每页条数
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	是	账号校验失败时，失败原因展示。
sk_startNo	页码	Int	是	当前页
sk_recordNum	页数	Int	是	每页条数
Sk_totalNum	总条数	Int	是	总条数
list
row
billBic	预查账号发报行BIC	varchar(11)	否	交易成功且查询到账户时返回
billCurr	预查账号币种	varchar(3)	否	交易成功且查询到账户时返回
				
billNum	预查账号	varchar(40)	否	交易成功且查询到账户时返回
instNm	机构名称	varchar(66)	否	交易成功且查询到账户时返回
qryDt	查询日期	varchar(10)	否	交易成功且查询到账户时返回
stat	处理状态	varchar(1)	否	交易成功且查询到账户时返回
row
list

3.6.11.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCJWA</action>
<userName>11100170528733974284</userName>
<instNm>司库</instNm>
<billNum>103601201080141858</billNum>
<qryDtStart>********</qryDtStart>
<qryDtEnd>20240410</qryDtEnd>
<billCurr>USD</billCurr>
<billBic>CHASUS33XXX</billBic>
<stat>1</stat>
<sk_startNo>1</sk_startNo>
<sk_recordNum>20</sk_recordNum>
</stream>
3.6.11.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <failReason></failReason>
   <sk_recordNum>20</sk_recordNum>
   <sk_startNo>1</sk_startNo>
   <sk_totalNum>2</sk_totalNum>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <list name="list">
      <row>
         <billBic>CHASUS33XXX</billBic>
         <billCurr>USD</billCurr>
         <billCurrNm>美元</billCurrNm>
         <billNum>103601201080141858</billNum>
         <instNm>北京五金有限公司01</instNm>
         <qryDt>2024-02-19</qryDt>
         <stat>1</stat>
      </row>
      <row>
         <billBic>CHASUS33XXX</billBic>
         <billCurr>USD</billCurr>
         <billCurrNm>美元</billCurrNm>
         <billNum>103601201080141858</billNum>
         <instNm>天赋模拟开户所平随知并信</instNm>
         <qryDt>2024-02-04</qryDt>
         <stat>1</stat>
      </row>
   </list>
</stream>

3.6.12 汇入汇款列表查询
请求代码： SKCBCHR1
	接口说明：
	汇入汇款列表信息查询。
	接口使用须知：
1.请求使用的银企直联用户需有相关查询权限；
3.6.12.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
sk_startNo	起始记录号	char(4)	是	查询开始的记录编号，从1开始，超过最大记录数将返回空列表
sk_recordNum	请求记录条数	char(4)	是	每次查询请求的记录数量，最多支持20条记录
aplyOrg	机构名称	Varchar(168)	否	机构名称
rmtCurr	币种	Char(3)	否	币种编码（三位英文字母）
rmtAmtMin	金额最小金额	Decimal(17,2)	否	金额最小金额
rmtAmtMax	金额最大金额	Decimal(17,2)	否	金额最大金额
rmter	汇款人	Varchar(80)	否	汇款人
startRmtDt	汇款开始日期	Varchar(8)	否	汇款开始日期（汇款日期格式YYYYMMDD）
endRmtDt	汇款结束日期	Varchar(8)	否	汇款结束日期（汇款日期格式YYYYMMDD）
origOutBnk	原始汇出行	Char(11)	否	原始汇出行
pcsStat	系统状态	Char(2)	否	系统状态（工作流审批状态 [DF:草稿；IP：审批中；FN:完成；TM:中止；TS:暂存]）
rmtStat	汇款状态	Char(1)	否	汇款状态[ W-待确认，F-异常，P-处理中，M-成功]
txnStat	交易状态	Char(1)	否	交易状态[0:处理中; 1:处理成功; 2:处理失败;3:暂存;]
hdlCfrmTmStart	经办确认日期开始日期	Varchar(19)	否	经办确认日期开始日期（yyyy-MM-dd HH:mm:ss）
hdlCfrmTmEnd	经办确认日期结束日期	Varchar(19)	否	经办确认日期结束日期（yyyy-MM-dd HH:mm:ss）
bnkBsnCode	银行业务编号	Varchar(50)	否	银行业务编号
Response
status	交易状态	varchar(7)	是	交易状态	
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息描述	varchar(254)	否	
sk_startNo	起始记录号	int	是	查询开始的记录编号
sk_recordNum	请求记录条数	int	是	每次查询请求的记录数量
sk_totalNum	总条数	int	是	总条数
thisNum	当前条数	Int	是	当前条数
list
row
rmtDt	汇款日期	date	否	汇款日期
orgNm	机构名称	Varchar(64)	否	机构名称
rmtCurr	币种	Char(3)	否	币种
rmtAmt	金额	Decimal(17,2)	否	金额
rmter	汇款人	Varchar(80)	否	汇款人
origOutBnk	原始汇出行	Char(11)	否	原始汇出行
payeeNm	收款人（报文）	Varchar(80)	否	收款人（报文）
payeeAcc	收款账号（报文）	Varchar(40)	否	收款账号（报文）
rmtStat	汇款状态	Char(1)	否	汇款状态[ W-待确认，F-异常，P-处理中，M-成功]
pcsStat	系统状态	Char(2)	否	系统状态[DF:草稿；IP：审批中；FN:完成；TM:中止；TS:暂存]
txnStat	交易状态	Char(1)	否	交易状态[0:处理中; 1:处理成功; 2:处理失败;3:暂存;]
retInf	交易返回信息	Varchar(256)	否	交易返回信息
tfrinCustAccCurrcgy	入账币种	Char(3)	否	入账币种
tfrinCustAccAmt	入账金额	Decimal(17,2)	否	入账金额
setlexgExgrate	结汇汇率	Varchar(20)	否	结汇汇率
hdlCfrmTm	经办确认日期	Timestamp	否	经办确认日期
cntprDctr	汇款人国别/地区	Char(3)	否	汇款人国别/地区
bnkBsnCode	银行业务编号	Varchar(50)	否	银行业务编号
bsnSrlnum	制单号	Varchar(20)	否	制单号
row
list

******** 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCHR1</action>
<userName>11100151648613611837</userName>
<aplyOrg>测试</aplyOrg>
<rmtCurr>USD</rmtCurr>
<rmtAmtMin>1</rmtAmtMin>
<rmtAmtMax>100</rmtAmtMax>
<rmter>sfsdfs</rmter>
<startRmtDt>20230721</startRmtDt>
<endRmtDt>20230730</endRmtDt>
<origOutBnk>BKCHCNBJXXX</origOutBnk>
<pcsStat>FN</pcsStat>
<rmtStat>W</rmtStat>
<txnStat>2</txnStat>
<hdlCfrmTmStart>2023-08-30 00:00:00</hdlCfrmTmStart>
<hdlCfrmTmEnd>2023-08-30 23:59:59</hdlCfrmTmEnd>
<bnkBsnCode>7111*****1002316</bnkBsnCode>
<sk_startNo>1</sk_startNo>
<sk_recordNum>10</sk_recordNum>
</stream>
3.6.12.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <failReason>成功</failReason>
   <sk_recordNum>10</sk_recordNum>
   <sk_startNo>1</sk_startNo>
   <sk_totalNum>1</sk_totalNum>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <thisNum>1</thisNum>
   <list name="list">
      <row>
         <bnkBsnCode>71110*******316</bnkBsnCode>
         <bsnSrlnum>2023*****5550009</bsnSrlnum>
         <cntprDctr>CHN</cntprDctr>
         <hdlCfrmTm>2023-08-30 15:00:04</hdlCfrmTm>
         <orgNm>账户中心测试机构谁动谁死</orgNm>
         <origOutBnk>BKCHCNBJXXX</origOutBnk>
         <payeeAcc>811071*****01429983</payeeAcc>
         <payeeNm>ceshi22</payeeNm>
         <pcsStat>FN</pcsStat>
         <retInf>汇入汇款确认提交失败</retInf>
         <rmtAmt>100.00</rmtAmt>
         <rmtCurr>USD</rmtCurr>
         <rmtDt>20230721</rmtDt>
         <rmtStat>W</rmtStat>
         <rmter>sfsdfs</rmter>
         <setlexgExgrate>1.0</setlexgExgrate>
         <tfrinCustAccAmt>5555.00</tfrinCustAccAmt>
         <tfrinCustAccCurrcgy>USD</tfrinCustAccCurrcgy>
         <txnStat>2</txnStat>
      </row>
   </list>
</stream>
3.6.13 汇入汇款确认经办
请求代码： SKKJ22A6
	接口说明：
	汇入汇款确认经办。
	接口使用须知：
1.请求使用的银企直联用户需有相关账号的经办权限；
3.6.13.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	银企直联用户登陆用户名
bsnSrlnum	业务编号	varchar(20)	是	汇入汇款交易业务编号；
用户需要有该笔汇入汇款交易所属机构权限
cfrmEntracctAcc	确认入账账户	varchar(40)	是	用户需要有该账号所属机构权限
tfrinCustAccCurrcgy	入账币种	char(3)	否	三位币种简码；
确认入账账户类型为FT账户时，入账币种须与汇款币种一致；其他账户类型可空
tfrinCustAccAmt	入账金额	decimal(17,2)	是	入账金额不得大于收款金额。
fndTp	款项类别	char(1)	是	1：货物贸易；2：服务贸易：3：资本
isUdbdgdsExgrcpt	是否保税货物项下收汇	char(1)	是	Y：是；N：否
款项类别为资本，是否保税货物项下收汇必须为否
rcvpyChar	收款性质	char(1)	是	A：预收款；R：退款；O：其他
xbrdrId	跨境标识	char(1)	是	1：境内；2：境外
cntprDctr	汇款人国别/地区	char(3)	是	三位简码；
跨境标识为境内时，汇款人国别/地区必须为中国
txnId	交易编码1	varchar(6)	是	款项类别为货物贸易,1开头或999998/999999; 
款项类别为服务贸易,2/3/4开头或999998/999999; 
款项类别为资本,5/6/7/8/9开头
txnPscpt	交易附言1	varchar(100)	是	一个汉字占2位。
txnId2	交易编码2	varchar(6)	否	交易编码1和交易编码2不能相同；
交易编码2与交易附言2必须同时存在
txnPscpt2	交易附言2	varchar(100)	否	交易编码2与交易附言2必须同时存在；
一个汉字占2位。
safeApvlNum	外汇局批件号/备案表号	varchar(15)	否	代理成员企业收款必填。
最多15位非汉字字符。
agncSubsIsRcvpyFlag	是否代理成员企业收款	char(1)	是	1：是； 2：否
payeeNmCn	收款人户名（中文）	varchar(70)	否	代理成员企业收款必填，非代理必须为空。
一个汉字占3位。
agncSubsNmCn	代理成员企业名称(中文)	varchar(70)	否	代理成员企业收款必填，非代理必须为空。
一个汉字占3位。
agncSubsNm	代理成员企业名称(英文)	varchar(70)	否	代理成员企业收款必填，非代理必须为空。
grpSubsCrspOrgInstCode	代理成员企业组织机构代码	char(9)	否	代理成员企业收款必填，非代理必须为空。
仅限输入大写字母与数字。
dclFndSrc	还原款项来源	char(1)	否	代理成员企业收款必填，非代理必须为空。
1：贸易项下；2：服务贸易；3：资本
dclIsUdbdgdsExgrcpt	还原是否保税货物项下收汇	char(1)	否	代理成员企业收款必填，非代理必须为空。
Y：是；N：否
dclTxnId	还原申报交易编码1	varchar(6)	否	代理成员企业收款必填，非代理必须为空。
还原申报交易编码1与还原申报交易附言1必须同时存在。
dclTxnPscpt	还原申报交易附言1	varchar(256)	否	代理成员企业收款必填，非代理必须为空。
还原申报交易编码2与还原申报交易附言2必须同时存在。
一个汉字占3位。
dclCorpdAmt	还原相应金额1	decimal(17,2)	否	代理成员企业收款必填，非代理必须为空。
仅允许输入整数金额。
还原相应金额1与还原相应金额2之和等于收款金额取整。
dclTxnId2	还原申报交易编码2	varchar(6)	否	代理成员企业收款可空，非代理必须为空。
交易编码1和交易编码2不能相同；
还原申报交易编码2与还原申报交易附言2必须同时存在。
dclTxnPscpt2	还原申报交易附言2	varchar(256)	否	代理成员企业收款可空，非代理必须为空。
还原申报交易编码2与还原申报交易附言2必须同时存在。
一个汉字占3位。
dclCorpdAmt2	还原相应金额2	decimal(17,2)	否	代理成员企业收款可空，非代理必须为空。
仅允许输入整数金额。
还原相应金额1与还原相应金额2之和等于收款金额取整。
dclApl	还原申报申请人	varchar(20)	否	代理成员企业收款必填，非代理必须为空。
一个汉字占3位。
dclAplTel	还原申报申请人电话	varchar(20)	否	代理成员企业收款必填，非代理必须为空。
最多输入20位正确号码。
Response
status	交易状态	varchar(7)	是	交易状态	
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息描述	varchar(254)	否	
bsnSrlnum	业务编号	varchar(20)	是	汇入汇款交易业务编号
srcMode	汇入汇款确认ERP处理模式	char(1)		1：审批处理；2：直接出账；3：经办处理

3.6.13.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKKJ22A6</action>
<userName>11100*****9543</userName>
<bsnSrlnum>2023*****550009</bsnSrlnum>
<cfrmEntracctAcc>8110*****9753</cfrmEntracctAcc>
<tfrinCustAccCurrcgy>USD</tfrinCustAccCurrcgy>
<tfrinCustAccAmt>99.99</tfrinCustAccAmt>
<fndTp>1</fndTp>
<isUdbdgdsExgrcpt>N</isUdbdgdsExgrcpt>
<rcvpyChar>O</rcvpyChar>
<xbrdrId>1</xbrdrId>
<cntprDctr>CHN</cntprDctr>
<safeApvlNum>85****14</safeApvlNum>
<txnId>121010</txnId>
<txnPscpt>一般贸易</txnPscpt>
<txnId2>121020</txnId2>
<txnPscpt2>进料加工贸易</txnPscpt2>
<agncSubsIsRcvpyFlag>1</agncSubsIsRcvpyFlag>
<payeeNmCn>测试收款人</payeeNmCn>
<agncSubsNmCn>测试代理企业</agncSubsNmCn>
<agncSubsNm>test agent inst</agncSubsNm>
<grpSubsCrspOrgInstCode>28*****ASS</grpSubsCrspOrgInstCode>
<dclFndSrc>1</dclFndSrc>
<dclIsUdbdgdsExgrcpt>N</dclIsUdbdgdsExgrcpt>
<dclTxnId>121010</dclTxnId>
<dclTxnPscpt>一般贸易</dclTxnPscpt>
<dclCorpdAmt>99</dclCorpdAmt>
<dclTxnId2>121020</dclTxnId2>
<dclCorpdAmt2>1</dclCorpdAmt2>
<dclTxnPscpt2>进料加工贸易</dclTxnPscpt2>
<dclApl>test</dclApl>
<dclAplTel>18******00</dclAplTel>
</stream>
3.6.13.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <bsnSrlnum>2023****0009</bsnSrlnum>
   <failReason>成功</failReason>
   <srcMode>1</srcMode>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
</stream>

3.6.14 境外账户余额查询
请求代码： SKCBCABQ
	接口说明：
	境外账户余额查询-ERP
	接口使用须知：
1.请求使用的银企直联用户需有相关查询权限；
3.6.14.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	VARCHAR(8)	是	标识要请求的接口
userName	登录名	VARCHAR(50)	是	银企直联用户登陆用户名
instNm	机构名称	VARCHAR(168)	否	支持模糊查询汉字84，数字字母168
accNo	账号	VARCHAR(40)	否	客户输入账号
curr	币种	VARCHAR(40)	否	客户输入币种的简码
acctBankNum	对账单发报行BIC	VARCHAR(40)	否	客户输入的对账单发报行BIC
sk_recordNum	
每页条数	VARCHAR(10)	是	每次查询请求的记录数量
sk_startNo	起始页	VARCHAR(10)	是	起始页数
Response
status	交易状态	VARCHAR(7)	是	交易状态
statusText	交易状态信息	VARCHAR(254)	是	交易状态结果描述
sk_startNo	起始页数	int	是	查询开始页数
sk_recordNum	请求记录条数	int	是	每次查询请求的记录数量
sk_totalNum	总条数	int	是	总条数
thisNum	当前条数	Int	是	当前条数
list
row
instNm 	机构名称 	VARCHAR(500)	是	交易成功时返回，机构名称
accNo  	账号	VARCHAR(40)	是	交易成功时返回，账号
accNm 	账户名称 	VARCHAR(300)	是	交易成功时返回，账户名称 
curr  	币种	VARCHAR(40)	是	交易成功时返回，币种
balanceDate 	余额日期 	VARCHAR(20)	是	交易成功时返回，余额日期 yyyyMMdd
balance  	余额	DECIMAL(17,2)	是	交易成功时返回，余额长度17，小数点2位
acctBankNum 	对账单发报行BIC 	VARCHAR(40)	是	交易成功时返回，对账单发报行BIC 
depBank 	国别 	VARCHAR(500)	是	交易成功时返回，国别 
balanceCNYAmt 	转人民币余额 	VARCHAR	是	交易成功时返回，转人民币余额，长度：17，小数点后2位
CNYrate  	转人民币汇率	VARCHAR	是	交易成功时返回，转人民币汇率，长度：28，小数点4位
balanceUSDAmt 	转美元余额 	VARCHAR	是	交易成功时返回，转美元余额 ，长度17，小数点后2位
USDrate 	转美元汇率 	VARCHAR	是	交易成功时返回，转美元汇率 ，长度：28，小数点4位
row
list

3.6.14.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<accNo>8110701032601451959</accNo>
<acctBankNum>AAAARSBGXXX</acctBankNum>
<action>SKCBCABQ</action>
<curr>GBP</curr>
<instNm>6</instNm>
<sk_recordNum>10</sk_recordNum>
<sk_startNo>1</sk_startNo>
<userName>11100181941579822824</userName>
</stream>
3.6.14.3 响应报文
<stream>
   <failReason>成功</failReason>
   <sk_recordNum>10</sk_recordNum>
   <sk_startNo>1</sk_startNo>
   <sk_totalNum>1</sk_totalNum>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <thisNum>0</thisNum>
   <list name="list">
      <row>
         <CNYrate>8.9540</CNYrate>
         <USDrate>1.2523</USDrate>
         <accNm>中信熊哮膨公司42</accNm>
         <accNo>8110701032601451959</accNo>
         <acctBankNum>AAAARSBGXXX</acctBankNum>
         <balance>2675.00</balance>
         <balanceCNYAmt>23951.95</balanceCNYAmt>
         <balanceDate>********</balanceDate>
         <balanceUSDAmt>3349.91</balanceUSDAmt>
         <curr>GBP</curr>
         <depBank>中国</depBank>
         <instNm>5566</instNm>
      </row>
   </list>
</stream>

3.6.15 境外账户历史余额查询
请求代码： SKCBCAHQ
	接口说明：
	境外账户历史余额查询-ERP
	接口使用须知：
1.请求使用的银企直联用户需有相关查询权限；
3.6.15.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	VARCHAR(8)	是	标识要请求的接口
userName	登录名	VARCHAR(50)	是	银企直联用户登陆用户名
instNm	机构名称	VARCHAR(168)	否	支持模糊查询汉字84，数字字母168
accNo	账号	VARCHAR(40)	否	客户输入的账号
curr	币种	VARCHAR(40)	否	客户输入币种的简码
acctBankNum	对账单发报行BIC	VARCHAR(40)	否	客户输入的对账单发报行BIC
balanceDateStart	余额开始日期	VARCHAR(8)	否	客户输入yyyyMMdd选择的日期余额开始日期
balanceDateEnd	余额结束日期	VARCHAR(8)	否	客户输入yyyyMMdd余额开始日期
sk_recordNum	每页条数	VARCHAR(10)	是	每次查询请求的记录数量
sk_startNo	起始页	VARCHAR(10)	是	起始页数
Response
status	交易状态	varchar(7)	是	交易状态
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
sk_startNo	起始页数	int	是	查询开始页数
sk_recordNum	请求记录条数	int	是	每次查询请求的记录数量
sk_totalNum	总条数	int	是	总条数
thisNum	当前条数	Int	是	当前条数
list
row
instNm 	机构名称 	VARCHAR(500)	是	交易成功时返回，机构名称
accNo  	账号	VARCHAR(40)	是	交易成功时返回，账号
accNm 	账户名称 	VARCHAR(300)	是	交易成功时返回，账户名称 
curr  	币种	VARCHAR(40)	是	交易成功时返回，币种
balanceDate 	余额日期 	VARCHAR(8)	是	交易成功时返回，余额日期 yyyyMMdd
balance  	余额	VARCHAR	是	交易成功时返回，余额长度17，小数点2位
acctBankNum 	对账单发报行BIC 	VARCHAR(40)	是	交易成功时返回，对账单发报行BIC 
depBank 	国别 	VARCHAR(500)	是	交易成功时返回，国别 
balanceCNYAmt 	转人民币余额 	VARCHAR	是	交易成功时返回，转人民币余额，长度：17，小数点后2位
CNYrate  	转人民币汇率	VARCHAR	是	交易成功时返回，转人民币汇率，长度：28，小数点4位
balanceUSDAmt 	转美元余额 	VARCHAR	是	交易成功时返回，转美元余额 ，长度17，小数点后2位
USDrate 	转美元汇率 	VARCHAR	是	交易成功时返回，转美元汇率 ，长度：28，小数点4位
row
list

3.6.15.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKCBCAHQ</action>
<sk_recordNum>10</sk_recordNum>
<sk_startNo>1</sk_startNo>
<userName>11100181941579822824</userName>
<balanceDateStart>********</balanceDateStart>
<balanceDateEnd>********</balanceDateEnd>
<curr>GBP</curr>
<acctBankNum>AAAARSBGXXX</acctBankNum>
<accNo>8110701032601451959</accNo>
<instNm>5</instNm>
</stream>
3.6.15.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
   <failReason>成功</failReason>
   <sk_recordNum>10</sk_recordNum>
   <sk_startNo>1</sk_startNo>
   <sk_totalNum>1</sk_totalNum>
   <status>AAAAAAA</status>
   <statusText>交易成功</statusText>
   <thisNum>0</thisNum>
   <list name="list">
      <row>
         <CNYrate>8.9540</CNYrate>
         <USDrate>1.2523</USDrate>
         <accNm>中信熊哮膨公司42</accNm>
         <accNo>8110701032601451959</accNo>
         <acctBankNum>AAAARSBGXXX</acctBankNum>
         <balance>2675.00</balance>
         <balanceCNYAmt>23951.95</balanceCNYAmt>
         <balanceDate>********</balanceDate>
         <balanceUSDAmt>3349.91</balanceUSDAmt>
         <curr>GBP</curr>
         <depBank>中国</depBank>
         <instNm>5566</instNm>
      </row>
   </list>
</stream>

3.7 资讯中心
3.7.1 企业工商信息查询
请求代码： SKDQYXCX
	接口说明：
	企业ERP等系统调用该接口进行企业相关信息查询，司库系统接收该请求后会调用中数系统的企业信息，企业画像，企业标签等接口并对数据进行加工处理，并返回企业的相关信息。。
	接口使用须知：
1.	请求使用的用户需有erp签约接口的权限，若没签约，返回“该用户不存在”；
2.	用户在司库系统有机构的授权；若没有机构的授权，返回“没有机构权限”；
3.7.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	接口请求代码	varchar(8)	是	标识要请求的接口
userName	登录名	varchar(50)	是	erp签约司库用户代码，用于校验司库权限
key	企业全称/统一社会信用代码	varchar(200)	是	要查询的企业全称或统一社会信用代码
Response
status	交易状态	varchar(7)	是	交易状态
AAAAAAA 交易成功
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
ENTID	企业 ID	varchar(100)	否	通过接口请求成功、并查询成功后返回企业ID
ENTNAME	企业名称	varchar(100)	否	通过接口请求成功、并查询成功后返回企业名称
REGNO	注册号	varchar(100)	否	通过接口请求成功、并查询成功后返回注册号
ESDATE	成立日期	varchar(50)	否	通过接口请求成功、并查询成功后返回成立日期
CREDITCODE	统一社会信用代码	varchar(50)	否	通过接口请求成功、并查询成功后返回统一社会信用代码
AREACODE	区域代码	varchar(300)	否	通过接口请求成功、并查询成功后返回区域代码
AREANAME	区域名称/省市	varchar(50)	否	通过接口请求成功、并查询成功后返回区域名称/省市
REGCAP	注册资本(企业:万元)	varchar(50)	否	通过接口请求成功、并查询成功后返回注册资本
ENTSTATUS	经营状态	varchar(50)	否	通过接口请求成功、并查询成功后返回经营状态
ENTTYPE	实体类型	varchar(50)	否	通过接口请求成功、并查询成功后返回实体类型
NAME	法人名称	varchar(2000)	否	通过接口请求成功、并查询成功后返回法人名称
REGCITYNAME	登记机关所在城市	varchar(50)	否	通过接口请求成功、并查询成功后返回登记机关所在城市
REGCITY	登记机关所在城市编码	varchar(50)	否	通过接口请求成功、并查询成功后返回登记机关所在城市编码
REGORG	登记机关代码/区县代码	varchar(200)	否	通过接口请求成功、并查询成功后返回登记机关代码/区县代码
REGDISTRICTNAME	区县名称	varchar(50)	否	通过接口请求成功、并查询成功后返回区县名称
DOM	住所	varchar(2000)	否	通过接口请求成功、并查询成功后返回住所
TEL	电话	varchar(300)	否	通过接口请求成功、并查询成功后返回电话
OPSCOPE	经营（业务）范围	varchar(4000)	否	通过接口请求成功、并查询成功后返回经营（业务）范围
OPFROM	经营期限自	varchar(50)	否	通过接口请求成功、并查询成功后返回经营期限自日期
OPTO	经营期限至	varchar(50)	否	通过接口请求成功、并查询成功后返回经营期限至日期
MAGRTYPCOD	法人类型码值	varchar(2)	否	通过接口请求成功、并查询成功后返回法人类型码值，码值-名称对应关系为：
01-	个体经营者
02-	执行事物合伙人
03-	负责人
04-	法定代表人
MAGRTYPNAM	法人类型名称	varchar(50)	否	通过接口请求成功、并查询成功后返回法人类型名称，码值-名称对应关系为：
01-个体经营者
02-执行事物合伙人
03-负责人
04-法定代表人
MATCHED_NAME	匹配名称	varchar(50)	否	通过接口请求成功、并查询成功后返回匹配名称
MATCHED_TYPE	匹配类型	varchar(50)	否	通过接口请求成功、并查询成功后返回匹配类型
SHORTNAME	企业简称	varchar(100)	否	通过接口请求成功、并查询成功后返回企业简称
ENTNAME_OLD	企业曾用名	varchar(4000)	否	通过接口请求成功、并查询成功后返回企业曾用名
REGCAPCURCODE	注册资本币种编码	varchar(50)	否	通过接口请求成功、并查询成功后返回注册资本币种编码
REGCAPCUR	注册资本币种	varchar(50)	否	通过接口请求成功、并查询成功后返回注册资本币种
ENTTYPECODE	企业(机构)类型编码	varchar(100)	否	通过接口请求成功、并查询成功后返回机构类型编码
SHAREHOLDERNUM	股东数量	varchar(20)	否	通过接口请求成功、并查询成功后返回股东数量
B0203	产业分类	varchar(50)	否	通过接口请求成功、并查询成功后返回产业分类信息
J0301	当前是否为有效高新技术企业	varchar(50)	否	通过接口请求成功、并查询成功后返回当前是否为有效高新技术企业标识
J0327	当前是否为有效专精特新“小 巨人”企业	varchar(50)	否	通过接口请求成功、并查询成功后返回当前是否为有效专精特新“小 巨人”企业标识
L0101	是否上市	varchar(50)	否	通过接口请求成功、并查询成功后返回是否上市标识
L0102	上市类型	varchar(50)	否	通过接口请求成功、并查询成功后返回上市类型标识
L0104	股票类别	varchar(50)	否	通过接口请求成功、并查询成功后返回股票类别信息
L0106	央企及成员企业	varchar(50)	否	通过接口请求成功、并查询成功后返回央企及成员企业标识
L0107	地方国资委直属国企	varchar(50)	否	通过接口请求成功、并查询成功后返回地方国资委直属国企标识
L0108	是否为公开发债企业	varchar(50)	否	通过接口请求成功、并查询成功后返回是否为公开发债企业标识
L0138	是否 H 股公司	varchar(50)	否	通过接口请求成功、并查询成功后返回是否 H 股公司标识
CONTROLLER	疑似实际控制人	数组	否	通过接口请求成功、并查询成功后返回疑似实际控制人
List
Row
ENTID	企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回企业ID
TYPE	类型 1-企业；2-自然人；3-其 他	varchar(1)	否	通过接口请求成功、并查询成功后返回类型
HOLDRATIO	控股比例	varchar(50)	否	通过接口请求成功、并查询成功后返回控股比例
NAME	疑似实际控制人名称	varchar(100)	否	通过接口请求成功、并查询成功后返回疑似实际控制人名称
RELATEDENTID	高管关联企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回高管关联企业ID
Row
List
BENEFICIARY	受益所有人企业	数组	否	通过接口请求成功、并查询成功后返回受益所有人企业
List
Row
POSITION	职务	varchar(100)	否	通过接口请求成功、并查询成功后返回职务
SYRTYPE	受益所有人类型	varchar(100)	否	通过接口请求成功、并查询成功后返回受益所有人类型
HOLDRATIO	控股比例	varchar(50)	否	通过接口请求成功、并查询成功后返回控股比例
TYPE	类型 1-企业；2-自然人；3-其	varchar(1)	否	通过接口请求成功、并查询成功后返回类型信息
NAME	受益所有人名称	varchar(100)	否	通过接口请求成功、并查询成功后返回受益所有人名称
RELATEDENTID	高管关联企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回企业ID
Row
List
SHAREHOLDER	股东	数组	否	通过接口请求成功、并查询成功后返回股东信息
List
Row
ENTSTATUS	企业状态	varchar(50)	否	通过接口请求成功、并查询成功后返回企业状态
SUBCONAM	认缴出资额（万元）	varchar(50)	否	通过接口请求成功、并查询成功后返回认缴出资额
HOLDSTATUS	是否控股 0-未控股；1、控股	varchar(1)	否	通过接口请求成功、并查询成功后返回是否控股标识
ENTID	企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回企业ID
FUNDEDRATIO	投资比例	varchar(50)	否	通过接口请求成功、并查询成功后返回投资比例
ISSEARCH	是否可以下探 Y-可以下探；N不可以下探	varchar(1)	否	通过接口请求成功、并查询成功后返回是否可以下探标识
TYPE	类型 1-企业；2-自然人；3-其 他	varchar(1)	否	通过接口请求成功、并查询成功后返回类型信息
NAME	股东名称	varchar(100)	否	通过接口请求成功、并查询成功后返回股东名称
Row
List
PERSON	高管	数组	否	通过接口请求成功、并查询成功后返回高管
List
Row
NAME	高管名称	varchar(100)	否	通过接口请求成功、并查询成功后返回高管名称
POSITION	高管职务	varchar(100)	否	通过接口请求成功、并查询成功后返回高管职务
TYPE	类型 1-企业；2-自然人；3-其 他	varchar(1)	否	通过接口请求成功、并查询成功后返回类型
Row
List
ENTINV	对外投资	数组	否	通过接口请求成功、并查询成功后返回对外投资信息
List
Row
NAME	企业名称	varchar(50)	否	通过接口请求成功、并查询成功后返回企业名称
ENTID	企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回企业ID
TYPE	类型 1-企业；2-自然人；3- 其他	varchar(1)	否	通过接口请求成功、并查询成功后返回类型
ISSEARCH	类型 1-企业；2-自然人；3- 其他	varchar(1)	否	通过接口请求成功、并查询成功后返回类型
SUBCONAM	投资数额（万）	varchar(50)	否	通过接口请求成功、并查询成功后返回投资数额
HOLDSTATUS	是否控股 0-未控股；1-控股	varchar(50)	否	通过接口请求成功、并查询成功后返回是否控股标识
ENTSTATUS	企业状态	varchar(50)	否	通过接口请求成功、并查询成功后返回企业状态
FUNDEDRATIO	投资比例	varchar(50)	否	通过接口请求成功、并查询成功后返回投资比例
Row
List
FILIATION	分支机构对外投资	数组	否	通过接口请求成功、并查询成功后返回分支机构对外投资
List
Row
ENTID	企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回企业ID
NAME	分支机构名称	varchar(100)	否	通过接口请求成功、并查询成功后返回分支机构名称
TYPE	类型 1-企业；2-自然人；3-其他	varchar(1)	否	通过接口请求成功、并查询成功后返回类型
Row
List
ENTINVHIS	历史对外投资	数组	否	通过接口请求成功、并查询成功后返回历史对外投资
List
Row
NAME	企业名称	varchar(100)		通过接口请求成功、并查询成功后返回企业名称
TYPE	类型 1-企业；2-自然人；3-其 他	varchar(1)		通过接口请求成功、并查询成功后返回类型
FUNDEDRATIO	投资比例	varchar(50)		通过接口请求成功、并查询成功后返回投资比例
ENTID	企业 entid	varchar(50)		通过接口请求成功、并查询成功后返回企业ID
ENTSTATUS	企业状态	varchar(50)		通过接口请求成功、并查询成功后返回企业状态
Row
List
LEGALPERSONHIS	历史法定代表人	数组	否	通过接口请求成功、并查询成功后返回历史法定代表人
List
Row
NAME	法人姓名	varchar(2000)	否	通过接口请求成功、并查询成功后返回法人姓名
RELATEDENTID	高管关联企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回高管关联企业 entid
TYPE	类型 1-企业；2-自然人；3-其 他	varchar(50)	否	通过接口请求成功、并查询成功后返回类型
Row
List
SHAREHOLDERHIS	历史股东企业状态	数组	否	通过接口请求成功、并查询成功后返回历史股东企业状态
List
Row
ENTSTATUS	企业状态	varchar(50)	否	通过接口请求成功、并查询成功后返回企业状态
ENTID	股东名称	varchar(50)	否	通过接口请求成功、并查询成功后返回股东名称
FUNDEDRATIO	投资比例	varchar(50)	否	通过接口请求成功、并查询成功后返回投资比例
TYPE	类型 1-企业；2-自然人；3-其 他	varchar(1)	否	通过接口请求成功、并查询成功后返回类型信息
NAME	股东名称	varchar(100)	否	通过接口请求成功、并查询成功后返回股东名称
RELATEDENTID	高管关联企业 entid	varchar(50)	否	通过接口请求成功、并查询成功后返回高管关联企业 entid
Row
List

3.7.1.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action>SKDQYXCX</action>
<userName>登录名</userName>
<key>中信银行股份有限公司</key>
</stream>

3.7.1.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
    <status>AAAAAAA</status>
    <statusText>成功</statusText>
    <ENTID>82889d06480fb50215b2b60d7f18895f</ENTID>
    <ENTNAME>中信银行股份有限公司</ENTNAME>
    <REGNO>*********006002</REGNO>
    <ESDATE>1987-04-20</ESDATE>
    <CREDITCODE>91110000101690725E</CREDITCODE>
    <AREACODE>110000</AREACODE>
    <AREANAME>北京市</AREANAME>
    <REGCAP>4893479.6573</REGCAP>
    <ENTSTATUS>在营（开业）</ENTSTATUS>
    <ENTTYPE>1</ENTTYPE>
    <NAME>方合英</NAME>
    <REGCITYNAME>北京市</REGCITYNAME>
    <REGCITY>110000</REGCITY>
    <REGORG>110105</REGORG>
    <REGDISTRICTNAME>朝阳区</REGDISTRICTNAME>
    <DOM>北京市朝阳区光华路10号院1号楼6-30层、32-42层</DOM>
    <TEL>89938950</TEL>
    <OPSCOPE>
        保险兼业代理业务；吸收公众存款；发放短期、中期和长期贷款；办理国内外结算；办理票据承兑与贴现；发行金融债券；代理发行、代理兑付、承销政府债券；买卖政府债券、金融债券；从事同业拆借；买卖、代理买卖外汇；从事银行卡业务；提供信用证服务及担保；代理收付款项；提供保管箱服务；结汇、售汇业务；代理开放式基金业务；办理黄金业务；黄金进出口；开展证券投资基金、企业年金基金、保险资金、合格境外机构投资者托管业务；经国务院银行业监督管理机构批准的其他业务。（市场主体依法自主选择经营项目，开展经营活动；依法须经批准的项目，经相关部门批准后依批准的内容开展经营活动；不得从事国家和本市产业政策禁止和限制类项目的经营活动。）
    </OPSCOPE>
    <OPFROM>1987-04-20</OPFROM>
    <OPTO></OPTO>
<MAGRTYPCOD>04</MAGRTYPCOD>
<MAGRTYPNAM>法定代表人</MAGRTYPNAM>
    <MATCHED_NAME>中信银行股份有限公司</MATCHED_NAME>
    <MATCHED_TYPE>11</MATCHED_TYPE>
    <SHORTNAME>中信银行</SHORTNAME>
    <ENTNAME_OLD></ENTNAME_OLD>
    <REGCAPCURCODE>156</REGCAPCURCODE>
    <REGCAPCUR>人民币元</REGCAPCUR>
    <ENTTYPECODE>1219</ENTTYPECODE>
    <SHAREHOLDERNUM>10</SHAREHOLDERNUM>
    <B0203>新能源</B0203>
    <J0301>有效高新技术企业</J0301>
    <J0327>有效专精特新“小巨人”企业</J0327>
    <L0101>已上市</L0101>
    <L0102>主板</L0102>
    <L0104>国内A股</L0104>
    <L0106>金融类央企成员企业</L0106>
    <L0107>金融类央企成员企业</L0107>
    <L0108>公开发债企业</L0108>
    <L0138>H股公司</L0138>
    <list name="CONTROLLER">
        <row>
            <ENTID></ENTID>
            <TYPE>3</TYPE>
            <HOLDRATIO>100.00%</HOLDRATIO>
            <NAME>国务院</NAME>
            <RELATEDENTID></RELATEDENTID>
        </row>
    </list>
    <list name="BENEFICIARY">
        <row>
            <POSITION>董事长,法人</POSITION>
            <SYRTYPE>人事财务控制人,法定代表人/负责人/执行事务合伙人</SYRTYPE>
            <HOLDRATIO></HOLDRATIO>
            <TYPE>2</TYPE>
            <NAME>方合英</NAME>
            <RELATEDENTID>82889d06480fb50215b2b60d7f18895f</RELATEDENTID>
        </row>
    </list>
    <list name="SHAREHOLDER">
        <row>
            <ENTSTATUS>在营（开业）</ENTSTATUS>
            <SUBCONAM></SUBCONAM>
            <HOLDSTATUS>1</HOLDSTATUS>
            <ENTID>86b5a92bb0b8e6ebb1ca530356d009ad</ENTID>
            <FUNDEDRATIO>67.05%</FUNDEDRATIO>
            <ISSEARCH>Y</ISSEARCH>
            <TYPE>1</TYPE>
            <NAME>中国中信金融控股有限公司</NAME>
        </row>
    </list>
    <list name="PERSON">
        <row>
            <POSITION>董事长,党委书记,执行董事</POSITION>
            <TYPE>2</TYPE>
            <NAME>方合英</NAME>
        </row>
    </list>
    <list name="ENTINV">
        <row>
            <ENTSTATUS>在营（开业）</ENTSTATUS>
            <HOLDSTATUS>1</HOLDSTATUS>
            <SUBCONAM>500000.000000</SUBCONAM>
            <ENTID>63b3c6d58a48265205f94e7b93fe1e24</ENTID>
            <FUNDEDRATIO>100.00%</FUNDEDRATIO>
            <ISSEARCH>Y</ISSEARCH>
            <TYPE>1</TYPE>
            <NAME>信银理财有限责任公司</NAME>
        </row>
    </list>
    <list name="FILIATION">
        <row>
            <ENTID>46109c78e1e8560b2296f9e37b6d2255</ENTID>
            <TYPE>1</TYPE>
            <NAME>中信银行(国际)有限公司上海分行</NAME>
        </row>
    </list>
    <list name="ENTINVHIS">
        <row>
            <ENTSTATUS>在营（开业）</ENTSTATUS>
            <ENTID>eb5e1c15c80b7fa0a294dc96d0140369</ENTID>
            <FUNDEDRATIO>18.30%</FUNDEDRATIO>
            <TYPE>1</TYPE>
            <NAME>汕头华汕电子器件有限公司</NAME>
        </row>
    </list>
    <list name="LEGALPERSONHIS">
        <row>
            <TYPE>2</TYPE>
            <NAME>李庆萍</NAME>
            <RELATEDENTID></RELATEDENTID>
        </row>
    </list>
    <list name="SHAREHOLDERHIS">
        <row>
            <ENTSTATUS>在营（开业）</ENTSTATUS>
            <ENTID>82e8caf35d453265a1f94071f04795c5</ENTID>
            <FUNDEDRATIO>0.00%</FUNDEDRATIO>
            <TYPE>1</TYPE>
            <NAME>中国中信集团有限公司</NAME>
            <RELATEDENTID>82e8caf35d453265a1f94071f04795c5</RELATEDENTID>
        </row>
    </list>
</stream>


3.8 应收应付中心
3.8.1 企业风险查询
请求代码： SKKSFXCX
	接口说明：
	通过上送企业名称、统一社会信用代码，查询司库系统已登记的客商的风险评估状态以及风险评估结果；风险结果信息目前暂共包含13种风险类型，后续司库系统将根据需求及使用情况扩充风险类型，本接口将会同步更新；如需更新风险评估状态，请先到司库系统中使用对应功能完成更新。
	接口使用须知：
1、根据请求中的【用户代码】，校验erp签约接口权限，若没签约，返回“该用户不存在”；
2、可以输入【企业名称】、【统一社会信用代码】进行查询，目前支持2种查询方式。
3、支持单个客商查询，【企业名称】必传：
①　若用户仅上送【企业名称】，根据企业名称精确查询：
	若存在同名客商情况，取做过风险评估且更新时间最新的一条数据返回；若不存在同名客商，则返回该企业下最新的风险评估结果和更新时间；
	若最新一次的风险评估状态为评估失败，则风险评估状态返回为“评估失败”，风险评估更新时间为失败更新时间，风险结果为失败；
	若上送的企业名称在司库客商表存在，但非境内对公客商，，则返回风险评估状态为“未评估”，风险评估更新时间为空，风险结果为空；
	若均未做过风险评估，也取客商更新时间最新的一条数据返回，风险评估状态为“未评估”，风险评估更新时间为空，风险结果为空；
	若未查询到该企业，则返还提示为“被查询企业未在司库客商列表中，请到司库系统进行客商维护”；
	若查询的客商正在风险评估中，若非第一次评估，则评估状态为评估中，但取上一版本的风险评估红黄绿灯及对应更新时间返回；若是第一次风险评估，则评估状态为评估中，风险结果对应的红黄绿灯结果为初始为空；
②　若用户上送【企业名称】和【统一社会信用代码】精确查询：
	若存在（同名且同统代）情况，取做过风险评估且更新时间最新的一条数据返回；若不存在（同名且同统代）客商，则返回该企业最新的风险评估结果和更新时间；
	若最新一次的风险评估状态为评估失败，则风险评估状态返回为“评估失败”，风险评估更新时间为失败更新时间，风险结果为失败；
	若上送的企业名称及统一社会信用代码在司库客商表存在，但非境内对公客商，则返回风险评估状态为“未评估”，风险评估更新时间为空，风险结果为空；
	若均未做过风险评估，取也更新时间最新的一条数据返回，风险评估状态为“未评估”，风险评估更新时间为空，风险结果为空；
	若未查询到该企业，则返还提示为“被查询企业未在司库客商列表中，请到司库系统进行客商维护”；
	若查询的客商正在风险评估中，评估状态为评估中，但取上一版本的风险评估红黄绿灯及对应更新时间返回；若是第一次风险评估，则评估状态为评估中，风险结果对应的红黄绿灯结果为初始为空；
4、支持同时保存新客商信息（建议开启该功能）：
1）用户可上送【是否保存客商】且该参数值为“是”开启此功能，开启后系统将根据工商核验结果保存客商至【司库系统】并通过接口返回风险评估查询结果；
2）开启此功能需同时上送【统一社会信用代码】
3）保存时客商信息赋值逻辑为[客商编码]：自动赋值、[客商名称]：接口上送[企业名称]、[是否对公]：是、[是否境外]：否、[客商类型]：其他、[数据来源]：ERP”。


3.8.1.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
输入字段
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码：SKKSFXCX
userName	用户代码	varchar(50)	是	erp签约司库用户代码，用于校验司库权限
custInfoName	企业名称	varchar(300)	是	
undSocCrCode	统一社会信用代码	CHAR(18)	否	
isSave	是否保存客商	CHAR(1)	否	0-否
1-是
缺省默认为“0-否”
输出字段
status	交易状态	varchar(7)	是	交易状态
AAAAAAA 交易成功
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
custInfoCd	企业编码	 CHAR(20)	否	
custInfoName	企业名称	varchar(300)	否	
undSocCrCode	统一社会信用代码	CHAR(18)	否	
rskAsesStat	风险评估状态	char(1)	否	0 未评估 
1 评估中
2 处理完成（8个接口均成功或部分成功）
3 处理失败（8个风险接口均失败）
lastUdtTms	风险评估更新时间	TIMESTAMP	否	
YYYY-MM-DD HH:MM:SS
List（riskList）
Row
riskType	风险类型	varchar(50)	否	TYPE-1-1 主体金融风险 
TYPE-1-2 法人金融风险
TYPE-1-3 主体严重违法风险
TYPE-1-4 主体或法人失信风险
TYPE-1-5 主体或法人被执行风险
TYPE-1-6 主体海关行政处罚风险
TYPE-1-7 主体行政处罚风险
TYPE-1-8 主体纳税人违法风险
TYPE-2-1 主体经营状态风险
TYPE-2-2 主体经营异常风险
TYPE-2-3 受益人风险
TYPE-2-4 股权风险
TYPE-2-5 票据逾期风险

riskTypeNm	风险类型名称	varchar(100)	否	TYPE-1-1 主体金融风险 
TYPE-1-2 法人金融风险
TYPE-1-3 主体严重违法风险
TYPE-1-4 主体或法人失信风险
TYPE-1-5 主体或法人被执行风险
TYPE-1-6 主体海关行政处罚风险
TYPE-1-7 主体行政处罚风险
TYPE-1-8 主体纳税人违法风险
TYPE-2-1 主体经营状态风险
TYPE-2-2 主体经营异常风险
TYPE-2-3 受益人风险
TYPE-2-4 股权风险
TYPE-2-5 票据逾期风险
riskResult	风险结果	varchar(100)	否	初始为空 
99 调用外部接口失败异常
100 调用外部接口成功无返回
110 绿灯
119 黄灯
120 红灯

List
Row

3.8.1.2 请求报文
<?xml version="1.0" encoding="GBK">
<stream>
     <action>接口代码待定</action>
	 <userName>用户代码</userName>
	 <custInfoName>企业名称</custInfoName>
	 <undSocCrCode>统一社会信用代码</undSocCrCode>
    <isSave>0/1</isSave>
</stream>
3.8.1.3 响应报文
<?xml version="1.0" encoding="GBK">
<stream>
<custInfoCd>企业编码</custInfoCd>
	 <custInfoName>企业名称</custInfoName>
 <failReason></failReason>
 <lastUdtTms>风险更新时间</lastUdtTms>
 <rskAsesStat>风险评估状态</rskAsesStat>
     <status>AAAAAAA</status>
	 <statusText>交易成功</statusText>
	 <undSocCrCode>统一社会信用代码</undSocCrCode>
		 <list name = "riskList">
	   <row>
<riskResult>110</riskResult>
	     <riskType>TYPE-1-1</riskType>
		 <riskTypeNm>主体金融风险</riskTypeNm>
	   </row>
	    <row>
 <riskResult>110</riskResult>
	     <riskType>TYPE-1-2</riskType>
		 <riskTypeNm>法人金融风险</riskTypeNm>
	   </row>
	   ....
	    <row>
<riskResult>110</riskResult>
	     <riskType>TYPE-2-4</riskType>
		 <riskTypeNm>股权风险</riskTypeNm>
	   </row>
	 </list>
</stream>
3.8.2 企业风险详情查询
请求代码： SKFXCXMX
	接口说明：
	通过上送企业名称、统一社会信用代码、风险类型，查询司库系统已登记客商的风险评估状态以及风险评估结果详情。目前暂共包含13种风险类型，后续司库系统将根据需求及使用情况扩充风险类型，本接口将会同步更新。如需查询风险评估详细结果，请先到司库系统中完成该客商的风险评估。

	接口使用须知：
1、根据请求中的【用户代码】，校验ERP签约接口权限，若没签约，返回“该用户不存在”；
2、支持查询单个客商的风险评估详情，其中【企业名称】必传、【统一社会信用代码】选传：
若仅上送【企业名称】，根据企业名称精确查询；若上送【企业名称】和【统一社会信用代码】，则根据两个参数组合进行精确查询客商信息：
1）若存在同名（同代）客商，取做过风险评估且更新时间最新的一条数据返回；若不存在同名客商，则返回该客商下最新的风险评估结果和风险详情；
2）若上送的企业名称（统一社会信息代码）在司库客商表存在，但非境内对公客商，则返回提示为“客商未进行过风险评估操作”；
3）若客商未做过风险评估或正在评估中，取客商更新时间最新的一条客商数据返回，返回提示为“客商未进行过风险评估操作”；
4）若未查询到该客商，则返还提示为“被查询企业未在司库客商列表中，请到司库系统进行客商维护”；
3、每项风险类型，均对应各自的风险评估详情输出字段，风险类型与风险详情字段名称的映射关系如下，详情具体字段可参考请求、响应报文章节说明；各风险类型详情列表最多支持返回1000条，超出部分做截断处理。
风险类型	风险类型名称	风险详情名称	风险详情输出字段	字段类型
TYPE-1-1	主体金融风险	-	-	-
TYPE-1-2	法人金融风险	-	-	-
TYPE-1-3	主体严重违法风险	严重违法信息	dataList1	列表
TYPE-1-4	主体或法人失信风险	失信被执行人信息	dataList1	列表
		关联失信被执行人信息	dataList2	列表
TYPE-1-5	主体或法人被执行风险	被执行人信息	dataList1	列表
		关联被执行人信息	dataList2	列表
TYPE-1-6	主体海关行政处罚风险	海关行政处罚	dataList1	列表
TYPE-1-7	主体行政处罚风险	行政处罚基本信息	dataList1	列表
TYPE-1-8	主体纳税人违法风险	纳税人违法事件	dataList1	列表
TYPE-2-1	主体经营状态风险	经营状态	entstatus 	字段
TYPE-2-2	主体经营异常风险	企业异常名录信息	dataList1	列表
		清算信息	dataList2	列表
TYPE-2-3	受益人风险	受益人风险信息	dataList1	列表
		受益所有人控制企业数量	dataList2	列表
TYPE-2-4	股权风险	企业股权风险	dataList1	列表
		企业股权风险明细	dataList2	列表
TYPE-2-5	票据逾期风险	票据逾期信息	dataList1	列表



3.8.2.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
输入字段
action	接口请求代码	varchar(8)	是	标识要请求的接口，交易代码：SKKSFXCX
userName	用户代码	varchar(50)	是	erp签约司库用户代码，用于校验司库权限
custInfoName	企业名称	varchar(300)	是	
undSocCrCode	统一社会信用代码	CHAR(18)	否	
riskType	查询风险类型	CHAR(10)	是	TYPE-1-1 主体金融风险 
TYPE-1-2 法人金融风险
TYPE-1-3 主体严重违法风险
TYPE-1-4 主体或法人失信风险
TYPE-1-5 主体或法人被执行风险
TYPE-1-6 主体海关行政处罚风险
TYPE-1-7 主体行政处罚风险
TYPE-1-8 主体纳税人违法风险
TYPE-2-1 主体经营状态风险
TYPE-2-2 主体经营异常风险
TYPE-2-3 受益人风险
TYPE-2-4 股权风险
TYPE-2-5 票据逾期风险
输出字段
status	交易状态	varchar(7)	是	交易状态
AAAAAAA 交易成功
statusText	交易状态信息	varchar(254)	是	交易状态结果描述
failReason	错误信息展示	varchar(254)	否	校验失败时，失败原因展示。
custInfoCd	企业编码	 CHAR(20)	否	
custInfoName	企业名称	varchar(300)	否	
undSocCrCode	统一社会信用代码	CHAR(18)	否	
riskResult	风险评估结果	varchar(100)	是	初始为空 
99 调用外部接口失败异常
100调用外部接口成功无返回
110 绿灯
119 黄灯
120 红灯
List（占位列表）
Row
占位字段1				
占位字段2				
占位字段3				
List
Row
其中占位列表、占位字段的名称、数量根据各风险类型而定，各风险类型对应详情字段如下：

1、TYPE-1-1 主体金融风险：无

2、TYPE-1-2 法人金融风险：无

3、TYPE-1-3 主体严重违法风险：
List（dataList1）
Row
id	主键		否	
inreason	列入原因		否	
indate	列入日期		否	
outdate	移出日期		否	
outreason	移出原因		否	
inregorg	列入作出决定机关		否	
insn	列入作出决定文号		否	
outregorg	移出作出决定机关		否	
outsn	移出作出决定文号		否	
Row
List

4、TYPE-1-4 主体或法人失信风险
List（dataList1）失信被执行人信息
Row
id	主键		否	
isonslf	是否本人(增加)		否	
type	失信人类型		否	
userName	被执行人姓名/名称		否	
publishdateclean	发布时间		否	
regdateclean	立案时间		否	
idcard	证件号码		否	
courtname	执行法院		否	
gistid	执行依据文号		否	
casecode	案号		否	
gistunit	做出执行依据单位		否	
duty	生效法律文书确定的义务		否	
performance	被执行人履行情况		否	
performedpart	已履行（元）		否	
unperformpart	未履行（元）		否	
disrupttypename	失信被执行人行为具体情形		否	
benfNm	法定代表人或者负责人姓名		否	
areanameclean	省份		否	
sexyclean	性别		否	
ageclean	年龄		否	
focusnumber	关注次数		否	
ysfzd	身份证原始发证地		否	
exitdate	退出日期		否	
casestate	案件状态		否	
Row
List
List（dataList2）关联失信被执行人信息
Row
id	主键		否	
type	失信人类型		否	
userName	被执行人姓名/名称		否	
publishdateclean	发布时间		否	
regdateclean	立案时间		否	
idcard	证件号码		否	
courtname	执行法院		否	
gistid	执行依据文号		否	
casecode	案号		否	
gistunit	做出执行依据单位		否	
duty	生效法律文书确定的义务		否	
performance	被执行人履行情况		否	
performedpart	已履行（元）		否	
unperformpart	未履行（元）		否	
disrupttypename	失信被执行人行为具体情形		否	
benfNm	法定代表人或者负责人姓名		否	
areanameclean	省份		否	
sexyclean	性别		否	
ageclean	年龄		否	
focusnumber	关注次数		否	
ysfzd	身份证原始发证地		否	
exitdate	退出日期		否	
casestate	案件状态		否	
Row
List

5、TYPE-1-5 主体或法人被执行风险

List（dataList1）   被执行人信息
Row
id	主键		否	
userName	被执行人姓名/名称		否	
casestate	案件状态		否	
execmoney	执行标的（元）		否	
regdateclean	立案时间		否	
courtname	执行法院		否	
casecode	案号		否	
type	失信人类型		否	
idcard	证件号码		否	
areanameclean	省份		否	
sexyclean	性别		否	
ageclean	年龄		否	
focusnumber	关注次数		否	
ysfzd	身份证原始发证地		否	
Row
List
List（dataList2）   关联被执行人信息
Row
id	主键		否	
userName	被执行人姓名/名称		否	
casestate	案件状态		否	
execmoney	执行标的（元）		否	
regdateclean	立案时间		否	
courtname	执行法院		否	
casecode	案号		否	
type	失信人类型		否	
idcard	证件号码		否	
areanameclean	省份		否	
sexyclean	性别		否	
ageclean	年龄		否	
focusnumber	关注次数		否	
ysfzd	身份证原始发证地		否	
Row
List

6、TYPE-1-6 主体海关行政处罚风险
List（dataList1）  
Row
id	主键		否	
cachar	案件性质		否	
entname	企业名称		否	
gaccid	海关注册编号		否	
pendate	处罚日期		否	
pendecno	行政处罚决定书编号		否	
benfNm	当事人		否	
Row
List

7、TYPE-1-7 主体行政处罚风险
List（dataList1）
Row
id	主键		否	
pendecno	决定书文号		否	
illegacttype	违法行为类型		否	
pencontent	行政处罚内容		否	
penauth	决定机关		否	
penauthCn	决定机关名称		否	
pendecissdate	处罚决定日期		否	
publicdate	公示日期			
Row
List

8、TYPE-1-8 主体纳税人违法风险
List（dataList1）  
Row
id	主键		否	
caseType	案件性质		否	
punishDetail	相关法律依据及税务处理处罚情况		否	
taxRegno	纳税人识别号		否	
finaDetail	负有直接责任的财务负责人概要信息		否	
brkDetail	主要违法事实		否	
legDetail	法人概要信息		否	
benfNm	纳税人登记名称		否	
ageDetail	有直接责任的中介机构概要信息		否	
Row
List

9、TYPE-2-1 主体经营状态风险
entstatus	经营状态		是	在营(开业)
吊销
注销
迁出
撤销
临时(个体工商户使用)
歇业
停业
其他
吊销，未注销
吊销，已注销

10、TYPE-2-2 主体经营异常风险
List（dataList1）企业异常名录信息
Row
id	主键		否	
entname	企业名称		否	
regno	注册号		否	
shxydm	统一社会信用代码		否	
indate	列入日期		否	
inreason	列入原因		否	
outreason	退出异常名录原因		否	
yrRegorg	列入机关名称		否	
ycRegorg	移出机关名称		否	
outdate	移出日期		否	
Row
List
List（dataList2）清算信息
Row
id	主键		否	
benfNm	债务承接人		否	
rmter	债权承接人		否	
legalNm	清算负责人		否	
ctctPsn	清算组成员		否	
addr	地址		否	
tel	联系电话		否	
ligst	清算完结情况		否	
ligenddate	清算完结日期		否	
Row
List

11、TYPE-2-3 受益人风险
List（dataList1）受益人风险信息
Row
id	主键		否	
name	受益人名称		否	
beneficiaryType	受益人类型：（1: 直接或间接控股人 、2: 人事财务控制人、 3: 法定代表人/负责人 、4: 高级管理人员、5: 其他类型）		否	
totalInvScale	总投资比例		否	
position	受益人职位列表描述		否	
Row
List
List（dataList2）受益所有人控制企业数量
Row
id	主键		否	
name	受益人名称		否	
controCount	受益所有人控制企业数量		否	
Row
List

12、TYPE-2-4 股权风险
List（dataList1）企业股权风险
Row
id	主键		否	
parentId	被执行人		否	
modifyid	股权变更		否	
userName	被执行人		否	
shaream	股权数额		否	
courtname	执行法院		否	
courtno	协助公示通知书文号		否	
status	股权冻结状态		否	
cur	币种		否	
List
Row
List（dataList2）企业股权风险明细
Row
id	主键		否	
parentId	被执行人		否	
execution	执行事项		否	
courtname	执行法院		否	
judgmentNo	执行裁定书文号		否	
exNoticeNo	协助执行通知书文号		否	
inameType	被执行人类型		否	
userName	被执行人		否	
idType	证件类型		否	
licenceType	证照类型		否	
idcard	证照编号		否	
marketName	被冻结股权所在市场主体名称		否	
registNo	被冻结股权所在市场主体注册号		否	
marketCreditCode	被冻结股权所在市场统一社会信用代码		否	
shaream	股权数额		否	
shareamUnit	股权数额单位		否	
cur	币种		否	
fperiod	冻结期限		否	
frofrom	冻结期限自		否	
froto	冻结期限至		否	
cfrofrom	续行冻结期限自		否	
cfroto	续行冻结期限至		否	
cfperiod	续行冻结期限		否	
publicdate	公示日期		否	
freezeFlag	股权冻结状态		否	
freezeDate	解冻日期		否	
expirationDate	失效日期		否	
expirationReason	失效原因		否	
Row
List

13、TYPE-2-5 票据逾期风险
List（dataList1）  
Row
id	主键		否	
acprNam	承兑人名称		否	
socCod	统一社会信用代码		否	
acptOrgTyp	机构类别		否	
regrDate	注册日期		否	
showDate	披露信息时间点日期		否	
acptOpactOrgNam	承兑人开户机构名称		否	
acptAmt	累计承兑发生额		否	
acptOver	承兑余额		否	
totlOdueAmt	累计逾期发生额		否	
odueOver	逾期余额		否	
billMeda	票据介质		否	
remk	系统备注		否	
entRemk
	企业备注		否	
relDate	披露日期		否	
Row
List

3.8.2.2 请求报文
以查询“TYPE-1-3 主体严重违法风险”为例：
<?xml version="1.0" encoding="GBK"?>
<stream>
     <action>SKFXCXMX</action>
	 <userName>用户代码</userName>
	 <custInfoName>企业名称</custInfoName>
	 <undSocCrCode>统一社会信用代码</undSocCrCode>
	 <riskType>TYPE-1-4</riskType>
</stream>

3.8.2.3 响应报文
以查询“TYPE-1-3 主体严重违法风险”为例：
<?xml version="1.0" encoding="GBK"?>
<stream>
     <status>AAAAAAA</status>
	 <statusText>交易成功</statusText>
	 <failReason></failReason>
	 <custInfoCd>企业编码</custInfoCd>
	 <custInfoName>企业名称</custInfoName>
	 <undSocCrCode>统一社会信用代码</undSocCrCode>
	 <rskAsesStat>风险评估状态</rskAsesStat>
	 <lastUdtTms>风险更新时间</lastUdtTms>
	 <riskResult>风险评估结果</riskResult>
	 <list name = "dataList1">
	   <row>
		<id>主键</id>
		<isonslf>是否本人(增加)</isonslf>
		<type>失信人类型</type>
		<userName>被执行人姓名/名称</userName>
		<publishdateclean>发布时间</publishdateclean>
		<regdateclean>立案时间</regdateclean>
		<idcard>证件号码</idcard>
		<courtname>执行法院</courtname>
		<gistid>执行依据文号</gistid>
		<casecode>案号</casecode>
		<gistunit>做出执行依据单位</gistunit>
		<duty>生效法律文书确定的义务</duty>
		<performance>被执行人履行情况</performance>
		<performedpart>已履行（元）</performedpart>
		<unperformpart>未履行（元）</unperformpart>
		<disrupttypename>失信被执行人行为具体情形</disrupttypename>
		<benfNm>法定代表人或者负责人姓名</benfNm>
		<areanameclean>省份</areanameclean>
		<sexyclean>性别</sexyclean>
		<ageclean>年龄</ageclean>
		<focusnumber>关注次数</focusnumber>
		<ysfzd>身份证原始发证地</ysfzd>
		<exitdate>退出日期</exitdate>
		<casestate>案件状态</casestate>
	   </row>
	 </list>
	 <list name = "dataList2">
	  <row>
	    <id>主键</id>
		<type>失信人类型:自然人，法人或其他组织</type>
		<userName>被执行人姓名/名称</userName>
		<publishdateclean>发布时间</publishdateclean>
		<regdateclean>立案时间</regdateclean>
		<idcard>证件号码</idcard>
		<courtname>执行法院</courtname>
		<gistid>执行依据文号</gistid>
		<casecode>案号</casecode>
		<gistunit>做出执行依据单位</gistunit>
		<duty>生效法律文书确定的义务</duty>
		<performance>被执行人履行情况</performance>
		<performedpart>已履行（元）</performedpart>
		<unperformpart>未履行（元）</unperformpart>
		<disrupttypename>失信被执行人行为具体情形</disrupttypename>
		<benfNm>法定代表人或者负责人姓名</benfNm>
		<areanameclean>省份</areanameclean>
		<sexyclean>性别</sexyclean>
		<ageclean>年龄</ageclean>
		<focusnumber>关注次数</focusnumber>
		<ysfzd>身份证原始发证地</ysfzd>
		<exitdate>退出日期</exitdate>
		<casestate>案件状态</casestate>
	  </row>
	</list>  
</stream>

3.9 供应链中心
3.9.1 排款供应链凭证查询ERP接口
请求代码：SKSVCHRS
接口说明：排款供应链凭证查询
接口使用须须知：
当输入账号进行查询时，系统需校验用户对票据所属的账号具有查询权限；
3.9.1.1参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码		是	标识要请求的接口，SKSVCHRS
userName	登录用户名		是	银企直联用户名
orderId	外部排款批次号/批次号/业务编号		否	
ocpTskNum	子任务编号		否	
startRecord	起始记录号		是	起始记录号
pageNumber	请求记录条数		是	请求记录条数，最大50
 startDate	排款开始时间		是	格式：YYYY-MM-dd,开始时间和结束时间范围不能超过180
endDate	排款结束时间		是	格式：YYYY-MM-dd,开始时间和结束时间范围不能超过180
Response
status	交易返回码		是	
statusText	交易返回信息		是	
totalRecords	总记录条数		否	交易成功时返回，返回该登陆用户具有查询权限的排款供应链凭证数量
returnRecords	返回记录条数		否	交易成功时返回，返回该登陆用户本次查询获取到的排款供应链凭证数量
List
Row
fndarSrcSysJrnId	外部排款批次号	VARCHAR(50)	否	推送的原单据外部排款流水号
fndarMgmtId	批次号	VARCHAR(20)	否	批次号
fddDocId	业务编号	VARCHAR(20)	否	业务编号
ocpTskNum	子任务编号	VARCHAR(300)	否	在司库结算做排期操作后生成的子任务编号
settlementMode	结算方式	CHAR(4)	否	结算方式：OC供应链凭证支付
owninstNm	所属公司	VARCHAR(300)	杏	持有人名称
vchrTpNm	凭证类别	VARCHAR(300)	否	分为迪信、航信、金单等字符最大
长度100个字符
vchrStat	凭证状态	CHAR(2)	否	下拉选择：枚举值：
A1 转让
A2 提示付款
A3 融资
A4 质押
A5 持有
acptSrlnum	接收序号	VARCHAR(20)	否	企业收取供应链凭证登记的序号，
不超过100位，例如：010108 ，字母、
数字、符号
rcvpyDt	收款日期	DATE()	否	格式yyyy-MM-dd
chrCustNm	客户名称（前手）	VARCHAR(300)	否	
vchrAmt	凭证金额	BigDecimal(17,2)	否	精确度17.2（共17位，2位小数）
vchrSrlnum	凭证号码	VARCHAR(300)	咨	不超过100位，例如：010108，字
母、数字、符号
estbDt	开立日期	DATE()	否	格式yyyy-B-dd
expDt	到期日期	DATE()	否	格式 yyyy-MM-dd
extbComNm	开立方名称	VARCHAR(300)	否	
extbBnkNm	开立方银行	VARCHAR(300)	否	
prmsPypartyNm	承诺付款方名称	VARCHAR(300)	否	
tfroutSrInum	转出序号	VARCHAR(300)	否	不超过100位，例如：010108,
字母、数字、符号
pyDt	付款日期	DATE()	否	格式yyyy-MM-dd
rcvCustNm	凭证去向	VARCHAR(300)	否	接收方客户名称
nowVchrNum	现凭证号	VARCHAR(300)	否	新的供应链凭证编号，不超过100位，例如：010108，字母、数字、符号
vchrTfrAmt	凭证转让金额	BigDecimal()	否	单位：元，精确度17.2（共17位，2位小数）
fncAmt	融资金额	BigDecimal(17,2)	否	单位：元，精确度17.2（共17位，2位小数）
intrate	利率	BigDecimal(9,7)	否	百分比展示，精确度9.7(2位整数、7位小数）如0.2%.0.00002%
Row
List
3.9.1.2请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
	<action>SKSVCHRS</action>
	<userName></userName>
	<orderId></orderId>
	<ocpTskNum></ocpTskNum>
<startDate></startDate>
<endDate></endDate>
	<startRecord></startRecord>
	<pageNumber></pageNumber>
</stream>
3.9.1.3响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status></status>
    <statusText></statusText>
    <totalRecords></totalRecords>
<returnRecords></returnRecords>
 <list name="dataList">
      <row>
<fndarSrcSysJrnId ></fndarSrcSysJrnId >
<fndarMgmtId></fndarMgmtId>
<fdDocId></fdDocId>
<ocpTskNum></ocpTskNum>
<settlementMode></settlementMode>
<owninstNm></owninstNm>
<vchrTpNm></vchrTpNm>
<vchrStat></vchrStat>
<acptSrlnum></acptSrlnum>
<rcvpyDt></rcvpyDt>
<chrCustNm></chrCustNm>
<vchrAmt></vchrAmt>
<vchrSrlnum></vchrSrlnum>
<estbDt></estbDt>
<expDt></expDt>
<extbComNm></extbComNm>
<extbBnkNm></extbBnkNm>
<prmsPypartyNm></prmsPypartyNm>
<tfroutSrInum></tfroutSrInum>
<pyDt></pyDt>
<rcvCustNm></rcvCustNm>
<nowVchrNum></nowVchrNum>
<vchrTfrAmt></vchrTfrAmt>
<fncAmt></fncAmt>
<intrate></intrate>
 </row>
</list> 
</stream>
3.9.2 供应链凭证登记信息查询ERP接口
3.9.2.1 参数说明
字段标识	字段名	字段类型	是否必输	字段描述
Request
action	交易码		是	标识要请求的接口，SKSVCINF
userName	登录用户名		是	银企直联用户名
owninstNm	所属公司		否	机构名称
vchrSrlnum	凭证号码		否	
vchrStat	凭证状态		否	A1 转让 A2 提示付款 A3 融资 A4 质押A5 持有
vchrTp	凭证类别		否	
vchrAmtMin	凭证金额（小）		否	必须时金额，小于等于凭证金额（大）
vchrAmtMax	凭证金额（大）		否	必须时金额，大于等于凭证金额（小）
estbDtStart	开立日期（开始）		否	格式：YYYY-MM-dd,开始时间小于等于结束时间
estbDtEnd	开立日期（结束）		否	格式：YYYY-MM-dd,开始时间小于等于结束时间
expDtStart	到期日期（开始）		否	格式格式：YYYY-MM-dd,开始时间小于等于结束时间
expDtEnd	到期日期（结束）		否	格式格式：YYYY-MM-dd,开始时间小于等于结束时间
rcvpyDtStart	收款日期（开始）		否	格式格式：YYYY-MM-dd,开始时间小于等于结束时间
rcvpyDtEnd	收款日期（结束）		否	格式格式：YYYY-MM-dd,开始时间小于等于结束时间
pyDtStart	付款日期（开始）		否	格式格式：YYYY-MM-dd,开始时间小于等于结束时间
pyDtEnd	付款日期（结束）		否	格式格式：YYYY-MM-dd,开始时间小于等于结束时间
estbComNm	开立方名称		否	
rcvCustNm	凭证去向		否	
ocpTskNum	排款子任务编号		否	
bsnStat	流程状态		否	A2 处理中A3 处理成功 A4 处理失败
startRecord	起始记录号		是	起始记录号
pageNumber	请求记录条数		是	请求记录条数，最大50
Response
status	交易返回码		是	
statusText	交易返回信息		是	
totalRecords	总记录条数		否	交易成功时返回，返回该登陆用户具有查询权限的排款供应链凭证数量
returnRecords	返回记录条数		否	交易成功时返回，返回该登陆用户本次查询获取到的排款供应链凭证数量
List
Row
owninstNm	所属公司	VARCHAR(300)	杏	持有人名称
ocpTskNum	业务编号	VARCHAR(20)		
vchrTpNm	凭证类别名称	VARCHAR(300)	否	分为迪信、航信、金单等字符最大
长度100个字符
vchrTp	凭证类别编码	VARCHAR(20)	否	
vchrStat	凭证状态	CHAR(2)	否	下拉选择：枚举值：
A1 转让
A2 提示付款
A3 融资
A4 质押
A5 持有
acptSrlnum	接收序号	VARCHAR(20)	否	企业收取供应链凭证登记的序号，
不超过100位，例如：010108 ，字母、
数字、符号
rcvpyDt	收款日期	DATE()	否	格式yyyy-MM-dd
chrCustNm	客户名称（前手）	VARCHAR(300)	否	
vchrAmt	凭证金额	BigDecimal(17,2)	否	精确度17.2（共17位，2位小数）
vchrSrlnum	凭证号码	VARCHAR(300)	咨	不超过100位，例如：010108，字
母、数字、符号
estbDt	开立日期	DATE()	否	格式yyyy-B-dd
expDt	到期日期	DATE()	否	格式 yyyy-MM-dd
extbComNm	开立方名称	VARCHAR(300)	否	
extbBnkNm	开立方银行	VARCHAR(300)	否	
prmsPypartyNm	承诺付款方名称	VARCHAR(300)	否	
tfroutSrInum	转出序号	VARCHAR(300)	否	不超过100位，例如：010108,
字母、数字、符号
pyDt	付款日期	DATE()	否	格式yyyy-MM-dd
rcvCustNm	凭证去向	VARCHAR(300)	否	接收方客户名称
nowVchrNum	现凭证号	VARCHAR(300)	否	新的供应链凭证编号，不超过100位，例如：010108，字母、数字、符号
vchrTfrAmt	凭证转让金额	BigDecimal()	否	单位：元，精确度17.2（共17位，2位小数）
fncAmt	融资金额	BigDecimal(17,2)	否	单位：元，精确度17.2（共17位，2位小数）
intrate	利率	BigDecimal(9,7)	否	百分比展示，精确度9.7(2位整数、7位小数）如0.2%.0.00002%
mnpltTp	操作类型	CHAR(2)		A1 新增 A2 修改 A3 排款支付登记
bsnStat	流程状态	CHAR(2)		A2 处理中 A3 处理成功 A4 处理失败
ocpTskNum	排款子任务编号	VARCHAR(300)		
isOcp	是否已占用	CHAR(1)		0 未占用 1 已占用
Row
List
3.9.2.2 请求报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<action></action>>
<userName></userName>
<owninstNm></owninstNm>
<vchrSrlnum></vchrSrlnum>
<vchrStat></vchrStat>
<vchrTp></vchrTp>
<vchrAmtMin></vchrAmtMin>
<vchrAmtMax></vchrAmtMax>
<estbDtStart></estbDtStart>
<estbDtEnd></estbDtEnd>
<expDtStart></expDtStart>
<expDtEnd></expDtEnd>
<rcvpyDtStart></rcvpyDtStart>
<rcvpyDtEnd></rcvpyDtEnd>
<pyDtStart></pyDtStart>
<pyDtEnd></pyDtEnd>
<estbComNm></estbComNm>
<rcvCustNm></rcvCustNm>
<ocpTskNum></ocpTskNum>
<bsnStat></bsnStat>
<startRecord></startRecord>
<pageNumber></pageNumber>
</stream>
3.9.2.3 响应报文
<?xml version="1.0" encoding="GBK"?>
<stream>
<status></status>
    <statusText></statusText>
    <totalRecords></totalRecords>
<returnRecords></returnRecords>
 <list name="dataList">
      <row>
<owninstNm></owninstNm>
<ocpTskNum></ocpTskNum>
<vchrTpNm></vchrTpNm>
<vchrTp></vchrTp>
<vchrStat></vchrStat>
<acptSrlnum></acptSrlnum>
<rcvpyDt></rcvpyDt>
<chrCustNm></chrCustNm>
<vchrAmt></vchrAmt>
<vchrSrlnum></vchrSrlnum>
<estbDt></estbDt>
<expDt></expDt>
<extbComNm></extbComNm>
<extbBnkNm></extbBnkNm>
<prmsPypartyNm></prmsPypartyNm>
<tfroutSrInum></tfroutSrInum>
<pyDt></pyDt>
<rcvCustNm></rcvCustNm>
<nowVchrNum></nowVchrNum>
<vchrTfrAmt></vchrTfrAmt>
<fncAmt></fncAmt>
<intrate></intrate>
<mnpltTp></mnpltTp>
<bsnStat></bsnStat>
<ocpTskNum></ocpTskNum>
<isOcp></isOcp>
 </row>
</list> 
</stream>

第四章 支付对账机制
 
天元司库支持对部分银行的支付任务、银行流水明细及回单建立匹配关系，具体实现如下：
①　调用单笔付款接口时ERP系统传入唯一的外部请求流水号；调用批量付款接口时ERP系统传入唯一的外部请求批次号，每笔支付事项都有唯一的外部请求流水号；
②　在当日交易明细查询、账户历史明细结果查询接口的输出中，包含了之前单笔付款接口、批量付款接口所传入的外部请求流水号、外部请求批次号（批量支付时包含），从而可建立支付任务与银行流水的匹配关系；
③　电子回单查询接口的输出中，包含了之前单笔付款接口、批量付款接口所传入的外部请求流水号、外部请求批次号（批量支付时包含），从而可建立支付任务、银行流水与银行回单的匹配关系；
④　若支付任务未从天元司库发起，仍可通过当日交易明细查询、账户历史明细结果查询接口输出中的“银行流水号”与电子回单查询接口输出中的“银行流水号”进行匹配，建立银行流水与回单的匹配关系。
若对方银行不支持该对账机制，则当日交易明细查询、账户历史明细结果查询及电子回单查询接口中的外部请求流水号、外部请求批次号为空。

第五章 附录
5.1 制单状态
制单状态	状态描述	状态说明
		

5.2 交易状态
编码	名称	说明
AAAAAAA	交易成功	成功终态
BBBBBBB	批次处理部分成功；	交易终态
CCCCCCC	交易处理中；	非终态
MEQF001	交易处理失败	失败终态
AAAAAAR	部分交易成功	数据查询时部分查询成功
余下均为权限和校验类错误	本次交易参数或数据错误
UNKNOWN	交易状态未知	
SE01XXX	账户类	
SE02XXX	结算类	
SE02001	失败	
SE02002	删除	ERP推送司库数据被经办用户删除后返回（单笔/批量付款）
SE99XXX	其他	
SEO1100	XXX银行暂不支持当日回单	若申请的银行不支持当日回单查询时，则返回该错误码
5.3 币种标识
编码	名称
CNY	人民币
USD	美元
	

5.4 直联银行标识
银行标识	银行名称
NJCB	南京银行
CSK	招商银行
ICBC	中国工商银行
TCCB	天津银行
PZHCB	攀枝花市商业银行
HKB	汉口银行
HB	华夏银行
ABC	中国农业银行
BOC	中国银行
CCB	中国建设银行
HCCB	杭州银行
CBHB	渤海银行
NBCB	宁波银行
ZZB	郑州银行
CSKC	中国民生银行
GDB	广东发展银行
CIB	兴业银行
JSB	江苏银行
CZB	浙商银行
CDB	国家开发银行
BCCB	北京银行
CITIC	中信银行
BCM	交通银行
SPDB	上海浦东发展银行
PSBC	中国邮政储蓄银行
JSHB	晋商银行
QDCY	青岛城阳农村合作银行
HSBC	汇丰银行
BCS	长沙银行
UCCB	乌鲁木齐市商业银行
FUDIAN	富滇银行
BOCD	成都银行
FJHX	福建海峡银行
TZB	台州银行
HFB	恒丰银行
CQCB	重庆银行
SCB	渣打银行
ANZ	澳新银行
BOA	美国银行
CEB	光大银行
SZDB	平安银行
JPM	摩根银行
BOS	上海银行
XMCCB	厦门银行
HSHB	徽商银行
ZRCB	张家港商业农村银行
BJRCB	北京农村商业银行
LZB	兰州银行
BBG	北部湾银行
BEA	东亚银行
MIB	瑞穗实业银行
WZCB	温州银行
CRBC	华润银行
MUFG	三菱银行
GDRC	广东省农村信用社联合社
ZJRCB	紫金农商银行
XIB	厦门国际银行
BSB	包商银行
GLB	桂林银行
HRBCB	哈尔滨银行
ZJKB	张家口银行
QJCCB	曲靖市商业银行
LJB	龙江银行
SJB	盛京银行
YNRCC	云南农信社
GSB	甘肃银行
HRXJB	华融湘江银行
WHRCB	武汉农商行
MYB	网商银行
XACB	西安银行
FJNX	福建农村信用社联合社
WJRCB	苏州农村商业银行
ZB	武汉众邦银行
SZB	苏州银行
CAB	长安银行
OCBC	华侨银行
GZCB	广州银行
CZCB	稠州商行银行
SJZY	三井住友
5.5 交易类接口请求代码
编号	交易名称	交易请求代码
		
5.6 支持对账银行范围
银行名称
招商银行
浦发银行
宁波银行
交通银行
兴业银行
光大银行
新疆农村信用社
农业银行
民生银行
华夏银行
平安银行
青岛银行
富邦华一
中原银行
上海银行
重庆农村商业银行
中国建设银行
中国工商银行
中国邮政储蓄银行
郑州银行
兰州银行
国家开发银行
进出口银行
兴业银行
中国银行
徽商银行
江南农商行
顺德农商行
支付宝
哈密城商行
新疆银行
乌鲁木齐银行
云南农村信用社
泰隆银行
富滇银行
青海银行
厦门银行
吉林银行
上海农商行
甘肃农信社
渤海银行
南京银行
广发银行
5.7 支持历史余额银行范围
银行名称
招商银行
浦发银行
宁波银行
交通银行
兴业银行
光大银行
农业银行
民生银行
华夏银行
平安银行
建设银行
中国工商银行
中原银行
上海银行
重庆农村商业银行
广发银行
中国邮政储蓄银行
中信银行
长沙银行
富邦华一
江苏银行

5.8 不同付方银行支持附言长度
银行名称	附言长度
中国农业银行	70
中国工商银行	60
中国银行	200
交通银行	150
中信银行	102
光大银行	128
中国邮政储蓄银行	40
上海浦东发展银行	60
华夏银行	120
上海银行	70
北京银行	52
浙商银行	60
汉口银行	60
绍兴银行	60
泉州银行	200
温州银行	64
青岛银行	60
富邦华一银行	60
郑州银行	100
恒丰银行	100
甘肃银行	60
兰州银行	30
日照银行	200
国家开发银行	300
齐鲁银行	40
浙江农信银行	40
嘉兴商业银行	40
新疆银行	20
沧州银行	30
东营银行	200
新疆维吾尔自治区农村信用社联合社	40
九江银行	60
乌鲁木齐市商业银行	20
富滇银行	30
江西银行	60
张家港商业农村银行	200
山东省农村信用社联合社	80
威海银行	200
汇丰银行	60
甘肃省农信社	102
贵阳银行	22
临商银行	200
吉林银行	60
厦门银行	256
重庆农村商业银行	60
上海农商银行	40
重庆三峡银行	60
支付宝	200
江西农商行	60
苏州银行	33
顺德信用社	30
深圳农村商业银行	90
河北银行	30
韩亚银行(中国)有限公司	200
金华银行股份有限公司	60
福建农商行	60
浦发硅谷银行股份有限公司	30
西藏银行	100
厦门国际银行	255
东亚银行	30
泰隆银行	60
宁夏银行	60
渤海银行	60
宜宾市商业银行	70
平安银行	100
兴业银行	255
中国民生银行	150
招商银行	300
重庆银行	80
天津滨海农村商业银行	40
乐山市商业银行	60
创兴银行	100
南洋商业银行（中国）有限公司	80
苏州农村商业银行	40
江苏银行	180
中原银行	30
天津银行	200
成都银行	50
泸州银行	128
成都农商银行	50
南京银行	60
安徽无为农村商业银行	120
浙江民泰商业银行股份有限公司	135
江南农商行	40
湖南银行	60
芜湖扬子农村商业银行	120
华润银行	100
合肥科技农村商业银行	120
安徽肥西农村商业银行	120
安徽肥东农村合作银行	120
安徽长丰农村商业银行	120
西安银行	30
杭州银行	200
山西农村商业联合银行股份有限公司	30
中国建设银行	200
宁波银行	64
广东发展银行	64
宁波通商银行	120
长沙银行	200
徽商银行	126
紫金农商银行	32
浙江农信社	40
杭州联合农商银行	40
瑞丰农村商业银行	40
禾城农村商业银行	40
青海银行股份有限公司	60
重庆富民银行股份有限公司	25
潍坊银行	200
内蒙古银行	200
渣打银行	140


5.9 银行编码信息和区域编码信息
 
5.10 支持薪酬代发银行范围
银行名称	支持模式	单批最大笔数
中信银行	仅同行	1000笔/批
招商银行	支持同行、跨行	1000笔/批
浦发银行	仅同行	1000笔/批
农业银行	仅同行	1000笔/批
中国银行	支持同行、跨行	1000笔/批
建设银行	支持同行、跨行	1000笔/批
交通银行	支持同行、跨行	500笔/批
齐鲁银行	仅同行	1000笔/批
中原银行	仅同行	1000笔/批
上海农商银行	仅同行	1000笔/批
广州农商银行	仅同行	1000笔/批
浙江农商银行	仅同行	1000笔/批
平安银行	支持同行、跨行	1000笔/批
兴业银行	支持同行、跨行	1000笔/批
中国工商银行	支持同行、跨行	1000笔/批
光大银行	支持同行、跨行	1000笔/批
民生银行	支持同行、跨行	1000笔/批
广发银行	支持同行、跨行	1000笔/批
渤海银行	仅同行	1000笔/批
华夏银行	支持同行、跨行	1000笔/批
威海银行	支持同行	1000笔/批
重庆银行	支持同行、跨行	1000笔/批
郑州银行	支持同行	1000笔/批
无锡农村商业银行	支持本行代发	1000笔/批
山西农信社	支持同行、跨行	1000笔/批
西宁农商行	支持本行代发	1000笔/批
徽商银行	支持本行代发	500笔/批
西安银行	支持本行代发	1000笔/批

河北银行	支持本行代发	1000笔/批
5.11 薪酬代发银行代发项目、代发用途码表
代发银行	类别名称	子项编码	子项名称	数据字典编码	数据字典名称
建设银行	代发项目	公共中心-数据字典-结算中心数据字典中自行维护	CCB_PROJECT	建行代发项目
	代发用途	公共中心-数据字典-结算中心数据字典中自行维护	CCB_USE	建行代发用途
交通银行	协议编号	公共中心-数据字典-结算中心数据字典中自行维护	BCM_AGRM_ID	交行协议编号
	签约类型	1	报销差旅费	BCM_SIGN_TP	交行签约类型
		0	代发工资	BCM_SIGN_TP	交行签约类型
		3	奖金	BCM_SIGN_TP	交行签约类型
		4	保费给付	BCM_SIGN_TP	交行签约类型
		5	保险收益	BCM_SIGN_TP	交行签约类型
		6	理财收益	BCM_SIGN_TP	交行签约类型
		7	期权费	BCM_SIGN_TP	交行签约类型
		8	基金分红	BCM_SIGN_TP	交行签约类型
		9	消费积分中奖	BCM_SIGN_TP	交行签约类型
		A	劳保费	BCM_SIGN_TP	交行签约类型
		B	退休金	BCM_SIGN_TP	交行签约类型
		C	工会补助	BCM_SIGN_TP	交行签约类型
		D	福利费	BCM_SIGN_TP	交行签约类型
		E	节日慰问金	BCM_SIGN_TP	交行签约类型
		G	补贴	BCM_SIGN_TP	交行签约类型
		H	交通补贴	BCM_SIGN_TP	交行签约类型
		J	伙食补贴	BCM_SIGN_TP	交行签约类型
		K	津贴	BCM_SIGN_TP	交行签约类型
		L	养老金	BCM_SIGN_TP	交行签约类型
		M	失业救济金	BCM_SIGN_TP	交行签约类型
		N	房屋拆迁补偿款	BCM_SIGN_TP	交行签约类型
		O	代发省公积金	BCM_SIGN_TP	交行签约类型
		P	代发市公积金	BCM_SIGN_TP	交行签约类型
		Q	省财政统发工资	BCM_SIGN_TP	交行签约类型
		R	市财政统发工资	BCM_SIGN_TP	交行签约类型
		S	代发其他款项	BCM_SIGN_TP	交行签约类型
		T	代发交通费	BCM_SIGN_TP	交行签约类型
		U	代发公积金	BCM_SIGN_TP	交行签约类型
		V	代发奖学金	BCM_SIGN_TP	交行签约类型
		W	代发补助	BCM_SIGN_TP	交行签约类型
		X	房屋补贴	BCM_SIGN_TP	交行签约类型
		Y	福利彩票返奖	BCM_SIGN_TP	交行签约类型
		Z	医疗保险	BCM_SIGN_TP	交行签约类型
		a	基金赎回	BCM_SIGN_TP	交行签约类型
		b	保证金	BCM_SIGN_TP	交行签约类型
		c	期货保证金	BCM_SIGN_TP	交行签约类型
		d	理赔支付	BCM_SIGN_TP	交行签约类型
		e	退保支付	BCM_SIGN_TP	交行签约类型
		f	电子商务划款	BCM_SIGN_TP	交行签约类型
		g	劳务费	BCM_SIGN_TP	交行签约类型
		h	评审费	BCM_SIGN_TP	交行签约类型
		i	监考费	BCM_SIGN_TP	交行签约类型
		j	答辩费	BCM_SIGN_TP	交行签约类型
		k	信用卡还款	BCM_SIGN_TP	交行签约类型
		l	信用卡转账	BCM_SIGN_TP	交行签约类型
		m	银联返现	BCM_SIGN_TP	交行签约类型
		n	课时费	BCM_SIGN_TP	交行签约类型
		o	支付宝	BCM_SIGN_TP	交行签约类型
		p	支付宝代发	BCM_SIGN_TP	交行签约类型
		q	支付宝还款	BCM_SIGN_TP	交行签约类型
		r	支付宝提现	BCM_SIGN_TP	交行签约类型
浦发银行	代发用途	1001	其他代发	PDB_COSTLTEMCODE	浦发费项编码
		1002	代发工资	PDB_COSTLTEMCODE	浦发费项编码
		1030	报销款	PDB_COSTLTEMCODE	浦发费项编码
		1003	代发奖金	PDB_COSTLTEMCODE	浦发费项编码
兴业银行	代发用途	6	工资	CIB_USECODE	兴业代发工资用途
		22	奖金	CIB_USECODE	兴业代发工资用途
		47	福利	CIB_USECODE	兴业代发工资用途
		8	水费	CIB_USECODE	兴业代发工资用途
		7	电费	CIB_USECODE	兴业代发工资用途
		813	高温费	CIB_USECODE	兴业代发工资用途
		80	报刊费	CIB_USECODE	兴业代发工资用途
		48	费用报销	CIB_USECODE	兴业代发工资用途
		605	保险理赔	CIB_USECODE	兴业代发工资用途
		747	住房公积金	CIB_USECODE	兴业代发工资用途
		826	补偿金	CIB_USECODE	兴业代发工资用途
		a26	业务服务费	CIB_USECODE	兴业代发工资用途
		a27	经营所得	CIB_USECODE	兴业代发工资用途
		816	劳务收入	CIB_USECODE	兴业代发工资用途
		768	补贴	CIB_USECODE	兴业代发工资用途
		950	职业年金	CIB_USECODE	兴业代发工资用途
		b77	佣金	CIB_USECODE	兴业代发工资用途
工商银行	代发用途	1	工资	ICBC_USECODE	工行代发用途代码
		2	奖金	ICBC_USECODE	工行代发用途代码
		3	报销	ICBC_USECODE	工行代发用途代码
		4	补贴	ICBC_USECODE	工行代发用途代码
		5	社保	ICBC_USECODE	工行代发用途代码
		6	贷款	ICBC_USECODE	工行代发用途代码
		7	佣金	ICBC_USECODE	工行代发用途代码
		8	租金	ICBC_USECODE	工行代发用途代码
		9	稿费	ICBC_USECODE	工行代发用途代码
		10	公积金	ICBC_USECODE	工行代发用途代码
		11	养老金	ICBC_USECODE	工行代发用途代码
		12	助学金	ICBC_USECODE	工行代发用途代码
		13	劳务费	ICBC_USECODE	工行代发用途代码
		14	演出费	ICBC_USECODE	工行代发用途代码
		15	福利费	ICBC_USECODE	工行代发用途代码
		16	代返还	ICBC_USECODE	工行代发用途代码
		17	企业年金	ICBC_USECODE	工行代发用途代码
		18	保险理赔	ICBC_USECODE	工行代发用途代码
		19	保费退还	ICBC_USECODE	工行代发用途代码
		20	付保险款	ICBC_USECODE	工行代发用途代码
		21	副食补贴	ICBC_USECODE	工行代发用途代码
		22	纳税退还	ICBC_USECODE	工行代发用途代码
		23	基金撤销	ICBC_USECODE	工行代发用途代码
		24	基金赎回	ICBC_USECODE	工行代发用途代码
		25	基金分红	ICBC_USECODE	工行代发用途代码
		26	员工安家费	ICBC_USECODE	工行代发用途代码
		27	校内奖学金	ICBC_USECODE	工行代发用途代码
		28	国家助学金	ICBC_USECODE	工行代发用途代码
		29	励志奖学金	ICBC_USECODE	工行代发用途代码
		30	保险到期还款	ICBC_USECODE	工行代发用途代码
		31	个人贷款转存	ICBC_USECODE	工行代发用途代码
		32	期货交易保证金	ICBC_USECODE	工行代发用途代码
		33	继承或赠与款项	ICBC_USECODE	工行代发用途代码
		34	证卷交易结算资金	ICBC_USECODE	工行代发用途代码
		35	个人小件商品付款	ICBC_USECODE	工行代发用途代码
		36	债卷投资本金和收益	ICBC_USECODE	工行代发用途代码
		37	信托投资本金和收益	ICBC_USECODE	工行代发用途代码
		38	期货投资本金和收益	ICBC_USECODE	工行代发用途代码
		39	其他投资本金和收益	ICBC_USECODE	工行代发用途代码
		40	农、副、矿产品收购款	ICBC_USECODE	工行代发用途代码
		41	个人债权或产权转让收益	ICBC_USECODE	工行代发用途代码
		42	服务费	ICBC_USECODE	工行代发用途代码
		43	养老保险	ICBC_USECODE	工行代发用途代码
		44	失业保险	ICBC_USECODE	工行代发用途代码
		45	工伤保险	ICBC_USECODE	工行代发用途代码
		46	医保代发	ICBC_USECODE	工行代发用途代码
		47	运输费	ICBC_USECODE	工行代发用途代码
		48	工会费	ICBC_USECODE	工行代发用途代码
		49	外服工资	ICBC_USECODE	工行代发用途代码
		50	集团内付款	ICBC_USECODE	工行代发用途代码
		51	遗属生活费	ICBC_USECODE	工行代发用途代码
		52	风沙费	ICBC_USECODE	工行代发用途代码
		53	调节金	ICBC_USECODE	工行代发用途代码
		54	培训费	ICBC_USECODE	工行代发用途代码
		55	独生子女费	ICBC_USECODE	工行代发用途代码
		56	押金	ICBC_USECODE	工行代发用途代码
		57	咨询费	ICBC_USECODE	工行代发用途代码
		58	慰问金	ICBC_USECODE	工行代发用途代码
		59	劳模津贴	ICBC_USECODE	工行代发用途代码
		60	体检费	ICBC_USECODE	工行代发用途代码
		61	补助	ICBC_USECODE	工行代发用途代码
		62	报刊费	ICBC_USECODE	工行代发用途代码
		63	电话费	ICBC_USECODE	工行代发用途代码
		64	交通费	ICBC_USECODE	工行代发用途代码
招商银行	代发用途	AYHS	代扣房租	CMB_BUS_COD	招行代发业务类型
		BYSA	代发工资	CMB_BUS_COD	招行代发业务类型
		AYNT	代扣上网费	CMB_BUS_COD	招行代发业务类型
		AYTX	代扣税费	CMB_BUS_COD	招行代发业务类型
		AYBK	代扣其他	CMB_BUS_COD	招行代发业务类型
		AYEL	代扣电费	CMB_BUS_COD	招行代发业务类型
		AYTV	代扣电视费	CMB_BUS_COD	招行代发业务类型
		AYWT	代扣水费	CMB_BUS_COD	招行代发业务类型
		AYTL	代扣电话费	CMB_BUS_COD	招行代发业务类型
		BYFD	代发信托返还资金	CMB_BUS_COD	招行代发业务类型
		BYXI	代发递延奖金	CMB_BUS_COD	招行代发业务类型
		BYXH	代发通讯费	CMB_BUS_COD	招行代发业务类型
		BYXG	代发交通费	CMB_BUS_COD	招行代发业务类型
		BYXF	代发差旅费	CMB_BUS_COD	招行代发业务类型
		AYGS	代扣煤气费	CMB_BUS_COD	招行代发业务类型
		BYXE	代发分红款	CMB_BUS_COD	招行代发业务类型
		BYXD	代发车贴	CMB_BUS_COD	招行代发业务类型
		BYXC	代发烤火费	CMB_BUS_COD	招行代发业务类型
		AYMT	代扣按揭费	CMB_BUS_COD	招行代发业务类型
		BYXB	代发住房公积金	CMB_BUS_COD	招行代发业务类型
		AYSW	代扣污水费	CMB_BUS_COD	招行代发业务类型
		BYXA	代发改制费	CMB_BUS_COD	招行代发业务类型
		AYPP	代扣物业管理费	CMB_BUS_COD	招行代发业务类型
		BYBK	代发其他	CMB_BUS_COD	招行代发业务类型
		BYBJ	代发农副品销售	CMB_BUS_COD	招行代发业务类型
		BYBI	代发纳税退还	CMB_BUS_COD	招行代发业务类型
		BYBH	代发继承赠与款	CMB_BUS_COD	招行代发业务类型
		BYBG	代发证券期货款	CMB_BUS_COD	招行代发业务类型
		BYBF	代发个人贷款	CMB_BUS_COD	招行代发业务类型
		BYBE	代发债产权转让	CMB_BUS_COD	招行代发业务类型
		BYBD	代发投资本益	CMB_BUS_COD	招行代发业务类型
		AYMC	代扣移动电话费	CMB_BUS_COD	招行代发业务类型
		BYBC	代发劳务费	CMB_BUS_COD	招行代发业务类型
		AYMB	代扣手机费	CMB_BUS_COD	招行代发业务类型
		AYSC	代扣学费	CMB_BUS_COD	招行代发业务类型
		BYWK	代发加班费	CMB_BUS_COD	招行代发业务类型
		BYTF	代发报销款	CMB_BUS_COD	招行代发业务类型
		AYCR	代扣出租车规费	CMB_BUS_COD	招行代发业务类型
		BYWF	代发福利费	CMB_BUS_COD	招行代发业务类型
		AYFS	代扣财产保险费	CMB_BUS_COD	招行代发业务类型
		AYIS	代扣保险费	CMB_BUS_COD	招行代发业务类型
		AYOW	代扣超计划用水费	CMB_BUS_COD	招行代发业务类型
		AYCN	代扣贷款利息	CMB_BUS_COD	招行代发业务类型
		AYLS	代扣人寿保险费	CMB_BUS_COD	招行代发业务类型
		AYCL	代扣清洁费	CMB_BUS_COD	招行代发业务类型
		AYLN	代扣委托贷款本息	CMB_BUS_COD	招行代发业务类型
		AYCF	代扣贷款本金	CMB_BUS_COD	招行代发业务类型
		BYSU	代发保险费	CMB_BUS_COD	招行代发业务类型
		BYSS	离岸代发工资	CMB_BUS_COD	招行代发业务类型
		AYRB	代扣垃圾费	CMB_BUS_COD	招行代发业务类型
		AYEW	代扣水电费	CMB_BUS_COD	招行代发业务类型
		AYBT	代扣批量扣费	CMB_BUS_COD	招行代发业务类型
		BYXN	综合代发款	CMB_BUS_COD	招行代发业务类型
平安银行	代发项目	公共中心-数据字典-结算中心数据字典中自行维护	SZDB_PROJECT	平安银行代发项目
	代发用途	5101	工资（全国大小额）	SZDB_BUSITYPE	平安银行代发费项代码
		5102	奖金（全国大小额）	SZDB_BUSITYPE	平安银行代发费项代码
		999	代发工资（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		51	薪金（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		895	劳务费（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		670	分红款（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		C50	代发佣金（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		C51	平安付款（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		4900	其他（全国大小额）	SZDB_BUSITYPE	平安银行代发费项代码
		99	其他代付（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		ZYL0332	分享奖励（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		ZYL0495	津贴（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		ZYL0655	个人养老金批量入金（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
中国银行	代发用途	E1	还款	BOC_BUS_CODE	中行代发用途代码
		E8	奖金	BOC_BUS_CODE	中行代发用途代码
		E9	缴费	BOC_BUS_CODE	中行代发用途代码
		EA	医保	BOC_BUS_CODE	中行代发用途代码
		EB	保险	BOC_BUS_CODE	中行代发用途代码
		EC	报销	BOC_BUS_CODE	中行代发用途代码
		ED	补偿	BOC_BUS_CODE	中行代发用途代码
		EE	补贴	BOC_BUS_CODE	中行代发用途代码
		EF	差旅	BOC_BUS_CODE	中行代发用途代码
		EG	冲正	BOC_BUS_CODE	中行代发用途代码
		EH	代付	BOC_BUS_CODE	中行代发用途代码
		EJ	电费	BOC_BUS_CODE	中行代发用途代码
		EK	电信	BOC_BUS_CODE	中行代发用途代码
		EL	短信	BOC_BUS_CODE	中行代发用途代码
		ES	房租	BOC_BUS_CODE	中行代发用途代码
		ET	分红	BOC_BUS_CODE	中行代发用途代码
		EU	个税	BOC_BUS_CODE	中行代发用途代码
		EV	工资	BOC_BUS_CODE	中行代发用途代码
		EW	公积	BOC_BUS_CODE	中行代发用途代码
		EZ	户费	BOC_BUS_CODE	中行代发用途代码
		F4	有线	BOC_BUS_CODE	中行代发用途代码
		FA	扣费	BOC_BUS_CODE	中行代发用途代码
		FB	扣划	BOC_BUS_CODE	中行代发用途代码
		FD	理财	BOC_BUS_CODE	中行代发用途代码
		FK	年费	BOC_BUS_CODE	中行代发用途代码
		FO	社保	BOC_BUS_CODE	中行代发用途代码
		FQ	水费	BOC_BUS_CODE	中行代发用途代码
		FR	税款	BOC_BUS_CODE	中行代发用途代码
		FZ	学费	BOC_BUS_CODE	中行代发用途代码
		GB	收费	BOC_BUS_CODE	中行代发用途代码
		GG	捐赠	BOC_BUS_CODE	中行代发用途代码
		G1	药费	BOC_BUS_CODE	中行代发用途代码
		K4	福利	BOC_BUS_CODE	中行代发用途代码
		G8	保费	BOC_BUS_CODE	中行代发用途代码
		GH	基养	BOC_BUS_CODE	中行代发用途代码
		KB	过节	BOC_BUS_CODE	中行代发用途代码
		KD	劳保	BOC_BUS_CODE	中行代发用途代码
		NC	低保	BOC_BUS_CODE	中行代发用途代码
		Ed	过渡	BOC_BUS_CODE	中行代发用途代码
		Ec	冬春	BOC_BUS_CODE	中行代发用途代码
		JF	退款	BOC_BUS_CODE	中行代发用途代码
		f9	借款	BOC_BUS_CODE	中行代发用途代码
		fa	贷款	BOC_BUS_CODE	中行代发用途代码
		IN	年金	BOC_BUS_CODE	中行代发用途代码
农业银行	代发用途	99020001	工资发放	ABC_BUSITYPE	农行银行代发费项代码
		99020002	费用报销	ABC_BUSITYPE	农行银行代发费项代码
光大银行	代发用途	0	代发工资	CEB_BUSITYPE	光大银行代发费项代码
		1	其他代发	CEB_BUSITYPE	光大银行代发费项代码
民生银行	代发用途	311	报销费用--差旅费	CMBC_BUSITYPE	民生银行代发费项代码
		312	报销费用--工资奖金	CMBC_BUSITYPE	民生银行代发费项代码
		313	报销费用--日常报销费用	CMBC_BUSITYPE	民生银行代发费项代码
		314	报销费用--日常经营费用	CMBC_BUSITYPE	民生银行代发费项代码
		315	报销费用--办公费	CMBC_BUSITYPE	民生银行代发费项代码
		316	报销费用--水电费	CMBC_BUSITYPE	民生银行代发费项代码
		317	报销费用--通讯费	CMBC_BUSITYPE	民生银行代发费项代码
		318	报销费用--交通费	CMBC_BUSITYPE	民生银行代发费项代码
		319	报销费用--报刊费	CMBC_BUSITYPE	民生银行代发费项代码
		341	报销费用--餐费	CMBC_BUSITYPE	民生银行代发费项代码
		342	报销费用--医药费	CMBC_BUSITYPE	民生银行代发费项代码
		343	报销费用--会议费	CMBC_BUSITYPE	民生银行代发费项代码
		344	个人投资本金和收益	CMBC_BUSITYPE	民生银行代发费项代码
		345	个人债券产权转让收益	CMBC_BUSITYPE	民生银行代发费项代码
		346	个人贷款转存	CMBC_BUSITYPE	民生银行代发费项代码
		347	证券结算金期货保证金	CMBC_BUSITYPE	民生银行代发费项代码
		348	个人继承、赠予款项	CMBC_BUSITYPE	民生银行代发费项代码
		349	保险理赔、保费退还	CMBC_BUSITYPE	民生银行代发费项代码
		374	个人纳税退还	CMBC_BUSITYPE	民生银行代发费项代码
		375	农副矿产品销售收入	CMBC_BUSITYPE	民生银行代发费项代码
		376	其他合法收入--律师费	CMBC_BUSITYPE	民生银行代发费项代码
		377	其他合法收入--资金退还	CMBC_BUSITYPE	民生银行代发费项代码
		378	其他合法收入--拆迁补偿款	CMBC_BUSITYPE	民生银行代发费项代码
		379	其他合法收入--垫付资金	CMBC_BUSITYPE	民生银行代发费项代码
		380	其他合法收入--柜台销售结算款	CMBC_BUSITYPE	民生银行代发费项代码
		381	其他合法收入--网上交易款	CMBC_BUSITYPE	民生银行代发费项代码
		382	其他合法收入--小件商品销售收入	CMBC_BUSITYPE	民生银行代发费项代码
		383	个人合法收入--奖助学金	CMBC_BUSITYPE	民生银行代发费项代码
		384	个人合法收入--退学费	CMBC_BUSITYPE	民生银行代发费项代码
		381	制片费	CMBC_BUSITYPE	民生银行代发费项代码
		385 	报销费用--日常报销费用 	CMBC_BUSITYPE	民生银行代发费项代码
		386  	报销费用--日常经营费用 	CMBC_BUSITYPE	民生银行代发费项代码
		388	案件款	CMBC_BUSITYPE	民生银行代发费项代码
		389	投资赎回款	CMBC_BUSITYPE	民生银行代发费项代码
		390	投资退款	CMBC_BUSITYPE	民生银行代发费项代码
		391	投资分红	CMBC_BUSITYPE	民生银行代发费项代码
		392	其他合法收入--工程款	CMBC_BUSITYPE	民生银行代发费项代码
		393	其他合法收入--租赁费	CMBC_BUSITYPE	民生银行代发费项代码
		394	其他合法收入--运费	CMBC_BUSITYPE	民生银行代发费项代码
		395	个人合法收入--公积金	CMBC_BUSITYPE	民生银行代发费项代码
		396	代收货款	CMBC_BUSITYPE	民生银行代发费项代码
渤海银行	代发用途	1	工资	BOHAIB_BUSITYPE	渤海银行代发费项代码
		2	奖金	BOHAIB_BUSITYPE	渤海银行代发费项代码
		3	津贴	BOHAIB_BUSITYPE	渤海银行代发费项代码
		4	劳动分红	BOHAIB_BUSITYPE	渤海银行代发费项代码
		5	劳务费	BOHAIB_BUSITYPE	渤海银行代发费项代码
		6	报销	BOHAIB_BUSITYPE	渤海银行代发费项代码
华夏银行	代发用途	2006	代发奖金	HXB_BUSITYPE	华夏银行代发费项代码
		2007	代发过节费	HXB_BUSITYPE	华夏银行代发费项代码
		2008	代发住房补贴	HXB_BUSITYPE	华夏银行代发费项代码
		2009	代发交通补贴	HXB_BUSITYPE	华夏银行代发费项代码
		2010	代发防暑降温费	HXB_BUSITYPE	华夏银行代发费项代码
		2011	代发物业费	HXB_BUSITYPE	华夏银行代发费项代码
		2012	代发取暖费	HXB_BUSITYPE	华夏银行代发费项代码
		2013	代发报销款	HXB_BUSITYPE	华夏银行代发费项代码
		2002	代发养老金	HXB_BUSITYPE	华夏银行代发费项代码
		2014	代发社保金	HXB_BUSITYPE	华夏银行代发费项代码
		2048	处室慰问金	HXB_BUSITYPE	华夏银行代发费项代码
		2015	代发保险金	HXB_BUSITYPE	华夏银行代发费项代码
		2016	代发公积金	HXB_BUSITYPE	华夏银行代发费项代码
		2017	代发拆迁款	HXB_BUSITYPE	华夏银行代发费项代码
		2018	代发加班费	HXB_BUSITYPE	华夏银行代发费项代码
		2003	其他代发	HXB_BUSITYPE	华夏银行代发费项代码
		0246	代发改制补偿金	HXB_BUSITYPE	华夏银行代发费项代码
		2037	代发误餐补贴	HXB_BUSITYPE	华夏银行代发费项代码
		2038	代发劳务费	HXB_BUSITYPE	华夏银行代发费项代码
		2039	代发晚夜班费	HXB_BUSITYPE	华夏银行代发费项代码
		2040	代发医药费	HXB_BUSITYPE	华夏银行代发费项代码
		2041	代发运输费	HXB_BUSITYPE	华夏银行代发费项代码
		2041	代发零星发放	HXB_BUSITYPE	华夏银行代发费项代码
		2047	代发模糊工资	HXB_BUSITYPE	华夏银行代发费项代码
广发银行	代发用途	ACBDA001	代发工资（汇总记账）	CGB_BUSITYPE	广发银行代发费项代码
		ACBDA002	费用报销（汇总记账）	CGB_BUSITYPE	广发银行代发费项代码
重庆银行（暂未上线）	代发用途	00100	电费	BOC_BUSITYPE	重庆银行代发费项代码
		00200	水暖费	BOC_BUSITYPE	重庆银行代发费项代码
		00300	煤气费	BOC_BUSITYPE	重庆银行代发费项代码
		00400	电话费	BOC_BUSITYPE	重庆银行代发费项代码
		00500	通讯费	BOC_BUSITYPE	重庆银行代发费项代码
		00600	保险费	BOC_BUSITYPE	重庆银行代发费项代码
		00700	房屋管理费	BOC_BUSITYPE	重庆银行代发费项代码
		00800	代理服务费	BOC_BUSITYPE	重庆银行代发费项代码
		00900	学教费	BOC_BUSITYPE	重庆银行代发费项代码
		01000	有线电视费	BOC_BUSITYPE	重庆银行代发费项代码
		01100	企业管理费用	BOC_BUSITYPE	重庆银行代发费项代码
		09001	其他	BOC_BUSITYPE	重庆银行代发费项代码
		01200	薪金报酬	BOC_BUSITYPE	重庆银行代发费项代码
		6201	代发工资	BOC_BUSITYPE	重庆银行代发费项代码
		6202	代发补贴	BOC_BUSITYPE	重庆银行代发费项代码
		6203	代发加班费	BOC_BUSITYPE	重庆银行代发费项代码
		6204-	代发节日费	BOC_BUSITYPE	重庆银行代发费项代码
		6205	代发奖金	BOC_BUSITYPE	重庆银行代发费项代码
		6206	代发报销费	BOC_BUSITYPE	重庆银行代发费项代码
		6207	代发奖励	BOC_BUSITYPE	重庆银行代发费项代码
		6208	代发赔偿费	BOC_BUSITYPE	重庆银行代发费项代码
		6209	代发劳保费	BOC_BUSITYPE	重庆银行代发费项代码
		6210	代发其他	BOC_BUSITYPE	重庆银行代发费项代码
		6211	报销车旅费	BOC_BUSITYPE	重庆银行代发费项代码
		6212	报销医药费	BOC_BUSITYPE	重庆银行代发费项代码
		6213	报销其他费用	BOC_BUSITYPE	重庆银行代发费项代码
		6214	代发劳务费	BOC_BUSITYPE	重庆银行代发费项代码
		6215	代发酬金	BOC_BUSITYPE	重庆银行代发费项代码
		6216	代发课时费	BOC_BUSITYPE	重庆银行代发费项代码
		6217	代发教学科研津贴	BOC_BUSITYPE	重庆银行代发费项代码
		6218	代发电话费补贴	BOC_BUSITYPE	重庆银行代发费项代码
		6219	代发交通补贴	BOC_BUSITYPE	重庆银行代发费项代码
		6220	代发稿酬	BOC_BUSITYPE	重庆银行代发费项代码
		6221	代发津贴	BOC_BUSITYPE	重庆银行代发费项代码
		6222	代发监考费	BOC_BUSITYPE	重庆银行代发费项代码
		6223	代发巡考费	BOC_BUSITYPE	重庆银行代发费项代码
郑州银行（暂未上线）	代发项目	
公共中心-数据字典-结算中心数据字典中自行维护

	ZZB_PROJECT	郑州银行代发项目
无锡农村商业银行【技术上线】（暂未上线）	代发用途	M1	工资	RCB_PROJECT	无锡农村商业银行代发费项代码
		JJ	奖金	RCB_PROJECT	无锡农村商业银行代发费项代码
		JB	加班费	RCB_PROJECT	无锡农村商业银行代发费项代码
		BX	报销	RCB_PROJECT	无锡农村商业银行代发费项代码
		ZZ	其他	RCB_PROJECT	无锡农村商业银行代发费项代码
山西农信社	代发用途	1	代发工资	RCU_PROJECT	山西农信社代发费项代码
		2	费用报销	RCU_PROJECT	山西农信社代发费项代码
西宁农商行【技术上线】（暂未上线）	代发用途	01	代发工资   		西宁农商行代发费项代码
		02	代发补贴		西宁农商行代发费项代码
		03	代发其他		西宁农商行代发费项代码
徽商银行	代发项目	
公共中心-数据字典-结算中心数据字典中自行维护

	HSHB__PROJECT	徽商银行代发项目
	代发用途 	1001	代发工资	HSHB_BUSITYPE	徽商银行代发用途
		1002	费用报销 	HSHB_BUSITYPE	徽商银行代发用途
中信银行	代发用途	1	代发工资		
		2	代发奖金		
		3	代发补贴		
		4	代发福利费		
		5	代发报销款		
		6	代发拆迁款		
		7	代发其他		
		9	企业年金		
		10	代发养老金		
		11	代发劳务费		
		12	代发奖学金		
		13	代发加班费		
		14	代发差旅费		
		15	代发交通费		
		16	代发通讯费		
		17	代发社保金		
		18	代发车贴		
		19	代发资金		
代发银行	类别名称	子项编码	子项名称	数据字典编码	数据字典名称
建设银行	代发项目	公共中心-数据字典-结算中心数据字典中自行维护	CCB_PROJECT	建行代发项目
	代发用途	公共中心-数据字典-结算中心数据字典中自行维护	CCB_USE	建行代发用途
交通银行	协议编号	公共中心-数据字典-结算中心数据字典中自行维护	BCM_AGRM_ID	交行协议编号
	签约类型	1	报销差旅费	BCM_SIGN_TP	交行签约类型
		0	代发工资	BCM_SIGN_TP	交行签约类型
		3	奖金	BCM_SIGN_TP	交行签约类型
		4	保费给付	BCM_SIGN_TP	交行签约类型
		5	保险收益	BCM_SIGN_TP	交行签约类型
		6	理财收益	BCM_SIGN_TP	交行签约类型
		7	期权费	BCM_SIGN_TP	交行签约类型
		8	基金分红	BCM_SIGN_TP	交行签约类型
		9	消费积分中奖	BCM_SIGN_TP	交行签约类型
		A	劳保费	BCM_SIGN_TP	交行签约类型
		B	退休金	BCM_SIGN_TP	交行签约类型
		C	工会补助	BCM_SIGN_TP	交行签约类型
		D	福利费	BCM_SIGN_TP	交行签约类型
		E	节日慰问金	BCM_SIGN_TP	交行签约类型
		G	补贴	BCM_SIGN_TP	交行签约类型
		H	交通补贴	BCM_SIGN_TP	交行签约类型
		J	伙食补贴	BCM_SIGN_TP	交行签约类型
		K	津贴	BCM_SIGN_TP	交行签约类型
		L	养老金	BCM_SIGN_TP	交行签约类型
		M	失业救济金	BCM_SIGN_TP	交行签约类型
		N	房屋拆迁补偿款	BCM_SIGN_TP	交行签约类型
		O	代发省公积金	BCM_SIGN_TP	交行签约类型
		P	代发市公积金	BCM_SIGN_TP	交行签约类型
		Q	省财政统发工资	BCM_SIGN_TP	交行签约类型
		R	市财政统发工资	BCM_SIGN_TP	交行签约类型
		S	代发其他款项	BCM_SIGN_TP	交行签约类型
		T	代发交通费	BCM_SIGN_TP	交行签约类型
		U	代发公积金	BCM_SIGN_TP	交行签约类型
		V	代发奖学金	BCM_SIGN_TP	交行签约类型
		W	代发补助	BCM_SIGN_TP	交行签约类型
		X	房屋补贴	BCM_SIGN_TP	交行签约类型
		Y	福利彩票返奖	BCM_SIGN_TP	交行签约类型
		Z	医疗保险	BCM_SIGN_TP	交行签约类型
		a	基金赎回	BCM_SIGN_TP	交行签约类型
		b	保证金	BCM_SIGN_TP	交行签约类型
		c	期货保证金	BCM_SIGN_TP	交行签约类型
		d	理赔支付	BCM_SIGN_TP	交行签约类型
		e	退保支付	BCM_SIGN_TP	交行签约类型
		f	电子商务划款	BCM_SIGN_TP	交行签约类型
		g	劳务费	BCM_SIGN_TP	交行签约类型
		h	评审费	BCM_SIGN_TP	交行签约类型
		i	监考费	BCM_SIGN_TP	交行签约类型
		j	答辩费	BCM_SIGN_TP	交行签约类型
		k	信用卡还款	BCM_SIGN_TP	交行签约类型
		l	信用卡转账	BCM_SIGN_TP	交行签约类型
		m	银联返现	BCM_SIGN_TP	交行签约类型
		n	课时费	BCM_SIGN_TP	交行签约类型
		o	支付宝	BCM_SIGN_TP	交行签约类型
		p	支付宝代发	BCM_SIGN_TP	交行签约类型
		q	支付宝还款	BCM_SIGN_TP	交行签约类型
		r	支付宝提现	BCM_SIGN_TP	交行签约类型
浦发银行	代发用途	1001	其他代发	PDB_COSTLTEMCODE	浦发费项编码
		1002	代发工资	PDB_COSTLTEMCODE	浦发费项编码
		1030	报销款	PDB_COSTLTEMCODE	浦发费项编码
		1003	代发奖金	PDB_COSTLTEMCODE	浦发费项编码
兴业银行	代发用途	6	工资	CIB_USECODE	兴业代发工资用途
		22	奖金	CIB_USECODE	兴业代发工资用途
		47	福利	CIB_USECODE	兴业代发工资用途
		8	水费	CIB_USECODE	兴业代发工资用途
		7	电费	CIB_USECODE	兴业代发工资用途
		813	高温费	CIB_USECODE	兴业代发工资用途
		80	报刊费	CIB_USECODE	兴业代发工资用途
		48	费用报销	CIB_USECODE	兴业代发工资用途
		605	保险理赔	CIB_USECODE	兴业代发工资用途
		747	住房公积金	CIB_USECODE	兴业代发工资用途
		826	补偿金	CIB_USECODE	兴业代发工资用途
		a26	业务服务费	CIB_USECODE	兴业代发工资用途
		a27	经营所得	CIB_USECODE	兴业代发工资用途
		816	劳务收入	CIB_USECODE	兴业代发工资用途
		768	补贴	CIB_USECODE	兴业代发工资用途
		950	职业年金	CIB_USECODE	兴业代发工资用途
		b77	佣金	CIB_USECODE	兴业代发工资用途
工商银行	代发用途	1	工资	ICBC_USECODE	工行代发用途代码
		2	奖金	ICBC_USECODE	工行代发用途代码
		3	报销	ICBC_USECODE	工行代发用途代码
		4	补贴	ICBC_USECODE	工行代发用途代码
		5	社保	ICBC_USECODE	工行代发用途代码
		6	贷款	ICBC_USECODE	工行代发用途代码
		7	佣金	ICBC_USECODE	工行代发用途代码
		8	租金	ICBC_USECODE	工行代发用途代码
		9	稿费	ICBC_USECODE	工行代发用途代码
		10	公积金	ICBC_USECODE	工行代发用途代码
		11	养老金	ICBC_USECODE	工行代发用途代码
		12	助学金	ICBC_USECODE	工行代发用途代码
		13	劳务费	ICBC_USECODE	工行代发用途代码
		14	演出费	ICBC_USECODE	工行代发用途代码
		15	福利费	ICBC_USECODE	工行代发用途代码
		16	代返还	ICBC_USECODE	工行代发用途代码
		17	企业年金	ICBC_USECODE	工行代发用途代码
		18	保险理赔	ICBC_USECODE	工行代发用途代码
		19	保费退还	ICBC_USECODE	工行代发用途代码
		20	付保险款	ICBC_USECODE	工行代发用途代码
		21	副食补贴	ICBC_USECODE	工行代发用途代码
		22	纳税退还	ICBC_USECODE	工行代发用途代码
		23	基金撤销	ICBC_USECODE	工行代发用途代码
		24	基金赎回	ICBC_USECODE	工行代发用途代码
		25	基金分红	ICBC_USECODE	工行代发用途代码
		26	员工安家费	ICBC_USECODE	工行代发用途代码
		27	校内奖学金	ICBC_USECODE	工行代发用途代码
		28	国家助学金	ICBC_USECODE	工行代发用途代码
		29	励志奖学金	ICBC_USECODE	工行代发用途代码
		30	保险到期还款	ICBC_USECODE	工行代发用途代码
		31	个人贷款转存	ICBC_USECODE	工行代发用途代码
		32	期货交易保证金	ICBC_USECODE	工行代发用途代码
		33	继承或赠与款项	ICBC_USECODE	工行代发用途代码
		34	证卷交易结算资金	ICBC_USECODE	工行代发用途代码
		35	个人小件商品付款	ICBC_USECODE	工行代发用途代码
		36	债卷投资本金和收益	ICBC_USECODE	工行代发用途代码
		37	信托投资本金和收益	ICBC_USECODE	工行代发用途代码
		38	期货投资本金和收益	ICBC_USECODE	工行代发用途代码
		39	其他投资本金和收益	ICBC_USECODE	工行代发用途代码
		40	农、副、矿产品收购款	ICBC_USECODE	工行代发用途代码
		41	个人债权或产权转让收益	ICBC_USECODE	工行代发用途代码
		42	服务费	ICBC_USECODE	工行代发用途代码
		43	养老保险	ICBC_USECODE	工行代发用途代码
		44	失业保险	ICBC_USECODE	工行代发用途代码
		45	工伤保险	ICBC_USECODE	工行代发用途代码
		46	医保代发	ICBC_USECODE	工行代发用途代码
		47	运输费	ICBC_USECODE	工行代发用途代码
		48	工会费	ICBC_USECODE	工行代发用途代码
		49	外服工资	ICBC_USECODE	工行代发用途代码
		50	集团内付款	ICBC_USECODE	工行代发用途代码
		51	遗属生活费	ICBC_USECODE	工行代发用途代码
		52	风沙费	ICBC_USECODE	工行代发用途代码
		53	调节金	ICBC_USECODE	工行代发用途代码
		54	培训费	ICBC_USECODE	工行代发用途代码
		55	独生子女费	ICBC_USECODE	工行代发用途代码
		56	押金	ICBC_USECODE	工行代发用途代码
		57	咨询费	ICBC_USECODE	工行代发用途代码
		58	慰问金	ICBC_USECODE	工行代发用途代码
		59	劳模津贴	ICBC_USECODE	工行代发用途代码
		60	体检费	ICBC_USECODE	工行代发用途代码
		61	补助	ICBC_USECODE	工行代发用途代码
		62	报刊费	ICBC_USECODE	工行代发用途代码
		63	电话费	ICBC_USECODE	工行代发用途代码
		64	交通费	ICBC_USECODE	工行代发用途代码
招商银行	代发用途	AYHS	代扣房租	CMB_BUS_COD	招行代发业务类型
		BYSA	代发工资	CMB_BUS_COD	招行代发业务类型
		AYNT	代扣上网费	CMB_BUS_COD	招行代发业务类型
		AYTX	代扣税费	CMB_BUS_COD	招行代发业务类型
		AYBK	代扣其他	CMB_BUS_COD	招行代发业务类型
		AYEL	代扣电费	CMB_BUS_COD	招行代发业务类型
		AYTV	代扣电视费	CMB_BUS_COD	招行代发业务类型
		AYWT	代扣水费	CMB_BUS_COD	招行代发业务类型
		AYTL	代扣电话费	CMB_BUS_COD	招行代发业务类型
		BYFD	代发信托返还资金	CMB_BUS_COD	招行代发业务类型
		BYXI	代发递延奖金	CMB_BUS_COD	招行代发业务类型
		BYXH	代发通讯费	CMB_BUS_COD	招行代发业务类型
		BYXG	代发交通费	CMB_BUS_COD	招行代发业务类型
		BYXF	代发差旅费	CMB_BUS_COD	招行代发业务类型
		AYGS	代扣煤气费	CMB_BUS_COD	招行代发业务类型
		BYXE	代发分红款	CMB_BUS_COD	招行代发业务类型
		BYXD	代发车贴	CMB_BUS_COD	招行代发业务类型
		BYXC	代发烤火费	CMB_BUS_COD	招行代发业务类型
		AYMT	代扣按揭费	CMB_BUS_COD	招行代发业务类型
		BYXB	代发住房公积金	CMB_BUS_COD	招行代发业务类型
		AYSW	代扣污水费	CMB_BUS_COD	招行代发业务类型
		BYXA	代发改制费	CMB_BUS_COD	招行代发业务类型
		AYPP	代扣物业管理费	CMB_BUS_COD	招行代发业务类型
		BYBK	代发其他	CMB_BUS_COD	招行代发业务类型
		BYBJ	代发农副品销售	CMB_BUS_COD	招行代发业务类型
		BYBI	代发纳税退还	CMB_BUS_COD	招行代发业务类型
		BYBH	代发继承赠与款	CMB_BUS_COD	招行代发业务类型
		BYBG	代发证券期货款	CMB_BUS_COD	招行代发业务类型
		BYBF	代发个人贷款	CMB_BUS_COD	招行代发业务类型
		BYBE	代发债产权转让	CMB_BUS_COD	招行代发业务类型
		BYBD	代发投资本益	CMB_BUS_COD	招行代发业务类型
		AYMC	代扣移动电话费	CMB_BUS_COD	招行代发业务类型
		BYBC	代发劳务费	CMB_BUS_COD	招行代发业务类型
		AYMB	代扣手机费	CMB_BUS_COD	招行代发业务类型
		AYSC	代扣学费	CMB_BUS_COD	招行代发业务类型
		BYWK	代发加班费	CMB_BUS_COD	招行代发业务类型
		BYTF	代发报销款	CMB_BUS_COD	招行代发业务类型
		AYCR	代扣出租车规费	CMB_BUS_COD	招行代发业务类型
		BYWF	代发福利费	CMB_BUS_COD	招行代发业务类型
		AYFS	代扣财产保险费	CMB_BUS_COD	招行代发业务类型
		AYIS	代扣保险费	CMB_BUS_COD	招行代发业务类型
		AYOW	代扣超计划用水费	CMB_BUS_COD	招行代发业务类型
		AYCN	代扣贷款利息	CMB_BUS_COD	招行代发业务类型
		AYLS	代扣人寿保险费	CMB_BUS_COD	招行代发业务类型
		AYCL	代扣清洁费	CMB_BUS_COD	招行代发业务类型
		AYLN	代扣委托贷款本息	CMB_BUS_COD	招行代发业务类型
		AYCF	代扣贷款本金	CMB_BUS_COD	招行代发业务类型
		BYSU	代发保险费	CMB_BUS_COD	招行代发业务类型
		BYSS	离岸代发工资	CMB_BUS_COD	招行代发业务类型
		AYRB	代扣垃圾费	CMB_BUS_COD	招行代发业务类型
		AYEW	代扣水电费	CMB_BUS_COD	招行代发业务类型
		AYBT	代扣批量扣费	CMB_BUS_COD	招行代发业务类型
		BYXN	综合代发款	CMB_BUS_COD	招行代发业务类型
平安银行	代发项目	公共中心-数据字典-结算中心数据字典中自行维护	SZDB_PROJECT	平安银行代发项目
	代发用途	5101	工资（全国大小额）	SZDB_BUSITYPE	平安银行代发费项代码
		5102	奖金（全国大小额）	SZDB_BUSITYPE	平安银行代发费项代码
		999	代发工资（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		51	薪金（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		895	劳务费（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		670	分红款（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		C50	代发佣金（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		C51	平安付款（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		4900	其他（全国大小额）	SZDB_BUSITYPE	平安银行代发费项代码
		99	其他代付（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		ZYL0332	分享奖励（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		ZYL0495	津贴（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
		ZYL0655	个人养老金批量入金（仅本行）	SZDB_BUSITYPE	平安银行代发费项代码
中国银行	代发用途	E1	还款	BOC_BUS_CODE	中行代发用途代码
		E8	奖金	BOC_BUS_CODE	中行代发用途代码
		E9	缴费	BOC_BUS_CODE	中行代发用途代码
		EA	医保	BOC_BUS_CODE	中行代发用途代码
		EB	保险	BOC_BUS_CODE	中行代发用途代码
		EC	报销	BOC_BUS_CODE	中行代发用途代码
		ED	补偿	BOC_BUS_CODE	中行代发用途代码
		EE	补贴	BOC_BUS_CODE	中行代发用途代码
		EF	差旅	BOC_BUS_CODE	中行代发用途代码
		EG	冲正	BOC_BUS_CODE	中行代发用途代码
		EH	代付	BOC_BUS_CODE	中行代发用途代码
		EJ	电费	BOC_BUS_CODE	中行代发用途代码
		EK	电信	BOC_BUS_CODE	中行代发用途代码
		EL	短信	BOC_BUS_CODE	中行代发用途代码
		ES	房租	BOC_BUS_CODE	中行代发用途代码
		ET	分红	BOC_BUS_CODE	中行代发用途代码
		EU	个税	BOC_BUS_CODE	中行代发用途代码
		EV	工资	BOC_BUS_CODE	中行代发用途代码
		EW	公积	BOC_BUS_CODE	中行代发用途代码
		EZ	户费	BOC_BUS_CODE	中行代发用途代码
		F4	有线	BOC_BUS_CODE	中行代发用途代码
		FA	扣费	BOC_BUS_CODE	中行代发用途代码
		FB	扣划	BOC_BUS_CODE	中行代发用途代码
		FD	理财	BOC_BUS_CODE	中行代发用途代码
		FK	年费	BOC_BUS_CODE	中行代发用途代码
		FO	社保	BOC_BUS_CODE	中行代发用途代码
		FQ	水费	BOC_BUS_CODE	中行代发用途代码
		FR	税款	BOC_BUS_CODE	中行代发用途代码
		FZ	学费	BOC_BUS_CODE	中行代发用途代码
		GB	收费	BOC_BUS_CODE	中行代发用途代码
		GG	捐赠	BOC_BUS_CODE	中行代发用途代码
		G1	药费	BOC_BUS_CODE	中行代发用途代码
		K4	福利	BOC_BUS_CODE	中行代发用途代码
		G8	保费	BOC_BUS_CODE	中行代发用途代码
		GH	基养	BOC_BUS_CODE	中行代发用途代码
		KB	过节	BOC_BUS_CODE	中行代发用途代码
		KD	劳保	BOC_BUS_CODE	中行代发用途代码
		NC	低保	BOC_BUS_CODE	中行代发用途代码
		Ed	过渡	BOC_BUS_CODE	中行代发用途代码
		Ec	冬春	BOC_BUS_CODE	中行代发用途代码
		JF	退款	BOC_BUS_CODE	中行代发用途代码
		f9	借款	BOC_BUS_CODE	中行代发用途代码
		fa	贷款	BOC_BUS_CODE	中行代发用途代码
		IN	年金	BOC_BUS_CODE	中行代发用途代码
农业银行	代发用途	99020001	工资发放	ABC_BUSITYPE	农行银行代发费项代码
		99020002	费用报销	ABC_BUSITYPE	农行银行代发费项代码
光大银行	代发用途	0	代发工资	CEB_BUSITYPE	光大银行代发费项代码
		1	其他代发	CEB_BUSITYPE	光大银行代发费项代码
民生银行	代发用途	311	报销费用--差旅费	CMBC_BUSITYPE	民生银行代发费项代码
		312	报销费用--工资奖金	CMBC_BUSITYPE	民生银行代发费项代码
		313	报销费用--日常报销费用	CMBC_BUSITYPE	民生银行代发费项代码
		314	报销费用--日常经营费用	CMBC_BUSITYPE	民生银行代发费项代码
		315	报销费用--办公费	CMBC_BUSITYPE	民生银行代发费项代码
		316	报销费用--水电费	CMBC_BUSITYPE	民生银行代发费项代码
		317	报销费用--通讯费	CMBC_BUSITYPE	民生银行代发费项代码
		318	报销费用--交通费	CMBC_BUSITYPE	民生银行代发费项代码
		319	报销费用--报刊费	CMBC_BUSITYPE	民生银行代发费项代码
		341	报销费用--餐费	CMBC_BUSITYPE	民生银行代发费项代码
		342	报销费用--医药费	CMBC_BUSITYPE	民生银行代发费项代码
		343	报销费用--会议费	CMBC_BUSITYPE	民生银行代发费项代码
		344	个人投资本金和收益	CMBC_BUSITYPE	民生银行代发费项代码
		345	个人债券产权转让收益	CMBC_BUSITYPE	民生银行代发费项代码
		346	个人贷款转存	CMBC_BUSITYPE	民生银行代发费项代码
		347	证券结算金期货保证金	CMBC_BUSITYPE	民生银行代发费项代码
		348	个人继承、赠予款项	CMBC_BUSITYPE	民生银行代发费项代码
		349	保险理赔、保费退还	CMBC_BUSITYPE	民生银行代发费项代码
		374	个人纳税退还	CMBC_BUSITYPE	民生银行代发费项代码
		375	农副矿产品销售收入	CMBC_BUSITYPE	民生银行代发费项代码
		376	其他合法收入--律师费	CMBC_BUSITYPE	民生银行代发费项代码
		377	其他合法收入--资金退还	CMBC_BUSITYPE	民生银行代发费项代码
		378	其他合法收入--拆迁补偿款	CMBC_BUSITYPE	民生银行代发费项代码
		379	其他合法收入--垫付资金	CMBC_BUSITYPE	民生银行代发费项代码
		380	其他合法收入--柜台销售结算款	CMBC_BUSITYPE	民生银行代发费项代码
		381	其他合法收入--网上交易款	CMBC_BUSITYPE	民生银行代发费项代码
		382	其他合法收入--小件商品销售收入	CMBC_BUSITYPE	民生银行代发费项代码
		383	个人合法收入--奖助学金	CMBC_BUSITYPE	民生银行代发费项代码
		384	个人合法收入--退学费	CMBC_BUSITYPE	民生银行代发费项代码
		381	制片费	CMBC_BUSITYPE	民生银行代发费项代码
		385 	报销费用--日常报销费用 	CMBC_BUSITYPE	民生银行代发费项代码
		386  	报销费用--日常经营费用 	CMBC_BUSITYPE	民生银行代发费项代码
		388	案件款	CMBC_BUSITYPE	民生银行代发费项代码
		389	投资赎回款	CMBC_BUSITYPE	民生银行代发费项代码
		390	投资退款	CMBC_BUSITYPE	民生银行代发费项代码
		391	投资分红	CMBC_BUSITYPE	民生银行代发费项代码
		392	其他合法收入--工程款	CMBC_BUSITYPE	民生银行代发费项代码
		393	其他合法收入--租赁费	CMBC_BUSITYPE	民生银行代发费项代码
		394	其他合法收入--运费	CMBC_BUSITYPE	民生银行代发费项代码
		395	个人合法收入--公积金	CMBC_BUSITYPE	民生银行代发费项代码
		396	代收货款	CMBC_BUSITYPE	民生银行代发费项代码
渤海银行	代发用途	1	工资	BOHAIB_BUSITYPE	渤海银行代发费项代码
		2	奖金	BOHAIB_BUSITYPE	渤海银行代发费项代码
		3	津贴	BOHAIB_BUSITYPE	渤海银行代发费项代码
		4	劳动分红	BOHAIB_BUSITYPE	渤海银行代发费项代码
		5	劳务费	BOHAIB_BUSITYPE	渤海银行代发费项代码
		6	报销	BOHAIB_BUSITYPE	渤海银行代发费项代码
华夏银行	代发用途	2006	代发奖金	HXB_BUSITYPE	华夏银行代发费项代码
		2007	代发过节费	HXB_BUSITYPE	华夏银行代发费项代码
		2008	代发住房补贴	HXB_BUSITYPE	华夏银行代发费项代码
		2009	代发交通补贴	HXB_BUSITYPE	华夏银行代发费项代码
		2010	代发防暑降温费	HXB_BUSITYPE	华夏银行代发费项代码
		2011	代发物业费	HXB_BUSITYPE	华夏银行代发费项代码
		2012	代发取暖费	HXB_BUSITYPE	华夏银行代发费项代码
		2013	代发报销款	HXB_BUSITYPE	华夏银行代发费项代码
		2002	代发养老金	HXB_BUSITYPE	华夏银行代发费项代码
		2014	代发社保金	HXB_BUSITYPE	华夏银行代发费项代码
		2048	处室慰问金	HXB_BUSITYPE	华夏银行代发费项代码
		2015	代发保险金	HXB_BUSITYPE	华夏银行代发费项代码
		2016	代发公积金	HXB_BUSITYPE	华夏银行代发费项代码
		2017	代发拆迁款	HXB_BUSITYPE	华夏银行代发费项代码
		2018	代发加班费	HXB_BUSITYPE	华夏银行代发费项代码
		2003	其他代发	HXB_BUSITYPE	华夏银行代发费项代码
		0246	代发改制补偿金	HXB_BUSITYPE	华夏银行代发费项代码
		2037	代发误餐补贴	HXB_BUSITYPE	华夏银行代发费项代码
		2038	代发劳务费	HXB_BUSITYPE	华夏银行代发费项代码
		2039	代发晚夜班费	HXB_BUSITYPE	华夏银行代发费项代码
		2040	代发医药费	HXB_BUSITYPE	华夏银行代发费项代码
		2041	代发运输费	HXB_BUSITYPE	华夏银行代发费项代码
		2041	代发零星发放	HXB_BUSITYPE	华夏银行代发费项代码
		2047	代发模糊工资	HXB_BUSITYPE	华夏银行代发费项代码
广发银行	代发用途	ACBDA001	代发工资（汇总记账）	CGB_BUSITYPE	广发银行代发费项代码
		ACBDA002	费用报销（汇总记账）	CGB_BUSITYPE	广发银行代发费项代码
重庆银行（暂未上线）	代发用途	00100	电费	BOC_BUSITYPE	重庆银行代发费项代码
		00200	水暖费	BOC_BUSITYPE	重庆银行代发费项代码
		00300	煤气费	BOC_BUSITYPE	重庆银行代发费项代码
		00400	电话费	BOC_BUSITYPE	重庆银行代发费项代码
		00500	通讯费	BOC_BUSITYPE	重庆银行代发费项代码
		00600	保险费	BOC_BUSITYPE	重庆银行代发费项代码
		00700	房屋管理费	BOC_BUSITYPE	重庆银行代发费项代码
		00800	代理服务费	BOC_BUSITYPE	重庆银行代发费项代码
		00900	学教费	BOC_BUSITYPE	重庆银行代发费项代码
		01000	有线电视费	BOC_BUSITYPE	重庆银行代发费项代码
		01100	企业管理费用	BOC_BUSITYPE	重庆银行代发费项代码
		09001	其他	BOC_BUSITYPE	重庆银行代发费项代码
		01200	薪金报酬	BOC_BUSITYPE	重庆银行代发费项代码
		6201	代发工资	BOC_BUSITYPE	重庆银行代发费项代码
		6202	代发补贴	BOC_BUSITYPE	重庆银行代发费项代码
		6203	代发加班费	BOC_BUSITYPE	重庆银行代发费项代码
		6204-	代发节日费	BOC_BUSITYPE	重庆银行代发费项代码
		6205	代发奖金	BOC_BUSITYPE	重庆银行代发费项代码
		6206	代发报销费	BOC_BUSITYPE	重庆银行代发费项代码
		6207	代发奖励	BOC_BUSITYPE	重庆银行代发费项代码
		6208	代发赔偿费	BOC_BUSITYPE	重庆银行代发费项代码
		6209	代发劳保费	BOC_BUSITYPE	重庆银行代发费项代码
		6210	代发其他	BOC_BUSITYPE	重庆银行代发费项代码
		6211	报销车旅费	BOC_BUSITYPE	重庆银行代发费项代码
		6212	报销医药费	BOC_BUSITYPE	重庆银行代发费项代码
		6213	报销其他费用	BOC_BUSITYPE	重庆银行代发费项代码
		6214	代发劳务费	BOC_BUSITYPE	重庆银行代发费项代码
		6215	代发酬金	BOC_BUSITYPE	重庆银行代发费项代码
		6216	代发课时费	BOC_BUSITYPE	重庆银行代发费项代码
		6217	代发教学科研津贴	BOC_BUSITYPE	重庆银行代发费项代码
		6218	代发电话费补贴	BOC_BUSITYPE	重庆银行代发费项代码
		6219	代发交通补贴	BOC_BUSITYPE	重庆银行代发费项代码
		6220	代发稿酬	BOC_BUSITYPE	重庆银行代发费项代码
		6221	代发津贴	BOC_BUSITYPE	重庆银行代发费项代码
		6222	代发监考费	BOC_BUSITYPE	重庆银行代发费项代码
		6223	代发巡考费	BOC_BUSITYPE	重庆银行代发费项代码
郑州银行（暂未上线）	代发项目	
公共中心-数据字典-结算中心数据字典中自行维护

	ZZB_PROJECT	郑州银行代发项目
无锡农村商业银行【技术上线】（暂未上线）	代发用途	M1	工资	RCB_PROJECT	无锡农村商业银行代发费项代码
		JJ	奖金	RCB_PROJECT	无锡农村商业银行代发费项代码
		JB	加班费	RCB_PROJECT	无锡农村商业银行代发费项代码
		BX	报销	RCB_PROJECT	无锡农村商业银行代发费项代码
		ZZ	其他	RCB_PROJECT	无锡农村商业银行代发费项代码
山西农信社【技术上线】（暂未上线）	代发用途	1	代发工资	RCU_PROJECT	山西农信社代发费项代码
		2	费用报销	RCU_PROJECT	山西农信社代发费项代码
西宁农商行【技术上线】（暂未上线）	代发用途	01	代发工资   		西宁农商行代发费项代码
		02	代发补贴		西宁农商行代发费项代码
		03	代发其他		西宁农商行代发费项代码
徽商银行	代发项目	
公共中心-数据字典-结算中心数据字典中自行维护

	HSHB__PROJECT	徽商银行代发项目
	代发用途 	1001	代发工资	HSHB_BUSITYPE	徽商银行代发用途
		1002	费用报销 	HSHB_BUSITYPE	徽商银行代发用途
中信银行	代发用途	1	代发工资	COTOC_USECODE	中信银行代发用途代码 
		2	代发奖金	COTOC_USECODE	中信银行代发用途代码 
		3	代发补贴	COTOC_USECODE	中信银行代发用途代码 
		4	代发福利费	COTOC_USECODE	中信银行代发用途代码 
		5	代发报销款	COTOC_USECODE	中信银行代发用途代码 
		6	代发拆迁款	COTOC_USECODE	中信银行代发用途代码 
		7	代发其他	COTOC_USECODE	中信银行代发用途代码 
		9	企业年金	COTOC_USECODE	中信银行代发用途代码 
		10	代发养老金	COTOC_USECODE	中信银行代发用途代码 
		11	代发劳务费	COTOC_USECODE	中信银行代发用途代码 
		12	代发奖学金	COTOC_USECODE	中信银行代发用途代码 
		13	代发加班费	COTOC_USECODE	中信银行代发用途代码 
		14	代发差旅费	COTOC_USECODE	中信银行代发用途代码 
		15	代发交通费	COTOC_USECODE	中信银行代发用途代码 
		16	代发通讯费	COTOC_USECODE	中信银行代发用途代码 
		17	代发社保金	COTOC_USECODE	中信银行代发用途代码 
		18	代发车贴	COTOC_USECODE	中信银行代发用途代码 
		19	代发资金	COTOC_USECODE	中信银行代发用途代码 




