# 司库系统配置
treasury:
  # 中信银行司库系统接口配置
  endpoint: http://10.25.1.15:6789
  username: ***********
  client-code: SZWR003_ZL
  client-name: 深圳万润科技股份有限公司ERP

  # 账户配置
  default-account: 8110701012901269085

  # 备用账户
  accounts:
    - account-no: 8110701012901269085
      account-name: 深圳万润科技股份有限公司ERP
      account-type: 付款账号1
      bank: 中信银行北京朝阳支行
    - account-no: 8110701013101269086
      account-name: 深圳万润科技股份有限公司ERP
      account-type: 付款账号2
      bank: 中信银行北京朝阳支行
    - account-no: 8110701013501269087
      account-name: 深圳万润科技股份有限公司ERP
      account-type: 付款账号3
      bank: 中信银行北京朝阳支行

  # 测试账户配置
  test-accounts:
    # 测试收款对公账户
    - account-no: 8110701013501262521
      account-name: 培训机构70
      account-type: 测试收款对公
      bank: 中信银行北京分行营业部
    # 测试收款对私账户
    - account-no: 8110701213401262624
      account-name: 收款对私测试账户
      account-type: 测试收款对私
      bank: 中信银行北京分行营业部

  # 连接配置
  connection:
    timeout: 30000  # 连接超时时间(毫秒)
    read-timeout: 60000  # 读取超时时间(毫秒)
    retry-count: 3  # 重试次数

  # 缓存配置
  cache:
    enabled: true
    balance-cache-duration: 300  # 余额缓存时间(秒)
    transaction-cache-duration: 600  # 交易记录缓存时间(秒)

# 日志配置
logging:
  level:
    com.laoshu198838.service.TreasuryService: DEBUG
    com.laoshu198838.controller.TreasuryController: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
