package com.laoshu198838.controller;

import com.laoshu198838.service.TreasuryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 司库系统REST接口控制器
 * 
 * <p>提供司库系统的HTTP接口，包括：</p>
 * <ul>
 *   <li>账户余额查询</li>
 *   <li>交易记录查询</li>
 *   <li>系统配置信息</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/api/treasury")
@CrossOrigin(origins = "*")
public class TreasuryController {
    
    @Autowired
    private TreasuryService treasuryService;
    
    /**
     * 查询账户余额
     * 
     * @param accountNo 账户号码（可选，不传则使用默认账户）
     * @return 余额查询结果
     */
    @GetMapping("/balance")
    public ResponseEntity<Map<String, Object>> queryBalance(
            @RequestParam(value = "accountNo", required = false) String accountNo) {
        
        try {
            Map<String, Object> result = treasuryService.queryBalance(accountNo);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "余额查询成功");
            response.put("data", result);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "余额查询失败: " + e.getMessage());
            errorResponse.put("error", "TREASURY_BALANCE_QUERY_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 查询交易记录
     * 
     * @param accountNo 账户号码（可选）
     * @param startDate 开始日期 (格式: YYYYMMDD)
     * @param endDate 结束日期 (格式: YYYYMMDD)
     * @return 交易记录查询结果
     */
    @GetMapping("/transactions")
    public ResponseEntity<Map<String, Object>> queryTransactions(
            @RequestParam(value = "accountNo", required = false) String accountNo,
            @RequestParam("startDate") String startDate,
            @RequestParam("endDate") String endDate) {
        
        try {
            Map<String, Object> result = treasuryService.queryTransactions(accountNo, startDate, endDate);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "交易记录查询成功");
            response.put("data", result);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "交易记录查询失败: " + e.getMessage());
            errorResponse.put("error", "TREASURY_TRANSACTION_QUERY_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 获取司库系统配置信息
     * 
     * @return 配置信息
     */
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        try {
            Map<String, Object> configInfo = treasuryService.getAccountInfo();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "配置信息获取成功");
            response.put("data", configInfo);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "配置信息获取失败: " + e.getMessage());
            errorResponse.put("error", "TREASURY_CONFIG_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 测试司库系统连接
     * 
     * @return 连接测试结果
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> testConnection() {
        try {
            // 使用默认账户进行余额查询来测试连接
            Map<String, Object> result = treasuryService.queryBalance(null);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "司库系统连接正常");
            response.put("data", Map.of(
                "connectionStatus", "SUCCESS",
                "testResult", result
            ));
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "司库系统连接失败: " + e.getMessage());
            errorResponse.put("error", "TREASURY_CONNECTION_ERROR");
            errorResponse.put("data", Map.of(
                "connectionStatus", "FAILED",
                "errorDetail", e.getMessage()
            ));
            errorResponse.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "司库模块运行正常");
        response.put("data", Map.of(
            "status", "UP",
            "module", "treasury",
            "version", "1.0.0"
        ));
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
