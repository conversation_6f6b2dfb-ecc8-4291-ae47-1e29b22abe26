package com.laoshu198838.service;

import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 司库系统接口服务
 * 
 * <p>提供与中信银行司库系统的接口对接功能，包括：</p>
 * <ul>
 *   <li>账户余额查询</li>
 *   <li>交易记录查询</li>
 *   <li>转账功能</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-23
 */
@Service
public class TreasuryService {
    
    // 司库系统配置参数
    @Value("${treasury.endpoint:http://10.25.1.20:6767}")
    private String endpoint;
    
    @Value("${treasury.username:***********}")
    private String userName;
    
    @Value("${treasury.client-code:SZWR003_ZL}")
    private String clientCode;
    
    @Value("${treasury.client-name:深圳万润科技股份有限公司ERP}")
    private String clientName;
    
    // 默认账户配置
    @Value("${treasury.default-account:8110701012901269085}")
    private String defaultAccount;
    
    /**
     * 查询账户余额
     * 
     * @param accountNo 账户号码，如果为null则使用默认账户
     * @return 包含余额信息的Map
     */
    public Map<String, Object> queryBalance(String accountNo) {
        System.out.println("开始查询账户余额，账户号: " + accountNo);
        
        String account = (accountNo != null && !accountNo.trim().isEmpty()) ? accountNo : defaultAccount;
        
        try {
            // 构造 XML 报文（GBK编码）
            String xmlRequest = buildBalanceQueryXml(account);
            
            // 发送请求
            String response = sendRequest(xmlRequest);
            
            // 解析响应
            Map<String, Object> result = parseBalanceResponse(response);
            
            System.out.println("账户余额查询成功，账户: " + account);
            return result;
            
        } catch (Exception e) {
            System.err.println("查询账户余额失败，账户: " + account + ", 错误: " + e.getMessage());
            throw new RuntimeException("司库系统余额查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 查询交易记录
     * 
     * @param accountNo 账户号码
     * @param startDate 开始日期 (格式: YYYYMMDD)
     * @param endDate 结束日期 (格式: YYYYMMDD)
     * @return 包含交易记录的Map
     */
    public Map<String, Object> queryTransactions(String accountNo, String startDate, String endDate) {
        System.out.println("开始查询交易记录，账户: " + accountNo + ", 日期范围: " + startDate + " - " + endDate);
        
        String account = (accountNo != null && !accountNo.trim().isEmpty()) ? accountNo : defaultAccount;
        
        try {
            // 构造交易查询XML报文
            String xmlRequest = buildTransactionQueryXml(account, startDate, endDate);
            
            // 发送请求
            String response = sendRequest(xmlRequest);
            
            // 解析响应
            Map<String, Object> result = parseTransactionResponse(response);
            
            System.out.println("交易记录查询成功，账户: " + account);
            return result;
            
        } catch (Exception e) {
            System.err.println("查询交易记录失败，账户: " + account + ", 错误: " + e.getMessage());
            throw new RuntimeException("司库系统交易查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构造余额查询XML报文
     */
    private String buildBalanceQueryXml(String accountNo) {
        return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
               "<stream>\n" +
               "  <action>SKBALQRY</action>\n" +
               "  <userName>" + userName + "</userName>\n" +
               "  <list name=\"userDataList\">\n" +
               "    <row>\n" +
               "      <accountNo>" + accountNo + "</accountNo>\n" +
               "    </row>\n" +
               "  </list>\n" +
               "</stream>";
    }
    
    /**
     * 构造交易查询XML报文
     */
    private String buildTransactionQueryXml(String accountNo, String startDate, String endDate) {
        return "<?xml version=\"1.0\" encoding=\"GBK\"?>\n" +
               "<stream>\n" +
               "  <action>SKTRANQRY</action>\n" +
               "  <userName>" + userName + "</userName>\n" +
               "  <list name=\"userDataList\">\n" +
               "    <row>\n" +
               "      <accountNo>" + accountNo + "</accountNo>\n" +
               "      <startDate>" + startDate + "</startDate>\n" +
               "      <endDate>" + endDate + "</endDate>\n" +
               "    </row>\n" +
               "  </list>\n" +
               "</stream>";
    }
    
    /**
     * 发送HTTP请求到司库系统
     */
    private String sendRequest(String xmlRequest) throws Exception {
        HttpPost post = new HttpPost(endpoint);
        post.setHeader("Content-Type", "application/xml;charset=GBK");
        post.setEntity(new StringEntity(xmlRequest, "GBK"));
        
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            return EntityUtils.toString(client.execute(post).getEntity(), "GBK");
        }
    }
    
    /**
     * 解析余额查询响应
     */
    private Map<String, Object> parseBalanceResponse(String response) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("rawResponse", response);
        
        try {
            // 解析XML响应中的余额信息
            // 这里需要根据实际的司库系统响应格式进行解析
            Pattern balancePattern = Pattern.compile("<balance>(.*?)</balance>");
            Matcher matcher = balancePattern.matcher(response);
            
            if (matcher.find()) {
                result.put("balance", matcher.group(1));
            }
            
            // 解析账户信息
            Pattern accountPattern = Pattern.compile("<accountNo>(.*?)</accountNo>");
            matcher = accountPattern.matcher(response);
            if (matcher.find()) {
                result.put("accountNo", matcher.group(1));
            }
            
        } catch (Exception e) {
            System.err.println("解析余额响应失败: " + e.getMessage());
            result.put("parseError", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 解析交易记录响应
     */
    private Map<String, Object> parseTransactionResponse(String response) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("rawResponse", response);
        
        try {
            // 这里需要根据实际的司库系统响应格式进行解析
            // 暂时返回原始响应，后续可以根据实际格式完善解析逻辑
            result.put("transactions", "需要根据实际响应格式解析");
            
        } catch (Exception e) {
            System.err.println("解析交易响应失败: " + e.getMessage());
            result.put("parseError", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取所有配置的账户信息
     */
    public Map<String, Object> getAccountInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("clientCode", clientCode);
        info.put("clientName", clientName);
        info.put("defaultAccount", defaultAccount);
        info.put("endpoint", endpoint);
        info.put("userName", userName);
        return info;
    }
}
