package com.laoshu198838.config;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * Treasury模块测试配置类
 * 
 * 为测试环境提供必要的Spring Boot配置和组件扫描
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-23
 */
@SpringBootConfiguration
@ComponentScan(basePackages = {
    "com.laoshu198838.service",
    "com.laoshu198838.config"
})
public class TreasuryTestConfig {
    // 测试配置类，可以添加必要的Bean配置
}
