# FinancialSystem项目专用Git配置
# 使用方法：git config --local include.path ../.gitconfig.local

[alias]
    # 安全操作别名
    safe-reset = !bash scripts/git-clean-reset.sh
    safe-checkout = !bash scripts/git-safe-checkout.sh
    smart-clean = !bash scripts/git-smart-clean.sh
    
    # 快速操作
    sr = !bash scripts/git-clean-reset.sh
    sc = !bash scripts/git-safe-checkout.sh
    clean-all = !bash scripts/git-smart-clean.sh 3
    
    # 状态查看
    st = status --short --branch
    ll = log --oneline -10
    la = log --oneline --graph --all -20
    
    # stash管理
    sl = stash list --date=relative
    ss = stash save --include-untracked
    sp = stash pop
    sd = stash drop
    
    # 分支管理
    br = branch -vv
    bra = branch -a -vv
    
    # 工作区管理
    unstage = reset HEAD --
    discard = checkout --
    
    # 提交相关
    amend = commit --amend --no-edit
    fixup = commit --fixup
    
    # 清理命令
    prune-all = !git remote prune origin && git gc --prune=now
    clean-merged = !git branch --merged | grep -v '\\*\\|main\\|master\\|develop' | xargs -n 1 git branch -d

[core]
    # 安全设置
    autocrlf = input
    safecrlf = warn
    whitespace = fix,-indent-with-non-tab,trailing-space,cr-at-eol
    
[pull]
    # 默认使用rebase
    rebase = true
    
[push]
    # 只推送当前分支
    default = current
    
[merge]
    # 合并时显示更多信息
    log = true
    
[diff]
    # 更好的diff算法
    algorithm = histogram
    
[rebase]
    # rebase时自动stash
    autoStash = true
    
[stash]
    # stash时显示更多信息
    showPatch = true
    
[gc]
    # 自动GC设置
    auto = 256
    autoPackLimit = 10
    
[help]
    # 自动纠正命令
    autocorrect = 10