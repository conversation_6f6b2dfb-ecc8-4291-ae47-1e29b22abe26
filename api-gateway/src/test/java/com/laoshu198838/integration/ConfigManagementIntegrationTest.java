package com.laoshu198838.integration;

import com.laoshu198838.service.ConfigManagementService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 配置管理功能集成测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = com.laoshu198838.Main.class)
@ActiveProfiles("test")
@Transactional // 确保测试后回滚数据库更改
public class ConfigManagementIntegrationTest {

    @Autowired
    private ConfigManagementService configManagementService;

    @Autowired
    private Environment environment;

    @Test
    public void testConfigManagementServiceExists() {
        assertNotNull(configManagementService);
    }

    @Test
    public void testEnvironmentExists() {
        assertNotNull(environment);
    }

    @Test
    public void testGetConfigValue() {
        // 测试获取不存在的配置
        String nonExistentValue = configManagementService.getConfigValue("non.existent.key");
        assertNull(nonExistentValue);

        // 测试获取带默认值的配置
        String defaultValue = configManagementService.getConfigValue("non.existent.key", "default");
        assertEquals("default", defaultValue);
    }

    @Test
    public void testSetAndGetConfigValue() {
        String key = "test.config.key";
        String value = "test.config.value";
        String description = "Test configuration";
        String type = "STRING";

        // 设置配置
        boolean setResult = configManagementService.setConfigValue(key, value, description, type);
        assertTrue(setResult);

        // 获取配置
        String retrievedValue = configManagementService.getConfigValue(key);
        assertEquals(value, retrievedValue);

        // 清理
        configManagementService.deleteConfig(key);
    }

    @Test
    public void testUpdateConfigValue() {
        String key = "test.update.key";
        String originalValue = "original.value";
        String updatedValue = "updated.value";
        String description = "Test update configuration";
        String type = "STRING";

        // 设置初始配置
        configManagementService.setConfigValue(key, originalValue, description, type);

        // 更新配置
        configManagementService.setConfigValue(key, updatedValue, description, type);

        // 验证更新
        String retrievedValue = configManagementService.getConfigValue(key);
        assertEquals(updatedValue, retrievedValue);

        // 清理
        configManagementService.deleteConfig(key);
    }

    @Test
    public void testDeleteConfig() {
        String key = "test.delete.key";
        String value = "test.delete.value";
        String description = "Test delete configuration";
        String type = "STRING";

        // 设置配置
        configManagementService.setConfigValue(key, value, description, type);

        // 验证配置存在
        assertNotNull(configManagementService.getConfigValue(key));

        // 删除配置
        boolean deleteResult = configManagementService.deleteConfig(key);
        assertTrue(deleteResult);

        // 验证配置已删除
        assertNull(configManagementService.getConfigValue(key));

        // 再次删除应该返回false
        boolean secondDeleteResult = configManagementService.deleteConfig(key);
        assertFalse(secondDeleteResult);
    }

    @Test
    public void testBooleanConfig() {
        String key = "test.boolean.key";

        // 测试true值
        configManagementService.setConfigValue(key, "true", "Boolean test", "BOOLEAN");
        assertTrue(configManagementService.getBooleanConfig(key, false));

        // 测试false值
        configManagementService.setConfigValue(key, "false", "Boolean test", "BOOLEAN");
        assertFalse(configManagementService.getBooleanConfig(key, true));

        // 测试默认值
        configManagementService.deleteConfig(key);
        assertTrue(configManagementService.getBooleanConfig(key, true));
        assertFalse(configManagementService.getBooleanConfig(key, false));
    }

    @Test
    public void testIntegerConfig() {
        String key = "test.integer.key";
        int testValue = 42;

        // 设置整数配置
        configManagementService.setConfigValue(key, String.valueOf(testValue), "Integer test", "INTEGER");
        assertEquals(testValue, configManagementService.getIntConfig(key, 0));

        // 测试无效整数
        configManagementService.setConfigValue(key, "invalid", "Invalid integer", "INTEGER");
        assertEquals(100, configManagementService.getIntConfig(key, 100)); // 应该返回默认值

        // 测试默认值
        configManagementService.deleteConfig(key);
        assertEquals(999, configManagementService.getIntConfig(key, 999));
    }

    @Test
    public void testLongConfig() {
        String key = "test.long.key";
        long testValue = 123456789L;

        // 设置长整数配置
        configManagementService.setConfigValue(key, String.valueOf(testValue), "Long test", "LONG");
        assertEquals(testValue, configManagementService.getLongConfig(key, 0L));

        // 测试无效长整数
        configManagementService.setConfigValue(key, "invalid", "Invalid long", "LONG");
        assertEquals(100L, configManagementService.getLongConfig(key, 100L)); // 应该返回默认值

        // 测试默认值
        configManagementService.deleteConfig(key);
        assertEquals(999L, configManagementService.getLongConfig(key, 999L));
    }

    @Test
    public void testBatchSetConfigs() {
        Map<String, Map<String, String>> configs = new HashMap<>();

        // 准备批量配置数据
        Map<String, String> config1 = new HashMap<>();
        config1.put("value", "value1");
        config1.put("description", "Batch config 1");
        config1.put("type", "STRING");
        configs.put("batch.config.1", config1);

        Map<String, String> config2 = new HashMap<>();
        config2.put("value", "value2");
        config2.put("description", "Batch config 2");
        config2.put("type", "STRING");
        configs.put("batch.config.2", config2);

        // 批量设置配置
        Map<String, Boolean> results = configManagementService.batchSetConfigs(configs);

        // 验证结果
        assertEquals(2, results.size());
        assertTrue(results.get("batch.config.1"));
        assertTrue(results.get("batch.config.2"));

        // 验证配置已设置
        assertEquals("value1", configManagementService.getConfigValue("batch.config.1"));
        assertEquals("value2", configManagementService.getConfigValue("batch.config.2"));

        // 清理
        configManagementService.deleteConfig("batch.config.1");
        configManagementService.deleteConfig("batch.config.2");
    }

    @Test
    public void testGetConfigsByGroup() {
        // 设置不同分组的配置
        configManagementService.setConfigValue("app.name", "FinancialSystem", "App name", "STRING");
        configManagementService.setConfigValue("app.version", "1.0", "App version", "STRING");
        configManagementService.setConfigValue("db.host", "localhost", "DB host", "STRING");
        configManagementService.setConfigValue("db.port", "3306", "DB port", "INTEGER");

        // 获取分组配置
        Map<String, List<Map<String, Object>>> groupedConfigs = configManagementService.getConfigsByGroup();

        // 验证分组
        assertTrue(groupedConfigs.containsKey("app"));
        assertTrue(groupedConfigs.containsKey("db"));

        // 验证app分组包含2个配置
        assertEquals(2, groupedConfigs.get("app").size());

        // 验证db分组包含2个配置
        assertEquals(2, groupedConfigs.get("db").size());

        // 清理
        configManagementService.deleteConfig("app.name");
        configManagementService.deleteConfig("app.version");
        configManagementService.deleteConfig("db.host");
        configManagementService.deleteConfig("db.port");
    }

    @Test
    public void testGetConfigStatistics() {
        // 设置一些测试配置
        configManagementService.setConfigValue("stats.test.1", "value1", "Stats test 1", "STRING");
        configManagementService.setConfigValue("stats.test.2", "value2", "Stats test 2", "STRING");

        // 获取统计信息
        Map<String, Object> stats = configManagementService.getConfigStatistics();

        // 验证统计信息
        assertNotNull(stats);
        assertTrue(stats.containsKey("totalConfigs"));
        assertTrue(stats.containsKey("typeStats"));
        assertTrue(stats.containsKey("groupStats"));

        // 验证总配置数大于等于2（我们刚设置的）
        Integer totalConfigs = (Integer) stats.get("totalConfigs");
        assertTrue(totalConfigs >= 2);

        // 清理
        configManagementService.deleteConfig("stats.test.1");
        configManagementService.deleteConfig("stats.test.2");
    }

    @Test
    public void testReloadAllConfigs() {
        // 设置测试配置
        String key = "reload.test.key";
        String value = "reload.test.value";
        configManagementService.setConfigValue(key, value, "Reload test", "STRING");

        // 重新加载配置
        configManagementService.reloadAllConfigs();

        // 验证配置仍然可以获取
        assertEquals(value, configManagementService.getConfigValue(key));

        // 清理
        configManagementService.deleteConfig(key);
    }
}
