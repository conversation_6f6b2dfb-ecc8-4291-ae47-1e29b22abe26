package com.laoshu198838.integration;

import com.laoshu198838.config.CustomMetricsConfig;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 监控功能单元测试（不使用Spring上下文，直接使用模拟对象）
 *
 * <AUTHOR>
 */
public class MonitoringIntegrationTest {

    @Spy
    private MeterRegistry meterRegistry = new SimpleMeterRegistry();
    
    @Mock
    private CustomMetricsConfig customMetricsConfig;
    
    private Counter userLoginCounter;
    private Counter userLoginFailureCounter;
    private Timer databaseQueryTimer;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        
        // 手动创建计数器和计时器
        userLoginCounter = Counter.builder("user.login.total")
                .description("用户登录总次数")
                .register(meterRegistry);
                
        userLoginFailureCounter = Counter.builder("user.login.failure.total")
                .description("用户登录失败总次数")
                .register(meterRegistry);
                
        databaseQueryTimer = Timer.builder("database.query.duration")
                .description("数据库查询耗时")
                .register(meterRegistry);
                
        // 配置模拟行为
        doNothing().when(customMetricsConfig).updateActiveUsers(anyInt());
        doNothing().when(customMetricsConfig).updateTotalDebts(anyInt());
        doNothing().when(customMetricsConfig).updateTotalReports(anyInt());
    }

    @Test
    public void testMeterRegistryExists() {
        assertNotNull(meterRegistry);
        assertTrue(meterRegistry.getMeters().size() > 0);
    }

    @Test
    public void testCustomMetricsConfigExists() {
        assertNotNull(customMetricsConfig);
    }

    @Test
    public void testUserLoginCounter() {
        assertNotNull(userLoginCounter);

        double initialCount = userLoginCounter.count();

        // 模拟用户登录
        userLoginCounter.increment();

        double newCount = userLoginCounter.count();
        assertEquals(initialCount + 1, newCount);
    }

    @Test
    public void testUserLoginFailureCounter() {
        assertNotNull(userLoginFailureCounter);

        double initialCount = userLoginFailureCounter.count();

        // 模拟登录失败
        userLoginFailureCounter.increment();

        double newCount = userLoginFailureCounter.count();
        assertEquals(initialCount + 1, newCount);
    }

    @Test
    public void testDatabaseQueryTimer() {
        assertNotNull(databaseQueryTimer);

        long initialCount = databaseQueryTimer.count();

        // 模拟数据库查询
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            // 模拟查询耗时
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        sample.stop(databaseQueryTimer);

        long newCount = databaseQueryTimer.count();
        assertEquals(initialCount + 1, newCount);

        // 验证平均时间大于0
        assertTrue(databaseQueryTimer.mean(TimeUnit.MILLISECONDS) > 0);
    }

    @Test
    public void testBusinessMetricsUpdate() {
        // 测试业务指标更新
        customMetricsConfig.updateActiveUsers(10);
        customMetricsConfig.updateTotalDebts(100);
        customMetricsConfig.updateTotalReports(5);

        // 验证方法调用
        verify(customMetricsConfig).updateActiveUsers(10);
        verify(customMetricsConfig).updateTotalDebts(100);
        verify(customMetricsConfig).updateTotalReports(5);
    }

    @Test
    public void testMetricsExist() {
        // 验证关键指标是否存在
        boolean hasUserLoginMetric = meterRegistry.getMeters().stream()
                .anyMatch(meter -> meter.getId().getName().equals("user.login.total"));

        boolean hasUserLoginFailureMetric = meterRegistry.getMeters().stream()
                .anyMatch(meter -> meter.getId().getName().equals("user.login.failure.total"));

        boolean hasDatabaseQueryMetric = meterRegistry.getMeters().stream()
                .anyMatch(meter -> meter.getId().getName().equals("database.query.duration"));

        assertTrue(hasUserLoginMetric);
        assertTrue(hasUserLoginFailureMetric);
        assertTrue(hasDatabaseQueryMetric);
    }

    @Test
    public void testCounterIncrement() {
        Counter testCounter = Counter.builder("test.counter")
                .description("Test counter")
                .register(meterRegistry);

        double initialCount = testCounter.count();

        testCounter.increment();
        testCounter.increment(5);

        double finalCount = testCounter.count();
        assertEquals(initialCount + 6, finalCount);
    }

    @Test
    public void testTimerMeasurement() {
        Timer testTimer = Timer.builder("test.timer")
                .description("Test timer")
                .register(meterRegistry);

        long initialCount = testTimer.count();
        
        // 记录时间
        testTimer.record(100, TimeUnit.MILLISECONDS);
        
        long newCount = testTimer.count();
        assertEquals(initialCount + 1, newCount);
        
        // 验证时间记录正确
        assertTrue(testTimer.totalTime(TimeUnit.MILLISECONDS) >= 100);
    }
}
