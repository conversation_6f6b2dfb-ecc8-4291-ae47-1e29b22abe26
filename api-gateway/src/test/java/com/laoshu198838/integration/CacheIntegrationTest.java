package com.laoshu198838.integration;

import com.laoshu198838.service.CacheService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 缓存功能集成测试
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = com.laoshu198838.Main.class)
@ActiveProfiles("test")
public class CacheIntegrationTest {

    @Autowired
    private CacheService cacheService;

    @Test
    public void testCacheBasicOperations() {
        String key = "test_key";
        String value = "test_value";

        // 测试设置缓存
        cacheService.set(key, value);

        // 测试获取缓存
        String cachedValue = cacheService.get(key, String.class);
        assertEquals(value, cachedValue);

        // 测试删除缓存
        cacheService.delete(key);
        String deletedValue = cacheService.get(key, String.class);
        assertNull(deletedValue);
    }

    @Test
    public void testCacheWithTTL() throws InterruptedException {
        String key = "test_ttl_key";
        String value = "test_ttl_value";

        // 设置带TTL的缓存（2秒）
        cacheService.set(key, value, 2, TimeUnit.SECONDS);

        // 立即获取应该存在
        String cachedValue = cacheService.get(key, String.class);
        assertEquals(value, cachedValue);

        // 等待3秒后应该过期
        Thread.sleep(3000);
        String expiredValue = cacheService.get(key, String.class);
        assertNull(expiredValue);
    }

    @Test
    public void testCacheExists() {
        String key = "test_exists_key";
        String value = "test_exists_value";

        // 初始状态不存在
        assertFalse(cacheService.exists(key));

        // 设置后存在
        cacheService.set(key, value);
        assertTrue(cacheService.exists(key));

        // 删除后不存在
        cacheService.delete(key);
        assertFalse(cacheService.exists(key));
    }

    @Test
    public void testCacheExpire() {
        String key = "test_expire_key";
        String value = "test_expire_value";

        // 设置缓存
        cacheService.set(key, value);

        // 设置过期时间
        cacheService.expire(key, 5, TimeUnit.SECONDS);

        // 获取过期时间
        long expireTime = cacheService.getExpire(key);
        assertTrue(expireTime > 0 && expireTime <= 5);

        // 清理
        cacheService.delete(key);
    }

    @Test
    public void testCacheKeys() {
        String prefix = "test_keys_";
        String key1 = prefix + "1";
        String key2 = prefix + "2";
        String value = "test_value";

        // 设置测试缓存
        cacheService.set(key1, value);
        cacheService.set(key2, value);

        // 查找缓存键
        java.util.Set<String> keys = cacheService.keys(prefix + "*");
        assertNotNull(keys);
        assertTrue(keys.contains(key1));
        assertTrue(keys.contains(key2));

        // 清理
        cacheService.delete(key1);
        cacheService.delete(key2);
    }

    @Test
    public void testCacheFlushByPrefix() {
        String prefix = "test_flush_";
        String key1 = prefix + "1";
        String key2 = prefix + "2";
        String value = "test_value";

        // 设置测试缓存
        cacheService.set(key1, value);
        cacheService.set(key2, value);

        // 验证缓存存在
        assertTrue(cacheService.exists(key1));
        assertTrue(cacheService.exists(key2));

        // 按前缀清空缓存
        cacheService.flushByPrefix(prefix);

        // 验证缓存已清空
        assertFalse(cacheService.exists(key1));
        assertFalse(cacheService.exists(key2));
    }

    @Test
    public void testCacheComplexObject() {
        String key = "test_object_key";
        TestObject testObject = new TestObject("test", 123);

        // 设置复杂对象
        cacheService.set(key, testObject);

        // 获取复杂对象
        TestObject retrievedObject = cacheService.get(key, TestObject.class);
        assertNotNull(retrievedObject);
        assertEquals(testObject.getName(), retrievedObject.getName());
        assertEquals(testObject.getValue(), retrievedObject.getValue());

        // 清理
        cacheService.delete(key);
    }

    @Test
    public void testCacheStats() {
        // 设置一些测试缓存
        cacheService.set("users:1", "user1");
        cacheService.set("debts:1", "debt1");
        cacheService.set("reports:1", "report1");
        cacheService.set("config:1", "config1");
        cacheService.set("other:1", "other1");

        // 获取缓存统计
        CacheService.CacheStats stats = cacheService.getCacheStats();
        assertNotNull(stats);

        // 验证统计信息
        assertTrue(stats.getTotalKeys() >= 5);
        assertTrue(stats.getUserKeys() >= 1);
        assertTrue(stats.getDebtKeys() >= 1);
        assertTrue(stats.getReportKeys() >= 1);
        assertTrue(stats.getConfigKeys() >= 1);
        assertTrue(stats.getOtherKeys() >= 1);

        // 清理
        cacheService.delete("users:1");
        cacheService.delete("debts:1");
        cacheService.delete("reports:1");
        cacheService.delete("config:1");
        cacheService.delete("other:1");
    }

    @Test
    public void testBatchDelete() {
        String key1 = "batch_test_1";
        String key2 = "batch_test_2";
        String key3 = "batch_test_3";
        String value = "test_value";

        // 设置多个缓存
        cacheService.set(key1, value);
        cacheService.set(key2, value);
        cacheService.set(key3, value);

        // 验证缓存存在
        assertTrue(cacheService.exists(key1));
        assertTrue(cacheService.exists(key2));
        assertTrue(cacheService.exists(key3));

        // 批量删除
        java.util.List<String> keys = java.util.Arrays.asList(key1, key2, key3);
        cacheService.delete(keys);

        // 验证缓存已删除
        assertFalse(cacheService.exists(key1));
        assertFalse(cacheService.exists(key2));
        assertFalse(cacheService.exists(key3));
    }

    // 测试用的简单对象
    public static class TestObject {
        private String name;
        private Integer value;

        public TestObject() {}

        public TestObject(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getValue() { return value; }
        public void setValue(Integer value) { this.value = value; }
    }
}
