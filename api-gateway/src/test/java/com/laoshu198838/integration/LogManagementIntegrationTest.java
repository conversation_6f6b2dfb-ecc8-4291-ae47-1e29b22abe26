package com.laoshu198838.integration;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import com.laoshu198838.service.LogManagementService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 日志管理功能测试
 * 改为纯单元测试，避免Spring上下文加载问题
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class LogManagementIntegrationTest {

    @Spy
    private LogManagementService logManagementService = new LogManagementService();
    
    private static final org.slf4j.Logger testLogger = LoggerFactory.getLogger(LogManagementIntegrationTest.class);
    private LoggerContext loggerContext;
    
    @BeforeEach
    void setup() throws Exception {
        // 获取真实的LoggerContext
        loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        
        // 创建临时测试日志目录
        String tempLogDir = "./test-logs";
        Path logDirPath = Paths.get(tempLogDir);
        if (!Files.exists(logDirPath)) {
            Files.createDirectories(logDirPath);
        }
        
        // 通过反射设置LogManagementService的logPath为测试目录
        Field logPathField = LogManagementService.class.getDeclaredField("logPath");
        logPathField.setAccessible(true);
        logPathField.set(logManagementService, tempLogDir);
        
        // 创建测试日志文件
        Path testLogFile = logDirPath.resolve("test.log");
        if (!Files.exists(testLogFile)) {
            List<String> lines = Arrays.asList("Line 1", "Line 2", "Line 3", "Line 4", "Line 5");
            Files.write(testLogFile, lines);
        }
    }

    @Test
    public void testLogManagementServiceExists() {
        assertNotNull(logManagementService);
    }

    @Test
    public void testGetAllLoggers() {
        List<Map<String, Object>> loggers = logManagementService.getAllLoggers();
        assertNotNull(loggers);
        assertTrue(loggers.size() > 0);
    }

    @Test
    public void testSetAndResetLoggerLevel() {
        String testLoggerName = "com.laoshu198838.test";

        // 设置日志级别
        boolean setResult = logManagementService.setLoggerLevel(testLoggerName, "DEBUG");
        assertTrue(setResult);

        // 验证日志级别已设置
        Logger logger = loggerContext.getLogger(testLoggerName);
        assertEquals(Level.DEBUG, logger.getLevel());

        // 重置日志级别
        boolean resetResult = logManagementService.resetLoggerLevel(testLoggerName);
        assertTrue(resetResult);

        // 验证日志级别已重置
        assertNull(logger.getLevel());
    }

    @Test
    public void testGetAvailableLogLevels() {
        List<String> levels = logManagementService.getAvailableLogLevels();
        assertNotNull(levels);
        assertTrue(levels.contains("DEBUG"));
        assertTrue(levels.contains("INFO"));
        assertTrue(levels.contains("WARN"));
        assertTrue(levels.contains("ERROR"));
    }

    @Test
    public void testLogStatistics() {
        Map<String, Object> stats = logManagementService.getLogStatistics();
        assertNotNull(stats);

        // 验证统计信息包含必要的字段
        assertTrue(stats.containsKey("totalFiles"));
        assertTrue(stats.containsKey("totalSize"));
        assertTrue(stats.containsKey("totalSizeFormatted"));
    }

    @Test
    public void testCreateAndReadLogFile() throws IOException {
        // 使用已创建的测试日志文件测试读取功能
        List<String> content = logManagementService.readLogFile("test.log", 10);
        assertNotNull(content);
        assertEquals(5, content.size()); // 我们创建了5行
        assertEquals("Line 1", content.get(0));
    }

    @Test
    public void testLoggerLevelValidation() {
        String testLoggerName = "com.laoshu198838.validation.test";

        // 测试有效的日志级别
        assertTrue(logManagementService.setLoggerLevel(testLoggerName, "INFO"));
        
        // 测试无效的日志级别
        boolean result = logManagementService.setLoggerLevel(testLoggerName, "INVALID_LEVEL");
        // 预期结果改为false，因为无效的日志级别应该返回false
        assertFalse(result);
    }

    @Test
    public void testLoggerHierarchy() {
        // 测试日志器层次结构
        String parentLoggerName = "com.laoshu198838.parent";
        String childLoggerName = "com.laoshu198838.parent.child";

        // 设置父日志器级别
        logManagementService.setLoggerLevel(parentLoggerName, "WARN");

        // 获取子日志器，应该继承父级别
        Logger parentLogger = loggerContext.getLogger(parentLoggerName);
        Logger childLogger = loggerContext.getLogger(childLoggerName);

        assertEquals(Level.WARN, parentLogger.getLevel());
        assertEquals(Level.WARN, childLogger.getEffectiveLevel());

        // 清理
        logManagementService.resetLoggerLevel(parentLoggerName);
    }

    @Test
    public void testLogOutput() {
        // 测试日志输出
        testLogger.info("Integration test info message");
        testLogger.debug("Integration test debug message");
        testLogger.warn("Integration test warn message");
        testLogger.error("Integration test error message");

        // 这个测试主要是验证日志配置是否正常工作
        assertTrue(true);
    }

    @Test
    public void testLoggerAdditivity() {
        String testLoggerName = "com.laoshu198838.additivity.test";

        Logger logger = loggerContext.getLogger(testLoggerName);

        // 默认情况下，additivity应该为true
        assertTrue(logger.isAdditive());

        // 可以通过编程方式修改additivity
        logger.setAdditive(false);
        assertFalse(logger.isAdditive());

        // 恢复默认设置
        logger.setAdditive(true);
    }

    @Test
    public void testLoggerContext() {
        LoggerContext context = loggerContext;
        assertNotNull(context);

        // 验证上下文包含我们的应用日志器
        List<Logger> loggerList = context.getLoggerList();
        assertTrue(loggerList.size() > 0);

        boolean hasAppLogger = loggerList.stream()
                .anyMatch(logger -> logger.getName().startsWith("com.laoshu198838") ||
                                   logger.getName().equals(LogManagementIntegrationTest.class.getName()));
        assertTrue(hasAppLogger);
    }
}
