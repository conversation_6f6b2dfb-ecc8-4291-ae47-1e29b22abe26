package com.laoshu198838.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.anyInt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.laoshu198838.config.ChecksConfiguration;
import com.laoshu198838.config.ChecksConfiguration.CheckItem;
import com.laoshu198838.model.datamonitor.dto.DataOverviewResult;
import com.laoshu198838.repository.overdue_debt.ConsistencyCheckRepository;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.repository.overdue_debt.LitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.NonLitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtAddRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDecreaseRepository;

/**
 * 数据一致性检查服务测试类
 *
 * <AUTHOR>
 */
public class DataConsistencyCheckServiceTest {

    @Mock
    private OverdueDebtAddRepository overdueDebtAddRepository;

    @Mock
    private OverdueDebtDecreaseRepository overdueDebtDecreaseRepository;

    @Mock
    private LitigationClaimRepository litigationClaimRepository;

    @Mock
    private NonLitigationClaimRepository nonLitigationClaimRepository;

    @Mock
    private ImpairmentReserveRepository impairmentReserveRepository;
    
    @Mock
    private ConsistencyCheckRepository consistencyCheckRepository;

    @Mock
    private ChecksConfiguration.ChecksConfig checksConfig;

    @InjectMocks
    private DataConsistencyCheckService dataConsistencyCheckService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);

        // 模拟配置
        List<CheckItem> checkItems = new ArrayList<>();
        CheckItem item1 = new CheckItem();
        item1.setKey("newAmount");
        item1.setLabel("新增金额");

        CheckItem item2 = new CheckItem();
        item2.setKey("disposedAmount");
        item2.setLabel("处置金额");

        CheckItem item3 = new CheckItem();
        item3.setKey("endingBalance");
        item3.setLabel("期末余额");

        checkItems.add(item1);
        checkItems.add(item2);
        checkItems.add(item3);

        when(checksConfig.getChecks()).thenReturn(checkItems);

        // 模拟空的数据库查询结果
        when(overdueDebtAddRepository.findAll()).thenReturn(new ArrayList<>());
        when(litigationClaimRepository.findByYearAndMonth(2023, 12)).thenReturn(new ArrayList<>());
        when(nonLitigationClaimRepository.findByYearAndMonth(2023, 12)).thenReturn(new ArrayList<>());
        when(impairmentReserveRepository.findByIdYearAndIdMonth(2023, 12)).thenReturn(new ArrayList<>());
        
        // 模拟 ConsistencyCheckRepository
        Map<String, Object> initialBalanceSummary = new HashMap<>();
        initialBalanceSummary.put("totalAmount", 10000.0);
        when(consistencyCheckRepository.getInitialBalanceSummary(anyInt())).thenReturn(initialBalanceSummary);
    }

    @Test
    public void testGetDataOverview() {
        // 执行测试
        DataOverviewResult result = dataConsistencyCheckService.getDataOverview("2023", "12");

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().containsKey("newAmount"));
        assertTrue(result.getData().containsKey("disposedAmount"));
        assertTrue(result.getData().containsKey("endingBalance"));
    }
}
