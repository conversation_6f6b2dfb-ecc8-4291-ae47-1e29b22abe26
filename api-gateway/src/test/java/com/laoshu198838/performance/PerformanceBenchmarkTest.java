package com.laoshu198838.performance;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 性能基准测试
 * 测试各种操作的性能表现
 * 
 * <AUTHOR>
 */
public class PerformanceBenchmarkTest {
    
    private long startTime;
    private long endTime;
    
    @BeforeEach
    public void setUp() {
        startTime = System.nanoTime();
    }
    
    @AfterEach
    public void tearDown() {
        endTime = System.nanoTime();
        long duration = endTime - startTime;
        System.out.printf("Test execution time: %.2f ms%n", duration / 1_000_000.0);
    }
    
    @Test
    public void testStringConcatenationPerformance() {
        int iterations = 10000;
        
        // StringBuilder性能测试
        long start = System.nanoTime();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < iterations; i++) {
            sb.append("test").append(i);
        }
        String result1 = sb.toString();
        long sbTime = System.nanoTime() - start;
        
        // String直接拼接性能测试
        start = System.nanoTime();
        String result2 = "";
        for (int i = 0; i < 1000; i++) { // 减少迭代次数避免过慢
            result2 += "test" + i;
        }
        long stringTime = System.nanoTime() - start;
        
        System.out.printf("StringBuilder: %.2f ms, String concatenation: %.2f ms%n", 
                sbTime / 1_000_000.0, stringTime / 1_000_000.0);
        
        // StringBuilder应该更快
        assertTrue(sbTime < stringTime);
        assertNotNull(result1);
        assertNotNull(result2);
    }
    
    @Test
    public void testCollectionPerformance() {
        int size = 100000;
        
        // ArrayList性能测试
        long start = System.nanoTime();
        List<Integer> arrayList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            arrayList.add(i);
        }
        long arrayListTime = System.nanoTime() - start;
        
        // LinkedList性能测试
        start = System.nanoTime();
        List<Integer> linkedList = new LinkedList<>();
        for (int i = 0; i < size; i++) {
            linkedList.add(i);
        }
        long linkedListTime = System.nanoTime() - start;
        
        // HashMap性能测试
        start = System.nanoTime();
        Map<Integer, Integer> hashMap = new HashMap<>();
        for (int i = 0; i < size; i++) {
            hashMap.put(i, i);
        }
        long hashMapTime = System.nanoTime() - start;
        
        System.out.printf("ArrayList: %.2f ms, LinkedList: %.2f ms, HashMap: %.2f ms%n",
                arrayListTime / 1_000_000.0, linkedListTime / 1_000_000.0, hashMapTime / 1_000_000.0);
        
        assertEquals(size, arrayList.size());
        assertEquals(size, linkedList.size());
        assertEquals(size, hashMap.size());
    }
    
    @Test
    public void testStreamPerformance() {
        List<Integer> numbers = IntStream.range(0, 1000000)
                .boxed()
                .collect(java.util.stream.Collectors.toList());
        
        // 传统for循环
        long start = System.nanoTime();
        int sum1 = 0;
        for (Integer num : numbers) {
            if (num % 2 == 0) {
                sum1 += num;
            }
        }
        long forLoopTime = System.nanoTime() - start;
        
        // Stream API
        start = System.nanoTime();
        int sum2 = numbers.stream()
                .filter(n -> n % 2 == 0)
                .mapToInt(Integer::intValue)
                .sum();
        long streamTime = System.nanoTime() - start;
        
        // 并行Stream
        start = System.nanoTime();
        int sum3 = numbers.parallelStream()
                .filter(n -> n % 2 == 0)
                .mapToInt(Integer::intValue)
                .sum();
        long parallelStreamTime = System.nanoTime() - start;
        
        System.out.printf("For loop: %.2f ms, Stream: %.2f ms, Parallel stream: %.2f ms%n",
                forLoopTime / 1_000_000.0, streamTime / 1_000_000.0, parallelStreamTime / 1_000_000.0);
        
        assertEquals(sum1, sum2);
        assertEquals(sum2, sum3);
    }
    
    @Test
    public void testConcurrencyPerformance() throws InterruptedException {
        int taskCount = 10000;
        int threadCount = 4;
        
        // 单线程执行
        long start = System.nanoTime();
        int sum1 = 0;
        for (int i = 0; i < taskCount; i++) {
            sum1 += computeTask(i);
        }
        long singleThreadTime = System.nanoTime() - start;
        
        // 多线程执行
        start = System.nanoTime();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<Future<Integer>> futures = new ArrayList<>();
        
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            futures.add(executor.submit(() -> computeTask(taskId)));
        }
        
        int sum2 = 0;
        for (Future<Integer> future : futures) {
            try {
                sum2 += future.get();
            } catch (ExecutionException e) {
                fail("Task execution failed: " + e.getMessage());
            }
        }
        
        executor.shutdown();
        executor.awaitTermination(10, TimeUnit.SECONDS);
        long multiThreadTime = System.nanoTime() - start;
        
        System.out.printf("Single thread: %.2f ms, Multi thread (%d threads): %.2f ms%n",
                singleThreadTime / 1_000_000.0, threadCount, multiThreadTime / 1_000_000.0);
        
        assertEquals(sum1, sum2);
    }
    
    @Test
    public void testMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        
        // 获取初始内存使用情况
        runtime.gc(); // 建议进行垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 创建大量对象
        List<String> strings = new ArrayList<>();
        for (int i = 0; i < 100000; i++) {
            strings.add("String " + i);
        }
        
        // 获取使用后的内存情况
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = usedMemory - initialMemory;
        
        System.out.printf("Initial memory: %d bytes, Used memory: %d bytes, Increase: %d bytes%n",
                initialMemory, usedMemory, memoryIncrease);
        
        assertTrue(memoryIncrease > 0);
        assertEquals(100000, strings.size());
        
        // 清理
        strings.clear();
        runtime.gc();
    }
    
    @Test
    public void testSortingPerformance() {
        int size = 100000;
        Random random = new Random(42); // 固定种子确保可重复性
        
        // 生成随机数据
        List<Integer> data1 = new ArrayList<>();
        List<Integer> data2 = new ArrayList<>();
        List<Integer> data3 = new ArrayList<>();
        
        for (int i = 0; i < size; i++) {
            int value = random.nextInt(size);
            data1.add(value);
            data2.add(value);
            data3.add(value);
        }
        
        // Collections.sort()
        long start = System.nanoTime();
        Collections.sort(data1);
        long collectionsTime = System.nanoTime() - start;
        
        // Stream sorted()
        start = System.nanoTime();
        List<Integer> sorted2 = data2.stream()
                .sorted()
                .collect(java.util.stream.Collectors.toList());
        long streamTime = System.nanoTime() - start;
        
        // Parallel stream sorted()
        start = System.nanoTime();
        List<Integer> sorted3 = data3.parallelStream()
                .sorted()
                .collect(java.util.stream.Collectors.toList());
        long parallelStreamTime = System.nanoTime() - start;
        
        System.out.printf("Collections.sort: %.2f ms, Stream sort: %.2f ms, Parallel stream sort: %.2f ms%n",
                collectionsTime / 1_000_000.0, streamTime / 1_000_000.0, parallelStreamTime / 1_000_000.0);
        
        // 验证排序结果
        assertEquals(data1, sorted2);
        assertEquals(data1, sorted3);
        assertTrue(isSorted(data1));
    }
    
    @Test
    public void testIOPerformance() throws Exception {
        int dataSize = 1000000;
        String testData = "A".repeat(dataSize);
        
        // 写入性能测试
        java.nio.file.Path tempFile = java.nio.file.Files.createTempFile("perf_test", ".txt");
        
        try {
            // 使用Files.write
            long start = System.nanoTime();
            java.nio.file.Files.write(tempFile, testData.getBytes());
            long filesWriteTime = System.nanoTime() - start;
            
            // 读取性能测试
            start = System.nanoTime();
            byte[] readData = java.nio.file.Files.readAllBytes(tempFile);
            long filesReadTime = System.nanoTime() - start;
            
            System.out.printf("Files.write: %.2f ms, Files.readAllBytes: %.2f ms%n",
                    filesWriteTime / 1_000_000.0, filesReadTime / 1_000_000.0);
            
            assertEquals(testData.length(), readData.length);
            assertEquals(testData, new String(readData));
            
        } finally {
            java.nio.file.Files.deleteIfExists(tempFile);
        }
    }
    
    @Test
    public void testReflectionPerformance() throws Exception {
        int iterations = 100000;
        TestObject obj = new TestObject();
        
        // 直接方法调用
        long start = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            obj.getValue();
        }
        long directTime = System.nanoTime() - start;
        
        // 反射方法调用
        java.lang.reflect.Method method = TestObject.class.getMethod("getValue");
        start = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            method.invoke(obj);
        }
        long reflectionTime = System.nanoTime() - start;
        
        System.out.printf("Direct call: %.2f ms, Reflection call: %.2f ms%n",
                directTime / 1_000_000.0, reflectionTime / 1_000_000.0);
        
        // 直接调用应该更快
        assertTrue(directTime < reflectionTime);
    }
    
    // 辅助方法
    private int computeTask(int input) {
        // 模拟计算密集型任务
        int result = 0;
        for (int i = 0; i < 1000; i++) {
            result += input * i;
        }
        return result;
    }
    
    private boolean isSorted(List<Integer> list) {
        for (int i = 1; i < list.size(); i++) {
            if (list.get(i - 1) > list.get(i)) {
                return false;
            }
        }
        return true;
    }
    
    // 测试用类
    public static class TestObject {
        private int value = 42;
        
        public int getValue() {
            return value;
        }
    }
}
