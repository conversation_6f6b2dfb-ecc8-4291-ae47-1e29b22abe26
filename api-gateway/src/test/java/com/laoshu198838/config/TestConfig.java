package com.laoshu198838.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import static org.mockito.Mockito.mock;

/**
 * 测试环境专用配置类
 * 提供测试所需的各种Bean和配置
 *
 * <AUTHOR>
 */
@Configuration
@Profile("test")
@Import(EmbeddedRedisConfig.class) // 引入Redis测试配置
public class TestConfig {

    /**
     * 提供简单的指标注册表
     */
    @Bean
    @Primary
    public SimpleMeterRegistry meterRegistry() {
        return new SimpleMeterRegistry();
    }
    
    /**
     * 提供模拟的CustomMetricsConfig
     */
    @Bean
    @Primary
    public CustomMetricsConfig customMetricsConfig() {
        return mock(CustomMetricsConfig.class);
    }

    /**
     * 提供用户登录计数器
     */
    @Bean
    public Counter userLoginCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("user.login.total")
                .description("用户登录总次数")
                .register(meterRegistry);
    }

    /**
     * 提供用户登录失败计数器
     */
    @Bean
    public Counter userLoginFailureCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("user.login.failure.total")
                .description("用户登录失败总次数")
                .register(meterRegistry);
    }

    /**
     * 提供数据库查询计时器
     */
    @Bean
    public Timer databaseQueryTimer(SimpleMeterRegistry meterRegistry) {
        return Timer.builder("database.query.duration")
                .description("数据库查询耗时")
                .register(meterRegistry);
    }

    /**
     * 提供债务处理计数器
     */
    @Bean
    public Counter debtProcessCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("debt.process.total")
            .description("债务处理总数")
            .register(meterRegistry);
    }

    /**
     * 提供用户认证计数器
     */
    @Bean
    public Counter userAuthCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("user.auth.total")
            .description("用户认证总数")
            .register(meterRegistry);
    }

    /**
     * 提供数据导出计数器
     */
    @Bean
    public Counter dataExportCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("data.export.total")
            .description("数据导出总数")
            .register(meterRegistry);
    }

    /**
     * 提供数据处理计时器
     */
    @Bean
    public Timer dataProcessingTimer(SimpleMeterRegistry meterRegistry) {
        return Timer.builder("data.processing.time")
            .description("数据处理时间")
            .register(meterRegistry);
    }

    /**
     * 提供债务查询计数器
     */
    @Bean
    public Counter debtQueryCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("debt.query.total")
                .description("债务查询总次数")
                .register(meterRegistry);
    }

    /**
     * 提供报表生成计数器
     */
    @Bean
    public Counter reportGenerationCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("report.generation.total")
                .description("报表生成总次数")
                .register(meterRegistry);
    }

    /**
     * 提供缓存命中计数器
     */
    @Bean
    public Counter cacheHitCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("cache.hit.total")
                .description("缓存命中总次数")
                .register(meterRegistry);
    }

    /**
     * 提供缓存未命中计数器
     */
    @Bean
    public Counter cacheMissCounter(SimpleMeterRegistry meterRegistry) {
        return Counter.builder("cache.miss.total")
                .description("缓存未命中总次数")
                .register(meterRegistry);
    }

    /**
     * 提供API响应时间计时器
     */
    @Bean
    public Timer apiResponseTimer(SimpleMeterRegistry meterRegistry) {
        return Timer.builder("api.response.duration")
                .description("API响应时间")
                .register(meterRegistry);
    }
}
