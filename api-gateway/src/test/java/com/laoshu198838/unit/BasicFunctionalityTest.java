package com.laoshu198838.unit;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基本功能单元测试
 * 验证基本的Java功能和逻辑
 * 
 * <AUTHOR>
 */
public class BasicFunctionalityTest {
    
    @Test
    public void testBasicAssertions() {
        // 基本断言测试
        assertTrue(true);
        assertFalse(false);
        assertEquals(1, 1);
        assertNotNull("test");
        assertNull(null);
    }
    
    @Test
    public void testStringOperations() {
        String test = "Hello World";
        assertEquals(11, test.length());
        assertTrue(test.contains("World"));
        assertEquals("HELLO WORLD", test.toUpperCase());
        assertEquals("hello world", test.toLowerCase());
    }
    
    @Test
    public void testMathOperations() {
        assertEquals(4, 2 + 2);
        assertEquals(0, 2 - 2);
        assertEquals(4, 2 * 2);
        assertEquals(1, 2 / 2);
        assertEquals(1, 5 % 2);
    }
    
    @Test
    public void testCollections() {
        java.util.List<String> list = new java.util.ArrayList<>();
        list.add("item1");
        list.add("item2");
        
        assertEquals(2, list.size());
        assertTrue(list.contains("item1"));
        assertFalse(list.contains("item3"));
        
        java.util.Map<String, String> map = new java.util.HashMap<>();
        map.put("key1", "value1");
        map.put("key2", "value2");
        
        assertEquals(2, map.size());
        assertEquals("value1", map.get("key1"));
        assertTrue(map.containsKey("key2"));
    }
    
    @Test
    public void testExceptionHandling() {
        assertThrows(IllegalArgumentException.class, () -> {
            throw new IllegalArgumentException("Test exception");
        });
        
        assertDoesNotThrow(() -> {
            String test = "no exception";
        });
    }
    
    @Test
    public void testDateAndTime() {
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        assertNotNull(now);
        
        java.time.LocalDate today = java.time.LocalDate.now();
        assertNotNull(today);
        
        java.time.LocalTime currentTime = java.time.LocalTime.now();
        assertNotNull(currentTime);
    }
    
    @Test
    public void testOptional() {
        java.util.Optional<String> optional = java.util.Optional.of("test");
        assertTrue(optional.isPresent());
        assertEquals("test", optional.get());
        
        java.util.Optional<String> empty = java.util.Optional.empty();
        assertFalse(empty.isPresent());
        assertEquals("default", empty.orElse("default"));
    }
    
    @Test
    public void testStreams() {
        java.util.List<Integer> numbers = java.util.Arrays.asList(1, 2, 3, 4, 5);
        
        java.util.List<Integer> evenNumbers = numbers.stream()
                .filter(n -> n % 2 == 0)
                .collect(java.util.stream.Collectors.toList());
        
        assertEquals(2, evenNumbers.size());
        assertTrue(evenNumbers.contains(2));
        assertTrue(evenNumbers.contains(4));
        
        int sum = numbers.stream()
                .mapToInt(Integer::intValue)
                .sum();
        
        assertEquals(15, sum);
    }
    
    @Test
    public void testRegularExpressions() {
        String email = "<EMAIL>";
        String emailPattern = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        
        assertTrue(email.matches(emailPattern));
        
        String phone = "************";
        String phonePattern = "\\d{3}-\\d{3}-\\d{4}";
        
        assertTrue(phone.matches(phonePattern));
    }
    
    @Test
    public void testJsonLikeOperations() {
        // 模拟JSON操作
        java.util.Map<String, Object> jsonLike = new java.util.HashMap<>();
        jsonLike.put("name", "John Doe");
        jsonLike.put("age", 30);
        jsonLike.put("active", true);
        
        assertEquals("John Doe", jsonLike.get("name"));
        assertEquals(30, jsonLike.get("age"));
        assertEquals(true, jsonLike.get("active"));
        
        // 嵌套结构
        java.util.Map<String, Object> address = new java.util.HashMap<>();
        address.put("street", "123 Main St");
        address.put("city", "Anytown");
        jsonLike.put("address", address);
        
        @SuppressWarnings("unchecked")
        java.util.Map<String, Object> retrievedAddress = (java.util.Map<String, Object>) jsonLike.get("address");
        assertEquals("123 Main St", retrievedAddress.get("street"));
    }
    
    @Test
    public void testConcurrency() throws InterruptedException {
        java.util.concurrent.atomic.AtomicInteger counter = new java.util.concurrent.atomic.AtomicInteger(0);
        
        java.util.concurrent.ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(2);
        
        for (int i = 0; i < 10; i++) {
            executor.submit(() -> counter.incrementAndGet());
        }
        
        executor.shutdown();
        executor.awaitTermination(1, java.util.concurrent.TimeUnit.SECONDS);
        
        assertEquals(10, counter.get());
    }
    
    @Test
    public void testFileOperations() throws java.io.IOException {
        // 创建临时文件
        java.nio.file.Path tempFile = java.nio.file.Files.createTempFile("test", ".txt");
        
        try {
            // 写入文件
            String content = "Hello, World!";
            java.nio.file.Files.write(tempFile, content.getBytes());
            
            // 读取文件
            String readContent = new String(java.nio.file.Files.readAllBytes(tempFile));
            assertEquals(content, readContent);
            
            // 检查文件存在
            assertTrue(java.nio.file.Files.exists(tempFile));
            
        } finally {
            // 清理临时文件
            java.nio.file.Files.deleteIfExists(tempFile);
        }
    }
    
    @Test
    public void testReflection() throws Exception {
        Class<?> stringClass = String.class;
        assertEquals("String", stringClass.getSimpleName());
        assertEquals("java.lang.String", stringClass.getName());
        
        // 获取方法
        java.lang.reflect.Method lengthMethod = stringClass.getMethod("length");
        assertNotNull(lengthMethod);
        
        // 调用方法
        String testString = "test";
        Object result = lengthMethod.invoke(testString);
        assertEquals(4, result);
    }
    
    @Test
    public void testSerialization() throws Exception {
        // 测试序列化
        TestSerializableObject original = new TestSerializableObject("test", 123);
        
        // 序列化到字节数组
        java.io.ByteArrayOutputStream baos = new java.io.ByteArrayOutputStream();
        java.io.ObjectOutputStream oos = new java.io.ObjectOutputStream(baos);
        oos.writeObject(original);
        oos.close();
        
        // 从字节数组反序列化
        java.io.ByteArrayInputStream bais = new java.io.ByteArrayInputStream(baos.toByteArray());
        java.io.ObjectInputStream ois = new java.io.ObjectInputStream(bais);
        TestSerializableObject deserialized = (TestSerializableObject) ois.readObject();
        ois.close();
        
        assertEquals(original.getName(), deserialized.getName());
        assertEquals(original.getValue(), deserialized.getValue());
    }
    
    // 测试用的可序列化对象
    public static class TestSerializableObject implements java.io.Serializable {
        private static final long serialVersionUID = 1L;
        
        private String name;
        private Integer value;
        
        public TestSerializableObject(String name, Integer value) {
            this.name = name;
            this.value = value;
        }
        
        public String getName() { return name; }
        public Integer getValue() { return value; }
    }
}
