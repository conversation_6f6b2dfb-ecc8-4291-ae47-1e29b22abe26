//package com.laoshu198838.repository;
//
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//@SpringBootTest
//public class OverdueDebtRepositoryTest {
//
//    @Autowired
//    private OverdueDebtSummaryRepository overdueDebtRepository;
//
//    @Test
//    public void testFindTotalReductionAmount() {
//        // 调用 Repository 中的方法
//        Double totalReductionAmount = overdueDebtRepository.findTotalReductionAmount();
//
//        // 输出查询结果
//        System.out.println("总减少债权金额：" + (totalReductionAmount != null ? totalReductionAmount : 0.0));
//    }
//}
