spring:
  # 数据库配置 - 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver

  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: update
    show-sql: true

  # Redis配置 - 使用嵌入式Redis进行测试
  data:
    redis:
      host: localhost
      port: 6370
      timeout: 2000

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 3600
      cache-null-values: false
    default-ttl: 3600
    prefix:
      user: users
      debt: debts
      report: reports
      config: config

# 日志配置
logging:
  level:
    root: INFO
    com.laoshu198838: DEBUG
    org.springframework.data.redis: DEBUG
    org.springframework.cache: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 应用配置
app:
  cors:
    allowed-origins: "http://localhost:3000,http://localhost:5173"
  jwt:
    secret: "test-secret-key-for-jwt-token-generation-in-test-environment"
    expiration: 86400000

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
