package com.laoshu198838.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自定义业务指标配置
 * 定义业务相关的监控指标，使用Micrometer框架进行应用性能监控
 * 
 * 功能特性：
 * - 用户行为监控：登录成功/失败计数、活跃用户数统计
 * - 业务操作监控：债务查询、报表生成、数据导出计数
 * - 系统性能监控：缓存命中率、数据库查询时间、API响应时间
 * - 资源监控：Redis连接数、数据库连接池状态
 * 
 * 使用情况：
 * - MonitoringController: 注入并使用各种计数器和计时器
 * - 测试类: MonitoringIntegrationTest、TestConfig 中使用
 * - Spring Boot Actuator: 通过 /actuator/metrics 端点暴露指标
 * - 定时任务: updateBusinessMetrics() 方法定期更新业务指标
 * 
 * 监控指标类型：
 * - Counter: 累加型指标（登录次数、查询次数等）
 * - Gauge: 瞬时值指标（活跃用户数、连接数等）
 * - Timer: 时间测量指标（响应时间、查询时间等）
 *
 * <AUTHOR>
 */
@Configuration
@Profile("!test")
public class CustomMetricsConfig {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 业务指标计数器
    private final AtomicInteger activeUsers = new AtomicInteger(0);
    private final AtomicInteger totalDebts = new AtomicInteger(0);
    private final AtomicInteger totalReports = new AtomicInteger(0);

    /**
     * 用户登录计数器
     */
    @Bean
    public Counter userLoginCounter() {
        return Counter.builder("user.login.total")
                .description("Total number of user logins")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 用户登录失败计数器
     */
    @Bean
    public Counter userLoginFailureCounter() {
        return Counter.builder("user.login.failure.total")
                .description("Total number of failed user logins")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 债务查询计数器
     */
    @Bean
    public Counter debtQueryCounter() {
        return Counter.builder("debt.query.total")
                .description("Total number of debt queries")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 报表生成计数器
     */
    @Bean
    public Counter reportGenerationCounter() {
        return Counter.builder("report.generation.total")
                .description("Total number of report generations")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 数据导出计数器
     */
    @Bean
    public Counter dataExportCounter() {
        return Counter.builder("data.export.total")
                .description("Total number of data exports")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 缓存命中计数器
     */
    @Bean
    public Counter cacheHitCounter() {
        return Counter.builder("cache.hit.total")
                .description("Total number of cache hits")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 缓存未命中计数器
     */
    @Bean
    public Counter cacheMissCounter() {
        return Counter.builder("cache.miss.total")
                .description("Total number of cache misses")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 数据库查询时间计时器
     */
    @Bean
    public Timer databaseQueryTimer() {
        return Timer.builder("database.query.duration")
                .description("Database query execution time")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * API响应时间计时器
     */
    @Bean
    public Timer apiResponseTimer() {
        return Timer.builder("api.response.duration")
                .description("API response time")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 活跃用户数量指标
     */
    @Bean
    public Gauge activeUsersGauge() {
        return Gauge.builder("users.active.count", activeUsers, AtomicInteger::get)
                .description("Number of active users")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 债务总数指标
     */
    @Bean
    public Gauge totalDebtsGauge() {
        return Gauge.builder("debts.total.count", totalDebts, AtomicInteger::get)
                .description("Total number of debts")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 报表总数指标
     */
    @Bean
    public Gauge totalReportsGauge() {
        return Gauge.builder("reports.total.count", totalReports, AtomicInteger::get)
                .description("Total number of reports")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * Redis连接数指标
     */
    @Bean
    public Gauge redisConnectionsGauge() {
        return Gauge.builder("redis.connections.active", this, CustomMetricsConfig::getRedisConnections)
                .description("Number of active Redis connections")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 数据库连接池指标
     */
    @Bean
    public Gauge databaseConnectionsGauge() {
        return Gauge.builder("database.connections.active", this, CustomMetricsConfig::getDatabaseConnections)
                .description("Number of active database connections")
                .tag("application", "financial-system")
                .register(meterRegistry);
    }

    /**
     * 获取Redis连接数
     */
    private double getRedisConnections() {
        try {
            // 这里可以通过Redis INFO命令获取连接数
            // 简化实现，返回固定值
            return 1.0;
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * 获取数据库连接数
     */
    private double getDatabaseConnections() {
        try (Connection connection = dataSource.getConnection()) {
            // 这里可以查询数据库连接池状态
            // 简化实现，返回固定值
            return 1.0;
        } catch (Exception e) {
            return 0.0;
        }
    }

    /**
     * 更新活跃用户数
     */
    public void updateActiveUsers(int count) {
        activeUsers.set(count);
    }

    /**
     * 更新债务总数
     */
    public void updateTotalDebts(int count) {
        totalDebts.set(count);
    }

    /**
     * 更新报表总数
     */
    public void updateTotalReports(int count) {
        totalReports.set(count);
    }

    /**
     * 定期更新业务指标
     */
    public void updateBusinessMetrics() {
        try {
            // 更新活跃用户数
            updateActiveUsersFromDatabase();

            // 更新债务总数
            updateTotalDebtsFromDatabase();

            // 更新报表总数
            updateTotalReportsFromDatabase();

        } catch (Exception e) {
            // 记录错误但不影响应用运行
            System.err.println("Failed to update business metrics: " + e.getMessage());
        }
    }

    private void updateActiveUsersFromDatabase() {
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(
                     "SELECT COUNT(*) FROM users WHERE status = 'ACTIVE'")) {

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                updateActiveUsers(rs.getInt(1));
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }

    private void updateTotalDebtsFromDatabase() {
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(
                     "SELECT COUNT(*) FROM non_litigation_claim")) {

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                updateTotalDebts(rs.getInt(1));
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }

    private void updateTotalReportsFromDatabase() {
        // 这里可以统计报表相关的数据
        // 简化实现
        updateTotalReports(0);
    }
}
