package com.laoshu198838.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

/**
 * 定时任务配置类
 * 确保Spring定时任务功能被正确启用
 * 已解决autoCommit事务冲突问题，重新启用定时任务
 * 注意：@EnableScheduling已在Main.java中启用，避免重复配置
 */
@Configuration
public class SchedulingConfig implements SchedulingConfigurer {

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        // 使用自定义的TaskScheduler
        taskRegistrar.setTaskScheduler(taskScheduler());
    }

    /**
     * 创建一个TaskScheduler Bean
     * 解决Spring找不到TaskScheduler的问题
     *
     * @return TaskScheduler实例
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10); // 设置线程池大小
        scheduler.setThreadNamePrefix("scheduled-task-"); // 设置线程名前缀
        scheduler.initialize();
        return scheduler;
    }
}