package com.laoshu198838.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义健康检查指标
 * 检查数据库连接、Redis连接等关键组件的健康状态
 * 
 * 功能特性：
 * - 数据库健康检查：验证数据库连接有效性，获取连接元数据
 * - Redis健康检查：通过PING命令验证Redis连接状态
 * - 内存使用监控：检查JVM内存使用情况，超过90%时发出警告
 * - 磁盘空间监控：检查根目录磁盘使用率，超过85%时发出警告
 * 
 * 使用情况：
 * - Spring Boot Actuator: 通过 @Component 注解自动注册
 * - 健康检查端点: /actuator/health 自动包含此组件的检查结果
 * - 监控系统: 可通过健康检查端点监控应用状态
 * - 负载均衡: 可用于负载均衡器的健康检查
 * 
 * 检查项目：
 * - database: 数据库连接状态和元数据
 * - redis: Redis连接状态
 * - memory: JVM内存使用详情
 * - disk: 磁盘空间使用情况
 * 
 * <AUTHOR>
 */
@Component
public class CustomHealthIndicator implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public Health health() {
        Map<String, Object> details = new HashMap<>();
        boolean isHealthy = true;
        
        // 检查数据库连接
        try {
            checkDatabaseHealth(details);
        } catch (Exception e) {
            details.put("database", "DOWN: " + e.getMessage());
            isHealthy = false;
        }
        
        // 检查Redis连接
        try {
            checkRedisHealth(details);
        } catch (Exception e) {
            details.put("redis", "DOWN: " + e.getMessage());
            isHealthy = false;
        }
        
        // 检查内存使用情况
        checkMemoryHealth(details);
        
        // 检查磁盘空间
        checkDiskHealth(details);
        
        if (isHealthy) {
            return Health.up().withDetails(details).build();
        } else {
            return Health.down().withDetails(details).build();
        }
    }
    
    /**
     * 检查数据库健康状态
     */
    private void checkDatabaseHealth(Map<String, Object> details) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                details.put("database", "UP");
                details.put("database_url", connection.getMetaData().getURL());
                details.put("database_driver", connection.getMetaData().getDriverName());
            } else {
                throw new SQLException("Database connection is not valid");
            }
        }
    }
    
    /**
     * 检查Redis健康状态
     */
    private void checkRedisHealth(Map<String, Object> details) {
        try {
            String pong = redisTemplate.getConnectionFactory().getConnection().ping();
            if ("PONG".equals(pong)) {
                details.put("redis", "UP");
                details.put("redis_response", pong);
            } else {
                throw new RuntimeException("Redis ping failed: " + pong);
            }
        } catch (Exception e) {
            throw new RuntimeException("Redis connection failed", e);
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private void checkMemoryHealth(Map<String, Object> details) {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        
        Map<String, Object> memoryDetails = new HashMap<>();
        memoryDetails.put("max", formatBytes(maxMemory));
        memoryDetails.put("total", formatBytes(totalMemory));
        memoryDetails.put("used", formatBytes(usedMemory));
        memoryDetails.put("free", formatBytes(freeMemory));
        memoryDetails.put("usage_percent", String.format("%.2f%%", memoryUsagePercent));
        
        details.put("memory", memoryDetails);
        
        // 如果内存使用超过90%，标记为警告
        if (memoryUsagePercent > 90) {
            details.put("memory_warning", "High memory usage: " + String.format("%.2f%%", memoryUsagePercent));
        }
    }
    
    /**
     * 检查磁盘空间
     */
    private void checkDiskHealth(Map<String, Object> details) {
        try {
            java.io.File root = new java.io.File("/");
            long totalSpace = root.getTotalSpace();
            long freeSpace = root.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            
            double diskUsagePercent = (double) usedSpace / totalSpace * 100;
            
            Map<String, Object> diskDetails = new HashMap<>();
            diskDetails.put("total", formatBytes(totalSpace));
            diskDetails.put("used", formatBytes(usedSpace));
            diskDetails.put("free", formatBytes(freeSpace));
            diskDetails.put("usage_percent", String.format("%.2f%%", diskUsagePercent));
            
            details.put("disk", diskDetails);
            
            // 如果磁盘使用超过85%，标记为警告
            if (diskUsagePercent > 85) {
                details.put("disk_warning", "High disk usage: " + String.format("%.2f%%", diskUsagePercent));
            }
        } catch (Exception e) {
            details.put("disk", "Unable to check disk space: " + e.getMessage());
        }
    }
    
    /**
     * 格式化字节数为可读格式
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
