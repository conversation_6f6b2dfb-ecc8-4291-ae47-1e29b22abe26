package com.laoshu198838.config;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;

/**
 * Redis缓存配置
 * 配置Redis连接、序列化和缓存管理器
 * 
 * 功能特性：
 * - RedisTemplate配置：支持对象序列化/反序列化
 * - 缓存管理器：支持多种缓存策略和过期时间
 * - JSON序列化：使用Jackson进行对象序列化
 * - 分层缓存：不同业务模块使用不同的缓存策略
 * 
 * 使用情况：
 * - CacheService: 通过redisTemplate进行缓存操作
 * - CustomHealthIndicator: 使用redisTemplate进行健康检查
 * - Spring Cache: 通过@Cacheable等注解使用cacheManager
 * - 多个业务服务: 使用缓存提高查询性能
 * 
 * 缓存策略：
 * - users: 用户信息缓存（30分钟）
 * - debts: 债务数据缓存（5分钟）
 * - reports: 报表数据缓存（15分钟）
 * - config: 系统配置缓存（60分钟）
 * - permissions: 权限信息缓存（20分钟）
 * - dictionary: 数据字典缓存（120分钟）
 * 
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class RedisConfig {

    /**
     * 配置RedisTemplate
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        // 使用新的构造函数方式，避免过时的setObjectMapper方法
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);

        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(om, Object.class);

        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();

        return template;
    }

    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        // 配置序列化
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10)) // 默认缓存时间10分钟
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jackson2JsonRedisSerializer()))
                .disableCachingNullValues(); // 不缓存空值

        // 设置不同缓存的过期时间
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // 用户信息缓存 - 30分钟
        cacheConfigurations.put("users", config.entryTtl(Duration.ofMinutes(30)));

        // 债务数据缓存 - 5分钟
        cacheConfigurations.put("debts", config.entryTtl(Duration.ofMinutes(5)));

        // 报表数据缓存 - 15分钟
        cacheConfigurations.put("reports", config.entryTtl(Duration.ofMinutes(15)));

        // 系统配置缓存 - 60分钟
        cacheConfigurations.put("config", config.entryTtl(Duration.ofMinutes(60)));

        // 权限信息缓存 - 20分钟
        cacheConfigurations.put("permissions", config.entryTtl(Duration.ofMinutes(20)));

        // 数据字典缓存 - 120分钟
        cacheConfigurations.put("dictionary", config.entryTtl(Duration.ofMinutes(120)));

        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(config)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * Jackson2JsonRedisSerializer序列化器
     */
    private Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer() {
        // 使用新的构造函数方式，避免过时的setObjectMapper方法
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);

        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(om, Object.class);
        return jackson2JsonRedisSerializer;
    }
}
