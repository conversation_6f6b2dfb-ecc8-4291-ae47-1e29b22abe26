package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 债务数据更新定时任务代理服务
 * 由于data-processing模块中的@Scheduled注解不被识别，
 * 在api-gateway模块中创建代理来调用实际的业务方法
 */
@Service
public class DebtUpdateSchedulerProxy {

    private static final Logger logger = LoggerFactory.getLogger(DebtUpdateSchedulerProxy.class);

    @Autowired
    private OverdueDebtUpdateService overdueDebtUpdateService;

    /**
     * 债务数据更新定时任务
     * 每3分钟执行一次债务数据更新
     */
    @Scheduled(fixedRate = 180000, initialDelay = 10000) // 启动10秒后执行，然后每3分钟执行一次
    public void scheduleDebtUpdate() {
        try {
            logger.info("开始执行定时债务数据更新任务");
            overdueDebtUpdateService.scheduleOverdueYearUpdate();
            logger.info("定时债务数据更新任务执行完成");
        } catch (Exception e) {
            logger.error("定时债务数据更新任务执行异常", e);
        }
    }
}
