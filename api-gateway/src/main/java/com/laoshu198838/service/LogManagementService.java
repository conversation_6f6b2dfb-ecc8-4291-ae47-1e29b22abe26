package com.laoshu198838.service;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日志管理服务
 * 提供日志级别动态调整、日志文件管理等功能
 * 
 * <AUTHOR>
 */
@Service
public class LogManagementService {
    
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(LogManagementService.class);
    
    private final LoggerContext loggerContext;
    private final String logPath;
    
    public LogManagementService() {
        this.loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        this.logPath = System.getProperty("LOG_PATH", "./logs");
    }
    
    /**
     * 获取所有日志器信息
     */
    public List<Map<String, Object>> getAllLoggers() {
        List<Map<String, Object>> loggers = new ArrayList<>();
        
        for (Logger logger : loggerContext.getLoggerList()) {
            if (logger.getName().startsWith("com.laoshu198838") || 
                logger.getName().equals("ROOT") ||
                logger.getName().startsWith("org.springframework") ||
                logger.getName().startsWith("org.hibernate")) {
                
                Map<String, Object> loggerInfo = new HashMap<>();
                loggerInfo.put("name", logger.getName());
                loggerInfo.put("level", logger.getLevel() != null ? logger.getLevel().toString() : "INHERITED");
                loggerInfo.put("effectiveLevel", logger.getEffectiveLevel().toString());
                loggerInfo.put("additivity", logger.isAdditive());
                loggerInfo.put("appenders", logger.iteratorForAppenders().hasNext());
                
                loggers.add(loggerInfo);
            }
        }
        
        return loggers.stream()
                .sorted(Comparator.comparing(m -> (String) m.get("name")))
                .collect(Collectors.toList());
    }
    
    /**
     * 设置日志器级别
     */
    public boolean setLoggerLevel(String loggerName, String level) {
        try {
            // 首先验证日志级别是否有效
            if (!isValidLogLevel(level)) {
                LogManagementService.logger.warn("无效的日志级别: {}", level);
                return false;
            }
            
            Logger logger = loggerContext.getLogger(loggerName);
            Level logLevel = Level.valueOf(level.toUpperCase());
            logger.setLevel(logLevel);
            
            LogManagementService.logger.info("设置日志器 {} 级别为 {}", loggerName, level);
            return true;
        } catch (Exception e) {
            LogManagementService.logger.error("设置日志器级别失败: loggerName={}, level={}", loggerName, level, e);
            return false;
        }
    }
    
    /**
     * 验证日志级别是否有效
     */
    private boolean isValidLogLevel(String level) {
        if (level == null || level.trim().isEmpty()) {
            return false;
        }
        
        String upperLevel = level.toUpperCase().trim();
        return getAvailableLogLevels().contains(upperLevel);
    }
    
    /**
     * 重置日志器级别
     */
    public boolean resetLoggerLevel(String loggerName) {
        try {
            Logger logger = loggerContext.getLogger(loggerName);
            logger.setLevel(null); // 重置为继承父级别
            
            LogManagementService.logger.info("重置日志器 {} 级别", loggerName);
            return true;
        } catch (Exception e) {
            LogManagementService.logger.error("重置日志器级别失败: loggerName={}", loggerName, e);
            return false;
        }
    }
    
    /**
     * 获取日志文件列表
     */
    public List<Map<String, Object>> getLogFiles() {
        List<Map<String, Object>> logFiles = new ArrayList<>();
        
        try {
            Path logDir = Paths.get(logPath);
            if (!Files.exists(logDir)) {
                return logFiles;
            }
            
            Files.list(logDir)
                    .filter(path -> path.toString().endsWith(".log"))
                    .forEach(path -> {
                        try {
                            File file = path.toFile();
                            Map<String, Object> fileInfo = new HashMap<>();
                            fileInfo.put("name", file.getName());
                            fileInfo.put("path", file.getAbsolutePath());
                            fileInfo.put("size", file.length());
                            fileInfo.put("sizeFormatted", formatFileSize(file.length()));
                            fileInfo.put("lastModified", file.lastModified());
                            fileInfo.put("lastModifiedFormatted", 
                                    new Date(file.lastModified()).toString());
                            
                            logFiles.add(fileInfo);
                        } catch (Exception e) {
                            LogManagementService.logger.warn("获取日志文件信息失败: {}", path, e);
                        }
                    });
            
        } catch (IOException e) {
            LogManagementService.logger.error("获取日志文件列表失败", e);
        }
        
        return logFiles.stream()
                .sorted(Comparator.comparing((Map<String, Object> m) -> (String) m.get("name")))
                .collect(Collectors.toList());
    }
    
    /**
     * 读取日志文件内容（最后N行）
     */
    public List<String> readLogFile(String fileName, int lines) {
        List<String> content = new ArrayList<>();
        
        try {
            Path filePath = Paths.get(logPath, fileName);
            if (!Files.exists(filePath)) {
                return content;
            }
            
            List<String> allLines = Files.readAllLines(filePath);
            int startIndex = Math.max(0, allLines.size() - lines);
            content = allLines.subList(startIndex, allLines.size());
            
        } catch (IOException e) {
            LogManagementService.logger.error("读取日志文件失败: {}", fileName, e);
            content.add("读取日志文件失败: " + e.getMessage());
        }
        
        return content;
    }
    
    /**
     * 删除日志文件
     */
    public boolean deleteLogFile(String fileName) {
        try {
            Path filePath = Paths.get(logPath, fileName);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                LogManagementService.logger.info("删除日志文件: {}", fileName);
                return true;
            }
            return false;
        } catch (IOException e) {
            LogManagementService.logger.error("删除日志文件失败: {}", fileName, e);
            return false;
        }
    }
    
    /**
     * 清理旧日志文件
     */
    public Map<String, Object> cleanOldLogFiles(int daysToKeep) {
        Map<String, Object> result = new HashMap<>();
        int deletedCount = 0;
        long deletedSize = 0;
        
        try {
            Path logDir = Paths.get(logPath);
            if (!Files.exists(logDir)) {
                result.put("success", false);
                result.put("message", "日志目录不存在");
                return result;
            }
            
            long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24L * 60 * 60 * 1000);
            
            List<Path> filesToDelete = Files.list(logDir)
                    .filter(path -> path.toString().endsWith(".log"))
                    .filter(path -> {
                        try {
                            return Files.getLastModifiedTime(path).toMillis() < cutoffTime;
                        } catch (IOException e) {
                            return false;
                        }
                    })
                    .collect(Collectors.toList());
            
            for (Path path : filesToDelete) {
                try {
                    long fileSize = Files.size(path);
                    Files.delete(path);
                    deletedCount++;
                    deletedSize += fileSize;
                    LogManagementService.logger.info("清理旧日志文件: {}", path.getFileName());
                } catch (IOException e) {
                    LogManagementService.logger.warn("删除旧日志文件失败: {}", path, e);
                }
            }
            
            result.put("success", true);
            result.put("deletedCount", deletedCount);
            result.put("deletedSize", deletedSize);
            result.put("deletedSizeFormatted", formatFileSize(deletedSize));
            result.put("message", String.format("清理完成，删除了 %d 个文件，释放了 %s 空间", 
                    deletedCount, formatFileSize(deletedSize)));
            
        } catch (IOException e) {
            LogManagementService.logger.error("清理旧日志文件失败", e);
            result.put("success", false);
            result.put("message", "清理失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取日志统计信息
     */
    public Map<String, Object> getLogStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            Path logDir = Paths.get(logPath);
            if (!Files.exists(logDir)) {
                stats.put("totalFiles", 0);
                stats.put("totalSize", 0);
                stats.put("totalSizeFormatted", "0 B");
                return stats;
            }
            
            List<Path> logFiles = Files.list(logDir)
                    .filter(path -> path.toString().endsWith(".log"))
                    .collect(Collectors.toList());
            
            long totalSize = logFiles.stream()
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .sum();
            
            stats.put("totalFiles", logFiles.size());
            stats.put("totalSize", totalSize);
            stats.put("totalSizeFormatted", formatFileSize(totalSize));
            stats.put("logPath", logPath);
            
            // 按类型统计
            Map<String, Integer> typeStats = new HashMap<>();
            Map<String, Long> typeSizeStats = new HashMap<>();
            
            for (Path path : logFiles) {
                String fileName = path.getFileName().toString();
                String type = "other";
                
                if (fileName.contains("-error.")) {
                    type = "error";
                } else if (fileName.contains("-business.")) {
                    type = "business";
                } else if (fileName.contains("-performance.")) {
                    type = "performance";
                } else if (fileName.contains("-all.")) {
                    type = "all";
                }
                
                typeStats.put(type, typeStats.getOrDefault(type, 0) + 1);
                try {
                    typeSizeStats.put(type, typeSizeStats.getOrDefault(type, 0L) + Files.size(path));
                } catch (IOException e) {
                    // 忽略错误
                }
            }
            
            stats.put("typeStats", typeStats);
            stats.put("typeSizeStats", typeSizeStats);
            
        } catch (IOException e) {
            LogManagementService.logger.error("获取日志统计信息失败", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
    
    /**
     * 获取可用的日志级别列表
     */
    public List<String> getAvailableLogLevels() {
        return Arrays.asList("TRACE", "DEBUG", "INFO", "WARN", "ERROR", "OFF");
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        int exp = (int) (Math.log(size) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", size / Math.pow(1024, exp), pre);
    }
}
