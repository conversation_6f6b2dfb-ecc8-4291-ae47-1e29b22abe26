package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务
 * 提供Redis缓存的基础操作
 * 
 * <AUTHOR>
 */
@Service
public class CacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheService.class);
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 设置缓存
     */
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            logger.debug("设置缓存成功: key={}", key);
        } catch (Exception e) {
            logger.error("设置缓存失败: key={}", key, e);
        }
    }
    
    /**
     * 设置缓存并指定过期时间
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            logger.debug("设置缓存成功: key={}, timeout={} {}", key, timeout, unit);
        } catch (Exception e) {
            logger.error("设置缓存失败: key={}", key, e);
        }
    }
    
    /**
     * 获取缓存
     */
    public Object get(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            logger.debug("获取缓存: key={}, found={}", key, value != null);
            return value;
        } catch (Exception e) {
            logger.error("获取缓存失败: key={}", key, e);
            return null;
        }
    }
    
    /**
     * 获取缓存并指定类型
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value != null && type.isInstance(value)) {
                logger.debug("获取缓存成功: key={}, type={}", key, type.getSimpleName());
                return (T) value;
            }
            logger.debug("获取缓存失败或类型不匹配: key={}, type={}", key, type.getSimpleName());
            return null;
        } catch (Exception e) {
            logger.error("获取缓存失败: key={}", key, e);
            return null;
        }
    }
    
    /**
     * 删除缓存
     */
    public void delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            logger.debug("删除缓存: key={}, success={}", key, result);
        } catch (Exception e) {
            logger.error("删除缓存失败: key={}", key, e);
        }
    }
    
    /**
     * 批量删除缓存
     */
    public void delete(Collection<String> keys) {
        try {
            Long result = redisTemplate.delete(keys);
            logger.debug("批量删除缓存: count={}, deleted={}", keys.size(), result);
        } catch (Exception e) {
            logger.error("批量删除缓存失败: keys={}", keys, e);
        }
    }
    
    /**
     * 检查缓存是否存在
     */
    public boolean exists(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            logger.debug("检查缓存存在: key={}, exists={}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            logger.error("检查缓存存在失败: key={}", key, e);
            return false;
        }
    }
    
    /**
     * 设置缓存过期时间
     */
    public void expire(String key, long timeout, TimeUnit unit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, unit);
            logger.debug("设置缓存过期时间: key={}, timeout={} {}, success={}", key, timeout, unit, result);
        } catch (Exception e) {
            logger.error("设置缓存过期时间失败: key={}", key, e);
        }
    }
    
    /**
     * 获取缓存过期时间
     */
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key);
            logger.debug("获取缓存过期时间: key={}, expire={}", key, expire);
            return expire != null ? expire : -1;
        } catch (Exception e) {
            logger.error("获取缓存过期时间失败: key={}", key, e);
            return -1;
        }
    }
    
    /**
     * 根据模式查找缓存键
     */
    public Set<String> keys(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            logger.debug("查找缓存键: pattern={}, count={}", pattern, keys != null ? keys.size() : 0);
            return keys;
        } catch (Exception e) {
            logger.error("查找缓存键失败: pattern={}", pattern, e);
            return null;
        }
    }
    
    /**
     * 清空所有缓存
     */
    public void flushAll() {
        try {
            Set<String> keys = redisTemplate.keys("*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                logger.info("清空所有缓存: count={}", keys.size());
            }
        } catch (Exception e) {
            logger.error("清空所有缓存失败", e);
        }
    }
    
    /**
     * 根据前缀清空缓存
     */
    public void flushByPrefix(String prefix) {
        try {
            Set<String> keys = redisTemplate.keys(prefix + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                logger.info("清空前缀缓存: prefix={}, count={}", prefix, keys.size());
            }
        } catch (Exception e) {
            logger.error("清空前缀缓存失败: prefix={}", prefix, e);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        try {
            Set<String> allKeys = redisTemplate.keys("*");
            int totalKeys = allKeys != null ? allKeys.size() : 0;
            
            // 按前缀统计
            int userKeys = 0;
            int debtKeys = 0;
            int reportKeys = 0;
            int configKeys = 0;
            int otherKeys = 0;
            
            if (allKeys != null) {
                for (String key : allKeys) {
                    if (key.startsWith("users:")) {
                        userKeys++;
                    } else if (key.startsWith("debts:")) {
                        debtKeys++;
                    } else if (key.startsWith("reports:")) {
                        reportKeys++;
                    } else if (key.startsWith("config:")) {
                        configKeys++;
                    } else {
                        otherKeys++;
                    }
                }
            }
            
            return new CacheStats(totalKeys, userKeys, debtKeys, reportKeys, configKeys, otherKeys);
        } catch (Exception e) {
            logger.error("获取缓存统计信息失败", e);
            return new CacheStats(0, 0, 0, 0, 0, 0);
        }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private final int totalKeys;
        private final int userKeys;
        private final int debtKeys;
        private final int reportKeys;
        private final int configKeys;
        private final int otherKeys;
        
        public CacheStats(int totalKeys, int userKeys, int debtKeys, int reportKeys, int configKeys, int otherKeys) {
            this.totalKeys = totalKeys;
            this.userKeys = userKeys;
            this.debtKeys = debtKeys;
            this.reportKeys = reportKeys;
            this.configKeys = configKeys;
            this.otherKeys = otherKeys;
        }
        
        // Getters
        public int getTotalKeys() { return totalKeys; }
        public int getUserKeys() { return userKeys; }
        public int getDebtKeys() { return debtKeys; }
        public int getReportKeys() { return reportKeys; }
        public int getConfigKeys() { return configKeys; }
        public int getOtherKeys() { return otherKeys; }
    }
}
