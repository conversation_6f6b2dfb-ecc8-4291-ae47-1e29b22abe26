package com.laoshu198838.service;

import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationInfo;
import org.flywaydb.core.api.MigrationInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库版本管理服务
 * 提供数据库迁移和版本管理功能
 * 
 * <AUTHOR>
 */
@Service
public class DatabaseVersionService {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseVersionService.class);
    
    @Autowired
    private DataSource dataSource;
    
    /**
     * 获取数据库迁移信息
     */
    public Map<String, Object> getMigrationInfo() {
        try {
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/migration")
                    .table("flyway_schema_history")
                    .baselineOnMigrate(true)
                    .load();
            
            MigrationInfoService infoService = flyway.info();
            MigrationInfo[] migrations = infoService.all();
            
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> migrationList = new ArrayList<>();
            
            for (MigrationInfo migration : migrations) {
                Map<String, Object> migrationMap = new HashMap<>();
                migrationMap.put("version", migration.getVersion() != null ? migration.getVersion().toString() : "");
                migrationMap.put("description", migration.getDescription());
                migrationMap.put("type", migration.getType().toString());
                migrationMap.put("state", migration.getState().toString());
                migrationMap.put("installedOn", migration.getInstalledOn());
                migrationMap.put("executionTime", migration.getExecutionTime());
                migrationList.add(migrationMap);
            }
            
            result.put("migrations", migrationList);
            result.put("current", infoService.current() != null ? infoService.current().getVersion().toString() : "");
            result.put("pending", infoService.pending().length);
            result.put("applied", infoService.applied().length);
            
            return result;
            
        } catch (Exception e) {
            logger.error("获取数据库迁移信息失败", e);
            throw new RuntimeException("获取数据库迁移信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行数据库迁移
     */
    public Map<String, Object> migrate() {
        try {
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/migration")
                    .table("flyway_schema_history")
                    .baselineOnMigrate(true)
                    .load();
            
            int migrationsExecuted = flyway.migrate().migrationsExecuted;
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("migrationsExecuted", migrationsExecuted);
            result.put("message", "数据库迁移完成，执行了 " + migrationsExecuted + " 个迁移脚本");
            
            logger.info("数据库迁移完成，执行了 {} 个迁移脚本", migrationsExecuted);
            
            return result;
            
        } catch (Exception e) {
            logger.error("数据库迁移失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "数据库迁移失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 验证数据库迁移
     */
    public Map<String, Object> validate() {
        try {
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/migration")
                    .table("flyway_schema_history")
                    .baselineOnMigrate(true)
                    .load();
            
            flyway.validate();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "数据库迁移验证通过");
            
            logger.info("数据库迁移验证通过");
            
            return result;
            
        } catch (Exception e) {
            logger.error("数据库迁移验证失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "数据库迁移验证失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取当前数据库版本
     */
    public String getCurrentVersion() {
        try {
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/migration")
                    .table("flyway_schema_history")
                    .baselineOnMigrate(true)
                    .load();
            
            MigrationInfo current = flyway.info().current();
            return current != null ? current.getVersion().toString() : "0";
            
        } catch (Exception e) {
            logger.error("获取当前数据库版本失败", e);
            return "unknown";
        }
    }
    
    /**
     * 检查是否有待执行的迁移
     */
    public boolean hasPendingMigrations() {
        try {
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/migration")
                    .table("flyway_schema_history")
                    .baselineOnMigrate(true)
                    .load();
            
            return flyway.info().pending().length > 0;
            
        } catch (Exception e) {
            logger.error("检查待执行迁移失败", e);
            return false;
        }
    }
    
    /**
     * 修复数据库迁移（清理失败的迁移）
     */
    public Map<String, Object> repair() {
        try {
            Flyway flyway = Flyway.configure()
                    .dataSource(dataSource)
                    .locations("classpath:db/migration")
                    .table("flyway_schema_history")
                    .baselineOnMigrate(true)
                    .load();
            
            flyway.repair();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "数据库迁移修复完成");
            
            logger.info("数据库迁移修复完成");
            
            return result;
            
        } catch (Exception e) {
            logger.error("数据库迁移修复失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "数据库迁移修复失败: " + e.getMessage());
            return result;
        }
    }
}
