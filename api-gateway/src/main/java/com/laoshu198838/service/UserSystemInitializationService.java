package com.laoshu198838.service;

import java.sql.Connection;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.stereotype.Service;

/// **
// * 用户系统初始化服务
// * 负责在应用启动时自动初始化user_system数据库
// *
// * <AUTHOR>
// */
@Service
public class UserSystemInitializationService implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(UserSystemInitializationService.class);

    @Autowired
    @Qualifier("userSystemDataSource")
    private DataSource userSystemDataSource;

    @Autowired
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;

    @Override
    public void run(String... args) throws Exception {
        logger.info("=== 开始用户系统数据库初始化 ===");

        try {
            // 第1步：初始化user_system数据库结构
            initializeUserSystemDatabase();

            // 第2步：检查是否需要数据迁移
            if (shouldMigrateData()) {
                migrateUserData();
            } else {
                logger.info("用户数据已存在，跳过迁移");
            }

            logger.info("=== 用户系统数据库初始化完成 ===");

        } catch (Exception e) {
            logger.error("用户系统数据库初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }

    /**
     * 初始化user_system数据库结构
     */
    private void initializeUserSystemDatabase() {
        logger.info("开始初始化user_system数据库结构...");

        try (Connection connection = userSystemDataSource.getConnection()) {
            // 执行数据库初始化脚本
            ClassPathResource resource = new ClassPathResource("sql/user_system_init.sql");
            ScriptUtils.executeSqlScript(connection, resource);

            logger.info("user_system数据库结构初始化完成");

        } catch (Exception e) {
            logger.error("初始化user_system数据库结构失败", e);
            throw new RuntimeException("数据库初始化失败", e);
        }
    }

    /**
     * 检查是否需要进行数据迁移
     */
    private boolean shouldMigrateData() {
        try {
            JdbcTemplate userSystemJdbc = new JdbcTemplate(userSystemDataSource);
            JdbcTemplate primaryJdbc = new JdbcTemplate(primaryDataSource);

            // 检查user_system中是否已有用户数据（除了默认admin用户）
            Integer userSystemUserCount = userSystemJdbc.queryForObject(
                    "SELECT COUNT(*) FROM users WHERE username != 'admin'", Integer.class);

            // 检查overdue_debt中是否有用户数据 - 先检查表是否存在
            Integer primaryUserCount = 0;
            try {
                primaryUserCount = primaryJdbc.queryForObject(
                        "SELECT COUNT(*) FROM users", Integer.class);
            } catch (Exception tableNotExistException) {
                logger.info("overdue_debt_db中不存在users表，跳过数据迁移");
                return false;
            }

            logger.info("user_system用户数量: {}, overdue_debt用户数量: {}",
                        userSystemUserCount, primaryUserCount);

            // 如果user_system没有用户数据，但overdue_debt有，则需要迁移
            return userSystemUserCount == 0 && primaryUserCount > 0;

        } catch (Exception e) {
            logger.warn("检查数据迁移状态时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 执行用户数据迁移
     */
    private void migrateUserData() {
        logger.info("开始迁移用户数据...");

        try (Connection connection = primaryDataSource.getConnection()) {
            // 执行数据迁移脚本
            ClassPathResource resource = new ClassPathResource("sql/migrate_users_to_user_system.sql");
            ScriptUtils.executeSqlScript(connection, resource);

            // 验证迁移结果
            validateMigration();

            logger.info("用户数据迁移完成");

        } catch (Exception e) {
            logger.error("用户数据迁移失败", e);
            throw new RuntimeException("数据迁移失败", e);
        }
    }

    /**
     * 验证数据迁移结果
     */
    private void validateMigration() {
        try {
            JdbcTemplate userSystemJdbc = new JdbcTemplate(userSystemDataSource);
            JdbcTemplate primaryJdbc = new JdbcTemplate(primaryDataSource);

            Integer sourceUserCount = primaryJdbc.queryForObject(
                    "SELECT COUNT(*) FROM users", Integer.class);
            Integer targetUserCount = userSystemJdbc.queryForObject(
                    "SELECT COUNT(*) FROM users WHERE username != 'admin'", Integer.class);

            logger.info("迁移验证 - 源数据库用户数: {}, 目标数据库用户数: {}",
                        sourceUserCount, targetUserCount);

            if (!sourceUserCount.equals(targetUserCount)) {
                logger.warn("用户数据迁移数量不匹配，可能存在问题");
            } else {
                logger.info("用户数据迁移验证通过");
            }

        } catch (Exception e) {
            logger.warn("验证数据迁移结果时出错: {}", e.getMessage());
        }
    }

    /**
     * 手动触发数据库初始化（用于测试）
     */
    public void manualInitialize() {
        logger.info("手动触发用户系统数据库初始化");
        try {
            run();
        } catch (Exception e) {
            logger.error("手动初始化失败", e);
            throw new RuntimeException("手动初始化失败", e);
        }
    }

    /**
     * 手动触发数据迁移（用于测试）
     */
    public void manualMigrate() {
        logger.info("手动触发用户数据迁移");
        try {
            initializeUserSystemDatabase();
            migrateUserData();
        } catch (Exception e) {
            logger.error("手动迁移失败", e);
            throw new RuntimeException("手动迁移失败", e);
        }
    }

    /**
     * 获取数据库状态信息
     */
    public String getDatabaseStatus() {
        try {
            JdbcTemplate userSystemJdbc = new JdbcTemplate(userSystemDataSource);
            JdbcTemplate primaryJdbc = new JdbcTemplate(primaryDataSource);

            Integer userSystemUserCount = userSystemJdbc.queryForObject(
                    "SELECT COUNT(*) FROM users", Integer.class);
            Integer userSystemRoleCount = userSystemJdbc.queryForObject(
                    "SELECT COUNT(*) FROM roles", Integer.class);
            Integer primaryUserCount = primaryJdbc.queryForObject(
                    "SELECT COUNT(*) FROM users", Integer.class);

            return String.format(
                    "数据库状态 - user_system: %d用户/%d角色, overdue_debt: %d用户",
                    userSystemUserCount, userSystemRoleCount, primaryUserCount
                                );

        } catch (Exception e) {
            return "获取数据库状态失败: " + e.getMessage();
        }
    }
}
