package com.laoshu198838.service;

import com.laoshu198838.security.CustomUserDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户系统认证服务
 * 使用user_system数据库进行用户认证
 * 使用JdbcTemplate直接查询，绕过Spring Data JPA的代理机制
 * <AUTHOR>
 */
@Service("userSystemDetailsService")
public class UserSystemDetailsService implements UserDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(UserSystemDetailsService.class);

    private final JdbcTemplate userSystemJdbcTemplate;

    @Autowired
    public UserSystemDetailsService(@Qualifier("userSystemDataSource") DataSource userSystemDataSource) {
        this.userSystemJdbcTemplate = new JdbcTemplate(userSystemDataSource);
        logger.info("UserSystemDetailsService初始化完成，使用userSystemDataSource");
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.info("从user_system数据库加载用户: {}", username);

        try {
            // 直接使用JdbcTemplate查询user_system数据库，包含用户ID和用户姓名信息
            String sql = "SELECT u.id, u.username, u.password, u.status, r.role_name, u.name, u.companyname, u.department " +
                        "FROM users u LEFT JOIN roles r ON u.role_id = r.role_id " +
                        "WHERE u.username = ?";

            logger.info("执行SQL查询: {}", sql);

            // 使用新的queryForObject API，避免过时的Object[]参数
            Object[] userData = userSystemJdbcTemplate.queryForObject(sql,
                (rs, rowNum) -> new Object[]{
                    rs.getLong("id"),
                    rs.getString("username"),
                    rs.getString("password"),
                    rs.getString("status"),
                    rs.getString("role_name"),
                    rs.getString("name"),
                    rs.getString("companyname"),
                    rs.getString("department")
                }, username);

            if (userData == null || userData.length < 8) {
                logger.warn("用户不存在: {}", username);
                throw new UsernameNotFoundException("用户不存在: " + username);
            }

            Long id = (Long) userData[0];
            String dbUsername = (String) userData[1];
            String dbPassword = (String) userData[2];
            String status = (String) userData[3];
            String roleName = (String) userData[4];
            String name = (String) userData[5];
            String companyname = (String) userData[6];
            String department = (String) userData[7];

            logger.info("用户认证成功: {}, 状态: {}, 角色: {}, 姓名: {}", dbUsername, status, roleName, name);

            // 检查用户状态
            boolean isEnabled = "ACTIVE".equals(status);

            // 构建权限列表
            List<GrantedAuthority> authorities = new ArrayList<>();
            if (roleName != null) {
                authorities.add(new SimpleGrantedAuthority("ROLE_" + roleName.toUpperCase()));
            } else {
                authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            }

            // 返回自定义UserDetails对象，包含额外的用户信息
            return new CustomUserDetails(
                    id,
                    dbUsername,
                    dbPassword,
                    name,
                    companyname,
                    department,
                    authorities,
                    isEnabled
            );
        } catch (Exception e) {
            logger.error("加载用户失败: {}", username, e);
            throw new UsernameNotFoundException("用户认证失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否存在且活跃
     */
    public boolean isUserActiveByUsername(String username) {
        try {
            String sql = "SELECT COUNT(*) FROM users WHERE username = ? AND status = 'ACTIVE'";
            Integer count = userSystemJdbcTemplate.queryForObject(sql, Integer.class, username);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.error("检查用户状态失败: {}", username, e);
            return false;
        }
    }
}
