package com.laoshu198838.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 配置管理服务
 * 提供动态配置管理功能
 *
 * <AUTHOR>
 */
@Service
public class ConfigManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ConfigManagementService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ConfigurableEnvironment environment;

    private static final String DYNAMIC_CONFIG_SOURCE_NAME = "dynamicConfigSource";

    /**
     * 获取所有系统配置
     */
    @Cacheable(value = "config", key = "'all_configs'")
    public List<Map<String, Object>> getAllConfigs() {
        try {
            String sql = "SELECT config_key, config_value, description, config_type, is_encrypted, " +
                        "created_at, updated_at FROM system_config ORDER BY config_key";

            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            logger.error("获取系统配置失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据键获取配置值
     */
    @Cacheable(value = "config", key = "'config_' + #key")
    public String getConfigValue(String key) {
        try {
            String sql = "SELECT config_value FROM system_config WHERE config_key = ?";
            List<String> results = jdbcTemplate.queryForList(sql, String.class, key);

            if (!results.isEmpty()) {
                return results.get(0);
            }

            // 如果数据库中没有，尝试从环境变量获取
            return environment.getProperty(key);
        } catch (Exception e) {
            logger.error("获取配置值失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 根据键获取配置值，如果不存在则返回默认值
     */
    public String getConfigValue(String key, String defaultValue) {
        String value = getConfigValue(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取布尔类型配置值
     */
    public boolean getBooleanConfig(String key, boolean defaultValue) {
        String value = getConfigValue(key);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value);
    }

    /**
     * 获取整数类型配置值
     */
    public int getIntConfig(String key, int defaultValue) {
        String value = getConfigValue(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("配置值不是有效的整数: key={}, value={}", key, value);
            return defaultValue;
        }
    }

    /**
     * 获取长整数类型配置值
     */
    public long getLongConfig(String key, long defaultValue) {
        String value = getConfigValue(key);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            logger.warn("配置值不是有效的长整数: key={}, value={}", key, value);
            return defaultValue;
        }
    }

    /**
     * 设置配置值
     */
    @Transactional
    @CacheEvict(value = "config", allEntries = true)
    public boolean setConfigValue(String key, String value, String description, String configType) {
        try {
            // 检查配置是否已存在
            String checkSql = "SELECT COUNT(*) FROM system_config WHERE config_key = ?";
            int count = jdbcTemplate.queryForObject(checkSql, Integer.class, key);

            if (count > 0) {
                // 更新现有配置
                String updateSql = "UPDATE system_config SET config_value = ?, description = ?, " +
                                 "config_type = ?, updated_at = CURRENT_TIMESTAMP WHERE config_key = ?";
                jdbcTemplate.update(updateSql, value, description, configType, key);
            } else {
                // 插入新配置
                String insertSql = "INSERT INTO system_config (config_key, config_value, description, " +
                                 "config_type, is_encrypted, created_at, updated_at) VALUES (?, ?, ?, ?, false, " +
                                 "CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";
                jdbcTemplate.update(insertSql, key, value, description, configType);
            }

            // 更新运行时环境变量
            updateRuntimeConfig(key, value);

            logger.info("设置配置成功: key={}, value={}", key, value);
            return true;
        } catch (Exception e) {
            logger.error("设置配置失败: key={}, value={}", key, value, e);
            return false;
        }
    }

    /**
     * 删除配置
     */
    @Transactional
    @CacheEvict(value = "config", allEntries = true)
    public boolean deleteConfig(String key) {
        try {
            String sql = "DELETE FROM system_config WHERE config_key = ?";
            int affected = jdbcTemplate.update(sql, key);

            if (affected > 0) {
                // 从运行时环境变量中移除
                removeRuntimeConfig(key);
                logger.info("删除配置成功: key={}", key);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("删除配置失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 批量设置配置
     */
    @Transactional
    @CacheEvict(value = "config", allEntries = true)
    public Map<String, Boolean> batchSetConfigs(Map<String, Map<String, String>> configs) {
        Map<String, Boolean> results = new HashMap<>();

        for (Map.Entry<String, Map<String, String>> entry : configs.entrySet()) {
            String key = entry.getKey();
            Map<String, String> configData = entry.getValue();

            String value = configData.get("value");
            String description = configData.getOrDefault("description", "");
            String configType = configData.getOrDefault("type", "STRING");

            boolean success = setConfigValue(key, value, description, configType);
            results.put(key, success);
        }

        return results;
    }

    /**
     * 获取配置分组
     */
    public Map<String, List<Map<String, Object>>> getConfigsByGroup() {
        List<Map<String, Object>> allConfigs = getAllConfigs();
        Map<String, List<Map<String, Object>>> groupedConfigs = new HashMap<>();

        for (Map<String, Object> config : allConfigs) {
            String key = (String) config.get("config_key");
            String group = extractGroupFromKey(key);

            groupedConfigs.computeIfAbsent(group, k -> new ArrayList<>()).add(config);
        }

        return groupedConfigs;
    }

    /**
     * 从配置键中提取分组名
     */
    private String extractGroupFromKey(String key) {
        if (key.contains(".")) {
            return key.substring(0, key.indexOf("."));
        }
        return "default";
    }

    /**
     * 更新运行时配置
     */
    private void updateRuntimeConfig(String key, String value) {
        try {
            MutablePropertySources propertySources = environment.getPropertySources();

            // 获取或创建动态配置源
            MapPropertySource dynamicSource = (MapPropertySource) propertySources.get(DYNAMIC_CONFIG_SOURCE_NAME);
            if (dynamicSource == null) {
                Map<String, Object> dynamicProperties = new HashMap<>();
                dynamicSource = new MapPropertySource(DYNAMIC_CONFIG_SOURCE_NAME, dynamicProperties);
                propertySources.addFirst(dynamicSource);
            }

            // 更新配置值
            @SuppressWarnings("unchecked")
            Map<String, Object> source = (Map<String, Object>) dynamicSource.getSource();
            source.put(key, value);

        } catch (Exception e) {
            logger.warn("更新运行时配置失败: key={}, value={}", key, value, e);
        }
    }

    /**
     * 从运行时配置中移除
     */
    private void removeRuntimeConfig(String key) {
        try {
            MutablePropertySources propertySources = environment.getPropertySources();
            MapPropertySource dynamicSource = (MapPropertySource) propertySources.get(DYNAMIC_CONFIG_SOURCE_NAME);

            if (dynamicSource != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> source = (Map<String, Object>) dynamicSource.getSource();
                source.remove(key);
            }
        } catch (Exception e) {
            logger.warn("从运行时配置中移除失败: key={}", key, e);
        }
    }

    /**
     * 重新加载所有配置到运行时环境
     */
    @CacheEvict(value = "config", allEntries = true)
    public void reloadAllConfigs() {
        try {
            List<Map<String, Object>> allConfigs = getAllConfigs();

            MutablePropertySources propertySources = environment.getPropertySources();

            // 移除旧的动态配置源
            if (propertySources.contains(DYNAMIC_CONFIG_SOURCE_NAME)) {
                propertySources.remove(DYNAMIC_CONFIG_SOURCE_NAME);
            }

            // 创建新的动态配置源
            Map<String, Object> dynamicProperties = new HashMap<>();
            for (Map<String, Object> config : allConfigs) {
                String key = (String) config.get("config_key");
                String value = (String) config.get("config_value");
                dynamicProperties.put(key, value);
            }

            MapPropertySource dynamicSource = new MapPropertySource(DYNAMIC_CONFIG_SOURCE_NAME, dynamicProperties);
            propertySources.addFirst(dynamicSource);

            logger.info("重新加载所有配置完成，共加载 {} 个配置", allConfigs.size());
        } catch (Exception e) {
            logger.error("重新加载配置失败", e);
        }
    }

    /**
     * 获取配置统计信息
     */
    public Map<String, Object> getConfigStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 总配置数
            String countSql = "SELECT COUNT(*) FROM system_config";
            Integer totalConfigs = jdbcTemplate.queryForObject(countSql, Integer.class);
            stats.put("totalConfigs", totalConfigs != null ? totalConfigs : 0);

            // 按类型统计
            String typeSql = "SELECT config_type, COUNT(*) as count FROM system_config GROUP BY config_type";
            List<Map<String, Object>> typeStats = jdbcTemplate.queryForList(typeSql);
            stats.put("typeStats", typeStats);

            // 按分组统计
            Map<String, List<Map<String, Object>>> groupedConfigs = getConfigsByGroup();
            Map<String, Integer> groupStats = new HashMap<>();
            for (Map.Entry<String, List<Map<String, Object>>> entry : groupedConfigs.entrySet()) {
                groupStats.put(entry.getKey(), entry.getValue().size());
            }
            stats.put("groupStats", groupStats);

            // 最近更新的配置
            String recentSql = "SELECT config_key, updated_at FROM system_config " +
                             "ORDER BY updated_at DESC LIMIT 5";
            List<Map<String, Object>> recentConfigs = jdbcTemplate.queryForList(recentSql);
            stats.put("recentConfigs", recentConfigs);

        } catch (Exception e) {
            logger.error("获取配置统计信息失败", e);
            stats.put("error", e.getMessage());
        }

        return stats;
    }
}
