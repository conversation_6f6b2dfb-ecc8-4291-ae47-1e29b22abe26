package com.laoshu198838.exception;

import lombok.Getter;

/**
 * 数据库异常
 * 用于处理数据库操作相关的异常
 * 
 * <AUTHOR>
 */
@Getter
public class DatabaseException extends RuntimeException {
    
    private final String operation;
    private final String tableName;
    
    public DatabaseException(String message) {
        super(message);
        this.operation = null;
        this.tableName = null;
    }
    
    public DatabaseException(String message, Throwable cause) {
        super(message, cause);
        this.operation = null;
        this.tableName = null;
    }
    
    public DatabaseException(String message, String operation, String tableName) {
        super(message);
        this.operation = operation;
        this.tableName = tableName;
    }
    
    public DatabaseException(String message, Throwable cause, String operation, String tableName) {
        super(message, cause);
        this.operation = operation;
        this.tableName = tableName;
    }

}
