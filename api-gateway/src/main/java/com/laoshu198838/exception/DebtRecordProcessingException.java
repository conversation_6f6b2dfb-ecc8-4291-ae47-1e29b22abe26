//package com.laoshu198838.exception;
//
///**
// * 债务记录处理异常
// * 用于处理债务记录增加、修改、查询过程中发生的异常
// *
// * <AUTHOR>
// */
//public class DebtRecordProcessingException extends RuntimeException {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 创建债务记录处理异常
//     */
//    public DebtRecordProcessingException() {
//        super();
//    }
//
//    /**
//     * 创建债务记录处理异常
//     *
//     * @param message 错误消息
//     */
//    public DebtRecordProcessingException(String message) {
//        super(message);
//    }
//
//    /**
//     * 创建债务记录处理异常
//     *
//     * @param message 错误消息
//     * @param cause 原始异常
//     */
//    public DebtRecordProcessingException(String message, Throwable cause) {
//        super(message, cause);
//    }
//
//    /**
//     * 创建债务记录处理异常
//     *
//     * @param cause 原始异常
//     */
//    public DebtRecordProcessingException(Throwable cause) {
//        super(cause);
//    }
//}
