package com.laoshu198838.controller.user;

import com.laoshu198838.entity.user_system.Company;
import com.laoshu198838.repository.user_system.CompanyRepository;
import com.laoshu198838.service.DataPermissionService;
import com.laoshu198838.service.CompanyStructureService;
import com.laoshu198838.security.CustomUserDetails;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 公司管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/companies")
@Tag(name = "公司管理", description = "公司信息管理")
@Transactional("userSystemTransactionManager")
public class CompanyController {

    private static final Logger logger = LoggerFactory.getLogger(CompanyController.class);

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private DataPermissionService dataPermissionService;
    
    @Autowired
    private CompanyStructureService companyStructureService;

    /**
     * 获取所有活跃公司列表
     */
    @GetMapping
    @Operation(summary = "获取所有活跃公司列表")
    public ResponseEntity<Map<String, Object>> getAllCompanies() {
        try {
            List<Company> companies = companyRepository.findAllActiveCompanies();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", companies);
            response.put("count", companies.size());
            response.put("message", "获取公司列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取公司列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取公司列表失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 根据ID获取公司信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取公司信息")
    public ResponseEntity<Map<String, Object>> getCompanyById(
            @Parameter(description = "公司ID") @PathVariable Long id) {
        try {
            Optional<Company> companyOpt = companyRepository.findById(id);
            
            if (companyOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "公司不存在");
                return ResponseEntity.status(404).body(response);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", companyOpt.get());
            response.put("message", "获取公司信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取公司信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取公司信息失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 根据公司名称搜索
     */
    @GetMapping("/search")
    @Operation(summary = "根据公司名称搜索")
    public ResponseEntity<Map<String, Object>> searchCompanies(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {
        try {
            List<Company> companies = companyRepository.findByCompanyNameContaining(keyword);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", companies);
            response.put("count", companies.size());
            response.put("keyword", keyword);
            response.put("message", "搜索公司成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("搜索公司失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "搜索公司失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取管理公司列表
     */
    @GetMapping("/management")
    @Operation(summary = "获取管理公司列表")
    public ResponseEntity<Map<String, Object>> getManagementCompanies() {
        try {
            List<Company> companies = companyRepository.findManagementCompanies();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", companies);
            response.put("count", companies.size());
            response.put("message", "获取管理公司列表成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取管理公司列表失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取管理公司列表失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 创建新公司（仅管理员）
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "创建新公司")
    public ResponseEntity<Map<String, Object>> createCompany(
            @RequestBody CreateCompanyRequest request,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能创建新公司");
                return ResponseEntity.status(403).body(response);
            }

            // 检查公司名称是否已存在
            if (companyRepository.existsByCompanyName(request.getCompanyName())) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "公司名称已存在");
                return ResponseEntity.status(400).body(response);
            }

            // 检查公司代码是否已存在
            if (request.getCompanyCode() != null && companyRepository.existsByCompanyCode(request.getCompanyCode())) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "公司代码已存在");
                return ResponseEntity.status(400).body(response);
            }

            Company company = new Company(
                    request.getCompanyName(), 
                    request.getCompanyCode(), 
                    request.getIsManagementCompany()
            );
            
            company = companyRepository.save(company);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", company);
            response.put("message", "公司创建成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("创建公司失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "创建公司失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 更新公司信息（仅管理员）
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "更新公司信息")
    public ResponseEntity<Map<String, Object>> updateCompany(
            @Parameter(description = "公司ID") @PathVariable Long id,
            @RequestBody UpdateCompanyRequest request,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能更新公司信息");
                return ResponseEntity.status(403).body(response);
            }

            Optional<Company> companyOpt = companyRepository.findById(id);
            if (companyOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "公司不存在");
                return ResponseEntity.status(404).body(response);
            }

            Company company = companyOpt.get();
            
            // 更新公司信息
            if (request.getCompanyName() != null) {
                // 检查新名称是否与其他公司冲突
                if (!company.getCompanyName().equals(request.getCompanyName()) && 
                    companyRepository.existsByCompanyName(request.getCompanyName())) {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "公司名称已存在");
                    return ResponseEntity.status(400).body(response);
                }
                company.setCompanyName(request.getCompanyName());
            }
            
            if (request.getCompanyCode() != null) {
                // 检查新代码是否与其他公司冲突
                if (!request.getCompanyCode().equals(company.getCompanyCode()) && 
                    companyRepository.existsByCompanyCode(request.getCompanyCode())) {
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", false);
                    response.put("message", "公司代码已存在");
                    return ResponseEntity.status(400).body(response);
                }
                company.setCompanyCode(request.getCompanyCode());
            }
            
            if (request.getIsManagementCompany() != null) {
                company.setIsManagementCompany(request.getIsManagementCompany());
            }
            
            if (request.getStatus() != null) {
                company.setStatus(request.getStatus());
            }
            
            company = companyRepository.save(company);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", company);
            response.put("message", "公司信息更新成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("更新公司信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新公司信息失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 删除公司（仅管理员）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "删除公司")
    public ResponseEntity<Map<String, Object>> deleteCompany(
            @Parameter(description = "公司ID") @PathVariable Long id,
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能删除公司");
                return ResponseEntity.status(403).body(response);
            }

            Optional<Company> companyOpt = companyRepository.findById(id);
            if (companyOpt.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "公司不存在");
                return ResponseEntity.status(404).body(response);
            }

            // 软删除：将状态设为inactive
            Company company = companyOpt.get();
            company.setStatus(Company.CompanyStatus.inactive);
            companyRepository.save(company);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "公司删除成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("删除公司失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除公司失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 初始化公司架构数据（仅管理员）
     */
    @PostMapping("/initialize")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "初始化公司架构数据")
    public ResponseEntity<Map<String, Object>> initializeCompanyStructure(
            Authentication authentication) {
        try {
            CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
            Long currentUserId = userDetails.getId();

            // 检查是否为万润科技管理员
            if (!dataPermissionService.isManagementAdmin(currentUserId)) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "只有万润科技的管理员才能初始化公司架构数据");
                return ResponseEntity.status(403).body(response);
            }

            // 执行初始化
            companyStructureService.initializeCompanyStructure();
            
            // 获取统计信息
            CompanyStructureService.CompanyStructureStats stats = companyStructureService.getStructureStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "公司架构数据初始化成功");
            response.put("stats", Map.of(
                "totalCompanies", stats.getTotalCompanies(),
                "activeCompanies", stats.getActiveCompanies(),
                "managementCompanies", stats.getManagementCompanies()
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("初始化公司架构数据失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "初始化公司架构数据失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取公司架构统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取公司架构统计信息")
    public ResponseEntity<Map<String, Object>> getCompanyStats() {
        try {
            CompanyStructureService.CompanyStructureStats stats = companyStructureService.getStructureStats();
            boolean needsInit = companyStructureService.needsInitialization();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", Map.of(
                "totalCompanies", stats.getTotalCompanies(),
                "activeCompanies", stats.getActiveCompanies(),
                "managementCompanies", stats.getManagementCompanies(),
                "needsInitialization", needsInit
            ));
            response.put("message", "获取公司架构统计信息成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取公司架构统计信息失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取公司架构统计信息失败：" + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    // 内部类：请求参数对象
    public static class CreateCompanyRequest {
        private String companyName;
        private String companyCode;
        private Boolean isManagementCompany = false;

        // Getters and Setters
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        
        public String getCompanyCode() { return companyCode; }
        public void setCompanyCode(String companyCode) { this.companyCode = companyCode; }
        
        public Boolean getIsManagementCompany() { return isManagementCompany; }
        public void setIsManagementCompany(Boolean isManagementCompany) { this.isManagementCompany = isManagementCompany; }
    }

    public static class UpdateCompanyRequest {
        private String companyName;
        private String companyCode;
        private Boolean isManagementCompany;
        private Company.CompanyStatus status;

        // Getters and Setters
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        
        public String getCompanyCode() { return companyCode; }
        public void setCompanyCode(String companyCode) { this.companyCode = companyCode; }
        
        public Boolean getIsManagementCompany() { return isManagementCompany; }
        public void setIsManagementCompany(Boolean isManagementCompany) { this.isManagementCompany = isManagementCompany; }
        
        public Company.CompanyStatus getStatus() { return status; }
        public void setStatus(Company.CompanyStatus status) { this.status = status; }
    }
}