package com.laoshu198838.controller.debt;

import com.laoshu198838.dto.debt.DebtDeletionDTO;
import com.laoshu198838.dto.debt.DebtDeletionResult;
import com.laoshu198838.service.DebtDeletionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 债权删除控制器
 * 
 * 处理债权删除相关的所有请求，包括处置债权删除和新增债权删除。
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/debt/deletion")
@CrossOrigin
public class DebtDeletionController {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtDeletionController.class);
    
    @Autowired
    private DebtDeletionService debtDeletionService;
    
    /**
     * 删除处置债权
     * 
     * 通过负数处置的方式删除已有的处置记录
     * 
     * @param deletionDTO 删除请求数据
     * @return 删除结果
     */
    @PostMapping("/disposal")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Map<String, Object>> deleteDisposalDebt(@RequestBody DebtDeletionDTO deletionDTO) {
        String requestId = java.util.UUID.randomUUID().toString().substring(0, 8);
        logger.info("[{}] 收到删除处置债权请求: 债权人={}, 债务人={}, 期间={}, 年份={}, 月份={}, 金额={}",
            requestId,
            deletionDTO.getCreditor(), 
            deletionDTO.getDebtor(), 
            deletionDTO.getPeriod(),
            deletionDTO.getYear(), 
            deletionDTO.getMonth(), 
            deletionDTO.getAmount());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 设置删除类型为处置删除
            deletionDTO.setDeletionType("DELETE_DISPOSAL");
            
            // 调用服务层执行删除
            DebtDeletionResult result = debtDeletionService.deleteDebt(deletionDTO);
            
            if (result.isSuccess()) {
                response.put("success", true);
                response.put("message", result.getMessage());
                response.put("affectedRecords", result.getAffectedRecords());
                logger.info("[{}] 处置债权删除成功: {}", requestId, result.getMessage());
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", result.getMessage());
                response.put("error", result.getErrorDetail());
                logger.error("[{}] 处置债权删除失败: {}", requestId, result.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("[{}] 删除处置债权时发生错误", requestId, e);
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 删除新增债权
     * 
     * 通过负数新增的方式删除已有的新增记录
     * 
     * @param deletionDTO 删除请求数据
     * @return 删除结果
     */
    @PostMapping("/addition")
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<Map<String, Object>> deleteAdditionDebt(@RequestBody DebtDeletionDTO deletionDTO) {
        logger.info("收到删除新增债权请求: 债权人={}, 债务人={}, 期间={}, 年份={}, 月份={}, 金额={}", 
            deletionDTO.getCreditor(), 
            deletionDTO.getDebtor(), 
            deletionDTO.getPeriod(),
            deletionDTO.getYear(), 
            deletionDTO.getMonth(), 
            deletionDTO.getAmount());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 设置删除类型为新增删除
            deletionDTO.setDeletionType("DELETE_ADDITION");
            
            // 调用服务层执行删除
            DebtDeletionResult result = debtDeletionService.deleteDebt(deletionDTO);
            
            if (result.isSuccess()) {
                response.put("success", true);
                response.put("message", result.getMessage());
                response.put("affectedRecords", result.getAffectedRecords());
                logger.info("新增债权删除成功: {}", result.getMessage());
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", result.getMessage());
                response.put("error", result.getErrorDetail());
                logger.error("新增债权删除失败: {}", result.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
        } catch (Exception e) {
            logger.error("删除新增债权时发生错误", e);
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
}