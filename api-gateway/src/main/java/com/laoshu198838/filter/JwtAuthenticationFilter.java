package com.laoshu198838.filter;

import com.laoshu198838.util.JwtUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
    
    // 假设你有一个 TokenProvider 类来处理 JWT 逻辑
    private final JwtUtils tokenProvider;

    public JwtAuthenticationFilter(JwtUtils  tokenProvider) {
        this.tokenProvider = tokenProvider;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getServletPath();
        // 跳过以下路径的JWT验证
        return path.startsWith("/api/public/") || path.equals("/api/auth/login");
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String path = request.getServletPath();
        logger.info("JWT Filter processing request: {} {}", request.getMethod(), path);

        // 从请求头中获取 JWT
        String token = getTokenFromRequest(request);
        logger.info("Extracted token: {}", token != null ? "present (length=" + token.length() + ")" : "null");

        // 如果 token 不为空并且有效，设置用户认证信息
        if (token != null) {
            boolean isValid = tokenProvider.validateToken(token);
            logger.info("Token validation result: {}", isValid);
            
            if (isValid) {
                Authentication authentication = tokenProvider.getAuthentication(token);
                logger.info("Authentication created: {}, Principal: {}", 
                           authentication != null, 
                           authentication != null ? authentication.getName() : "null");
                
                // 将认证信息放入 SecurityContext
                SecurityContextHolder.getContext().setAuthentication(authentication);
                logger.info("Authentication set in SecurityContext");
            } else {
                logger.warn("Token validation failed for request: {}", path);
            }
        } else {
            logger.info("No token found for request: {}", path);
        }

        // 继续过滤请求
        filterChain.doFilter(request, response);
    }

    // 从请求头中获取 JWT
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            // 移除 "Bearer " 前缀
            return bearerToken.substring(7);
        }
        return null;
    }
}
