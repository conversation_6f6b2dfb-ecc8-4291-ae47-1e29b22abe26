package com.laoshu198838.security;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;

/**
 * 自定义UserDetails实现
 * 包含额外的用户信息，用于JWT生成
 * <AUTHOR>
 */

public class CustomUserDetails implements UserDetails {

    private final Long id;
    private final String username;
    private final String password;
    private final String name;
    private final String companyname;
    private final String department;
    private final Collection<? extends GrantedAuthority> authorities;
    private final boolean enabled;

    public CustomUserDetails(Long id, String username, String password, String name, 
                           String companyname, String department, 
                           Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.name = name;
        this.companyname = companyname;
        this.department = department;
        this.authorities = authorities;
        this.enabled = true;
    }

    public CustomUserDetails(Long id, String username, String password, String name, 
                           String companyname, String department, 
                           Collection<? extends GrantedAuthority> authorities, 
                           boolean enabled) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.name = name;
        this.companyname = companyname;
        this.department = department;
        this.authorities = authorities;
        this.enabled = enabled;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getCompanyname() {
        return companyname;
    }

    public String getDepartment() {
        return department;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    @Override
    public String toString() {
        return "CustomUserDetails{" +
                "username='" + username + '\'' +
                ", name='" + name + '\'' +
                ", companyname='" + companyname + '\'' +
                ", department='" + department + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
