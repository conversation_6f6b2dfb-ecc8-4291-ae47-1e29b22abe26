package com.laoshu198838.security;

import org.springframework.stereotype.Service;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.util.*;

/**
 * 路由权限服务
 * 提供基于角色的路由访问控制
 * 
 * <AUTHOR>
 */
@Service
public class RoutePermissionService {
    
    // 路由权限映射表
    private static final Map<String, Set<String>> ROUTE_PERMISSIONS = new HashMap<>();
    
    static {
        // 公共路由 - 无需认证
        Set<String> publicRoutes = Set.of("ANONYMOUS");
        ROUTE_PERMISSIONS.put("/api/auth/login", publicRoutes);
        ROUTE_PERMISSIONS.put("/api/users/register", publicRoutes);
        ROUTE_PERMISSIONS.put("/api/public/**", publicRoutes);
        
        // 用户级别路由 - 需要USER或ADMIN权限
        Set<String> userRoutes = Set.of("USER", "ADMIN", "ROLE_USER", "ROLE_ADMIN");
        ROUTE_PERMISSIONS.put("/api/debts/**", userRoutes);
        ROUTE_PERMISSIONS.put("/api/assets/**", userRoutes);
        ROUTE_PERMISSIONS.put("/api/reports/**", userRoutes);
        ROUTE_PERMISSIONS.put("/api/profile/**", userRoutes);
        
        // 管理员级别路由 - 仅ADMIN权限
        Set<String> adminRoutes = Set.of("ADMIN", "ROLE_ADMIN");
        ROUTE_PERMISSIONS.put("/api/users/**", adminRoutes);
        ROUTE_PERMISSIONS.put("/api/datamonitor/**", adminRoutes);
        ROUTE_PERMISSIONS.put("/api/consistency/**", adminRoutes);
        ROUTE_PERMISSIONS.put("/api/admin/**", adminRoutes);
        
        // 审计相关路由 - 需要特殊权限
        Set<String> auditRoutes = Set.of("ADMIN", "AUDITOR", "ROLE_ADMIN", "ROLE_AUDITOR");
        ROUTE_PERMISSIONS.put("/api/audit/**", auditRoutes);
        
        // 数据导出路由 - 需要特殊权限
        Set<String> exportRoutes = Set.of("ADMIN", "EXPORT_USER", "ROLE_ADMIN", "ROLE_EXPORT_USER");
        ROUTE_PERMISSIONS.put("/api/export/**", exportRoutes);
    }
    
    /**
     * 检查用户是否有权限访问指定路由
     * 
     * @param authentication 用户认证信息
     * @param requestPath 请求路径
     * @return 是否有权限
     */
    public boolean hasPermission(Authentication authentication, String requestPath) {
        // 如果是公共路由，直接允许访问
        if (isPublicRoute(requestPath)) {
            return true;
        }
        
        // 如果用户未认证，拒绝访问
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        // 获取用户权限
        Set<String> userAuthorities = getUserAuthorities(authentication);
        
        // 检查路由权限
        Set<String> requiredPermissions = getRequiredPermissions(requestPath);
        
        // 如果没有配置权限要求，默认需要认证
        if (requiredPermissions.isEmpty()) {
            return true; // 已认证用户可以访问
        }
        
        // 检查用户是否具有所需权限
        return userAuthorities.stream().anyMatch(requiredPermissions::contains);
    }
    
    /**
     * 获取用户权限集合
     */
    private Set<String> getUserAuthorities(Authentication authentication) {
        Set<String> authorities = new HashSet<>();
        for (GrantedAuthority authority : authentication.getAuthorities()) {
            authorities.add(authority.getAuthority());
        }
        return authorities;
    }
    
    /**
     * 获取路由所需权限
     */
    private Set<String> getRequiredPermissions(String requestPath) {
        // 精确匹配
        if (ROUTE_PERMISSIONS.containsKey(requestPath)) {
            return ROUTE_PERMISSIONS.get(requestPath);
        }
        
        // 通配符匹配
        for (Map.Entry<String, Set<String>> entry : ROUTE_PERMISSIONS.entrySet()) {
            String pattern = entry.getKey();
            if (pattern.endsWith("/**")) {
                String prefix = pattern.substring(0, pattern.length() - 3);
                if (requestPath.startsWith(prefix)) {
                    return entry.getValue();
                }
            }
        }
        
        return Collections.emptySet();
    }
    
    /**
     * 检查是否为公共路由
     */
    private boolean isPublicRoute(String requestPath) {
        Set<String> permissions = getRequiredPermissions(requestPath);
        return permissions.contains("ANONYMOUS");
    }
    
    /**
     * 获取用户可访问的路由列表
     */
    public Set<String> getAccessibleRoutes(Authentication authentication) {
        Set<String> accessibleRoutes = new HashSet<>();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            // 未认证用户只能访问公共路由
            for (Map.Entry<String, Set<String>> entry : ROUTE_PERMISSIONS.entrySet()) {
                if (entry.getValue().contains("ANONYMOUS")) {
                    accessibleRoutes.add(entry.getKey());
                }
            }
            return accessibleRoutes;
        }
        
        Set<String> userAuthorities = getUserAuthorities(authentication);
        
        for (Map.Entry<String, Set<String>> entry : ROUTE_PERMISSIONS.entrySet()) {
            String route = entry.getKey();
            Set<String> requiredPermissions = entry.getValue();
            
            // 公共路由或用户有权限的路由
            if (requiredPermissions.contains("ANONYMOUS") || 
                userAuthorities.stream().anyMatch(requiredPermissions::contains)) {
                accessibleRoutes.add(route);
            }
        }
        
        return accessibleRoutes;
    }
    
    /**
     * 检查用户是否为管理员
     */
    public boolean isAdmin(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        Set<String> userAuthorities = getUserAuthorities(authentication);
        return userAuthorities.contains("ADMIN") || userAuthorities.contains("ROLE_ADMIN");
    }
    
    /**
     * 检查用户是否有导出权限
     */
    public boolean hasExportPermission(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        Set<String> userAuthorities = getUserAuthorities(authentication);
        return userAuthorities.contains("ADMIN") || 
               userAuthorities.contains("ROLE_ADMIN") ||
               userAuthorities.contains("EXPORT_USER") ||
               userAuthorities.contains("ROLE_EXPORT_USER");
    }
    
    /**
     * 检查用户是否有审计权限
     */
    public boolean hasAuditPermission(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        Set<String> userAuthorities = getUserAuthorities(authentication);
        return userAuthorities.contains("ADMIN") || 
               userAuthorities.contains("ROLE_ADMIN") ||
               userAuthorities.contains("AUDITOR") ||
               userAuthorities.contains("ROLE_AUDITOR");
    }
}
