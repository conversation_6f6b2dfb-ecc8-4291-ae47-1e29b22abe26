package com.laoshu198838.util;

import com.laoshu198838.exception.JwtTokenGenerationException;
import com.laoshu198838.security.CustomUserDetails;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;
import java.util.List;

import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
public class JwtUtils {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);

    @Value("${jwt.expiration}")
    private long jwtExpiration;



//    2025.3.9
    private Key signingKey;

    @Value("${jwt.secret}")
    public void setJwtSecret(String jwtSecret) {
        this.signingKey = Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }

    private Key getSigningKey() {
        return signingKey;
    }

    // 生成 JWT
    public String generateToken(Authentication authentication) {
        try {
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            String username = userDetails.getUsername();

            // 确保所有角色都有ROLE_前缀
            List<String> roles = userDetails.getAuthorities().stream()
                    .map(authority -> {
                        String role = authority.getAuthority();
                        // 如果角色不是以ROLE_开头，则添加ROLE_前缀
                        if (!role.startsWith("ROLE_")) {
                            role = "ROLE_" + role;
                        }
                        return role;
                    })
                    .collect(Collectors.toList());

            logger.info("User '{}' has roles: {}", username, roles);

            // 构建基本JWT数据
            logger.info("Building JWT with subject (username): {}", username);
            JwtBuilder jwtBuilder = Jwts.builder()
                    .setSubject(username)  // 设置主题为用户名
                    .claim("username", username)
                    .claim("sub", username)  // 确保前JWT标准主题字段存在
                    .claim("roles", roles);

            // 如果是CustomUserDetails，添加额外的用户信息
            if (userDetails instanceof CustomUserDetails) {
                CustomUserDetails customUser = (CustomUserDetails) userDetails;
                jwtBuilder
                        .claim("name", customUser.getName())
                        .claim("companyname", customUser.getCompanyname())
                        .claim("department", customUser.getDepartment());
                logger.info("Added custom user info to JWT: name={}, company={}, department={}",
                           customUser.getName(), customUser.getCompanyname(), customUser.getDepartment());
            }

            logger.info("Generating JWT with user information for: {}", username);

            // 完成JWT构建
            String token = jwtBuilder
                    .setIssuedAt(new Date())
                    .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration))
                    .signWith(getSigningKey())
                    .compact();

            logger.info("Generated JWT token for user: {}", username);

            return token;
        } catch (Exception e) {
            String errorMessage = "Failed to generate JWT token: " + e.getMessage();
            logger.error(errorMessage, e);
            throw new JwtTokenGenerationException(errorMessage, e);
        }
    }

    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        }
        return false;
    }

    public Authentication getAuthentication(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        @SuppressWarnings("unchecked")
        List<String> roles = (List<String>) claims.get("roles");
        List<GrantedAuthority> authorities = roles.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());

        String username = claims.getSubject();
        return new UsernamePasswordAuthenticationToken(username, null, authorities);
    }
}