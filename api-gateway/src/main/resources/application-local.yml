spring:
  datasource:
    # 主数据源 - overdue_debt_db
    primary:
      jdbc-url: *****************************************************************************************************************************************************
      username: root
      password: ${DB_PASSWORD:Zlb&198838}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        connection-init-sql: SET autocommit=0
        auto-commit: false
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        pool-name: PrimaryHikariPool

    # 第二数据源 - Kingdee数据库
    secondary:
      jdbc-url: *********************************************************************************************************************************************
      username: root
      password: ${DB_PASSWORD:Zlb&198838}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        connection-timeout: 30000
        pool-name: SecondaryHikariPool

    # 用户系统数据源
    user-system:
      jdbc-url: *************************************************************************************************************************************************
      username: root
      password: ${DB_PASSWORD:Zlb&198838}
      driver-class-name: com.mysql.cj.jdbc.Driver
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
        connection-timeout: 30000
        pool-name: UserSystemHikariPool

server:
  port: 8080

# 允许所有来源的CORS（仅用于本地开发）
cors:
  allowed-origins: http://localhost:3000,http://127.0.0.1:3000
  
# 日志级别
logging:
  level:
    com.laoshu198838: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web.cors: DEBUG