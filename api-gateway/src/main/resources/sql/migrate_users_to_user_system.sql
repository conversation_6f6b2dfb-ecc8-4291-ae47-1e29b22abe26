-- =====================================================
-- 用户数据迁移脚本
-- 将现有用户数据从overdue_debt数据库迁移到user_system数据库
-- =====================================================

-- 确保user_system数据库存在
USE `user_system`;

-- =====================================================
-- 第1步：迁移角色数据
-- =====================================================
INSERT INTO `user_system`.`roles` (`role_id`, `role_name`, `description`)
SELECT 
    `role_id`,
    `role_name`,
    CONCAT('从overdue_debt迁移的角色: ', `role_name`) AS description
FROM `overdue_debt`.`roles`
ON DUPLICATE KEY UPDATE 
    `role_name` = VALUES(`role_name`),
    `description` = VALUES(`description`),
    `updated_time` = CURRENT_TIMESTAMP;

-- =====================================================
-- 第2步：迁移用户数据
-- =====================================================
INSERT INTO `user_system`.`users` (
    `id`, 
    `username`, 
    `name`, 
    `password`, 
    `companyname`, 
    `department`, 
    `status`, 
    `role_id`
)
SELECT 
    u.`id`,
    u.`username`,
    u.`name`,
    u.`password`,
    COALESCE(u.`companyname`, '所有公司') AS companyname,
    COALESCE(u.`department`, '所有部门') AS department,
    COALESCE(u.`status`, 'ACTIVE') AS status,
    u.`role_id`
FROM `overdue_debt`.`users` u
WHERE u.`role_id` IS NOT NULL
ON DUPLICATE KEY UPDATE 
    `username` = VALUES(`username`),
    `name` = VALUES(`name`),
    `password` = VALUES(`password`),
    `companyname` = VALUES(`companyname`),
    `department` = VALUES(`department`),
    `status` = VALUES(`status`),
    `role_id` = VALUES(`role_id`),
    `updated_time` = CURRENT_TIMESTAMP;

-- =====================================================
-- 第3步：处理没有角色的用户（分配默认角色）
-- =====================================================
INSERT INTO `user_system`.`users` (
    `id`, 
    `username`, 
    `name`, 
    `password`, 
    `companyname`, 
    `department`, 
    `status`, 
    `role_id`
)
SELECT 
    u.`id`,
    u.`username`,
    u.`name`,
    u.`password`,
    COALESCE(u.`companyname`, '所有公司') AS companyname,
    COALESCE(u.`department`, '所有部门') AS department,
    COALESCE(u.`status`, 'ACTIVE') AS status,
    2 AS role_id  -- 默认分配USER角色
FROM `overdue_debt`.`users` u
WHERE u.`role_id` IS NULL
ON DUPLICATE KEY UPDATE 
    `username` = VALUES(`username`),
    `name` = VALUES(`name`),
    `password` = VALUES(`password`),
    `companyname` = VALUES(`companyname`),
    `department` = VALUES(`department`),
    `status` = VALUES(`status`),
    `role_id` = CASE 
        WHEN `role_id` IS NULL THEN VALUES(`role_id`)
        ELSE `role_id`
    END,
    `updated_time` = CURRENT_TIMESTAMP;

-- =====================================================
-- 第4步：重置AUTO_INCREMENT值
-- =====================================================
-- 获取最大ID并设置AUTO_INCREMENT
SET @max_user_id = (SELECT COALESCE(MAX(id), 0) FROM `user_system`.`users`);
SET @sql = CONCAT('ALTER TABLE `user_system`.`users` AUTO_INCREMENT = ', @max_user_id + 1);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @max_role_id = (SELECT COALESCE(MAX(role_id), 0) FROM `user_system`.`roles`);
SET @sql = CONCAT('ALTER TABLE `user_system`.`roles` AUTO_INCREMENT = ', @max_role_id + 1);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 第5步：数据验证和统计
-- =====================================================
SELECT 'user_system数据迁移完成' AS message;

SELECT 
    '角色迁移统计' AS category,
    (SELECT COUNT(*) FROM `overdue_debt`.`roles`) AS source_count,
    (SELECT COUNT(*) FROM `user_system`.`roles`) AS target_count;

SELECT 
    '用户迁移统计' AS category,
    (SELECT COUNT(*) FROM `overdue_debt`.`users`) AS source_count,
    (SELECT COUNT(*) FROM `user_system`.`users`) AS target_count;

-- 显示迁移后的用户状态分布
SELECT 
    status,
    COUNT(*) AS user_count
FROM `user_system`.`users`
GROUP BY status
ORDER BY status;

-- 显示迁移后的角色分布
SELECT 
    r.role_name,
    COUNT(u.id) AS user_count
FROM `user_system`.`roles` r
LEFT JOIN `user_system`.`users` u ON r.role_id = u.role_id
GROUP BY r.role_id, r.role_name
ORDER BY r.role_id;

-- =====================================================
-- 第6步：创建迁移记录表（可选）
-- =====================================================
CREATE TABLE IF NOT EXISTS `migration_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `migration_name` varchar(100) NOT NULL,
  `source_database` varchar(50) NOT NULL,
  `target_database` varchar(50) NOT NULL,
  `records_migrated` int DEFAULT 0,
  `migration_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(20) DEFAULT 'SUCCESS',
  `notes` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 记录本次迁移
INSERT INTO `migration_log` (
    `migration_name`, 
    `source_database`, 
    `target_database`, 
    `records_migrated`, 
    `notes`
) VALUES (
    'User and Role Migration',
    'overdue_debt',
    'user_system',
    (SELECT COUNT(*) FROM `user_system`.`users`),
    'Initial migration of users and roles from overdue_debt to user_system database'
);
