-- =====================================================
-- 用户系统数据库初始化脚本
-- 创建user_system数据库和相关表结构
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `user_system`
DEFAULT CHARACTER SET utf8mb4
DEFAULT COLLATE utf8mb4_unicode_ci;

USE `user_system`;

-- =====================================================
-- 创建角色表 (roles)
-- =====================================================
CREATE TABLE IF NOT EXISTS `roles` (
  `role_id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID，主键',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称，如USER、ADMIN',
  PRIMARY KEY (`role_id`),
  UNIQUE KEY `uk_role_name` (`role_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色表';

-- =====================================================
-- 创建用户表 (users)
-- =====================================================
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `username` varchar(50) NOT NULL COMMENT '用户名，登录用',
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `password` varchar(150) NOT NULL COMMENT '密码（BCrypt加密）',
  `companyname` varchar(50) DEFAULT '所有公司' COMMENT '所属公司',
  `department` varchar(50) DEFAULT '所有部门' COMMENT '所属部门',
  `status` varchar(50) DEFAULT 'ACTIVE' COMMENT '用户状态：ACTIVE、INACTIVE、PENDING',
  `role_id` int NOT NULL COMMENT '角色ID，外键',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_company_dept` (`companyname`, `department`),
  KEY `fk_user_role` (`role_id`),
  CONSTRAINT `fk_user_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- =====================================================
-- 插入默认角色数据
-- 注意：匹配当前数据库中roles表的实际结构（只有role_id和role_name字段）
-- =====================================================
INSERT INTO `roles` (`role_id`, `role_name`) VALUES
(1, 'ADMIN'),
(2, 'USER'),
(3, 'VIEWER')
ON DUPLICATE KEY UPDATE
  `role_name` = VALUES(`role_name`);

-- =====================================================
-- 插入默认管理员用户
-- 用户名: admin
-- 密码: admin123 (BCrypt加密后的值)
-- 注意：匹配当前数据库中users表的实际结构
-- =====================================================
INSERT INTO `users` (`username`, `name`, `password`, `companyname`, `department`, `status`, `role_id`) VALUES
('admin', '系统管理员', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P8jskrvi.6hwMK', '所有公司', '所有部门', 'ACTIVE', 1),
('laoshu198838', '周先生', '$2a$10$7THXRE0rp/EtC3lmxyydV.RMZq8mMhL4lOP2PveMB1YOfRwk/Cpj.', '所有公司', '所有部门', 'ACTIVE', 1)
ON DUPLICATE KEY UPDATE
  `name` = VALUES(`name`),
  `companyname` = VALUES(`companyname`),
  `department` = VALUES(`department`),
  `status` = VALUES(`status`),
  `role_id` = VALUES(`role_id`);

-- =====================================================
-- 创建用户会话表 (user_sessions) - 可选
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_sessions` (
  `session_id` varchar(128) NOT NULL COMMENT '会话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_access_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后访问时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理信息',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否活跃',
  PRIMARY KEY (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_access` (`last_access_time`),
  CONSTRAINT `fk_session_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- =====================================================
-- 创建用户操作日志表 (user_audit_logs) - 可选
-- =====================================================
CREATE TABLE IF NOT EXISTS `user_audit_logs` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `action` varchar(100) NOT NULL COMMENT '操作类型',
  `resource` varchar(200) DEFAULT NULL COMMENT '操作资源',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `result` varchar(20) DEFAULT 'SUCCESS' COMMENT '操作结果：SUCCESS、FAILED',
  `error_message` text COMMENT '错误信息',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`),
  KEY `idx_action` (`action`),
  KEY `idx_created_time` (`created_time`),
  CONSTRAINT `fk_audit_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作审计日志表';

-- =====================================================
-- 显示创建结果
-- =====================================================
SELECT 'user_system数据库初始化完成' AS message;
SELECT COUNT(*) AS role_count FROM roles;
SELECT COUNT(*) AS user_count FROM users;
