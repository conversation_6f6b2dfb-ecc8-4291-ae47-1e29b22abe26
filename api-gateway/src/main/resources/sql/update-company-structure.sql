-- 更新Company表结构以支持组织架构
-- 执行时间: 2025-07-02
-- 作者: laoshu198838

USE user_system;

-- 检查并添加新字段以支持组织架构
-- 检查company_short_name字段是否存在
SET @column_exists_short_name = (SELECT COUNT(*) FROM information_schema.COLUMNS 
                                WHERE TABLE_SCHEMA = 'user_system' AND TABLE_NAME = 'companies' AND COLUMN_NAME = 'company_short_name');

SET @sql = IF(@column_exists_short_name = 0, 
             'ALTER TABLE companies ADD COLUMN company_short_name VARCHAR(20) COMMENT "公司简称"',
             'SELECT "company_short_name字段已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查parent_company_id字段是否存在
SET @column_exists_parent = (SELECT COUNT(*) FROM information_schema.COLUMNS 
                            WHERE TABLE_SCHEMA = 'user_system' AND TABLE_NAME = 'companies' AND COLUMN_NAME = 'parent_company_id');

SET @sql = IF(@column_exists_parent = 0, 
             'ALTER TABLE companies ADD COLUMN parent_company_id BIGINT NULL COMMENT "上级公司ID"',
             'SELECT "parent_company_id字段已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查level字段是否存在
SET @column_exists_level = (SELECT COUNT(*) FROM information_schema.COLUMNS 
                           WHERE TABLE_SCHEMA = 'user_system' AND TABLE_NAME = 'companies' AND COLUMN_NAME = 'level');

SET @sql = IF(@column_exists_level = 0, 
             'ALTER TABLE companies ADD COLUMN level INT DEFAULT 1 COMMENT "公司层级"',
             'SELECT "level字段已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @index_exists_parent = (SELECT COUNT(*) FROM information_schema.STATISTICS 
                           WHERE TABLE_SCHEMA = 'user_system' AND TABLE_NAME = 'companies' AND INDEX_NAME = 'idx_parent_company_id');

SET @sql = IF(@index_exists_parent = 0, 
             'ALTER TABLE companies ADD INDEX idx_parent_company_id (parent_company_id)',
             'SELECT "idx_parent_company_id索引已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @index_exists_level = (SELECT COUNT(*) FROM information_schema.STATISTICS 
                          WHERE TABLE_SCHEMA = 'user_system' AND TABLE_NAME = 'companies' AND INDEX_NAME = 'idx_level');

SET @sql = IF(@index_exists_level = 0, 
             'ALTER TABLE companies ADD INDEX idx_level (level)',
             'SELECT "idx_level索引已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @index_exists_short_name = (SELECT COUNT(*) FROM information_schema.STATISTICS 
                               WHERE TABLE_SCHEMA = 'user_system' AND TABLE_NAME = 'companies' AND INDEX_NAME = 'idx_company_short_name');

SET @sql = IF(@index_exists_short_name = 0, 
             'ALTER TABLE companies ADD INDEX idx_company_short_name (company_short_name)',
             'SELECT "idx_company_short_name索引已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加外键约束（如果不存在）
-- 注意：这里使用IF NOT EXISTS语法，但MySQL的ALTER TABLE IF NOT EXISTS对约束支持有限
-- 所以我们先检查约束是否存在
SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.TABLE_CONSTRAINTS 
                         WHERE CONSTRAINT_SCHEMA = 'user_system' 
                         AND TABLE_NAME = 'companies' 
                         AND CONSTRAINT_NAME = 'fk_parent_company');

SET @sql = IF(@constraint_exists = 0, 
             'ALTER TABLE companies ADD CONSTRAINT fk_parent_company FOREIGN KEY (parent_company_id) REFERENCES companies(id) ON DELETE SET NULL',
             'SELECT "外键约束已存在" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有数据的公司简称
UPDATE companies SET company_short_name = 
  CASE 
    WHEN company_name = '深圳万润科技股份有限公司' THEN '万润科技'
    WHEN company_name = '万润科技' THEN '万润科技'
    WHEN company_name = 'A公司' THEN 'A公司'
    WHEN company_name = 'B公司' THEN 'B公司'
    WHEN company_name = 'C公司' THEN 'C公司'
    ELSE SUBSTRING(company_name, 1, 10) -- 默认取前10个字符作为简称
  END
WHERE company_short_name IS NULL;

-- 确保万润科技作为根公司
UPDATE companies 
SET parent_company_id = NULL, level = 1, is_management_company = TRUE 
WHERE company_name LIKE '%万润科技%' OR company_code = 'WRT';

-- 显示更新后的表结构
SHOW CREATE TABLE companies;

-- 显示当前数据
SELECT id, company_name, company_short_name, company_code, parent_company_id, level, is_management_company, status
FROM companies 
ORDER BY level, id;

-- 显示结果
SELECT "数据库表结构更新完成" as result;

COMMIT;