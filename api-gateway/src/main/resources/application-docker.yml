# Docker环境专用配置
spring:
  datasource:
    # 主数据源 - 使用环境变量覆盖
    primary:
      url: ${SPRING_DATASOURCE_PRIMARY_URL:***********************************************************************************************************************************************************************************}
      username: ${SPRING_DATASOURCE_PRIMARY_USERNAME:root}
      password: ${SPRING_DATASOURCE_PRIMARY_PASSWORD:Zlb&198838}
    
    # 副数据源
    secondary:
      url: ${SPRING_DATASOURCE_SECONDARY_URL:***************************************************************************************************************************************************************************}
      username: ${SPRING_DATASOURCE_SECONDARY_USERNAME:root}
      password: ${SPRING_DATASOURCE_SECONDARY_PASSWORD:Zlb&198838}
    
    # 用户系统数据源
    user-system:
      url: ${SPRING_DATASOURCE_USER_SYSTEM_URL:*************************************************************************************************************************************************************************************************************}
      username: ${SPRING_DATASOURCE_USER_SYSTEM_USERNAME:root}
      password: ${SPRING_DATASOURCE_USER_SYSTEM_PASSWORD:Zlb&198838}