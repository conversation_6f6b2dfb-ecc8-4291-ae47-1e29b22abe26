# Docker环境专用配置 - 修复中文数据库名连接问题
spring:
  datasource:
    # 主数据源 - 使用英文数据库名
    primary:
      url: ${SPRING_DATASOURCE_PRIMARY_URL:***********************************************************************************************************************************************************************************}
      username: ${SPRING_DATASOURCE_PRIMARY_USERNAME:root}
      password: ${SPRING_DATASOURCE_PRIMARY_PASSWORD:Zlb&198838}
    
    # 副数据源
    secondary:
      url: ${SPRING_DATASOURCE_SECONDARY_URL:***************************************************************************************************************************************************************************}
      username: ${SPRING_DATASOURCE_SECONDARY_USERNAME:root}
      password: ${SPRING_DATASOURCE_SECONDARY_PASSWORD:Zlb&198838}
    
    # 用户系统数据源
    user-system:
      url: ${SPRING_DATASOURCE_USER_SYSTEM_URL:*************************************************************************************************************************************************************************************************************}
      username: ${SPRING_DATASOURCE_USER_SYSTEM_USERNAME:root}
      password: ${SPRING_DATASOURCE_USER_SYSTEM_PASSWORD:Zlb&198838}

# 日志配置 - 用于调试
logging:
  level:
    com.zaxxer.hikari: DEBUG
    org.springframework.jdbc: DEBUG
    com.mysql.cj.jdbc: DEBUG