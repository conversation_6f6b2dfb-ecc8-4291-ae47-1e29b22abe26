# OA工作流下载文件存储目录

此目录用于存储从OA系统提取的工作流文件。

## 目录结构

```
dev-oa-workflows/
├── 202506/                    # 按年月分组
│   ├── pdf/                   # PDF文件
│   │   ├── 关于XX的日常留言_20250703_161234.pdf
│   │   └── 日常留言_工作汇报_20250703_161245.pdf
│   ├── screenshots/           # 截图文件
│   │   ├── 关于XX的日常留言_20250703_161234.png
│   │   └── 日常留言_工作汇报_20250703_161245.png
│   └── html/                  # HTML文件
│       ├── 关于XX的日常留言_20250703_161234.html
│       └── 日常留言_工作汇报_20250703_161245.html
└── 202507/
    ├── pdf/
    ├── screenshots/
    └── html/
```

## 文件命名规则

```
{流程标题}_{时间戳}.{扩展名}
```

## 使用说明

1. **通过Web界面**: 访问 `http://localhost:3000/oa-workflow`
2. **通过API**: 调用 `/api/oa/workflow/extract-sync` 接口
3. **输入OA账号**: zhoulb / Zlb&198838
4. **选择时间范围**: 如最近3天
5. **开始提取**: 文件将自动保存到此目录

## 注意事项

- 文件按月份自动分组存储
- 支持PDF、截图、HTML三种格式
- 自动清理特殊字符以确保文件名安全
- 文件名包含时间戳避免重复

---
*由FinancialSystem OA工作流提取模块自动生成*