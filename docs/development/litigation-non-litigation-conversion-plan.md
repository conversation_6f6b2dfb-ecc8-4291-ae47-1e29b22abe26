# 诉讼与非诉讼债权互转功能实现方案


# 不要破坏现有


## 一、需求概述

实现诉讼表和非诉讼表之间债权的相互转换功能，主要包括：
- 诉讼债权转非诉讼债权
- 非诉讼债权转诉讼债权
- 同步更新减值准备表中的"是否涉诉"状态
- 转换操作不影响新增金额和处置金额统计

## 二、技术架构设计

### 2.1 前端架构

#### 2.1.1 页面路由配置
- **路径**: `/debt-management/litigation-conversion`
- **菜单位置**: 与"逾期债权新增录入"和"逾期债权处置更新"同级
- **菜单名称**: 诉讼和非诉讼互转

#### 2.1.2 页面功能设计
1. **筛选功能**
   - 债权人输入框（支持模糊搜索）
   - 债务人输入框（支持模糊搜索）
   - 自动搜索并显示匹配的债权记录
   - 显示字段：债权人、债务人、期间、债权余额、当前状态（诉讼/非诉讼）

2. **转换方向选择**
   - 单选按钮：诉讼转非诉讼 / 非诉讼转诉讼
   - 根据选择动态显示需要录入的额外字段

3. **录入信息**
   - 公共字段：
     - 转换年月选择器（年份和月份合并显示）
     - 备注说明
   - 诉讼转非诉讼：无额外字段
   - 非诉讼转诉讼：
     - 诉讼案件名称（必填）
     - 诉讼主张本金
     - 诉讼主张利息及罚金
     - 诉讼费
     - 中介费

4. **提交按钮**
   - 诉讼转非诉讼时显示："转为非诉讼"
   - 非诉讼转诉讼时显示："转为诉讼"

### 2.2 后端架构

#### 2.2.1 API设计

1. **搜索债权记录**
   - 端点：`GET /api/debts/conversion/search`
   - 参数：creditor（债权人）、debtor（债务人）
   - 返回：匹配的诉讼和非诉讼债权记录列表

2. **诉讼转非诉讼**
   - 端点：`POST /api/debts/conversion/litigation-to-non-litigation`
   - 请求体：
   ```json
   {
     "creditor": "债权人",
     "debtor": "债务人",
     "period": "期间",
     "year": 2025,
     "month": 1,
     "conversionYear": 2025,
     "conversionMonth": 1,
     "remark": "转换说明"
   }
   ```

3. **非诉讼转诉讼**
   - 端点：`POST /api/debts/conversion/non-litigation-to-litigation`
   - 请求体：
   ```json
   {
     "creditor": "债权人",
     "debtor": "债务人",
     "period": "期间",
     "year": 2025,
     "month": 1,
     "conversionYear": 2025,
     "conversionMonth": 1,
     "litigationCase": "案件名称",
     "litigationOccurredPrincipal": 100000,
     "litigationInterestFee": 10000,
     "litigationFee": 5000,
     "intermediaryFee": 3000,
     "remark": "转换说明"
   }
   ```

#### 2.2.2 服务层设计

创建新的服务类 `DebtConversionService`，主要方法：
- `searchConvertibleDebts()`: 搜索可转换的债权
- `convertLitigationToNonLitigation()`: 诉讼转非诉讼
- `convertNonLitigationToLitigation()`: 非诉讼转诉讼
- `updateImpairmentProvision()`: 更新减值准备表

## 三、数据转换逻辑

### 3.1 诉讼转非诉讼

1. **诉讼表更新**
   - 查找条件：年份、月份、债权人、债务人、期间
   - 更新操作：
     - 涉诉债权本金 = 0
     - 涉诉债权应收利息罚息服务费 = 0
     - 本月末债权余额 = 0
     - 添加转换备注

2. **非诉讼表新增**
   - 核心字段映射：
     - 债权人、债务人、期间 → 直接复制
     - 年份、月份 → 使用转换年月
     - 上月末本金/利息/违约金 = 0
     - 本月本金增减 = 诉讼表的上月末债权余额
     - 本月末本金 = 本月本金增减
     - 本月末利息/违约金 = 0
   - 其他字段：
     - 管理公司、科目名称、责任人等 → 从诉讼表复制
     - 本年度回收目标、累计回收 → 从诉讼表复制

3. **减值准备表更新**
   - 查找条件：债权人、债务人、年份、月份、期间、是否涉诉="是"
   - 更新：是否涉诉 = "否"

### 3.2 非诉讼转诉讼

1. **非诉讼表更新**
   - 查找条件：年份、月份、债权人、债务人、期间
   - 更新操作：
     - 本月末本金 = 0
     - 本月末利息 = 0
     - 本月末违约金 = 0
     - 添加转换备注

2. **诉讼表新增**
   - 核心字段映射：
     - 债权人、债务人、期间 → 直接复制
     - 年份、月份 → 使用转换年月
     - 上月末债权余额 = 非诉讼表的本月末余额
     - 涉诉债权本金 = 非诉讼表的本月末本金
     - 涉诉债权应收利息罚息服务费 = 非诉讼表的（本月末利息 + 本月末违约金）
     - 本月末债权余额 = 上月末债权余额
   - 诉讼特有字段：
     - 诉讼案件 = 用户输入
     - 诉讼主张本金 = 用户输入
     - 诉讼主张应收利息及罚金 = 用户输入
     - 诉讼费 = 用户输入
     - 中介费 = 用户输入
   - 其他字段：
     - 管理公司、科目名称、责任人等 → 从非诉讼表复制

3. **减值准备表更新**
   - 查找条件：债权人、债务人、年份、月份、期间、是否涉诉="否"
   - 更新：是否涉诉 = "是"

## 四、实现步骤

### 第一阶段：后端实现
1. 创建转换请求DTO类
2. 实现 `DebtConversionService` 服务类
3. 创建 `DebtConversionController` 控制器
4. 编写单元测试

### 第二阶段：前端实现
1. 创建 `LitigationConversion.js` 组件
2. 实现筛选功能（复用现有的搜索逻辑）
3. 实现转换方向选择和动态表单
4. 集成API调用
5. 添加路由配置

### 第三阶段：集成测试
1. 测试诉讼转非诉讼完整流程
2. 测试非诉讼转诉讼完整流程
3. 验证减值准备表同步更新
4. 验证数据一致性

## 五、注意事项

1. **事务处理**：整个转换过程必须在一个事务中完成，确保数据一致性
2. **权限控制**：需要适当的权限才能执行转换操作
3. **日志记录**：记录详细的转换日志，包括操作人、时间、转换内容
4. **数据验证**：
   - 验证源记录存在且有效
   - 验证目标表中不存在重复记录
   - 验证转换年月的合理性
5. **业务规则**：
   - 转换不影响新增金额和处置金额统计
   - 债权期间保持不变
   - 保留历史数据的可追溯性

## 六、前端路由配置注意事项

### 6.1 关键配置要点

1. **组件位置**：
   - 页面组件必须放在 `src/layouts/debtmanagement/pages/` 目录下
   - 不要放在 `src/components/` 目录（这是通用组件目录）

2. **组件结构**：
   - 必须使用 `DashboardLayout` 和 `DashboardNavbar` 包装
   - 示例：
   ```javascript
   return (
     <DashboardLayout>
       <DashboardNavbar />
       {/* 页面内容 */}
     </DashboardLayout>
   );
   ```

3. **路由配置**：
   - 在 `routes.jsx` 中添加路由时，父菜单不应有 `route` 属性
   - 只有叶子节点（最终页面）才应该有 `route` 和 `component` 属性
   - 错误示例：
   ```javascript
   {
     type: 'collapse',
     name: '债权管理',
     route: '/debt-management', // ❌ 父菜单不应有route
     collapse: [...]
   }
   ```
   - 正确示例：
   ```javascript
   {
     type: 'collapse',
     name: '债权管理',
     key: 'debt-management',
     icon: <Icon fontSize="small">credit_card</Icon>,
     collapse: [
       {
         type: 'collapse',
         name: '诉讼和非诉讼互转',
         key: 'litigation-conversion',
         route: '/debt-management/litigation-conversion', // ✅ 叶子节点有route
         component: <LitigationConversion />
       }
     ]
   }
   ```

4. **AppRoutes.js 配置**：
   - 需要在 `AppRoutes.js` 中注册路由
   - 添加在受保护的路由区域内

### 6.2 常见问题和解决方案

1. **问题：点击菜单显示错误页面**
   - 原因：组件未使用 DashboardLayout 包装或位置不正确
   - 解决：确保组件在正确目录并使用正确的包装器

2. **问题：点击父菜单跳转到默认页面**
   - 原因：父菜单有 `route` 属性
   - 解决：删除父菜单的 `route` 属性

3. **问题：菜单无法展开/折叠**
   - 原因：Sidenav 组件的点击处理逻辑问题
   - 解决：确保 `Sidenav/index.js` 中正确处理无 route 的菜单项

## 七、可复用的现有代码

1. **前端组件**：
   - `OverdueDebtAdd.js` 的搜索功能
   - 表单验证逻辑
   - API调用封装

2. **后端服务**：
   - `OverdueDebtManagementService` 的数据查询逻辑
   - Repository层的基础CRUD操作
   - 事务管理配置

3. **工具类**：
   - 日期处理工具
   - 数据验证工具
   - Excel导出工具（如需要导出转换记录）