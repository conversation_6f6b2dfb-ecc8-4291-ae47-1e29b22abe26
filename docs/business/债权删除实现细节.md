# 债权删除实现细节

## 一、代码实现位置

### 1. 后端实现

- **服务层**：`/services/debt-management/src/main/java/com/laoshu198838/service/DebtDeletionService.java`
- **控制器**：`/api-gateway/src/main/java/com/laoshu198838/controller/debt/DebtDeletionController.java`
- **DTO**：`/shared/common/src/main/java/com/laoshu198838/dto/debt/DebtDeletionDTO.java`

### 2. 前端实现

- **处置删除**：`/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueReductionUpdate.js`
- **新增删除**：`/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueDebtAdd.js`

## 二、API接口定义

### 1. 删除处置债权

```
POST /api/debt/deletion/disposal
```

请求体：
```json
{
    "creditor": "债权人名称",
    "debtor": "债务人名称",
    "managementCompany": "管理公司",
    "isLitigation": "是/否",
    "period": "2024-02",
    "year": 2024,
    "month": 2,
    "amount": 126.0,
    "deleteReason": "删除原因",
    "operatorName": "操作人"
}
```

### 2. 删除新增债权

```
POST /api/debt/deletion/addition
```

请求体格式相同。

## 三、关键方法说明

### 1. getMonthAmount / setMonthAmount

用于操作新增表的月份字段：

```java
private BigDecimal getMonthAmount(OverdueDebtAdd record, int month) {
    switch (month) {
        case 1: return record.getAmountJan();
        case 2: return record.getAmountFeb();
        case 3: return record.getAmountMar();
        // ... 其他月份
        default: return BigDecimal.ZERO;
    }
}

private void setMonthAmount(OverdueDebtAdd record, int month, BigDecimal amount) {
    switch (month) {
        case 1: record.setAmountJan(amount); break;
        case 2: record.setAmountFeb(amount); break;
        case 3: record.setAmountMar(amount); break;
        // ... 其他月份
    }
}
```

### 2. recalculateBalancesForMonth

重新计算指定月份的所有余额：

```java
private void recalculateBalancesForMonth(DebtDeletionDTO dto) {
    // 1. 计算处置总额
    BigDecimal totalDisposal = calculateTotalDisposalForMonth(dto);
    
    // 2. 计算新增总额
    BigDecimal totalAddition = calculateTotalAdditionForMonth(dto);
    
    // 3. 获取上月余额
    BigDecimal lastMonthBalance = getLastMonthBalance(dto);
    
    // 4. 计算本月末余额
    BigDecimal currentMonthBalance = lastMonthBalance + totalAddition - totalDisposal;
    
    // 5. 更新各表
    updateTableBalances(dto, currentMonthBalance, totalAddition, totalDisposal);
}
```

### 3. updateSubsequentMonthsWithBalanceTransfer

处理后续月份的余额传递：

```java
private void updateSubsequentMonthsWithBalanceTransfer(DebtDeletionDTO dto) {
    Calendar[] subsequentMonths = FiveTableUpdateHelper.getSubsequentMonths(createUpdateContext(dto));
    
    for (Calendar month : subsequentMonths) {
        int year = month.get(Calendar.YEAR);
        int monthValue = month.get(Calendar.MONTH) + 1;
        
        // 为每个后续月份重新计算余额
        DebtDeletionDTO monthDto = new DebtDeletionDTO();
        monthDto.setYear(year);
        monthDto.setMonth(monthValue);
        // ... 复制其他必要字段
        
        recalculateBalancesForMonth(monthDto);
    }
}
```

## 四、错误处理

### 1. 记录不存在

```java
if (!optionalRecord.isPresent()) {
    throw new RuntimeException("未找到要删除的记录");
}
```

### 2. 主键冲突处理

处置表删除时的主键冲突已通过"更新而非插入"策略解决。

### 3. 事务回滚

所有操作在 `@Transactional` 注解下执行，任何异常都会导致事务回滚。

## 五、日志记录

### 1. 操作日志

```java
log.info("[{}] 收到删除处置债权请求: 债权人={}, 债务人={}, 年份={}, 月份={}, 金额={}", 
    requestId, dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth(), dto.getAmount());
```

### 2. 审计日志

```java
Long auditLogId = auditLogService.logDeletion(
    "DELETE_DISPOSAL", 
    dto, 
    "删除处置债权"
);
```

### 3. 调试日志

```java
log.info("查询到{}条处置记录", disposals.size());
for (OverdueDebtDecrease disposal : disposals) {
    log.info("  处置记录: 金额={}, 备注={}, 更新时间={}", 
        disposal.getMonthlyReduceAmount(), disposal.getRemark(), disposal.getUpdateTime());
}
```

## 六、性能优化

### 1. 批量查询

使用 JPA 的批量查询减少数据库访问：
```java
List<OverdueDebtDecrease> disposals = overdueDebtDecreaseRepository
    .findByCreditorAndDebtorAndPeriodAndYearAndMonth(...);
```

### 2. 事务优化

将多个更新操作放在一个事务中，减少事务开销。

### 3. 缓存考虑

对于频繁查询的上月余额，可以考虑添加缓存。

## 七、测试用例

### 1. 单元测试示例

```java
@Test
public void testDeleteDisposal() {
    // 准备测试数据
    DebtDeletionDTO dto = new DebtDeletionDTO();
    dto.setCreditor("测试债权人");
    dto.setDebtor("测试债务人");
    dto.setYear(2024);
    dto.setMonth(2);
    dto.setAmount(new BigDecimal("126"));
    
    // 执行删除
    DebtDeletionResult result = debtDeletionService.deleteDebt(dto);
    
    // 验证结果
    assertTrue(result.isSuccess());
    assertEquals("处置债权删除成功", result.getMessage());
}
```

### 2. 集成测试要点

- 验证余额计算的正确性
- 验证事务的原子性
- 验证并发操作的安全性

## 八、常见问题

### 1. 为什么不直接删除记录？

- 保留操作历史便于审计
- 支持数据恢复
- 维护数据完整性

### 2. 如何处理重复删除？

通过更新现有记录的方式，重复删除会继续减少金额，这是业务允许的。

### 3. 删除操作是否可逆？

理论上可以通过再次"删除"负数金额来实现恢复，但建议通过正常的新增/处置流程。