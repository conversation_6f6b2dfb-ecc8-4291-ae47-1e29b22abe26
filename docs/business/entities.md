# 业务实体说明文档

本文档详细说明 FinancialSystem 中的核心业务实体及其关系。

## 核心业务实体

### 1. 债权人 (Creditor)
- **定义**：被欠款的实体，即债权的所有者
- **属性**：
  - 债权人名称
  - 债权人代码
  - 联系方式
  - 法人代表
  - 注册地址
- **业务规则**：
  - 一个债权可以有多个债权人（共同债权）
  - 债权人信息变更需要记录历史

### 2. 债务人 (Debtor)
- **定义**：欠款的实体，即债务的承担者
- **属性**：
  - 债务人名称
  - 债务人代码
  - 信用代码
  - 联系方式
  - 经营状态
- **业务规则**：
  - 债务人可能是企业或个人
  - 需要维护债务人的信用评级

### 3. 逾期债权 (Overdue Debt)
- **定义**：超过约定还款期限的债权
- **核心属性**：
  - 债权编号
  - 原始金额
  - 当前余额
  - 逾期天数
  - 债权类型
  - 发生日期
  - 到期日期
- **状态流转**：
  - 正常 → 逾期 → 处置中 → 已结清/已核销
- **业务特点**：
  - 支持本金和利息分别管理
  - 自动计算逾期利息和罚息

### 4. 处置 (Disposal)
- **定义**：为解决逾期债权而采取的各种措施
- **处置类型**：
  - **现金清收**：直接收回现金
  - **资产抵债**：以资产抵偿债务
  - **债务重组**：重新安排还款计划
  - **债权转让**：将债权转让给第三方
  - **债务核销**：认定无法收回并核销
- **记录要素**：
  - 处置日期
  - 处置金额
  - 处置方式
  - 处置依据
  - 审批信息

### 5. 减值准备 (Impairment Reserve)
- **定义**：针对可能无法全额收回的债权计提的会计准备
- **计提规则**：
  - 按逾期时间分级计提
  - 支持个别认定和组合计提
  - 可以调整和转回
- **核心字段**：
  - 计提期间
  - 计提比例
  - 计提金额
  - 计提依据
  - 审批状态

### 6. 诉讼债权 (Litigation Claim)
- **定义**：通过法律诉讼方式追讨的债权
- **诉讼阶段**：
  1. 立案阶段
  2. 审理阶段
  3. 判决阶段
  4. 执行阶段
- **关键信息**：
  - 案件编号
  - 受理法院
  - 诉讼请求
  - 判决结果
  - 执行情况

### 7. 非诉讼债权 (Non-litigation Claim)
- **定义**：通过非法律途径处理的债权
- **处理方式**：
  - 协商和解
  - 调解仲裁
  - 催收谈判
  - 债务重组
- **记录内容**：
  - 协商进展
  - 和解方案
  - 还款承诺
  - 跟进记录

## 实体关系

### 主要关系图
```
债权人 ←→ 逾期债权 ←→ 债务人
           ↓
      ┌────┴────┐
      ↓         ↓
   处置记录   减值准备
      ↓         
 ┌────┴────┐
 ↓         ↓
诉讼     非诉讼
```

### 关系说明

1. **债权人-逾期债权-债务人**
   - 多对多关系：一个债权可能涉及多个债权人和债务人
   - 通过关联表管理复杂关系

2. **逾期债权-处置记录**
   - 一对多关系：一个债权可以有多次处置记录
   - 处置记录累计影响债权余额

3. **逾期债权-减值准备**
   - 一对多关系：按期计提减值准备
   - 支持减值准备的调整和转回

4. **处置-诉讼/非诉讼**
   - 互斥关系：一个处置要么是诉讼要么是非诉讼
   - 不同类型有不同的处理流程

## 业务规则

### 1. 余额计算规则
```
当前余额 = 原始金额 - Σ(处置金额) + 利息累计 - 减值准备
```

### 2. 状态转换规则
- 新增债权自动设为"正常"状态
- 超过到期日自动转为"逾期"状态
- 有处置记录后转为"处置中"状态
- 余额为0时可转为"已结清"状态

### 3. 权限控制规则
- 新增债权需要录入权限
- 处置操作需要处置权限
- 核销操作需要高级权限
- 数据导出需要导出权限

### 4. 数据完整性规则
- 删除处置记录时自动恢复债权余额
- 处置金额不能超过当前余额
- 计提减值准备不能超过债权余额

## 业务流程

### 1. 债权生命周期
```
债权产生 → 债权确认 → 债权逾期 → 
债权处置 → 部分回收/全额回收 → 
债权结清/债权核销
```

### 2. 处置流程
```
发起处置 → 处置审批 → 
执行处置 → 登记结果 → 
更新余额 → 生成报告
```

### 3. 报表流程
```
数据采集 → 数据清洗 → 
指标计算 → 报表生成 → 
报表审核 → 报表发布
```

## 相关文档

- [业务需求文档](/docs/business/)
- [前端字段映射](/docs/business/前端字段与后端表字段映射分析.md)
- [债权删除业务逻辑](/docs/business/债权删除业务逻辑说明.md)
- [API接口文档](/docs/api/README.md)