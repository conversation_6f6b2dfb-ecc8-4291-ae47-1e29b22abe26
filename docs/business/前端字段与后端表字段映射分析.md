# 前端字段与后端表字段映射分析

## 概述

本文档详细分析FinancialSystem项目中前端页面输入字段与后端数据库表字段的映射关系，为重构提供准确的字段对应关系参考。

## 一、债权新增功能字段映射

### 1.1 前端表单字段（OverdueDebtAdd.js）

```javascript
const [formData, setFormData] = useState({
    creditor: '',                    // 债权人
    debtor: '',                      // 债务人
    managementCompany: '',           // 管理公司
    isLitigation: '否',              // 是否涉诉
    caseName: '',                    // 案件名称
    subjectName: '应收账款',         // 科目名称
    overdueAmount: '',               // 逾期金额
    overdueDate: '',                 // 逾期日期
    provisionAmount: '0',            // 坏账准备计提金额
    responsiblePerson: '',           // 责任人
    debtCategory: '生产经营类债权',   // 债权类型
    debtNature: '生产经营类债权',     // 债权性质
    litigationClaim: {               // 诉讼主张
        principal: 0,                // 本金
        interest: 0,                 // 利息
        penalty: 0,                  // 罚金
        litigationFee: 0,           // 诉讼费
        intermediaryFee: 0,         // 中介费
    },
    measures: '',                    // 处理措施
    addDate: '',                     // 新增年月（格式：2025-01）
});
```

### 1.2 DTO对象字段（OverdueDebtAddDTO.java）

```java
public class OverdueDebtAddDTO {
    // 基本信息
    private String creditor;              // 债权人
    private String debtor;                // 债务人
    
    // 管理信息
    private String managementCompany;     // 管理公司
    private String subjectName;           // 科目名称
    private String isLitigation;          // 是否涉诉
    private String responsiblePerson;     // 责任人
    
    // 金额信息
    private BigDecimal overdueAmount;     // 新增逾期金额
    private BigDecimal provisionAmount;   // 坏账准备计提金额
    
    // 债权信息
    private String debtCategory;          // 债权类别
    private String debtNature;            // 债权性质
    private String overdueDate;           // 逾期所属年月
    private String addDate;               // 新增年月
    private String period;                // 期间信息
    private String measures;              // 处理措施
}
```

### 1.3 后端表字段映射

#### 1.3.1 新增表（OverdueDebtAdd）映射

| 前端字段 | DTO字段 | 数据库字段 | 映射逻辑 |
|---------|---------|-----------|----------|
| creditor | creditor | 债权人 | 直接映射 |
| debtor | debtor | 债务人 | 直接映射 |
| managementCompany | managementCompany | 管理公司 | 直接映射 |
| isLitigation | isLitigation | 是否涉诉 | 直接映射 |
| debtNature | debtNature | 债权性质 | 直接映射 |
| subjectName | subjectName | 科目名称 | 直接映射 |
| overdueDate | overdueDate | 到期时间 | 日期格式转换 |
| measures | measures | 备注 | 直接映射 |
| addDate | addDate | 年份 + 对应月份字段 | 解析年月，更新对应月份 |
| overdueAmount | overdueAmount | 对应月份金额 | 根据addDate设置对应月份 |
| - | period | 期间 | 自动生成或前端传入 |

**特殊映射逻辑：**
```java
// 根据addDate设置对应月份的金额
private void setAmountByMonth(OverdueDebtAdd entity, String yearMonth, BigDecimal amount) {
    String month = yearMonth.split("-")[1];
    switch (month) {
        case "01": entity.setJanuary(amount); break;
        case "02": entity.setFebruary(amount); break;
        // ... 其他月份
    }
}
```

#### 1.3.2 减值准备表（ImpairmentReserve）映射

| 前端字段 | DTO字段 | 数据库字段 | 映射逻辑 |
|---------|---------|-----------|----------|
| creditor | creditor | 债权人 | 直接映射 |
| debtor | debtor | 债务人 | 直接映射 |
| isLitigation | isLitigation | 是否涉诉 | 直接映射 |
| addDate | addDate | 年份, 月份 | 解析年月分别设置 |
| overdueAmount | overdueAmount | 本月新增债权 | 直接映射 |
| overdueAmount | overdueAmount | 本月末债权余额 | 累加逻辑 |
| provisionAmount | provisionAmount | 计提减值金额 | 直接映射或计算 |
| managementCompany | managementCompany | 管理公司 | 直接映射 |
| subjectName | subjectName | 科目名称 | 直接映射 |
| - | period | 期间 | 自动生成 |

**计算逻辑：**
```java
// 如果未提供计提金额，按50%计算
if (dto.getProvisionAmount() == null || dto.getProvisionAmount().equals(BigDecimal.ZERO)) {
    BigDecimal calculatedProvision = dto.getOverdueAmount().multiply(new BigDecimal("0.5"));
    reserve.setImpairmentAmount(calculatedProvision);
}
```

#### 1.3.3 诉讼表（LitigationClaim）映射（当isLitigation="是"时）

| 前端字段 | DTO字段 | 数据库字段 | 映射逻辑 |
|---------|---------|-----------|----------|
| creditor | creditor | 债权人 | 直接映射 |
| debtor | debtor | 债务人 | 直接映射 |
| addDate | addDate | 年份, 月份 | 解析年月分别设置 |
| overdueAmount | overdueAmount | 涉诉债权本金 | 直接映射 |
| overdueAmount | overdueAmount | 本月新增债权 | 直接映射 |
| - | - | 上月末债权余额 | 从上月数据复制或计算 |
| - | - | 本月处置债权 | 从处置功能累加 |
| - | - | 本月末债权余额 | **公式计算：上月末+新增-处置** |
| managementCompany | managementCompany | 管理公司 | 直接映射 |
| overdueDate | overdueDate | 到期时间 | 日期格式转换 |
| debtCategory | debtCategory | 债权类型 | 直接映射 |
| - | period | 期间 | 根据逾期日期生成 |

**重要业务规则**：
```java
// 核心余额计算公式
本月末债权余额 = 上月末债权余额 + 本月新增债权 - 本月处置债权
```

#### 1.3.4 非诉讼表（NonLitigationClaim）映射（当isLitigation="否"时）

| 前端字段 | DTO字段 | 数据库字段 | 映射逻辑 |
|---------|---------|-----------|----------|
| creditor | creditor | 债权人 | 直接映射 |
| debtor | debtor | 债务人 | 直接映射 |
| addDate | addDate | 年份, 月份 | 解析年月分别设置 |
| overdueAmount | overdueAmount | 本月本金增减 | 直接映射 |
| overdueAmount | overdueAmount | 本月新增债权 | 直接映射 |
| overdueAmount | overdueAmount | 本月末本金 | 累加逻辑 |
| managementCompany | managementCompany | 管理公司 | 直接映射 |
| overdueDate | overdueDate | 到期时间 | 日期格式转换 |
| debtCategory | debtCategory | 债权类型 | 直接映射 |
| - | period | 期间 | 自动生成 |

## 二、债权处置功能字段映射

### 2.1 前端表单字段（OverdueReductionUpdate.js）

```javascript
// 主要处置信息
const [creditor, setCreditor] = useState('');           // 债权人
const [debtor, setDebtor] = useState('');               // 债务人
const [managementCompany, setManagementCompany] = useState(''); // 管理公司
const [yearMonth, setYearMonth] = useState('');         // 年月
const [dispositionAmount, setDispositionAmount] = useState(''); // 处置金额

// 处置方式详情
const [dispositionDetails, setDispositionDetails] = useState({
    cashDisposal: '',           // 现金处置
    installmentRepayment: '',   // 分期还款
    assetDebt: '',             // 资产抵债
    accountingAdjustment: '',   // 账务调整
    otherWays: ''              // 其他方式
});

// 其他信息
const [remark, setRemark] = useState('');               // 备注
```

### 2.2 前端数据组织（提交前处理）

```javascript
const prepareDataForSubmission = () => {
    const [yearStr, monthStr] = yearMonth.split('-');
    return {
        // 联合主键
        id: {
            creditor,
            debtor,
            period: debtPeriod,
            isLitigation: debtDetails?.isLitigation || '',
            year: parseInt(yearStr, 10),
            month: parseInt(monthStr, 10)
        },
        // 处置金额
        dispositionAmount: parseFloat(dispositionAmount),
        // 处置方式
        cashAmount: parseFloat(dispositionDetails.cashDisposal) || 0,
        installmentAmount: parseFloat(dispositionDetails.installmentRepayment) || 0,
        assetAmount: parseFloat(dispositionDetails.assetDebt) || 0,
        adjustmentAmount: parseFloat(dispositionDetails.accountingAdjustment) || 0,
        otherAmount: parseFloat(dispositionDetails.otherWays) || 0,
        // 其他信息
        managementCompany,
        remark
    };
};
```

### 2.3 后端数据转换（OverdueDebtDecreaseService）

```java
// 转换前端数据格式为内部处理格式
Map<String, Object> reductionData = new HashMap<>();

// 构建id对象（包含联合主键信息）
Map<String, Object> idMap = new HashMap<>();
idMap.put("creditor", inputData.get("creditor"));
idMap.put("debtor", inputData.get("debtor"));
idMap.put("period", inputData.get("period"));
idMap.put("isLitigation", inputData.get("isLitigation"));

// 解析yearMonth获取年份和月份
String yearMonth = (String) inputData.get("yearMonth");
String[] parts = yearMonth.split("-");
idMap.put("year", Integer.parseInt(parts[0]));
idMap.put("month", Integer.parseInt(parts[1]));

reductionData.put("id", idMap);

// 处理处置金额映射
if (inputData.containsKey("dispositionAmount")) {
    reductionData.put("monthlyReduceAmount", inputData.get("dispositionAmount"));
}

// 处理处置方式映射
if (inputData.containsKey("cashAmount")) {
    reductionData.put("cashDisposal", inputData.get("cashAmount"));
}
if (inputData.containsKey("installmentAmount")) {
    reductionData.put("installmentRepayment", inputData.get("installmentAmount"));
}
if (inputData.containsKey("assetAmount")) {
    reductionData.put("assetDebt", inputData.get("assetAmount"));
}
if (inputData.containsKey("otherAmount")) {
    reductionData.put("otherWays", inputData.get("otherAmount"));
}
```

### 2.4 后端表字段映射

#### 2.4.1 处置表（OverdueDebtDecrease）映射

| 前端字段 | 后端转换字段 | 数据库字段 | 映射逻辑 |
|---------|-------------|-----------|----------|
| creditor | creditor | 债权人 | 直接映射 |
| debtor | debtor | 债务人 | 直接映射 |
| period | period | 期间 | 直接映射 |
| isLitigation | isLitigation | 是否涉诉 | 直接映射 |
| yearMonth | year, month | 年份, 月份 | 解析年月分别设置 |
| dispositionAmount | monthlyReduceAmount | 每月处置金额 | 直接映射 |
| cashAmount | cashDisposal | 现金处置 | 直接映射 |
| installmentAmount | installmentRepayment | 分期还款 | 直接映射 |
| assetAmount | assetDebt | 资产抵债 | 直接映射 |
| otherAmount | otherWays | 其他方式 | 直接映射 |
| managementCompany | managementCompany | 管理公司 | 直接映射 |
| remark | remark | 备注 | 直接映射 |

#### 2.4.2 减值准备表处置更新映射

| 前端字段 | 数据库字段 | 映射逻辑 |
|---------|-----------|----------|
| creditor | 债权人 | 直接映射 |
| debtor | 债务人 | 直接映射 |
| period | 期间 | 直接映射 |
| isLitigation | 是否涉诉 | 直接映射 |
| yearMonth | 年份, 月份 | 解析年月分别设置 |
| dispositionAmount | 本月处置债权 | 直接映射 |
| dispositionAmount | 本月末债权余额 | 减少逻辑：原余额 - 处置金额 |
| dispositionAmount | 本月末余额 | 重新计算减值准备 |

#### 2.4.3 诉讼表处置更新映射（当isLitigation="是"时）

| 前端字段 | 数据库字段 | 映射逻辑 |
|---------|-----------|----------|
| creditor | 债权人 | 直接映射 |
| debtor | 债务人 | 直接映射 |
| yearMonth | 年份, 月份 | 解析年月分别设置 |
| dispositionAmount | 本月处置债权 | 直接映射 |
| dispositionAmount | 本月末债权余额 | 减少逻辑：原余额 - 处置金额 |
| dispositionAmount | 涉诉债权本金 | 调整逻辑：保持利息不变，减少本金 |

#### 2.4.4 非诉讼表处置更新映射（当isLitigation="否"时）

| 前端字段 | 数据库字段 | 映射逻辑 |
|---------|-----------|----------|
| creditor | 债权人 | 直接映射 |
| debtor | 债务人 | 直接映射 |
| yearMonth | 年份, 月份 | 解析年月分别设置 |
| dispositionAmount | 本月处置债权 | 直接映射 |
| dispositionAmount | 本月本金增减 | 减少逻辑：负数表示减少 |
| dispositionAmount | 本月末本金 | 减少逻辑：原本金 - 处置金额 |

## 三、债权查询功能字段映射

### 3.1 前端查询字段（DebtSearch.js）

```javascript
const [searchParams, setSearchParams] = useState({
    creditor: '',    // 债权人
    debtor: ''       // 债务人
});

// 查询结果显示字段
const columns = [
    { field: 'creditor', headerName: '债权人' },
    { field: 'debtor', headerName: '债务人' },
    { field: 'period', headerName: '归属期间' },
    { field: 'isLitigation', headerName: '是否诉讼' },
    { field: 'managementCompany', headerName: '管理公司' },
    { field: 'newAmount', headerName: '新增金额' },
    { field: 'reductionAmount', headerName: '处置金额' },
    { field: 'debtBalance', headerName: '剩余债权' }
];
```

### 3.2 后端查询映射

查询功能主要通过Repository的查询方法实现，字段映射相对简单：

| 前端字段 | 查询参数 | 数据库字段 | 映射逻辑 |
|---------|---------|-----------|----------|
| creditor | creditor | 债权人 | LIKE模糊查询 |
| debtor | debtor | 债务人 | LIKE模糊查询 |

**查询逻辑：**
```java
@Query("SELECT o FROM OverdueDebtAdd o WHERE " +
       "(o.id.creditor LIKE %:creditor% OR :creditor IS NULL) AND " +
       "(o.id.debtor LIKE %:debtor% OR :debtor IS NULL) " +
       "ORDER BY o.id.period DESC")
List<OverdueDebtAdd> findByCreditorAndDebtor(@Param("creditor") String creditor,
                                             @Param("debtor") String debtor);
```

## 四、重构建议

### 4.1 字段映射标准化

基于以上分析，建议在重构时：

1. **统一DTO设计**：确保所有模块使用相同的DTO字段名称
2. **标准化映射逻辑**：将字段映射逻辑抽取为通用工具类
3. **统一日期处理**：标准化日期字段的格式转换逻辑
4. **统一金额计算**：标准化金额字段的计算和累加逻辑

### 4.2 映射工具类设计

```java
@Component
public class FieldMappingUtil {

    /**
     * 将DTO字段映射到新增表实体
     */
    public static OverdueDebtAdd mapToOverdueDebtAdd(OverdueDebtAddDTO dto) {
        // 统一的映射逻辑
    }

    /**
     * 将DTO字段映射到减值准备表实体
     */
    public static ImpairmentReserve mapToImpairmentReserve(OverdueDebtAddDTO dto) {
        // 统一的映射逻辑
    }

    /**
     * 处理月份金额设置的通用逻辑
     */
    public static void setAmountByMonth(OverdueDebtAdd entity, String yearMonth, BigDecimal amount) {
        // 统一的月份处理逻辑
    }
}
```

这样的设计可以确保重构后的代码在字段映射方面保持一致性和可维护性。

## 五、诉讼表业务逻辑详细分析

### 5.1 诉讼表业务规则检查结果

根据您提供的业务规则，我检查了现有诉讼表的更新逻辑：

#### ✅ **已正确实现的业务逻辑**

1. **案件名称设置逻辑** ✅
```java
// 设置诉讼案件名称
if (dto.getCaseName() != null && !dto.getCaseName().isEmpty()) {
    litigationClaim.setLitigationCase(dto.getCaseName());
} else if (dto.getLitigationName() != null && !dto.getLitigationName().isEmpty()) {
    litigationClaim.setLitigationCase(dto.getLitigationName());
} else if (litigationClaim.getLitigationCase() == null || litigationClaim.getLitigationCase().isEmpty()) {
    // 使用"债权人-债务人-诉讼"格式（接近您要求的格式）
    litigationClaim.setLitigationCase(dto.getCreditor() + "-" + dto.getDebtor() + "-诉讼");
}
```

2. **本月新增债权** ✅
```java
// 直接从前端传入的新增逾期金额设置
litigationClaim.setCurrentMonthNewDebt(dto.getOverdueAmount());
```

3. **本月处置债权累加** ✅
```java
// 在处置服务中实现累加逻辑
BigDecimal currentDisposal = existingClaim.getCurrentMonthDisposalDebt() != null ?
    existingClaim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
BigDecimal newDisposal = currentDisposal.add(reduceAmount);
existingClaim.setCurrentMonthDisposalDebt(newDisposal);
```

4. **本年度累计回收计算** ✅
```java
// 通过SQL窗口函数计算累计回收
UPDATE 诉讼表 t1
JOIN (
    SELECT 债权人, 债务人, 期间, 年份, 月份,
           SUM(本月处置债权) OVER (
               PARTITION BY 债权人, 债务人, 期间, 年份
               ORDER BY 月份
               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
           ) AS 累计处置
    FROM 诉讼表
) t2 ON t1.债权人 = t2.债权人 AND t1.债务人 = t2.债务人
       AND t1.期间 = t2.期间 AND t1.年份 = t2.年份 AND t1.月份 = t2.月份
SET t1.本年度累计回收 = t2.累计处置
```

5. **逾期年限计算** ✅
```java
private String calculateOverdueYearCategory(Date dueDate, Date currentDate) {
    if (dueDate.after(currentDate)) return "";

    long yearsBetween = ChronoUnit.YEARS.between(
        dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
        currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
    );

    if (yearsBetween < 1) {
        return "1年（含）以下";
    } else if (yearsBetween >= 1 && yearsBetween < 5) {
        return "1年-5年";
    } else {
        return "5年（含）以上";
    }
}
```

6. **债权余额平衡计算** ✅
```java
// 计算本月末债权余额 = 上月末债权余额 + 本月新增债权 - 本月处置债权
BigDecimal lastMonthBalance = litigationClaim.getLastMonthDebtBalance() != null ?
    litigationClaim.getLastMonthDebtBalance() : BigDecimal.ZERO;
BigDecimal currentMonthDisposal = litigationClaim.getCurrentMonthDisposalDebt() != null ?
    litigationClaim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
BigDecimal currentMonthNewDebt = litigationClaim.getCurrentMonthNewDebt() != null ?
    litigationClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
BigDecimal currentMonthBalance = lastMonthBalance.add(currentMonthNewDebt).subtract(currentMonthDisposal);
litigationClaim.setCurrentMonthDebtBalance(currentMonthBalance);
```

7. **后续月份联动更新** ✅
```java
// 更新后续月份的上月末债权余额和本月末债权余额
BigDecimal currentMonthEndBalance = litigationClaim.getCurrentMonthDebtBalance();
nextClaim.setLastMonthDebtBalance(currentMonthEndBalance);

// 重新计算下一个月的本月末债权余额
BigDecimal nextMonthNewDebt = nextClaim.getCurrentMonthNewDebt() != null ?
                              nextClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
BigDecimal nextMonthDisposal = nextClaim.getCurrentMonthDisposalDebt() != null ?
                               nextClaim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
BigDecimal nextMonthBalance = currentMonthEndBalance.add(nextMonthNewDebt).subtract(nextMonthDisposal);
nextClaim.setCurrentMonthDebtBalance(nextMonthBalance);
```

#### ❌ **需要修正的业务逻辑**

1. **上月末债权余额计算逻辑** ❌
   - **您的要求**：上月末债权余额 = 涉诉债权本金 + 涉诉债权应收利息
   - **当前实现**：只是简单的数据复制，没有验证和调整逻辑

2. **本金和利息调整逻辑** ❌
   - **您的要求**：如果两者不相等调整本金和利息，默认调本金
   - **当前实现**：
   ```java
   // 当前只是简单计算利息 = 余额 - 本金，没有平衡调整逻辑
   BigDecimal litigationInterest = currentMonthBalance.subtract(litigationPrincipal);
   litigationClaim.setLitigationInterest(litigationInterest);
   ```

### 5.2 需要补充的业务逻辑

#### 5.2.1 案件名称格式修正
**当前格式**：`债权人-债务人-诉讼`
**您要求的格式**：`债权人诉债务人`

**建议修正**：
```java
// 修正案件名称格式
litigationClaim.setLitigationCase(dto.getCreditor() + "诉" + dto.getDebtor());
```

#### 5.2.2 上月末债权余额验证和调整逻辑
**需要添加的逻辑**：
```java
// 验证上月末债权余额 = 涉诉债权本金 + 涉诉债权应收利息
BigDecimal calculatedBalance = litigationPrincipal.add(litigationInterest);
if (!lastMonthBalance.equals(calculatedBalance)) {
    // 默认调整本金，保持利息不变
    BigDecimal adjustedPrincipal = lastMonthBalance.subtract(litigationInterest);
    litigationClaim.setLitigationPrincipal(adjustedPrincipal);
    logger.info("调整涉诉债权本金: 原值={}, 调整后={}", litigationPrincipal, adjustedPrincipal);
}
```

#### 5.2.3 债权余额平衡计算公式 ✅ **已正确实现**
**您要求的公式**：上月末债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额

**当前实现**：完全符合要求 ✅
```java
// 当前代码完全符合您的要求
BigDecimal lastMonthBalance = litigationClaim.getLastMonthDebtBalance() != null ?
    litigationClaim.getLastMonthDebtBalance() : BigDecimal.ZERO;
BigDecimal currentMonthDisposal = litigationClaim.getCurrentMonthDisposalDebt() != null ?
    litigationClaim.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
BigDecimal currentMonthNewDebt = litigationClaim.getCurrentMonthNewDebt() != null ?
    litigationClaim.getCurrentMonthNewDebt() : BigDecimal.ZERO;

// 完全按照您的公式计算
BigDecimal currentMonthBalance = lastMonthBalance.add(currentMonthNewDebt).subtract(currentMonthDisposal);
litigationClaim.setCurrentMonthDebtBalance(currentMonthBalance);
```

### 5.3 重构建议

#### 5.3.1 创建诉讼表业务逻辑工具类
```java
@Component
public class LitigationBusinessLogicUtil {

    /**
     * 生成案件名称
     */
    public static String generateCaseName(String creditor, String debtor, String inputCaseName) {
        if (inputCaseName != null && !inputCaseName.isEmpty()) {
            return inputCaseName;
        }
        return creditor + "诉" + debtor;
    }

    /**
     * 验证和调整本金利息平衡
     */
    public static void adjustPrincipalInterestBalance(LitigationClaim claim) {
        BigDecimal lastMonthBalance = claim.getLastMonthDebtBalance();
        BigDecimal principal = claim.getLitigationPrincipal();
        BigDecimal interest = claim.getLitigationInterest();

        if (lastMonthBalance != null && principal != null && interest != null) {
            BigDecimal calculatedBalance = principal.add(interest);
            if (!lastMonthBalance.equals(calculatedBalance)) {
                // 默认调整本金
                BigDecimal adjustedPrincipal = lastMonthBalance.subtract(interest);
                claim.setLitigationPrincipal(adjustedPrincipal);
            }
        }
    }

    /**
     * 计算逾期年限
     */
    public static String calculateOverdueYears(Date dueDate) {
        if (dueDate == null || dueDate.after(new Date())) {
            return "";
        }

        long yearsBetween = ChronoUnit.YEARS.between(
            dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
            LocalDate.now()
        );

        if (yearsBetween < 1) {
            return "1年（含）以下";
        } else if (yearsBetween >= 1 && yearsBetween < 5) {
            return "1年-5年";
        } else {
            return "5年（含）以上";
        }
    }
}
```

### 5.4 重构优先级

**高优先级（必须修正）**：
1. 案件名称格式修正（`债权人诉债务人`）
2. 上月末债权余额验证和调整逻辑

**中优先级（建议改进）**：
1. 抽取业务逻辑工具类
2. 统一逾期年限计算逻辑

**低优先级（长期优化）**：
1. 完善单元测试覆盖
2. 添加业务规则配置化

**✅ 已正确实现（无需修改）**：
1. **债权余额平衡计算公式**：上月末债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额
2. **后续月份联动更新**：修改当月数据时自动更新后续月份的余额
3. **本年度累计回收计算**：通过SQL窗口函数正确实现
4. **逾期年限分类计算**：正确实现三个年限档次

## 六、非诉讼表业务逻辑详细分析

### 6.1 非诉讼表业务规则检查结果

根据您提供的业务规则，我检查了现有非诉讼表的更新逻辑：

#### ✅ **已正确实现的业务逻辑**

1. **本月新增债权** ✅
```java
// 直接从前端传入的新增逾期金额设置
nonLitigationClaim.setCurrentMonthNewDebt(currentIncrease);
```

2. **本月处置债权累加** ✅
```java
// 在处置服务中实现累加逻辑
BigDecimal currentDisposal = existingClaim.getCurrentMonthDisposedDebt() != null ?
    existingClaim.getCurrentMonthDisposedDebt() : BigDecimal.ZERO;
BigDecimal newDisposal = currentDisposal.add(reduceAmount);
existingClaim.setCurrentMonthDisposedDebt(newDisposal);
```

3. **本年度累计回收计算** ✅
```java
// 通过SQL窗口函数计算累计回收
UPDATE 非诉讼表 t1
JOIN (
    SELECT 债权人, 债务人, 期间, 年份, 月份,
           SUM(本月处置债权) OVER (
               PARTITION BY 债权人, 债务人, 期间, 年份
               ORDER BY 月份
               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
           ) AS 累计处置
    FROM 非诉讼表
) t2 ON t1.债权人 = t2.债权人 AND t1.债务人 = t2.债务人
       AND t1.期间 = t2.期间 AND t1.年份 = t2.年份 AND t1.月份 = t2.月份
SET t1.本年度累计回收 = t2.累计处置
```

4. **逾期年限计算** ✅
```java
private String calculateOverdueYearCategory(Date dueDate, Date currentDate) {
    if (dueDate.after(currentDate)) return "";

    long yearsBetween = ChronoUnit.YEARS.between(
        dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
        currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
    );

    if (yearsBetween < 1) {
        return "1年（含）以下";
    } else if (yearsBetween >= 1 && yearsBetween < 5) {
        return "1年-5年";
    } else {
        return "5年（含）以上";
    }
}
```

5. **期间字段生成** ✅
```java
// 常量定义
private static final String NEW_DEBT_SUFFIX = "年新增债权";

// 期间生成逻辑
if (dto.getOverdueDate() != null && !dto.getOverdueDate().isEmpty()) {
    String overdueYear = dto.getOverdueDate().split("-")[0];
    period = overdueYear + "年430";
} else {
    period = year + NEW_DEBT_SUFFIX; // 例如："2025年新增债权"
}
```

#### ❌ **需要修正的业务逻辑**

1. **本金计算公式错误** ❌
   - **您的更正要求**：
     - ~~上月末本金 + 本月本金增减 = 上月末本金~~ （这个公式有误）
     - **正确公式1**：上月末本金 + 本月本金增减 = 本月末本金
     - **正确公式2**：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金

   - **当前实现**：只实现了公式1，缺少公式2
   ```java
   // 当前只有公式1的实现
   BigDecimal currentMonthPrincipal = lastMonthPrincipal.add(currentIncrease);
   nonLitigationClaim.setCurrentMonthPrincipal(currentMonthPrincipal);

   // 缺少公式2：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金
   ```

2. **利息和违约金的平衡计算逻辑不完整** ❌
   - **您的要求**：
     - 上月末利息 + 本月利息增减 = 本月末利息
     - 上月末违约金 + 本月违约金增减 = 本月末违约金

   - **当前实现**：只处理了本金，没有处理利息和违约金的完整逻辑

### 6.2 需要补充的业务逻辑

#### 6.2.1 本金的双重计算公式实现
**需要添加的逻辑**：
```java
/**
 * 实现本金的两个计算公式
 */
private void calculatePrincipalBalances(NonLitigationClaim claim) {
    BigDecimal lastMonthPrincipal = claim.getLastMonthPrincipal() != null ?
        claim.getLastMonthPrincipal() : BigDecimal.ZERO;

    // 公式1：上月末本金 + 本月本金增减 = 本月末本金（已实现）
    BigDecimal principalIncrease = claim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
        claim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
    BigDecimal calculatedByFormula1 = lastMonthPrincipal.add(principalIncrease);

    // 公式2：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金（需要新增）
    BigDecimal newDebt = claim.getCurrentMonthNewDebt() != null ?
        claim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
    BigDecimal disposedDebt = claim.getCurrentMonthDisposedDebt() != null ?
        claim.getCurrentMonthDisposedDebt() : BigDecimal.ZERO;
    BigDecimal calculatedByFormula2 = lastMonthPrincipal.add(newDebt).subtract(disposedDebt);

    // 验证两个公式的结果是否一致
    if (!calculatedByFormula1.equals(calculatedByFormula2)) {
        logger.warn("本金计算公式不一致: 公式1结果={}, 公式2结果={}",
                   calculatedByFormula1, calculatedByFormula2);
        // 可以选择使用公式2的结果，因为它更直观地反映了业务逻辑
        claim.setCurrentMonthPrincipal(calculatedByFormula2);
        // 同时调整本月本金增减，使公式1也成立
        BigDecimal adjustedIncrease = calculatedByFormula2.subtract(lastMonthPrincipal);
        claim.setCurrentMonthPrincipalIncreaseDecrease(adjustedIncrease);
    } else {
        claim.setCurrentMonthPrincipal(calculatedByFormula1);
    }
}
```

#### 6.2.2 完整的利息和违约金平衡计算
**需要添加的逻辑**：
```java
/**
 * 计算利息和违约金的平衡关系
 */
private void calculateInterestAndPenaltyBalances(NonLitigationClaim claim) {
    // 利息计算：上月末利息 + 本月利息增减 = 本月末利息
    BigDecimal lastMonthInterest = claim.getLastMonthInterest() != null ?
        claim.getLastMonthInterest() : BigDecimal.ZERO;
    BigDecimal interestIncrease = claim.getCurrentMonthInterestIncreaseDecrease() != null ?
        claim.getCurrentMonthInterestIncreaseDecrease() : BigDecimal.ZERO;
    claim.setCurrentMonthInterest(lastMonthInterest.add(interestIncrease));

    // 违约金计算：上月末违约金 + 本月违约金增减 = 本月末违约金
    BigDecimal lastMonthPenalty = claim.getLastMonthPenalty() != null ?
        claim.getLastMonthPenalty() : BigDecimal.ZERO;
    BigDecimal penaltyIncrease = claim.getCurrentMonthPenaltyIncreaseDecrease() != null ?
        claim.getCurrentMonthPenaltyIncreaseDecrease() : BigDecimal.ZERO;
    claim.setCurrentMonthPenalty(lastMonthPenalty.add(penaltyIncrease));
}
```

#### 6.2.2 处置时的金额分配逻辑
**需要添加的逻辑**：
```java
/**
 * 处置时按比例减少本金、利息、违约金
 */
private void distributeDisposalAmount(NonLitigationClaim claim, BigDecimal disposalAmount) {
    BigDecimal totalAmount = claim.getCurrentMonthPrincipal()
        .add(claim.getCurrentMonthInterest())
        .add(claim.getCurrentMonthPenalty());

    if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
        // 按比例分配处置金额
        BigDecimal principalRatio = claim.getCurrentMonthPrincipal().divide(totalAmount, 4, RoundingMode.HALF_UP);
        BigDecimal interestRatio = claim.getCurrentMonthInterest().divide(totalAmount, 4, RoundingMode.HALF_UP);
        BigDecimal penaltyRatio = claim.getCurrentMonthPenalty().divide(totalAmount, 4, RoundingMode.HALF_UP);

        // 计算各部分的处置金额
        BigDecimal principalDisposal = disposalAmount.multiply(principalRatio);
        BigDecimal interestDisposal = disposalAmount.multiply(interestRatio);
        BigDecimal penaltyDisposal = disposalAmount.multiply(penaltyRatio);

        // 更新各项金额
        claim.setCurrentMonthPrincipal(claim.getCurrentMonthPrincipal().subtract(principalDisposal));
        claim.setCurrentMonthInterest(claim.getCurrentMonthInterest().subtract(interestDisposal));
        claim.setCurrentMonthPenalty(claim.getCurrentMonthPenalty().subtract(penaltyDisposal));

        // 更新增减字段
        claim.setCurrentMonthPrincipalIncreaseDecrease(
            claim.getCurrentMonthPrincipalIncreaseDecrease().subtract(principalDisposal));
        claim.setCurrentMonthInterestIncreaseDecrease(
            claim.getCurrentMonthInterestIncreaseDecrease().subtract(interestDisposal));
        claim.setCurrentMonthPenaltyIncreaseDecrease(
            claim.getCurrentMonthPenaltyIncreaseDecrease().subtract(penaltyDisposal));
    }
}
```

### 6.3 重构建议

#### 6.3.1 创建非诉讼表业务逻辑工具类
```java
@Component
public class NonLitigationBusinessLogicUtil {

    private static final Logger logger = LoggerFactory.getLogger(NonLitigationBusinessLogicUtil.class);

    /**
     * 生成期间信息
     */
    public static String generatePeriod(String yearMonth, String overdueDate) {
        if (overdueDate != null && !overdueDate.isEmpty()) {
            String overdueYear = overdueDate.split("-")[0];
            return overdueYear + "年430";
        } else {
            String year = yearMonth.split("-")[0];
            return year + "年新增债权";
        }
    }

    /**
     * 实现本金的双重计算公式验证
     */
    public static void validateAndCalculatePrincipalBalances(NonLitigationClaim claim) {
        BigDecimal lastMonthPrincipal = claim.getLastMonthPrincipal() != null ?
            claim.getLastMonthPrincipal() : BigDecimal.ZERO;

        // 公式1：上月末本金 + 本月本金增减 = 本月末本金
        BigDecimal principalIncrease = claim.getCurrentMonthPrincipalIncreaseDecrease() != null ?
            claim.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
        BigDecimal calculatedByFormula1 = lastMonthPrincipal.add(principalIncrease);

        // 公式2：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金
        BigDecimal newDebt = claim.getCurrentMonthNewDebt() != null ?
            claim.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        BigDecimal disposedDebt = claim.getCurrentMonthDisposedDebt() != null ?
            claim.getCurrentMonthDisposedDebt() : BigDecimal.ZERO;
        BigDecimal calculatedByFormula2 = lastMonthPrincipal.add(newDebt).subtract(disposedDebt);

        // 验证并设置最终值
        if (!calculatedByFormula1.equals(calculatedByFormula2)) {
            logger.warn("本金计算公式不一致: 公式1结果={}, 公式2结果={}",
                       calculatedByFormula1, calculatedByFormula2);
            // 使用公式2的结果，并调整本月本金增减
            claim.setCurrentMonthPrincipal(calculatedByFormula2);
            BigDecimal adjustedIncrease = calculatedByFormula2.subtract(lastMonthPrincipal);
            claim.setCurrentMonthPrincipalIncreaseDecrease(adjustedIncrease);
        } else {
            claim.setCurrentMonthPrincipal(calculatedByFormula1);
        }
    }

    /**
     * 计算利息和违约金平衡
     */
    public static void calculateInterestAndPenaltyBalances(NonLitigationClaim claim) {
        // 利息计算：上月末利息 + 本月利息增减 = 本月末利息
        BigDecimal lastMonthInterest = claim.getLastMonthInterest() != null ?
            claim.getLastMonthInterest() : BigDecimal.ZERO;
        BigDecimal interestIncrease = claim.getCurrentMonthInterestIncreaseDecrease() != null ?
            claim.getCurrentMonthInterestIncreaseDecrease() : BigDecimal.ZERO;
        claim.setCurrentMonthInterest(lastMonthInterest.add(interestIncrease));

        // 违约金计算：上月末违约金 + 本月违约金增减 = 本月末违约金
        BigDecimal lastMonthPenalty = claim.getLastMonthPenalty() != null ?
            claim.getLastMonthPenalty() : BigDecimal.ZERO;
        BigDecimal penaltyIncrease = claim.getCurrentMonthPenaltyIncreaseDecrease() != null ?
            claim.getCurrentMonthPenaltyIncreaseDecrease() : BigDecimal.ZERO;
        claim.setCurrentMonthPenalty(lastMonthPenalty.add(penaltyIncrease));
    }

    /**
     * 计算逾期年限
     */
    public static String calculateOverdueYears(Date dueDate) {
        if (dueDate == null || dueDate.after(new Date())) {
            return "";
        }

        long yearsBetween = ChronoUnit.YEARS.between(
            dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
            LocalDate.now()
        );

        if (yearsBetween < 1) {
            return "1年（含）以下";
        } else if (yearsBetween >= 1 && yearsBetween < 5) {
            return "1年-5年";
        } else {
            return "5年（含）以上";
        }
    }
}
```

### 6.4 重构优先级

**高优先级（必须修正）**：
1. **实现本金的双重计算公式**：
   - 公式1：上月末本金 + 本月本金增减 = 本月末本金（已实现）
   - 公式2：上月末本金 + 本月新增债权 - 本月处置债权 = 本月末本金（需新增）
2. **补充利息和违约金的完整计算逻辑**
3. **实现两个公式之间的一致性验证机制**

**中优先级（建议改进）**：
1. 抽取业务逻辑工具类
2. 统一期间字段生成逻辑
3. 完善处置时的金额分配策略

**低优先级（长期优化）**：
1. 完善单元测试覆盖
2. 添加业务规则配置化

**✅ 已正确实现（无需修改）**：
1. **本年度累计回收计算**：通过SQL窗口函数正确实现
2. **逾期年限分类计算**：正确实现三个年限档次
3. **期间字段生成**：正确实现"年份+新增债权"格式
4. **本月新增债权和本月处置债权**：字段映射和累加逻辑正确

## 七、减值准备表业务逻辑详细分析

### 7.1 减值准备表业务规则检查结果

根据您提供的业务规则，我检查了现有减值准备表的更新逻辑：

#### ✅ **已正确实现的业务逻辑**

1. **计提减值金额=本月末余额** ✅
```java
// 设置计提减值金额等于本月末余额
reserve.setImpairmentAmount(finalProvisionBalance);

// 定时任务中的一致性检查
if (currentMonthAmount != null && (impairmentAmount == null ||
                                   currentMonthAmount.compareTo(impairmentAmount) != 0)) {
    record.setImpairmentAmount(currentMonthAmount);
}
```

2. **本月末余额=上月末余额+本月增减** ✅
```java
// 计算本月末减值准备余额 = 上月末减值准备余额 + 本月增减
BigDecimal finalProvisionBalance = lastProvisionBalance.add(provisionAmount);
reserve.setCurrentMonthAmount(finalProvisionBalance);
```

3. **本年度累计回收计算** ✅
```java
// 通过SQL窗口函数计算累计回收
UPDATE 减值准备表 t1
JOIN (
    SELECT 债权人, 债务人, 是否涉诉, 年份, 期间, 月份,
           SUM(本月处置债权) OVER (
               PARTITION BY 债权人, 债务人, 是否涉诉, 年份, 期间
               ORDER BY 月份
               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
           ) AS 累计处置
    FROM 减值准备表
) t2 ON t1.债权人 = t2.债权人 AND t1.债务人 = t2.债务人
       AND t1.是否涉诉 = t2.是否涉诉 AND t1.年份 = t2.年份
       AND t1.期间 = t2.期间 AND t1.月份 = t2.月份
SET t1.本年度累计回收 = t2.累计处置
```

4. **是否全额计提坏账判断** ✅
```java
// 判断是否全额计提坏账: 如果本月末债权余额 = 本月末余额，则为"是"，否则为"否"
boolean isFullyImpaired = false;
if (currentMonthBalance != null && currentMonthAmount != null) {
    isFullyImpaired = currentMonthBalance.compareTo(currentMonthAmount) == 0;
}
String newValue = isFullyImpaired ? "是" : "否";
record.setIsAllImpaired(newValue);
```

5. **本月新增债权和本月处置债权累加** ✅
```java
// 从前端传入的新增逾期金额
reserve.setCurrentMonthNewDebt(dto.getOverdueAmount());

// 处置时累加逻辑
BigDecimal currentDisposal = existingReserve.getCurrentMonthDisposeDebt() != null ?
    existingReserve.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;
BigDecimal newDisposal = currentDisposal.add(reduceAmount);
existingReserve.setCurrentMonthDisposeDebt(newDisposal);
```

6. **逾期年限计算** ✅
```java
private String calculateOverdueYearCategory(Date dueDate, Date currentDate) {
    if (dueDate.after(currentDate)) return "";

    long yearsBetween = ChronoUnit.YEARS.between(
        dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
        currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
    );

    if (yearsBetween < 1) {
        return "1年（含）以下";
    } else if (yearsBetween >= 1 && yearsBetween < 5) {
        return "1年-5年";
    } else {
        return "5年（含）以上";
    }
}
```

#### ❌ **需要修正的业务逻辑**

1. **缺少债权余额平衡计算公式** ❌
   - **您的要求**：本月初债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额
   - **当前状态**：没有实现这个核心的债权余额计算公式

2. **减值准备本年度累计回收计算公式不完整** ❌
   - **您的要求**：上月末余额 - 本月末债权余额 = 减值准备本年度累计回收
   - **当前状态**：只实现了处置债权的累加，没有实现这个特殊的计算公式

### 7.2 需要补充的业务逻辑

#### 7.2.1 债权余额平衡计算公式
**需要添加的逻辑**：
```java
/**
 * 实现债权余额平衡计算：本月初债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额
 */
private void calculateDebtBalanceFormula(ImpairmentReserve reserve) {
    BigDecimal lastMonthBalance = reserve.getLastMonthBalance() != null ?
        reserve.getLastMonthBalance() : BigDecimal.ZERO;
    BigDecimal newDebt = reserve.getCurrentMonthNewDebt() != null ?
        reserve.getCurrentMonthNewDebt() : BigDecimal.ZERO;
    BigDecimal disposedDebt = reserve.getCurrentMonthDisposeDebt() != null ?
        reserve.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;

    // 计算本月末债权余额
    BigDecimal calculatedBalance = lastMonthBalance.add(newDebt).subtract(disposedDebt);
    reserve.setCurrentMonthBalance(calculatedBalance);

    logger.info("计算本月末债权余额: {} = {} + {} - {}",
               calculatedBalance, lastMonthBalance, newDebt, disposedDebt);
}
```

#### 7.2.2 减值准备本年度累计回收特殊计算
**需要添加的逻辑**：
```java
/**
 * 实现减值准备本年度累计回收计算：上月末余额 - 本月末债权余额 = 减值准备本年度累计回收
 */
private void calculateImpairmentAnnualRecovery(ImpairmentReserve reserve) {
    BigDecimal previousMonthBalance = reserve.getPreviousMonthBalance() != null ?
        reserve.getPreviousMonthBalance() : BigDecimal.ZERO;
    BigDecimal currentMonthBalance = reserve.getCurrentMonthBalance() != null ?
        reserve.getCurrentMonthBalance() : BigDecimal.ZERO;

    // 计算减值准备本年度累计回收
    BigDecimal impairmentRecovery = previousMonthBalance.subtract(currentMonthBalance);

    // 如果结果为负数，设为0（表示没有回收）
    if (impairmentRecovery.compareTo(BigDecimal.ZERO) < 0) {
        impairmentRecovery = BigDecimal.ZERO;
    }

    reserve.setAnnualCumulativeRecovery(impairmentRecovery);

    logger.info("计算减值准备本年度累计回收: {} = {} - {}",
               impairmentRecovery, previousMonthBalance, currentMonthBalance);
}
```

### 7.3 重构建议

#### 7.3.1 创建减值准备表业务逻辑工具类
```java
@Component
public class ImpairmentReserveBusinessLogicUtil {

    private static final Logger logger = LoggerFactory.getLogger(ImpairmentReserveBusinessLogicUtil.class);

    /**
     * 实现债权余额平衡计算公式
     */
    public static void calculateDebtBalanceFormula(ImpairmentReserve reserve) {
        BigDecimal lastMonthBalance = reserve.getLastMonthBalance() != null ?
            reserve.getLastMonthBalance() : BigDecimal.ZERO;
        BigDecimal newDebt = reserve.getCurrentMonthNewDebt() != null ?
            reserve.getCurrentMonthNewDebt() : BigDecimal.ZERO;
        BigDecimal disposedDebt = reserve.getCurrentMonthDisposeDebt() != null ?
            reserve.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;

        // 本月初债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额
        BigDecimal calculatedBalance = lastMonthBalance.add(newDebt).subtract(disposedDebt);
        reserve.setCurrentMonthBalance(calculatedBalance);
    }

    /**
     * 实现减值准备余额计算公式
     */
    public static void calculateImpairmentBalanceFormula(ImpairmentReserve reserve) {
        BigDecimal previousMonthBalance = reserve.getPreviousMonthBalance() != null ?
            reserve.getPreviousMonthBalance() : BigDecimal.ZERO;
        BigDecimal increaseDecrease = reserve.getCurrentMonthIncreaseDecrease() != null ?
            reserve.getCurrentMonthIncreaseDecrease() : BigDecimal.ZERO;

        // 本月末余额 = 上月末余额 + 本月增减
        BigDecimal currentMonthAmount = previousMonthBalance.add(increaseDecrease);
        reserve.setCurrentMonthAmount(currentMonthAmount);

        // 计提减值金额 = 本月末余额
        reserve.setImpairmentAmount(currentMonthAmount);
    }

    /**
     * 判断是否全额计提坏账
     */
    public static void determineFullImpairmentStatus(ImpairmentReserve reserve) {
        BigDecimal currentMonthBalance = reserve.getCurrentMonthBalance();
        BigDecimal currentMonthAmount = reserve.getCurrentMonthAmount();

        boolean isFullyImpaired = false;
        if (currentMonthBalance != null && currentMonthAmount != null) {
            // 如果本月末债权余额 = 本月末余额，则为"是"，否则为"否"
            isFullyImpaired = currentMonthBalance.compareTo(currentMonthAmount) == 0;
        }

        reserve.setIsAllImpaired(isFullyImpaired ? "是" : "否");
    }

    /**
     * 计算减值准备本年度累计回收
     */
    public static void calculateImpairmentAnnualRecovery(ImpairmentReserve reserve) {
        BigDecimal previousMonthBalance = reserve.getPreviousMonthBalance() != null ?
            reserve.getPreviousMonthBalance() : BigDecimal.ZERO;
        BigDecimal currentMonthBalance = reserve.getCurrentMonthBalance() != null ?
            reserve.getCurrentMonthBalance() : BigDecimal.ZERO;

        // 上月末余额 - 本月末债权余额 = 减值准备本年度累计回收
        BigDecimal impairmentRecovery = previousMonthBalance.subtract(currentMonthBalance);

        // 如果结果为负数，设为0
        if (impairmentRecovery.compareTo(BigDecimal.ZERO) < 0) {
            impairmentRecovery = BigDecimal.ZERO;
        }

        reserve.setAnnualCumulativeRecovery(impairmentRecovery);
    }

    /**
     * 计算逾期年限
     */
    public static String calculateOverdueYears(Date dueDate) {
        if (dueDate == null || dueDate.after(new Date())) {
            return "";
        }

        long yearsBetween = ChronoUnit.YEARS.between(
            dueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
            LocalDate.now()
        );

        if (yearsBetween < 1) {
            return "1年（含）以下";
        } else if (yearsBetween >= 1 && yearsBetween < 5) {
            return "1年-5年";
        } else {
            return "5年（含）以上";
        }
    }

    /**
     * 执行所有业务逻辑计算
     */
    public static void executeAllBusinessLogic(ImpairmentReserve reserve) {
        // 1. 计算债权余额平衡
        calculateDebtBalanceFormula(reserve);

        // 2. 计算减值准备余额
        calculateImpairmentBalanceFormula(reserve);

        // 3. 判断是否全额计提坏账
        determineFullImpairmentStatus(reserve);

        // 4. 计算减值准备本年度累计回收
        calculateImpairmentAnnualRecovery(reserve);

        // 5. 计算逾期年限（如果有到期时间）
        if (reserve.getDueDate() != null) {
            String overdueCategory = calculateOverdueYears(reserve.getDueDate());
            // 注意：实体类中可能需要添加逾期年限字段
        }
    }
}
```

### 7.4 重构优先级

**高优先级（必须修正）**：
1. **实现债权余额平衡计算公式**：本月初债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额
2. **实现减值准备本年度累计回收特殊计算**：上月末余额 - 本月末债权余额 = 减值准备本年度累计回收

**中优先级（建议改进）**：
1. 抽取业务逻辑工具类
2. 统一各表的业务逻辑处理方式
3. 完善字段验证和一致性检查

**低优先级（长期优化）**：
1. 完善单元测试覆盖
2. 添加业务规则配置化
3. 优化定时任务性能

**✅ 已正确实现（无需修改）**：
1. **计提减值金额=本月末余额**：通过定时任务确保一致性
2. **本月末余额=上月末余额+本月增减**：新增时正确计算
3. **本年度累计回收计算**：通过SQL窗口函数实现（需要补充特殊公式）
4. **是否全额计提坏账判断**：定时任务中正确实现
5. **本月新增债权和本月处置债权累加**：字段映射和累加逻辑正确
6. **逾期年限计算**：正确实现三个年限档次

## 八、新增表业务逻辑详细分析

### 8.1 新增表业务规则检查结果

根据您提供的业务规则，我检查了现有新增表的更新逻辑：

#### ✅ **已正确实现的业务逻辑**

1. **1月+2月+...+12月=新增金额** ✅
```sql
-- 数据库触发器自动计算
CREATE TRIGGER trg_更新新增金额_before_update
BEFORE UPDATE ON 新增表
FOR EACH ROW
BEGIN
  SET NEW.`新增金额` =
    IFNULL(NEW.`1月`, 0) + IFNULL(NEW.`2月`, 0) + IFNULL(NEW.`3月`, 0) +
    IFNULL(NEW.`4月`, 0) + IFNULL(NEW.`5月`, 0) + IFNULL(NEW.`6月`, 0) +
    IFNULL(NEW.`7月`, 0) + IFNULL(NEW.`8月`, 0) + IFNULL(NEW.`9月`, 0) +
    IFNULL(NEW.`10月`, 0) + IFNULL(NEW.`11月`, 0) + IFNULL(NEW.`12月`, 0);
END;
```

2. **期间字段生成** ✅
```java
// 期间为年份+新增债权，如2025年新增债权
String year = newYearMonth.split("-")[0];
entity.setPeriod(year + "年新增债权");
```

3. **前端新增逾期债权更新流程** ✅
```java
@Transactional
public OverdueDebtAdd addOverdueDebt(OverdueDebtAddDTO dto) {
    // （1）更新新增表
    OverdueDebtAdd entity = updateAddTable(dto);

    // （2）新增债权更新到减值准备表中
    updateImpairmentReserveTable(dto);

    // （3）根据是否涉诉更新诉讼表或非诉讼表
    if ("是".equals(dto.getIsLitigation())) {
        updateLitigationClaimTable(dto);
    } else {
        updateNonLitigationClaimTable(dto);
    }

    return entity;
}
```

4. **前端逾期债权处置更新流程** ✅
```java
public ResponseEntity<?> updateDebtReductionData(Map<String, Object> inputData) {
    // 1、将数据更新到债权处置表
    updateOverdueDebtDecreaseTable(reductionData);

    // 2、将数据更新到减值准备表
    impairmentReserveTable(reductionData);

    // 根据是否涉诉字段决定后续处理流程
    if ("是".equals(isLitigation)) {
        // 3、如果是涉诉债权，将数据更新到诉讼表
        return updateLitigationClaimTable(reductionData);
    } else {
        // 4、如果是非涉诉债权，将数据更新到非诉讼表
        return updateNonLitigationClaimTable(reductionData);
    }
}
```

#### ❌ **需要修正的业务逻辑**

1. **缺少处置金额计算逻辑** ❌
   - **您的要求**：处置金额根据债权人、债务人、年份、是否涉诉和期间从处置表中筛选对应当年的累计处置金额
   - **当前状态**：新增表中有处置金额字段，但没有实现从处置表自动计算累计处置金额的逻辑

2. **缺少债权余额计算公式** ❌
   - **您的要求**：新增金额 - 处置金额 = 债权余额
   - **当前状态**：没有实现这个计算公式

### 8.2 需要补充的业务逻辑

#### 8.2.1 处置金额自动计算
**需要添加的逻辑**：
```java
/**
 * 从处置表计算累计处置金额
 */
private BigDecimal calculateDisposalAmountFromDisposalTable(String creditor, String debtor,
                                                           String year, String isLitigation, String period) {
    // 查询处置表中对应条件的累计处置金额
    String sql = """
        SELECT COALESCE(SUM(每月处置金额), 0)
        FROM 处置表
        WHERE 债权人 = ? AND 债务人 = ? AND 年份 = ?
          AND 是否涉诉 = ? AND 期间 = ?
        """;

    BigDecimal totalDisposal = jdbcTemplate.queryForObject(sql, BigDecimal.class,
                                                          creditor, debtor, year, isLitigation, period);

    return totalDisposal != null ? totalDisposal : BigDecimal.ZERO;
}
```

#### 8.2.2 债权余额自动计算
**需要添加的逻辑**：
```java
/**
 * 计算债权余额：新增金额 - 处置金额 = 债权余额
 */
private void calculateDebtBalance(OverdueDebtAdd entity) {
    BigDecimal newAmount = entity.getNewOverdueDebtAmount() != null ?
        entity.getNewOverdueDebtAmount() : BigDecimal.ZERO;

    // 从处置表计算累计处置金额
    BigDecimal disposalAmount = calculateDisposalAmountFromDisposalTable(
        entity.getCreditor(), entity.getDebtor(), entity.getYear(),
        entity.getIsLitigation(), entity.getPeriod());

    // 计算债权余额
    BigDecimal debtBalance = newAmount.subtract(disposalAmount);
    entity.setDebtBalance(debtBalance);

    // 同时更新处置金额字段
    entity.setCashDisposal(disposalAmount); // 或者分别设置各种处置方式

    logger.info("计算债权余额: {} = {} - {}", debtBalance, newAmount, disposalAmount);
}
```

#### 8.2.3 定时任务中的新增表更新
**需要添加的逻辑**：
```java
/**
 * 定时更新新增表的处置金额和债权余额
 */
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void updateAddTableDisposalAndBalance() {
    logger.info("开始更新新增表的处置金额和债权余额");

    List<OverdueDebtAdd> allRecords = overdueDebtAddRepository.findAll();

    for (OverdueDebtAdd record : allRecords) {
        // 重新计算处置金额和债权余额
        calculateDebtBalance(record);
        overdueDebtAddRepository.save(record);
    }

    logger.info("新增表处置金额和债权余额更新完成，共更新 {} 条记录", allRecords.size());
}
```

### 8.3 重构建议

#### 8.3.1 创建新增表业务逻辑工具类
```java
@Component
public class AddTableBusinessLogicUtil {

    private static final Logger logger = LoggerFactory.getLogger(AddTableBusinessLogicUtil.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 生成期间信息
     */
    public static String generatePeriod(String yearMonth) {
        String year = yearMonth.split("-")[0];
        return year + "年新增债权";
    }

    /**
     * 从处置表计算累计处置金额
     */
    public BigDecimal calculateDisposalAmountFromDisposalTable(String creditor, String debtor,
                                                              String year, String isLitigation, String period) {
        String sql = """
            SELECT COALESCE(SUM(每月处置金额), 0)
            FROM 处置表
            WHERE 债权人 = ? AND 债务人 = ? AND 年份 = ?
              AND 是否涉诉 = ? AND 期间 = ?
            """;

        BigDecimal totalDisposal = jdbcTemplate.queryForObject(sql, BigDecimal.class,
                                                              creditor, debtor, year, isLitigation, period);

        return totalDisposal != null ? totalDisposal : BigDecimal.ZERO;
    }

    /**
     * 计算债权余额：新增金额 - 处置金额 = 债权余额
     */
    public void calculateDebtBalance(OverdueDebtAdd entity) {
        BigDecimal newAmount = entity.getNewOverdueDebtAmount() != null ?
            entity.getNewOverdueDebtAmount() : BigDecimal.ZERO;

        // 从处置表计算累计处置金额
        BigDecimal disposalAmount = calculateDisposalAmountFromDisposalTable(
            entity.getCreditor(), entity.getDebtor(), entity.getYear(),
            entity.getIsLitigation(), entity.getPeriod());

        // 计算债权余额
        BigDecimal debtBalance = newAmount.subtract(disposalAmount);
        entity.setDebtBalance(debtBalance);

        // 更新处置金额相关字段
        updateDisposalFields(entity, disposalAmount);

        logger.info("计算债权余额: {} = {} - {}", debtBalance, newAmount, disposalAmount);
    }

    /**
     * 更新处置金额相关字段
     */
    private void updateDisposalFields(OverdueDebtAdd entity, BigDecimal totalDisposal) {
        // 可以根据处置表中的具体处置方式分别设置
        // 这里简化处理，将总处置金额设置到现金处置字段
        entity.setCashDisposal(totalDisposal);
        entity.setInstallmentRepayment(BigDecimal.ZERO);
        entity.setAssetDebt(BigDecimal.ZERO);
        entity.setOtherMethods(BigDecimal.ZERO);
    }

    /**
     * 执行所有业务逻辑计算
     */
    public void executeAllBusinessLogic(OverdueDebtAdd entity) {
        // 1. 计算债权余额（包含处置金额计算）
        calculateDebtBalance(entity);

        // 2. 生成期间信息（如果未设置）
        if (entity.getPeriod() == null || entity.getPeriod().isEmpty()) {
            String period = generatePeriod(entity.getYear() + "-01");
            entity.setPeriod(period);
        }
    }
}
```

### 8.4 重构优先级

**高优先级（必须修正）**：
1. **实现处置金额自动计算**：从处置表筛选累计处置金额
2. **实现债权余额计算公式**：新增金额 - 处置金额 = 债权余额
3. **添加定时任务更新机制**：定期同步处置表数据到新增表

**中优先级（建议改进）**：
1. 抽取业务逻辑工具类
2. 优化处置金额的分类设置逻辑
3. 完善数据一致性检查

**低优先级（长期优化）**：
1. 完善单元测试覆盖
2. 添加业务规则配置化
3. 优化查询性能

**✅ 已正确实现（无需修改）**：
1. **1月+2月+...+12月=新增金额**：通过数据库触发器自动计算
2. **期间字段生成**：正确实现"年份+新增债权"格式
3. **前端新增更新流程**：先新增表→减值准备表→诉讼表/非诉讼表
4. **前端处置更新流程**：先处置表→减值准备表→诉讼表/非诉讼表

## 九、完整业务流程总结

### 9.1 前端新增逾期债权完整流程

**您要求的流程**：先更新新增表，然后更新减值准备表，再根据是否涉诉确定更新诉讼表还是非诉讼表

**当前实现状态**：✅ **完全符合要求**

```java
// 完整的新增流程实现
@Transactional
public OverdueDebtAdd addOverdueDebt(OverdueDebtAddDTO dto) {
    // 步骤1：更新新增表
    OverdueDebtAdd entity = updateAddTable(dto);

    // 步骤2：更新减值准备表
    updateImpairmentReserveTable(dto);

    // 步骤3：根据是否涉诉确定更新哪个表
    if ("是".equals(dto.getIsLitigation())) {
        updateLitigationClaimTable(dto);    // 更新诉讼表
    } else {
        updateNonLitigationClaimTable(dto); // 更新非诉讼表
    }

    return entity;
}
```

### 9.2 前端逾期债权处置完整流程

**您要求的流程**：先更新处置表，然后更新减值准备表，再根据是否涉诉确定更新诉讼表还是非诉讼表

**当前实现状态**：✅ **完全符合要求**

```java
// 完整的处置流程实现
public ResponseEntity<?> updateDebtReductionData(Map<String, Object> inputData) {
    // 步骤1：更新处置表
    updateOverdueDebtDecreaseTable(reductionData);

    // 步骤2：更新减值准备表
    impairmentReserveTable(reductionData);

    // 步骤3：根据是否涉诉确定更新哪个表
    if ("是".equals(isLitigation)) {
        return updateLitigationClaimTable(reductionData);    // 更新诉讼表
    } else {
        return updateNonLitigationClaimTable(reductionData); // 更新非诉讼表
    }
}
```

### 9.3 需要重构的核心问题汇总

#### **高优先级问题（必须解决）**：

1. **新增表**：
   - 缺少处置金额自动计算逻辑
   - 缺少债权余额计算公式（新增金额 - 处置金额 = 债权余额）

2. **减值准备表**：
   - 缺少债权余额平衡计算公式（本月初债权余额 + 本月新增债权 - 本月处置债权 = 本月末债权余额）
   - 缺少减值准备本年度累计回收特殊计算（上月末余额 - 本月末债权余额 = 减值准备本年度累计回收）

3. **非诉讼表**：
   - 缺少本金的双重计算公式验证
   - 缺少利息和违约金的完整计算逻辑

4. **诉讼表**：
   - 案件名称格式需要修正（改为"债权人诉债务人"）
   - 缺少上月末债权余额验证和调整逻辑

#### **已正确实现的功能**：

1. **更新流程**：前端新增和处置的完整更新流程完全符合要求
2. **字段映射**：前端字段与后端表字段的映射关系清晰准确
3. **基础计算**：各表的基础业务逻辑大部分已正确实现
4. **数据一致性**：通过定时任务维护数据一致性

这样重构后，整个FinancialSystem将完全符合您的业务规则，确保所有财务计算的准确性和数据的一致性。
