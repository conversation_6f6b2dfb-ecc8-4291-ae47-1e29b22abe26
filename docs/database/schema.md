# 数据库架构设计文档

本文档详细描述了 FinancialSystem 的数据库架构设计，包括主要数据库、表结构和关系。

## 数据库概览

系统使用多数据源架构，包含三个主要数据库：

1. **逾期债权数据库** - 主业务数据库（中文名称）
2. **user_system** - 用户系统数据库
3. **kingdee** - 金蝶ERP集成数据库（只读）

## 主数据库（逾期债权数据库）

### 重要说明
- **数据库中存在中文数据库名,库名为逾期债权数据库** - Confirmed presence of Chinese database name for overdue debt database
- 这是系统的核心业务数据库，存储所有债权相关数据

### 核心表结构

#### 1. 新增表 (OverdueDebtAdd)
- **说明**：主要债权记录表，使用复合主键
- **用途**：存储新增的逾期债权基础信息
- **主键**：复合主键设计，确保数据唯一性

#### 2. 处置表 (OverdueDebtDecrease)
- **说明**：债权处置/减少记录表
- **用途**：记录债权的处置、回收、核销等减少操作
- **关联**：与新增表通过债权ID关联

#### 3. 减值准备表 (ImpairmentReserve)
- **说明**：减值准备数据表
- **用途**：存储债权的减值准备计提信息
- **特点**：支持按期计提和调整

#### 4. 诉讼表 (LitigationClaim)
- **说明**：诉讼债权记录表
- **用途**：记录通过法律诉讼途径处理的债权
- **字段**：包含诉讼状态、法院信息、判决结果等

#### 5. 非诉讼表 (NonLitigationClaim)
- **说明**：非诉讼债权记录表
- **用途**：记录通过协商、调解等非诉讼方式处理的债权
- **字段**：包含协商进度、和解方案等信息

## 用户系统数据库 (user_system)

### 核心表结构

#### 1. User 表
- **说明**：用户认证和配置信息表
- **字段**：
  - id: 主键
  - username: 用户名
  - password: 加密密码
  - email: 邮箱
  - status: 账户状态
  - created_at: 创建时间

#### 2. Role 表
- **说明**：角色定义和权限表
- **角色类型**：
  - ADMIN: 系统管理员
  - USER: 普通用户
  - VIEWER: 只读用户
  - EXPORT_USER: 导出权限用户

#### 3. Company 表
- **说明**：公司组织结构表
- **用途**：支持多级公司架构
- **特点**：树形结构设计

#### 4. UserCompanyPermission 表
- **说明**：用户-公司权限映射表
- **用途**：实现细粒度的数据权限控制
- **设计**：多对多关系映射

## 金蝶数据库 (kingdee)

### 集成说明
- **访问模式**：只读访问
- **同步机制**：定时同步财务数据
- **用途**：获取ERP系统中的财务基础数据
- **注意事项**：
  - 不允许写入操作
  - 需要处理数据格式转换
  - 注意字符编码问题

## 数据库连接配置

### 主数据库配置
```yaml
datasource:
  primary:
    url: *****************************************************************************************
    username: root
    password: Zlb&198838
```

### 用户系统数据库配置
```yaml
datasource:
  user:
    url: *******************************************************************************
    username: root
    password: Zlb&198838
```

### 金蝶数据库配置
```yaml
datasource:
  kingdee:
    url: ***************************************************************************
    username: readonly_user
    password: [配置的只读密码]
```

## 数据库设计原则

1. **字符编码**：所有数据库统一使用 UTF-8 编码
2. **命名规范**：
   - 表名使用驼峰命名（Java风格）
   - 字段名使用下划线命名（数据库风格）
3. **索引策略**：
   - 主键自动创建聚簇索引
   - 频繁查询字段创建普通索引
   - 复合查询创建联合索引
4. **数据完整性**：
   - 使用外键约束保证引用完整性
   - 使用触发器维护数据一致性
   - 定期运行数据一致性检查

## 性能优化建议

1. **查询优化**：
   - 避免全表扫描
   - 合理使用索引
   - 控制JOIN表的数量

2. **连接池配置**：
   - 初始连接数：10
   - 最大连接数：50
   - 连接超时：30秒

3. **定期维护**：
   - 定期分析表统计信息
   - 清理历史数据
   - 优化表碎片

## 相关文档

- [数据库性能优化指南](/docs/database/performance-optimization.md)
- [数据库迁移指南](/docs/operations/database-migration.md)
- [多数据源配置说明](/docs/development/README.md#database-configuration)