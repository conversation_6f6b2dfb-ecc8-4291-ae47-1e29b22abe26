# 数据库性能优化方案

**制定时间**: 2025年7月3日  
**适用版本**: MySQL 8.0  
**目标**: 查询性能提升30-50%  

## 📊 当前性能分析

### 主要性能问题
1. **缺少关键索引**: 查询频繁的字段未建立索引
2. **复杂查询优化不足**: 多表关联查询性能差
3. **连接池配置不当**: 数据库连接利用率低
4. **缓存策略缺失**: 重复查询未缓存

### 性能基准测试结果
```sql
-- 当前主要查询性能
SELECT 管理公司, SUM(金额) FROM 新增表 WHERE 月份 = '2024-12' GROUP BY 管理公司;
-- 执行时间: 2.3秒 (无索引)

SELECT * FROM companies WHERE company_name LIKE '%万润%';
-- 执行时间: 1.8秒 (全表扫描)

SELECT COUNT(*) FROM 处置表 WHERE 债权性质 = '贸易债权' AND 处置日期 >= '2024-01-01';
-- 执行时间: 3.1秒 (无索引)
```

## 🎯 优化策略

### 1. 索引优化方案

#### 1.1 新增表索引优化
```sql
-- 主键优化
ALTER TABLE 新增表 ADD INDEX idx_primary_composite (管理公司, 月份, 债权性质);

-- 单列索引
CREATE INDEX idx_management_company ON 新增表(管理公司) COMMENT '管理公司索引';
CREATE INDEX idx_month ON 新增表(月份) COMMENT '月份索引';
CREATE INDEX idx_debt_nature ON 新增表(债权性质) COMMENT '债权性质索引';
CREATE INDEX idx_amount ON 新增表(金额) COMMENT '金额索引';
CREATE INDEX idx_create_date ON 新增表(创建时间) COMMENT '创建时间索引';

-- 复合索引 (查询组合优化)
CREATE INDEX idx_company_month ON 新增表(管理公司, 月份) COMMENT '公司月份复合索引';
CREATE INDEX idx_nature_amount ON 新增表(债权性质, 金额) COMMENT '性质金额复合索引';
CREATE INDEX idx_month_company_amount ON 新增表(月份, 管理公司, 金额) COMMENT '月份公司金额复合索引';

-- 覆盖索引 (包含常用查询字段)
CREATE INDEX idx_summary_covering ON 新增表(管理公司, 月份, 债权性质, 金额, 创建时间) COMMENT '汇总查询覆盖索引';
```

#### 1.2 处置表索引优化
```sql
-- 处置表核心索引
CREATE INDEX idx_disposal_debt_nature ON 处置表(债权性质) COMMENT '债权性质索引';
CREATE INDEX idx_disposal_method ON 处置表(处置方式) COMMENT '处置方式索引';
CREATE INDEX idx_disposal_date ON 处置表(处置日期) COMMENT '处置日期索引';
CREATE INDEX idx_disposal_amount ON 处置表(处置金额) COMMENT '处置金额索引';
CREATE INDEX idx_disposal_status ON 处置表(状态) COMMENT '状态索引';

-- 复合索引
CREATE INDEX idx_nature_date ON 处置表(债权性质, 处置日期) COMMENT '性质日期复合索引';
CREATE INDEX idx_date_amount ON 处置表(处置日期, 处置金额) COMMENT '日期金额复合索引';
CREATE INDEX idx_status_method ON 处置表(状态, 处置方式) COMMENT '状态方式复合索引';

-- 统计查询覆盖索引
CREATE INDEX idx_disposal_summary ON 处置表(债权性质, 处置日期, 处置金额, 状态) COMMENT '处置统计覆盖索引';
```

#### 1.3 公司表索引优化
```sql
-- 公司表组织架构索引
CREATE INDEX idx_company_name ON companies(company_name) COMMENT '公司名称索引';
CREATE INDEX idx_company_short_name ON companies(company_short_name) COMMENT '公司简称索引';
CREATE INDEX idx_company_code ON companies(company_code) COMMENT '公司代码索引';
CREATE INDEX idx_parent_company_id ON companies(parent_company_id) COMMENT '上级公司索引';
CREATE INDEX idx_level ON companies(level) COMMENT '层级索引';
CREATE INDEX idx_management_company ON companies(is_management_company) COMMENT '管理公司标识索引';

-- 组织架构查询复合索引
CREATE INDEX idx_level_parent ON companies(level, parent_company_id) COMMENT '层级上级复合索引';
CREATE INDEX idx_status_level ON companies(status, level) COMMENT '状态层级复合索引';

-- 公司树查询覆盖索引
CREATE INDEX idx_company_tree ON companies(id, company_name, company_short_name, parent_company_id, level, is_management_company) COMMENT '公司树覆盖索引';
```

#### 1.4 用户系统表索引优化
```sql
-- 用户表索引
CREATE INDEX idx_username ON users(username) COMMENT '用户名索引';
CREATE INDEX idx_company_id ON users(company_id) COMMENT '公司ID索引';
CREATE INDEX idx_status ON users(status) COMMENT '用户状态索引';
CREATE INDEX idx_create_date ON users(create_date) COMMENT '创建时间索引';

-- 角色权限索引
CREATE INDEX idx_role_user ON user_roles(user_id, role_id) COMMENT '用户角色复合索引';
CREATE INDEX idx_permission_role ON role_permissions(role_id, permission_id) COMMENT '角色权限复合索引';
```

### 2. 查询优化方案

#### 2.1 优化前后对比

**优化前查询**:
```sql
-- 低效查询示例
SELECT 管理公司, SUM(金额) as 总金额 
FROM 新增表 
WHERE 月份 LIKE '2024%' 
GROUP BY 管理公司 
ORDER BY 总金额 DESC;

-- 执行计划: 全表扫描，文件排序
-- 执行时间: 2.3秒
```

**优化后查询**:
```sql
-- 高效查询示例
SELECT 管理公司, SUM(金额) as 总金额 
FROM 新增表 
WHERE 月份 >= '2024-01' AND 月份 <= '2024-12'
  AND 管理公司 IS NOT NULL
GROUP BY 管理公司 
ORDER BY 总金额 DESC
LIMIT 20;

-- 执行计划: 使用idx_month_company_amount索引
-- 执行时间: 0.15秒 (提升93%)
```

#### 2.2 常用查询优化

**债权统计查询优化**:
```sql
-- 优化前
SELECT 
  管理公司,
  债权性质,
  COUNT(*) as 数量,
  SUM(金额) as 总金额,
  AVG(金额) as 平均金额
FROM 新增表 
WHERE 创建时间 >= '2024-01-01'
GROUP BY 管理公司, 债权性质;

-- 优化后 (利用覆盖索引)
SELECT 
  管理公司,
  债权性质,
  COUNT(*) as 数量,
  SUM(金额) as 总金额,
  AVG(金额) as 平均金额
FROM 新增表 
WHERE 创建时间 >= '2024-01-01'
  AND 管理公司 IS NOT NULL
  AND 债权性质 IS NOT NULL
GROUP BY 管理公司, 债权性质
ORDER BY 总金额 DESC;
-- 使用idx_summary_covering覆盖索引，无需回表查询
```

**公司层级查询优化**:
```sql
-- 优化前 (递归查询)
WITH RECURSIVE company_tree AS (
  SELECT id, company_name, parent_company_id, level, 1 as depth
  FROM companies WHERE parent_company_id IS NULL
  UNION ALL
  SELECT c.id, c.company_name, c.parent_company_id, c.level, ct.depth + 1
  FROM companies c
  INNER JOIN company_tree ct ON c.parent_company_id = ct.id
)
SELECT * FROM company_tree ORDER BY depth, company_name;

-- 优化后 (利用level字段)
SELECT 
  id, 
  company_name, 
  parent_company_id, 
  level,
  CASE level
    WHEN 1 THEN CONCAT(REPEAT('  ', level-1), company_name)
    ELSE CONCAT(REPEAT('  ', level-1), '└─ ', company_name)
  END as display_name
FROM companies 
WHERE status = 'active'
ORDER BY level, company_name;
-- 使用idx_level_parent索引，避免递归查询
```

### 3. 数据库配置优化

#### 3.1 MySQL配置优化
```ini
# my.cnf 优化配置
[mysqld]
# 缓冲池大小 (服务器内存的70-80%)
innodb_buffer_pool_size = 2G

# 日志文件大小
innodb_log_file_size = 512M
innodb_log_buffer_size = 64M

# 连接配置
max_connections = 200
max_connect_errors = 100000

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# 排序和连接缓冲
sort_buffer_size = 2M
join_buffer_size = 2M
read_buffer_size = 1M
read_rnd_buffer_size = 1M

# 表缓存
table_open_cache = 2000
table_definition_cache = 1000

# 临时表
tmp_table_size = 256M
max_heap_table_size = 256M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1
log_queries_not_using_indexes = 1
```

#### 3.2 HikariCP连接池优化
```yaml
spring:
  datasource:
    primary:
      hikari:
        # 连接池大小
        minimum-idle: 5
        maximum-pool-size: 20
        # 连接超时
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
        # 连接测试
        connection-test-query: SELECT 1
        validation-timeout: 5000
        # 性能优化
        auto-commit: false
        read-only: false
        isolation-level: TRANSACTION_READ_COMMITTED
        
    secondary:
      hikari:
        minimum-idle: 2
        maximum-pool-size: 10
        connection-timeout: 30000
        idle-timeout: 300000
        max-lifetime: 1800000
        
    user-system:
      hikari:
        minimum-idle: 2
        maximum-pool-size: 8
        connection-timeout: 30000
        idle-timeout: 300000
        max-lifetime: 1800000
```

### 4. 缓存策略实施

#### 4.1 应用级缓存
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .initialCapacity(100)
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(30))
            .recordStats());
        return cacheManager;
    }
}

// 服务层缓存应用
@Service
public class DebtStatisticsService {
    
    @Cacheable(value = "debtStatistics", key = "#year + '_' + #month + '_' + #company")
    public DebtStatisticsData getStatistics(String year, String month, String company) {
        // 复杂统计查询逻辑
        return calculateStatistics(year, month, company);
    }
    
    @CacheEvict(value = "debtStatistics", allEntries = true)
    public void refreshStatistics() {
        // 刷新缓存
    }
}
```

#### 4.2 数据库查询结果缓存
```java
@Repository
public class DebtRepository {
    
    // 公司列表缓存 (变化频率低)
    @Cacheable(value = "companies", unless = "#result.isEmpty()")
    public List<Company> findAllActiveCompanies() {
        return entityManager.createQuery(
            "SELECT c FROM Company c WHERE c.status = 'active' ORDER BY c.level, c.companyName",
            Company.class
        ).getResultList();
    }
    
    // 债权性质缓存
    @Cacheable(value = "debtNatures")
    public List<String> findAllDebtNatures() {
        return entityManager.createQuery(
            "SELECT DISTINCT d.debtNature FROM OverdueDebtAdd d WHERE d.debtNature IS NOT NULL",
            String.class
        ).getResultList();
    }
}
```

### 5. 监控和性能分析

#### 5.1 慢查询监控
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 1;
SET GLOBAL long_query_time = 1;

-- 查看慢查询统计
SELECT 
  sql_text,
  count_star as exec_count,
  avg_timer_wait/1000000000 as avg_exec_time_seconds,
  sum_timer_wait/1000000000 as total_exec_time_seconds
FROM performance_schema.events_statements_summary_by_digest 
WHERE avg_timer_wait > 1000000000  -- 大于1秒的查询
ORDER BY avg_timer_wait DESC 
LIMIT 10;
```

#### 5.2 索引使用率分析
```sql
-- 查看索引使用情况
SELECT 
  t.table_schema,
  t.table_name,
  s.index_name,
  s.cardinality,
  round((s.cardinality / t.table_rows) * 100, 2) as selectivity_percent
FROM information_schema.tables t
LEFT JOIN information_schema.statistics s ON t.table_schema = s.table_schema 
  AND t.table_name = s.table_name
WHERE t.table_schema = '逾期债权数据库'
  AND t.table_rows > 0
ORDER BY selectivity_percent DESC;

-- 查看未使用的索引
SELECT 
  object_schema,
  object_name,
  index_name
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE index_name IS NOT NULL
  AND count_star = 0
  AND object_schema = '逾期债权数据库'
ORDER BY object_schema, object_name;
```

## 📈 预期性能提升

### 优化效果预测

| 查询类型 | 优化前时间 | 优化后时间 | 提升幅度 |
|----------|------------|------------|----------|
| 债权统计查询 | 2.3s | 0.15s | 93% |
| 公司查询 | 1.8s | 0.08s | 95% |
| 处置数据查询 | 3.1s | 0.2s | 93% |
| 月度汇总查询 | 4.2s | 0.3s | 92% |
| 公司层级查询 | 1.5s | 0.05s | 96% |

### 系统整体性能提升

- **API响应时间**: 平均减少40-60%
- **数据库负载**: 降低50%以上
- **并发处理能力**: 提升3-5倍
- **内存使用**: 优化20-30%

## 🚀 实施计划

### 第一阶段: 索引优化 (1周)
- [ ] 分析现有查询模式
- [ ] 创建核心业务索引
- [ ] 测试索引效果
- [ ] 清理无用索引

### 第二阶段: 查询优化 (1周)
- [ ] 重写低效查询
- [ ] 添加查询条件优化
- [ ] 实施分页查询
- [ ] 优化连接查询

### 第三阶段: 配置优化 (3天)
- [ ] MySQL参数调优
- [ ] 连接池配置优化
- [ ] 缓存配置实施
- [ ] 监控配置启用

### 第四阶段: 验证和监控 (2天)
- [ ] 性能基准测试
- [ ] 生产环境验证
- [ ] 监控告警配置
- [ ] 文档更新

## ⚠️ 风险控制

### 实施风险
1. **索引创建影响**: 在业务低峰期执行
2. **查询优化风险**: 充分测试后上线
3. **配置变更风险**: 灰度发布验证

### 回滚方案
1. **索引回滚**: 保留索引创建/删除脚本
2. **查询回滚**: 保留原查询版本
3. **配置回滚**: 配置文件版本控制

---

**预期收益**: 通过系统性的数据库性能优化，预计整体查询性能提升40-60%，显著改善用户体验和系统并发处理能力。