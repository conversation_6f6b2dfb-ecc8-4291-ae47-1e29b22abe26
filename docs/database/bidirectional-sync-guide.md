# MySQL双向同步完整实施指南

## 📋 概述

本文档提供了在FinancialSystem项目中实施MySQL双向同步的完整指南。该解决方案基于MySQL Master-Master复制架构，确保本地MySQL和Linux Docker MySQL之间的三个核心数据库（overdue_debt_db、kingdee、user_system）实现实时双向同步。

## 🏗️ 架构设计

### Master-Master复制架构

```
┌─────────────────┐     双向复制     ┌─────────────────┐
│   本地MySQL     │ ←──────────────→ │  Linux MySQL    │
│   (主服务器1)   │                   │  (主服务器2)    │
│   Server ID: 1  │                   │  Server ID: 2   │
└─────────────────┘                   └─────────────────┘
        │                                     │
        ├─ overdue_debt_db                    ├─ overdue_debt_db
        ├─ kingdee                            ├─ kingdee
        └─ user_system                        └─ user_system
```

### 核心特性

- **双向同步**: 任一端的数据变更都会自动同步到另一端
- **冲突避免**: 通过不同的AUTO_INCREMENT步长避免主键冲突
- **实时性**: 基于binlog的实时数据传输
- **高可用**: 任一端故障不影响另一端的正常运行

## 🚀 快速实施

### 前提条件

1. **MySQL版本**: 确保两端都是MySQL 8.0或更高版本
2. **网络连通性**: 确保两台服务器之间网络可达
3. **权限**: 具备两端MySQL的管理员权限
4. **备份**: 实施前完整备份所有数据

### 一键配置脚本

```bash
# 进入项目目录
cd /Volumes/ExternalSSD-1T/08.program/FinancialSystem

# 给脚本添加执行权限
chmod +x scripts/database/setup-bidirectional-sync.sh

# 执行配置
bash scripts/database/setup-bidirectional-sync.sh
```

### 手动配置步骤

如果自动脚本遇到问题，可以按以下步骤手动配置：

#### 步骤1: 配置本地MySQL

编辑本地MySQL配置文件（通常是 `/etc/mysql/my.cnf` 或 `/usr/local/etc/my.cnf`）：

```ini
[mysqld]
# 服务器标识
server-id = 1

# 开启二进制日志
log-bin = mysql-bin
binlog-format = ROW

# GTID模式
gtid-mode = ON
enforce-gtid-consistency = ON

# 指定要同步的数据库
binlog-do-db = overdue_debt_db
binlog-do-db = kingdee
binlog-do-db = user_system

# 避免主键冲突
auto-increment-increment = 2
auto-increment-offset = 1

# 性能优化
max-connections = 1000
innodb_buffer_pool_size = 1G
```

重启本地MySQL服务：
```bash
# macOS (使用brew安装)
brew services restart mysql

# 或使用系统服务
sudo systemctl restart mysql
```

#### 步骤2: 配置Linux MySQL容器

在Linux服务器上创建MySQL配置文件：

```bash
# 连接到Linux服务器
ssh admin@**********

# 创建MySQL配置文件
cat > /tmp/mysql-replication.cnf << 'EOF'
[mysqld]
server-id = 2
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON
binlog-do-db = overdue_debt_db
binlog-do-db = kingdee
binlog-do-db = user_system
auto-increment-increment = 2
auto-increment-offset = 2
max-connections = 1000
EOF

# 复制配置到容器并重启
docker cp /tmp/mysql-replication.cnf financial-mysql:/etc/mysql/conf.d/
docker restart financial-mysql

# 等待容器重启完成
sleep 20
```

#### 步骤3: 创建复制用户

在两端分别创建复制用户：

```sql
-- 在本地MySQL执行
CREATE USER 'repl_user'@'%' IDENTIFIED BY 'Repl#2025@Sync';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
GRANT SELECT ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;
```

```bash
# 在Linux MySQL执行
ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e \"
CREATE USER 'repl_user'@'%' IDENTIFIED BY 'Repl#2025@Sync';
GRANT REPLICATION SLAVE ON *.* TO 'repl_user'@'%';
GRANT SELECT ON *.* TO 'repl_user'@'%';
FLUSH PRIVILEGES;
\""
```

#### 步骤4: 配置主从关系

获取主服务器状态并配置从服务器：

```bash
# 获取Linux主服务器状态
ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e 'SHOW MASTER STATUS;'"

# 根据输出配置本地为从服务器
mysql -hlocalhost -uroot -p'Zlb&198838' --skip-ssl -e "
STOP SLAVE;
RESET SLAVE ALL;
CHANGE MASTER TO
    MASTER_HOST='**********',
    MASTER_PORT=3306,
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Repl#2025@Sync',
    MASTER_LOG_FILE='mysql-bin.000001',  -- 替换为实际值
    MASTER_LOG_POS=154;                  -- 替换为实际值
START SLAVE;
"
```

反向配置Linux为从服务器：

```bash
# 获取本地主服务器状态
mysql -hlocalhost -uroot -p'Zlb&198838' --skip-ssl -e "SHOW MASTER STATUS;"

# 配置Linux为从服务器
ssh admin@********** "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e \"
STOP SLAVE;
RESET SLAVE ALL;
CHANGE MASTER TO
    MASTER_HOST='host.docker.internal',
    MASTER_PORT=3306,
    MASTER_USER='repl_user',
    MASTER_PASSWORD='Repl#2025@Sync',
    MASTER_LOG_FILE='mysql-bin.000001',  -- 替换为实际值
    MASTER_LOG_POS=154;                  -- 替换为实际值
START SLAVE;
\""
```

## 📊 监控和维护

### 使用监控脚本

项目提供了专门的监控脚本：

```bash
# 给监控脚本添加执行权限
chmod +x scripts/database/sync-monitor.sh

# 检查同步状态
bash scripts/database/sync-monitor.sh status

# 实时监控
bash scripts/database/sync-monitor.sh monitor

# 生成详细报告
bash scripts/database/sync-monitor.sh report

# 性能分析
bash scripts/database/sync-monitor.sh perf

# 修复复制问题
bash scripts/database/sync-monitor.sh repair
```

### 手动监控命令

#### 检查复制状态

```sql
-- 检查从服务器状态
SHOW SLAVE STATUS\G

-- 关键字段说明：
-- Slave_IO_Running: Yes (IO线程正常)
-- Slave_SQL_Running: Yes (SQL线程正常)
-- Seconds_Behind_Master: 0 (延迟秒数)
-- Last_Error: (错误信息)
```

#### 检查主服务器状态

```sql
-- 检查主服务器状态
SHOW MASTER STATUS;

-- 检查二进制日志
SHOW BINARY LOGS;

-- 检查连接的从服务器
SHOW PROCESSLIST;
```

#### 检查数据一致性

```sql
-- 比较记录数量
SELECT COUNT(*) FROM overdue_debt_db.新增表;
SELECT COUNT(*) FROM kingdee.table_name;
SELECT COUNT(*) FROM user_system.users;
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 复制线程停止

**症状**: `Slave_IO_Running` 或 `Slave_SQL_Running` 为 No

**解决方案**:
```sql
-- 重启复制
STOP SLAVE;
START SLAVE;

-- 跳过错误（谨慎使用）
SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1;
START SLAVE;
```

#### 2. 主键冲突

**症状**: 出现 "Duplicate entry" 错误

**原因**: AUTO_INCREMENT配置不正确

**解决方案**:
```sql
-- 检查当前设置
SHOW VARIABLES LIKE 'auto_increment%';

-- 重新设置（本地）
SET GLOBAL auto_increment_increment = 2;
SET GLOBAL auto_increment_offset = 1;

-- 重新设置（Linux）
SET GLOBAL auto_increment_increment = 2;
SET GLOBAL auto_increment_offset = 2;
```

#### 3. 网络连接问题

**症状**: "Lost connection to MySQL server"

**解决方案**:
```sql
-- 检查网络连接
SHOW PROCESSLIST;

-- 调整超时设置
SET GLOBAL slave_net_timeout = 60;
SET GLOBAL wait_timeout = 28800;
```

#### 4. GTID一致性问题

**症状**: "Statement violates GTID consistency"

**解决方案**:
```sql
-- 检查GTID状态
SHOW VARIABLES LIKE 'gtid%';

-- 重置GTID（谨慎操作）
RESET MASTER;
RESET SLAVE ALL;
```

### 紧急修复流程

如果双向同步完全中断：

1. **停止应用程序写入**
2. **备份当前数据**
3. **重置复制配置**
4. **重新建立主从关系**
5. **验证数据一致性**
6. **恢复应用程序**

```bash
# 使用修复脚本
bash scripts/database/sync-monitor.sh repair both

# 或重新运行配置脚本
bash scripts/database/setup-bidirectional-sync.sh
```

## 📈 性能优化

### 复制性能调优

```sql
-- 启用并行复制
SET GLOBAL slave_parallel_workers = 4;
SET GLOBAL slave_parallel_type = 'LOGICAL_CLOCK';

-- 优化二进制日志
SET GLOBAL sync_binlog = 1;
SET GLOBAL innodb_flush_log_at_trx_commit = 1;

-- 调整缓冲区
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
```

### 监控指标

关键性能指标：
- **复制延迟**: Seconds_Behind_Master < 5秒
- **IO/SQL线程状态**: 必须为 Yes
- **错误率**: Last_Error 应为空
- **网络延迟**: < 50ms

## 🔒 安全考虑

### 1. 复制用户权限最小化

```sql
-- 只授予必要权限
GRANT REPLICATION SLAVE, SELECT ON *.* TO 'repl_user'@'%';
```

### 2. SSL加密连接

```sql
-- 配置SSL复制
CHANGE MASTER TO
    MASTER_SSL = 1,
    MASTER_SSL_CA = '/path/to/ca.pem',
    MASTER_SSL_CERT = '/path/to/client-cert.pem',
    MASTER_SSL_KEY = '/path/to/client-key.pem';
```

### 3. 密码定期更换

```sql
-- 更新复制用户密码
ALTER USER 'repl_user'@'%' IDENTIFIED BY 'NewPassword';
FLUSH PRIVILEGES;

-- 更新主从配置
CHANGE MASTER TO MASTER_PASSWORD = 'NewPassword';
```

## 📝 维护计划

### 日常维护

- **每日**: 检查复制状态和延迟
- **每周**: 验证数据一致性
- **每月**: 生成性能报告
- **每季度**: 优化配置参数

### 监控告警

设置以下告警条件：
- 复制延迟 > 30秒
- 复制线程停止
- 错误日志出现ERROR级别消息
- 数据不一致检测

### 备份策略

- **全量备份**: 每日凌晨
- **增量备份**: 每小时
- **二进制日志备份**: 实时
- **配置文件备份**: 变更时

## 🎯 最佳实践

1. **避免直接操作从库**: 所有写操作应通过主库进行
2. **监控复制延迟**: 设置合理的告警阈值
3. **定期验证数据**: 使用checksum验证数据一致性
4. **备份策略**: 保持完整的备份和恢复计划
5. **文档更新**: 及时更新配置变更文档

## 📞 支持与联系

如遇到技术问题，请按以下顺序寻求支持：

1. 查看本文档的故障排除章节
2. 运行监控脚本诊断问题
3. 查看MySQL错误日志
4. 联系系统管理员

---

*MySQL双向同步实施指南 | FinancialSystem项目 | 版本 v1.0*