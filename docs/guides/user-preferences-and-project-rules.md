# 用户偏好与项目规则详细记录

本文档详细记录了用户Zhou Libing在项目开发过程中表达的偏好、制定的规则和重要决策，为团队协作和AI助手提供参考。

## 👤 用户基本信息

- **姓名**: Zhou Libing
- **角色**: 项目负责人/架构师
- **偏好语言**: 中文
- **技术背景**: Java企业级开发、Spring Boot、数据库设计

## 🗣️ 沟通与协作偏好

### 语言偏好
- **主要语言**: 中文沟通，所有回复应使用中文
- **文档语言**: 技术文档可中英文混合，但说明部分偏好中文

### 问题解决偏好
- **AI优先**: 偏好使用AI助手解决问题，而非Cursor IDE
- **全面方案**: 期望comprehensive的解决方案，展示AI的优越性
- **详细文档**: 解决问题后需要更新comprehensive文档

### 决策参与
- **方案确认**: 重要技术决策需要用户确认后执行
- **风险评估**: 高风险操作需要详细风险分析和用户授权
- **渐进实施**: 偏好分阶段实施，每阶段验证后再继续

## 🏗️ 架构与设计偏好

### 重构策略
- **渐进式重构**: 偏好方案A（简化配置方法），避免激进变更
- **版本控制**: 所有重构操作必须有版本控制支持，便于回滚
- **测试验证**: 每次重构后必须测试后端启动，确保服务正常运行

### 代码质量要求
- **删除冗余**: 偏好删除注释/过时代码，不保留冗余代码
- **重复分析**: 要求识别和重构重复功能，需要分析和批准
- **一致性检查**: 重构前需要comprehensive数据层优化分析

### 模块组织原则
- **共享实体**: 实体类放在common模块，供所有模块共享
- **独立仓库**: 每个模块使用独立仓库
- **职责分离**: 模块职责明确，避免功能重叠

## 🗄️ 数据库管理偏好

### 数据库配置
- **主数据库**: 逾期债权数据库（中文名称）
- **多数据源**: 支持kingdee等其他数据库，但需明确文档
- **默认策略**: 默认使用逾期债权数据库，其他数据库需明确说明

### 数据一致性要求
- **严格验证**: 数据层优化前后查询结果必须完全一致
- **详细记录**: 优化过程需要详细记录所有查询函数
- **测试覆盖**: 每个查询必须测试验证，避免数据差异

### 备份策略
- **不要自动备份**: 不需要系统自动数据库备份功能
- **手动控制**: 偏好手动控制备份时机和策略

## 🚀 部署与运维偏好

### 服务器管理
- **服务器信息**: Linux服务器 admin@10.25.1.85 (密码: Wrkj2520.)
- **权限策略**: 偏好使用root登录，避免权限问题
- **Java版本**: 项目使用Java 21，不是Java 17

### 自动化部署
- **触发机制**: 合并分支到main时自动触发部署
- **部署流程**: 自动代码备份 → 本地备份 → 自动部署
- **容器化**: 使用Docker部署，服务器已安装Docker

### 分支管理
- **命名规范**: 偏好基于日期的分支命名
- **提交策略**: 代码提交到分支，完成后合并到main

## 🔧 开发工作流偏好

### 错误处理策略
- **删除优于实现**: 遇到编译错误时，偏好删除未使用的方法调用
- **不实现缺失**: 不偏好实现缺失的方法，而是删除调用

### 定时任务管理
- **保持活跃**: 定时任务代码应保持活跃，不要注释掉
- **功能影响**: 禁用定时任务会导致前端功能丢失
- **数据过滤**: 需要从减值准备表过滤月初余额、新增、处置、月末余额字段

### 服务组织偏好
- **debt-management**: NonLitigationUpdateService、DataConsistencyCheckService
- **account-management**: CustomUserDetailService等账户相关服务
- **导出服务**: 根据导出数据类型决定模块位置

## 📁 文档管理偏好

### 文档组织
- **有用文档**: 移动到docs/文件夹分类管理
- **临时文档**: 删除临时/重构笔记
- **CI/CD脚本**: 按行业惯例组织，不散落在根目录

### 文档更新要求
- **同步更新**: 重构操作后必须更新comprehensive文档
- **分析记录**: 偏好在markdown文件中记录分析方法
- **问题追踪**: 调试问题时从前端到后端追踪现有接口

## 🎯 具体技术决策

### 模块位置决策
- **User/Role**: 位于主数据库（逾期债权数据库），不在独立数据库
- **MapConvertUtil**: 已存在的工具类，应定位使用而非创建新的
- **导出功能**: 已在ExcelExportOverdueDebt.java实现

### 架构演进记录
- **data-processing**: 从mysql_data重命名而来，保留所有代码
- **webservice**: 已被api-gateway替代
- **audit目录**: 根目录的audit目录可删除

### 配置管理
- **autoCommit**: 使用方案A解决事务问题，临时禁用定时任务
- **定时任务**: 逐步恢复定时任务，避免前端功能丢失

## 🚨 风险控制要求

### 高风险操作
用户要求以下操作必须获得明确授权：
- 提交或推送代码
- 更改工单状态  
- 合并分支
- 安装依赖
- 部署代码

### 数据安全
- **第三阶段风险**: 对数据层优化第三阶段风险有担忧
- **查询一致性**: 优化后查询结果必须与优化前完全一致
- **详细记录**: 所有查询函数必须详细记录

### 系统稳定性
- **启动验证**: 每次重构后必须验证系统能正常启动
- **功能测试**: 确保核心功能不受影响
- **回滚准备**: 重要变更需要准备回滚方案

## 📋 工作习惯总结

### 偏好的工作方式
1. **分析先行**: 先分析问题，制定详细计划
2. **渐进实施**: 分步骤实施，每步验证
3. **文档同步**: 代码变更后及时更新文档
4. **测试验证**: 重视测试，确保质量

### 不偏好的做法
1. **激进重构**: 避免大规模一次性重构
2. **保留冗余**: 不保留注释掉的过时代码
3. **自动备份**: 不需要系统自动数据库备份
4. **权限问题**: 避免因权限问题影响开发效率

## 📝 更新记录

- **2025-06-23**: 初始版本，整理用户偏好和项目规则
- **维护者**: AI Assistant (Augment Agent)
- **来源**: 用户在Windsurf对话中的表达和决策

---

**注意**: 本文档应定期更新，反映用户偏好的变化和新的项目决策。
