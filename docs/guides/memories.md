# 项目知识记忆库（memories.md）

本文件用于归档和整理用户在Windsurf对话中要求AI长期记忆的重要项目规则、架构说明、技术栈、业务约定、操作偏好、历史决策等内容，便于团队查阅和知识传承。内容将持续维护和扩展。

---

## 1. 项目架构与技术栈

- **项目名称**：FinancialSystem（企业级财务/债务管理系统）
- **架构**：前后端分离，多模块Maven项目
- **后端**：Java 21，Spring Boot 3.1.2，Spring Security 6.x（JWT），Spring Data JPA 3.x，Hibernate 6.x
- **数据库**：MySQL 8.0，多数据源（主业务库、金蝶库、独立用户库）
- **前端**：React 18.x，Material-UI 5.x/6.x，Axios
- **模块划分**：api-gateway、services、common、data-access、kingdee、report等

## 2. 业务领域与核心功能

- 债权管理（新增、处置、诉讼、非诉讼）
- 数据一致性检查与保障
- 财务报表生成与分析
- 与金蝶ERP系统集成
- 自动化数据备份（每日定时备份，保留7天）
- 用户系统独立认证与权限

## 3. 代码注释与清理规则

- 对所有核心业务Service、Controller、Entity、Config等文件进行详细注释，确保业务逻辑、参数、异常、数据库映射等说明清晰
- 发现完全注释掉且无用的代码文件（如ImpairmentReserveController.java、OverdueDebtCheckTime.java）可安全删除
- 注释掉的依赖注入代码如无误导风险可保留，作为后续扩展提示
- 删除无用代码必须极为谨慎，只有非常确定才执行
- 自动推进代码检查流程，遇到关键结构性问题或疑似无用但不确定的代码时再征询用户

## 4. 用户操作偏好与特殊要求

- **所有对话永久使用中文回复**（无论用户用何种语言提问）
- 自动推进，无需常规请示，只有遇到关键节点再征询
- 发现无解问题三次需生成详细Markdown文档记录

## 5. 重要历史决策与问题记录

- 业务逻辑逐步从Service层迁移至common模块工具类，支持渐进式重构
- JWT生成时需包含公司、部门、姓名等信息，采用数据库直接查询User表方式实现
- 发现OverdueDebtDecrease实体字段与“处置表”表结构不一致（debtAmount字段无对应列），导致SQL报错，需后续修正
- 自动化非诉跟踪记录、处置跟踪记录的创建与更新机制已实现
- 数据库密码硬编码风险已被发现，建议后续采用环境变量或密钥管理
- 前端表格UI风格优化（字体13px、行高40px、紧凑美观、hover效果等）已完成

## 6. 其他用户主动要求记忆的内容

- 项目所有自定义md文档统一集中存放于`docs`目录
- 备份脚本路径：`/Users/<USER>/backup_financial_system.sh`，备份目录：`~/Backups`，保留7天
- 代码清理报告、业务说明、问题归档、会议纪要等均建议放在docs中，便于团队协作

---

> 本文档为AI自动整理，后续如有新增重要规则、决策或操作偏好，请及时补充或通知AI更新。
