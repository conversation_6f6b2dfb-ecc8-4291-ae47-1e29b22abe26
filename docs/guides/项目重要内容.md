# 项目重要内容总览

本文件聚合了理解FinancialSystem项目不可或缺的核心信息，便于新成员快速上手、团队统一认知、后续维护与决策。

---

## 一、项目定位与核心价值

- 企业级财务信息化系统，专注于逾期债权管理、风险控制、数据一致性与分析。
- 主要服务对象：金融机构、企业财务部门、债权管理公司。
- 业务目标：提升债权管理效率、降低财务风险、优化资金配置、保障数据准确性。

---

## 二、架构与技术栈

- **后端**：Java 21，Spring Boot 3.1.2，Spring Security 6.x（JWT），Spring Data JPA 3.x，Hibernate 6.x
- **数据库**：MySQL 8.0，三大独立数据源（主业务库/逾期债权、用户系统、金蝶ERP）
- **前端**：React 18.x，Material-UI 5.x/6.x，Axios
- **架构分层**：api-gateway（服务层）、services（业务模块）、common（工具与实体）、data-access（数据访问）、kingdee、report、treasury、FinancialSystem-web（前端）
- **多模块Maven项目**，支持云原生和容器化部署（Docker）

---

## 三、核心业务与功能

- 全流程债权管理（新增、处置、诉讼、非诉讼、减值准备）
- 数据一致性自动检查与修复
- 智能财务报表与实时数据监控
- 用户系统独立认证、权限与审计
- 与金蝶ERP等外部系统集成
- 自动化定时数据备份与回滚能力

---

## 四、重构与安全保障

- 业务逻辑工具类重构，Service层与原有Service并存，支持渐进式迁移
- 角色权限、用户认证、数据隔离全面升级
- 生产环境风险评估与回滚机制完善，定期备份，支持一键恢复
- 测试覆盖率低于5%，建议补充核心功能/Repository/API测试

---

## 五、数据库与实体设计

- 逾期债权主库五大核心表：新增、处置、诉讼、非诉讼、减值准备
- 用户系统库：users、roles、audit_logs
- 金蝶库：ERP集成数据
- 实体类与表结构一一对应，字段注释完善，部分历史冗余实体已清理

---

## 六、部署与运维要点

- 支持传统部署、Docker容器化、云原生部署
- 推荐配置：4核8G、100G SSD、Linux（Ubuntu 22.04 LTS）
- 生产环境架构：前端Nginx反代、Spring Boot后端集群、MySQL集群
- 健康检查与监控接口：/actuator/health、/api/user-system/health
- 备份脚本：`/Users/<USER>/backup_financial_system.sh`，保留7天

---

## 七、团队协作与文档规范

- 所有自定义md文档统一存放于docs目录，便于归档与检索
- 重要历史决策、业务规则、代码清理报告均需归档
- 所有AI/团队对话永久使用中文

---

## 八、风险与注意事项

- 生产环境直接重构风险极高，建议先补充测试
- 多数据源配置需确保包扫描、事务隔离、连接池独立
- 部分历史测试类已失效，建议逐步补全
- 发现实体与表结构不一致需及时修正（如debtAmount字段问题）
- 数据库密码硬编码风险，建议采用环境变量或密钥管理

---

> 本文档为AI自动聚合整理，后续如有新增重要内容，请及时补充或通知AI更新。
