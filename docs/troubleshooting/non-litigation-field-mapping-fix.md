# 非诉讼表字段映射错误修复报告

## 问题描述
定时任务执行时报错：
```
SQLSyntaxErrorException: Unknown column 'n1_0.本月末余额' in 'field list'
```

## 问题分析

### 1. 错误原因
- NonLitigationClaim实体类中定义了 `@Column(name = "本月末余额")` 字段
- 但数据库非诉讼表中实际不存在"本月末余额"这个字段
- 数据库中只有"本月末本金"、"本月末利息"、"本月末违约金"三个字段

### 2. 数据库表结构确认
通过查询数据库确认，非诉讼表中确实没有"本月末余额"字段：
```sql
SHOW COLUMNS FROM 非诉讼表 LIKE '%本月末%';
```
结果只有：
- 本月末本金
- 本月末利息  
- 本月末违约金

## 解决方案

### 1. 修改实体类映射
将"本月末余额"字段标记为 `@Transient`，表示这是一个计算字段，不在数据库中存储：

```java
// 本月末余额是计算字段，不在数据库中存储
@jakarta.persistence.Transient
private BigDecimal currentMonthBalance;
```

### 2. 添加计算方法
添加方法来计算本月末余额：

```java
/**
 * 计算本月末余额
 * 本月末余额 = 本月末本金 + 本月末利息 + 本月末违约金
 */
@PrePersist
@PreUpdate
public void calculateCurrentMonthBalance() {
    BigDecimal principal = currentMonthPrincipal != null ? currentMonthPrincipal : BigDecimal.ZERO;
    BigDecimal interest = currentMonthInterest != null ? currentMonthInterest : BigDecimal.ZERO;
    BigDecimal penalty = currentMonthPenalty != null ? currentMonthPenalty : BigDecimal.ZERO;
    this.currentMonthBalance = principal.add(interest).add(penalty);
}

/**
 * 获取本月末余额
 * 如果currentMonthBalance为null，则重新计算
 */
public BigDecimal getCurrentMonthBalance() {
    if (currentMonthBalance == null) {
        calculateCurrentMonthBalance();
    }
    return currentMonthBalance;
}
```

## 修改的文件
- `/shared/common/src/main/java/com/laoshu198838/entity/overdue_debt/NonLitigationClaim.java`

## 影响范围
- 定时任务 NonLitigationOverdueUpdateService 将能正常执行
- 所有使用 NonLitigationClaim 实体的查询将不再包含"本月末余额"字段
- 需要获取本月末余额时，使用 `getCurrentMonthBalance()` 方法

## 测试建议
1. 重启应用程序
2. 观察定时任务是否正常执行
3. 验证本月末余额的计算是否正确

## 后续建议
1. 检查其他实体类是否存在类似的字段映射问题
2. 考虑在数据库设计文档中明确标注哪些是计算字段
3. 在实体类中使用 `@Formula` 注解来定义计算字段（如果需要在查询中使用）