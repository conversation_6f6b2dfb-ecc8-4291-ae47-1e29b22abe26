# OA系统技术实施文档

## 文档信息
- **创建时间**: 2025-07-03
- **文档类型**: 技术实施指南
- **适用系统**: FinancialSystem + OA系统集成
- **技术栈**: Spring Boot + React + 泛微E9 OA

## 核心技术信息

### 1. 许可证注册认证技术细节

#### 认证流程
```
客户端 → 发送许可证信息 → OA系统认证服务 → 返回access_token → 客户端缓存token
```

#### 关键代码片段
```javascript
// 前端Token管理
const TokenManager = {
  getToken: function() {
    var access_token = me.token ? me.token.access_token : "";
    if(!access_token) access_token = getQueryString("userKey") || "";
    return access_token;
  },
  
  isTokenValid: function(token) {
    return token && token.length > 0;
  },
  
  refreshToken: function() {
    // 重新获取token的逻辑
  }
};
```

#### 后端认证服务
```java
@Service
public class OAAuthenticationService {
    
    private static final String OA_BASE_URL = "https://e-cloudstore.com";
    private static final String AUTH_ENDPOINT = "/api/auth/register";
    
    @Value("${oa.license.key}")
    private String licenseKey;
    
    @Value("${oa.license.secret}")
    private String licenseSecret;
    
    public String registerLicense() {
        // 注意：只需注册一次，避免重复注册导致密钥更新
        if (isAlreadyRegistered()) {
            return getCachedToken();
        }
        
        LicenseRegistrationRequest request = new LicenseRegistrationRequest();
        request.setLicenseKey(licenseKey);
        request.setLicenseSecret(licenseSecret);
        
        // 发送注册请求
        ResponseEntity<LicenseRegistrationResponse> response = 
            restTemplate.postForEntity(OA_BASE_URL + AUTH_ENDPOINT, request, LicenseRegistrationResponse.class);
        
        if (response.getStatusCode() == HttpStatus.OK) {
            cacheToken(response.getBody().getAccessToken());
            return response.getBody().getAccessToken();
        }
        
        throw new OAAuthenticationException("许可证注册失败");
    }
    
    private boolean isAlreadyRegistered() {
        // 检查是否已经注册过的逻辑
        return redisTemplate.hasKey("oa:license:registered");
    }
    
    private String getCachedToken() {
        return redisTemplate.opsForValue().get("oa:access:token");
    }
    
    private void cacheToken(String token) {
        redisTemplate.opsForValue().set("oa:access:token", token, Duration.ofHours(24));
        redisTemplate.opsForValue().set("oa:license:registered", "true", Duration.ofDays(365));
    }
}
```

### 2. 前端数据提取实现

#### React组件示例
```javascript
// OA数据提取组件
import React, { useState, useEffect } from 'react';
import { oaApiService } from '../services/oaApiService';

const OADataExtractor = () => {
  const [oaData, setOaData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    fetchOAData();
  }, []);
  
  const fetchOAData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = TokenManager.getToken();
      if (!TokenManager.isTokenValid(token)) {
        throw new Error('OA系统认证失败，请重新登录');
      }
      
      const data = await oaApiService.extractBusinessData(token);
      setOaData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div>
      <h2>OA系统数据提取</h2>
      {loading && <p>正在获取数据...</p>}
      {error && <p style={{color: 'red'}}>错误: {error}</p>}
      {oaData.length > 0 && (
        <table>
          <thead>
            <tr>
              <th>单据ID</th>
              <th>标题</th>
              <th>状态</th>
              <th>创建时间</th>
            </tr>
          </thead>
          <tbody>
            {oaData.map(item => (
              <tr key={item.id}>
                <td>{item.docId}</td>
                <td>{item.title}</td>
                <td>{item.status}</td>
                <td>{item.createTime}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default OADataExtractor;
```

#### API服务封装
```javascript
// oaApiService.js
class OAApiService {
  constructor() {
    this.baseURL = 'https://e-cloudstore.com/api';
    this.timeout = 30000;
  }
  
  async extractBusinessData(token) {
    const response = await fetch(`${this.baseURL}/business/documents`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: this.timeout
    });
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }
    
    return await response.json();
  }
  
  async extractUserData(token) {
    const response = await fetch(`${this.baseURL}/users`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`用户数据获取失败: ${response.status}`);
    }
    
    return await response.json();
  }
  
  async extractDepartmentData(token) {
    const response = await fetch(`${this.baseURL}/departments`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`部门数据获取失败: ${response.status}`);
    }
    
    return await response.json();
  }
}

export const oaApiService = new OAApiService();
```

### 3. 后端数据提取实现

#### 数据提取控制器
```java
@RestController
@RequestMapping("/api/oa/extract")
@CrossOrigin
public class OADataExtractionController {
    
    @Autowired
    private OADataExtractionService oaDataExtractionService;
    
    @Autowired
    private OAAuthenticationService oaAuthService;
    
    @GetMapping("/business-data")
    public ResponseEntity<List<OABusinessData>> extractBusinessData() {
        try {
            String token = oaAuthService.getValidToken();
            List<OABusinessData> data = oaDataExtractionService.extractBusinessData(token);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Collections.emptyList());
        }
    }
    
    @GetMapping("/user-data")
    public ResponseEntity<List<OAUserData>> extractUserData() {
        try {
            String token = oaAuthService.getValidToken();
            List<OAUserData> data = oaDataExtractionService.extractUserData(token);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Collections.emptyList());
        }
    }
    
    @PostMapping("/sync-data")
    public ResponseEntity<SyncResult> syncDataWithOA(@RequestBody SyncRequest request) {
        try {
            String token = oaAuthService.getValidToken();
            SyncResult result = oaDataExtractionService.syncData(token, request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new SyncResult(false, e.getMessage()));
        }
    }
}
```

#### 数据提取服务
```java
@Service
public class OADataExtractionService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${oa.api.base-url}")
    private String oaBaseUrl;
    
    public List<OABusinessData> extractBusinessData(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<OABusinessData[]> response = restTemplate.exchange(
            oaBaseUrl + "/business/documents",
            HttpMethod.GET,
            entity,
            OABusinessData[].class
        );
        
        return Arrays.asList(response.getBody());
    }
    
    public List<OAUserData> extractUserData(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<OAUserData[]> response = restTemplate.exchange(
            oaBaseUrl + "/users",
            HttpMethod.GET,
            entity,
            OAUserData[].class
        );
        
        return Arrays.asList(response.getBody());
    }
    
    public SyncResult syncData(String token, SyncRequest request) {
        // 实现数据同步逻辑
        try {
            // 1. 提取OA数据
            List<OABusinessData> oaData = extractBusinessData(token);
            
            // 2. 转换数据格式
            List<BusinessData> convertedData = convertOADataToBusinessData(oaData);
            
            // 3. 保存到本地数据库
            businessDataRepository.saveAll(convertedData);
            
            return new SyncResult(true, "数据同步成功，同步了 " + convertedData.size() + " 条记录");
        } catch (Exception e) {
            return new SyncResult(false, "数据同步失败: " + e.getMessage());
        }
    }
    
    private List<BusinessData> convertOADataToBusinessData(List<OABusinessData> oaData) {
        return oaData.stream()
            .map(this::convertSingleRecord)
            .collect(Collectors.toList());
    }
    
    private BusinessData convertSingleRecord(OABusinessData oaData) {
        BusinessData businessData = new BusinessData();
        businessData.setDocId(oaData.getDocId());
        businessData.setTitle(oaData.getTitle());
        businessData.setStatus(mapOAStatusToLocalStatus(oaData.getStatus()));
        businessData.setCreateTime(oaData.getCreateTime());
        businessData.setCreatedBy(oaData.getCreatedBy());
        return businessData;
    }
    
    private String mapOAStatusToLocalStatus(String oaStatus) {
        // 状态映射逻辑
        switch (oaStatus) {
            case "draft": return "草稿";
            case "pending": return "待审批";
            case "approved": return "已审批";
            case "rejected": return "已拒绝";
            default: return "未知状态";
        }
    }
}
```

### 4. 数据模型定义

#### OA业务数据模型
```java
@Data
public class OABusinessData {
    private String docId;
    private String title;
    private String status;
    private String content;
    private String createdBy;
    private Date createTime;
    private Date updateTime;
    private String department;
    private String category;
    private List<String> attachments;
}

@Data
public class OAUserData {
    private String userId;
    private String username;
    private String realName;
    private String email;
    private String phone;
    private String department;
    private String position;
    private String status;
    private Date createTime;
}

@Data
public class SyncRequest {
    private String syncType;
    private Date startDate;
    private Date endDate;
    private List<String> departments;
    private List<String> categories;
}

@Data
public class SyncResult {
    private boolean success;
    private String message;
    private int recordCount;
    private Date syncTime;
    
    public SyncResult(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.syncTime = new Date();
    }
}
```

### 5. 配置文件

#### application.yml配置
```yaml
# OA系统集成配置
oa:
  api:
    base-url: https://e-cloudstore.com/api
    timeout: 30000
  license:
    key: ${OA_LICENSE_KEY:your-license-key}
    secret: ${OA_LICENSE_SECRET:your-license-secret}
  sync:
    enabled: true
    interval: 3600000  # 1小时同步一次
    batch-size: 100
    retry-count: 3
    retry-delay: 5000  # 5秒

# Redis配置（用于缓存token）
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 2000
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

### 6. 定时同步任务

#### 定时任务配置
```java
@Component
@EnableScheduling
public class OADataSyncScheduler {
    
    @Autowired
    private OADataExtractionService oaDataExtractionService;
    
    @Autowired
    private OAAuthenticationService oaAuthService;
    
    @Value("${oa.sync.enabled:true}")
    private boolean syncEnabled;
    
    @Scheduled(fixedDelayString = "${oa.sync.interval:3600000}")
    public void scheduledSync() {
        if (!syncEnabled) {
            return;
        }
        
        try {
            String token = oaAuthService.getValidToken();
            SyncRequest request = new SyncRequest();
            request.setSyncType("incremental");
            request.setStartDate(getLastSyncTime());
            request.setEndDate(new Date());
            
            SyncResult result = oaDataExtractionService.syncData(token, request);
            
            if (result.isSuccess()) {
                updateLastSyncTime(new Date());
                log.info("OA数据同步完成: {}", result.getMessage());
            } else {
                log.error("OA数据同步失败: {}", result.getMessage());
            }
        } catch (Exception e) {
            log.error("定时同步任务执行失败", e);
        }
    }
    
    private Date getLastSyncTime() {
        // 从数据库或缓存获取最后同步时间
        return new Date(System.currentTimeMillis() - 3600000); // 默认1小时前
    }
    
    private void updateLastSyncTime(Date syncTime) {
        // 更新最后同步时间
    }
}
```

## 安全注意事项

### 1. 密钥管理
- 使用环境变量存储敏感信息
- 定期更换access_token
- 避免在代码中硬编码密钥

### 2. 网络安全
- 使用HTTPS协议
- 设置合理的超时时间
- 实施请求频率限制

### 3. 数据安全
- 对敏感数据进行加密
- 实施数据访问权限控制
- 定期备份重要数据

## 性能优化建议

### 1. 缓存策略
- 缓存access_token
- 缓存频繁查询的数据
- 使用Redis分布式缓存

### 2. 批量处理
- 批量获取数据
- 批量保存数据
- 异步处理大量数据

### 3. 监控告警
- 监控API调用频率
- 监控数据同步状态
- 设置异常告警

---

*文档版本*: v1.0  
*最后更新*: 2025-07-03  
*技术负责人*: FinancialSystem开发团队