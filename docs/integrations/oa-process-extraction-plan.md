# OA流程界面获取实施方案

## 📋 需求分析

### 目标
- 利用个人OA账号获取特定时间段的流程界面
- 保存前端界面到本地存储
- 实现自动化数据提取

### 系统信息
- **OA地址**: http://**********:8888/wui/index.html?#/?_key=tdmf41
- **用户账号**: zhoulb
- **认证方式**: 用户名/密码登录
- **目标**: 获取某类流程的前端界面

## 🔍 技术架构分析

### OA系统特征
1. **Web界面**: 基于浏览器的前端系统
2. **前端路由**: 使用单页应用(SPA)架构 (`#/`)
3. **认证密钥**: URL中包含`_key=tdmf41`参数
4. **端口**: 8888（可能是自定义Web应用）

### 技术判断
- 现代前端框架（Vue.js/React/Angular）
- 可能使用Ajax/API进行数据交互
- 需要登录认证和会话管理

## 🛠️ 技术方案

### 方案一：Selenium WebDriver自动化（推荐）

#### 优点
- 完全模拟真实用户操作
- 可处理JavaScript动态内容
- 支持截图和HTML保存
- 可处理各种认证方式

#### 实现步骤
1. 使用Selenium WebDriver启动浏览器
2. 自动登录OA系统
3. 导航到流程管理页面
4. 设置时间筛选条件
5. 获取流程列表
6. 逐个打开流程详情页面
7. 保存页面截图和HTML源码

### 方案二：Puppeteer无头浏览器（备选）

#### 优点
- 性能更好，资源占用少
- 支持PDF生成
- 更好的程序化控制

#### 实现步骤
1. 启动无头Chrome浏览器
2. 执行登录和数据获取
3. 生成PDF或截图
4. 保存到指定目录

### 方案三：HTTP API调用（需要调研）

#### 条件
- 需要分析OA系统的API接口
- 需要逆向工程获取API调用方式
- 可能需要处理CSRF Token等安全机制

## 🚀 实施步骤（方案一详细）

### 第一阶段：环境准备

#### 1. 安装依赖
```bash
# Python环境
pip install selenium beautifulsoup4 requests Pillow

# Java环境（如果使用Java实现）
<!-- Maven依赖 -->
<dependency>
    <groupId>org.seleniumhq.selenium</groupId>
    <artifactId>selenium-java</artifactId>
    <version>4.11.0</version>
</dependency>
```

#### 2. 浏览器驱动
```bash
# 下载ChromeDriver
wget https://chromedriver.storage.googleapis.com/latest/chromedriver_linux64.zip
unzip chromedriver_linux64.zip
sudo mv chromedriver /usr/local/bin/
```

### 第二阶段：登录认证模块

#### Python实现示例
```python
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

class OAProcessor:
    def __init__(self):
        self.driver = None
        self.oa_url = "http://**********:8888/wui/index.html?#/?_key=tdmf41"
        self.username = "zhoulb"
        self.password = "Zlb&198838"
        self.save_dir = "oa_processes"
        
    def setup_driver(self):
        options = webdriver.ChromeOptions()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        # options.add_argument('--headless')  # 无头模式
        self.driver = webdriver.Chrome(options=options)
        self.driver.maximize_window()
        
    def login(self):
        self.driver.get(self.oa_url)
        
        # 等待登录页面加载
        wait = WebDriverWait(self.driver, 10)
        
        # 查找用户名输入框（需要根据实际页面调整选择器）
        username_field = wait.until(
            EC.presence_of_element_located((By.ID, "username"))
        )
        username_field.send_keys(self.username)
        
        # 查找密码输入框
        password_field = self.driver.find_element(By.ID, "password")
        password_field.send_keys(self.password)
        
        # 点击登录按钮
        login_button = self.driver.find_element(By.ID, "loginBtn")
        login_button.click()
        
        # 等待登录完成
        wait.until(EC.url_contains("main"))
        print("登录成功")
        
    def navigate_to_processes(self):
        # 导航到流程管理页面（需要根据实际页面调整）
        process_menu = self.driver.find_element(By.XPATH, "//a[contains(text(), '流程管理')]")
        process_menu.click()
        time.sleep(2)
```

#### Java实现示例
```java
@Service
public class OAProcessExtractor {
    
    private WebDriver driver;
    private final String OA_URL = "http://**********:8888/wui/index.html?#/?_key=tdmf41";
    private final String USERNAME = "zhoulb";
    private final String PASSWORD = "Zlb&198838";
    
    public void setupDriver() {
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        this.driver = new ChromeDriver(options);
        driver.manage().window().maximize();
    }
    
    public void login() {
        driver.get(OA_URL);
        
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        
        // 输入用户名
        WebElement usernameField = wait.until(
            ExpectedConditions.presenceOfElementLocated(By.id("username"))
        );
        usernameField.sendKeys(USERNAME);
        
        // 输入密码
        WebElement passwordField = driver.findElement(By.id("password"));
        passwordField.sendKeys(PASSWORD);
        
        // 点击登录
        WebElement loginButton = driver.findElement(By.id("loginBtn"));
        loginButton.click();
        
        // 等待登录完成
        wait.until(ExpectedConditions.urlContains("main"));
    }
}
```

### 第三阶段：流程数据获取

#### 1. 时间筛选
```python
def set_date_filter(self, start_date, end_date):
    # 设置开始日期
    start_date_field = self.driver.find_element(By.ID, "startDate")
    start_date_field.clear()
    start_date_field.send_keys(start_date)
    
    # 设置结束日期
    end_date_field = self.driver.find_element(By.ID, "endDate")
    end_date_field.clear()
    end_date_field.send_keys(end_date)
    
    # 点击查询按钮
    search_button = self.driver.find_element(By.ID, "searchBtn")
    search_button.click()
    
    # 等待查询结果加载
    time.sleep(3)
```

#### 2. 流程类型筛选
```python
def select_process_type(self, process_type):
    # 选择流程类型下拉框
    process_type_select = Select(self.driver.find_element(By.ID, "processType"))
    process_type_select.select_by_visible_text(process_type)
    
    # 触发查询
    search_button = self.driver.find_element(By.ID, "searchBtn")
    search_button.click()
    time.sleep(2)
```

#### 3. 获取流程列表
```python
def get_process_list(self):
    # 获取流程列表
    process_rows = self.driver.find_elements(By.XPATH, "//table[@id='processTable']/tbody/tr")
    
    processes = []
    for row in process_rows:
        cells = row.find_elements(By.TAG_NAME, "td")
        process_info = {
            'id': cells[0].text,
            'title': cells[1].text,
            'type': cells[2].text,
            'status': cells[3].text,
            'create_date': cells[4].text,
            'creator': cells[5].text
        }
        processes.append(process_info)
    
    return processes
```

### 第四阶段：界面保存

#### 1. 截图保存
```python
def save_process_screenshot(self, process_id, process_title):
    # 创建保存目录
    os.makedirs(self.save_dir, exist_ok=True)
    
    # 生成文件名
    filename = f"{process_id}_{process_title}_{int(time.time())}.png"
    filepath = os.path.join(self.save_dir, filename)
    
    # 截取整个页面
    self.driver.save_screenshot(filepath)
    print(f"截图已保存: {filepath}")
```

#### 2. HTML源码保存
```python
def save_process_html(self, process_id, process_title):
    # 获取页面源码
    html_content = self.driver.page_source
    
    # 保存HTML文件
    filename = f"{process_id}_{process_title}_{int(time.time())}.html"
    filepath = os.path.join(self.save_dir, filename)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML已保存: {filepath}")
```

#### 3. PDF生成
```python
def save_process_pdf(self, process_id, process_title):
    # 使用打印功能生成PDF
    filename = f"{process_id}_{process_title}_{int(time.time())}.pdf"
    filepath = os.path.join(self.save_dir, filename)
    
    # 执行打印到PDF
    print_options = {
        'paperFormat': 'A4',
        'printBackground': True
    }
    
    # 需要使用支持PDF的driver（如Chrome）
    self.driver.execute_script(f"window.print();")
    
    print(f"PDF已保存: {filepath}")
```

### 第五阶段：完整流程整合

```python
def extract_processes(self, start_date, end_date, process_type=None):
    try:
        # 1. 初始化浏览器
        self.setup_driver()
        
        # 2. 登录OA系统
        self.login()
        
        # 3. 导航到流程页面
        self.navigate_to_processes()
        
        # 4. 设置筛选条件
        self.set_date_filter(start_date, end_date)
        if process_type:
            self.select_process_type(process_type)
        
        # 5. 获取流程列表
        processes = self.get_process_list()
        
        # 6. 逐个处理流程
        for process in processes:
            # 打开流程详情页
            self.open_process_detail(process['id'])
            
            # 保存界面
            self.save_process_screenshot(process['id'], process['title'])
            self.save_process_html(process['id'], process['title'])
            
            # 返回列表页
            self.driver.back()
            time.sleep(1)
        
        print(f"成功处理 {len(processes)} 个流程")
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
    finally:
        if self.driver:
            self.driver.quit()
```

## 🔧 Spring Boot集成

### 1. 控制器实现
```java
@RestController
@RequestMapping("/api/oa/extract")
public class OAExtractionController {
    
    @Autowired
    private OAProcessExtractor oaExtractor;
    
    @PostMapping("/processes")
    public ResponseEntity<ExtractionResult> extractProcesses(
            @RequestBody ProcessExtractionRequest request) {
        
        try {
            ExtractionResult result = oaExtractor.extractProcesses(
                request.getStartDate(),
                request.getEndDate(),
                request.getProcessType()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ExtractionResult(false, e.getMessage()));
        }
    }
    
    @GetMapping("/status")
    public ResponseEntity<String> getExtractionStatus() {
        return ResponseEntity.ok("OA提取服务正常运行");
    }
}
```

### 2. 异步处理
```java
@Service
public class OAProcessExtractor {
    
    @Async
    public CompletableFuture<ExtractionResult> extractProcessesAsync(
            String startDate, String endDate, String processType) {
        
        // 执行提取逻辑
        return CompletableFuture.completedFuture(new ExtractionResult(true, "提取完成"));
    }
}
```

## 🎯 关键技术点

### 1. 元素定位策略
- **ID定位**: `By.id("elementId")`
- **类名定位**: `By.className("className")`
- **XPath定位**: `By.xpath("//div[@class='content']")`
- **CSS选择器**: `By.cssSelector("div.content")`

### 2. 等待机制
- **显式等待**: `WebDriverWait`
- **隐式等待**: `driver.implicitly_wait(10)`
- **强制等待**: `Thread.sleep(2000)`

### 3. 异常处理
- 网络超时处理
- 元素不存在处理
- 登录失败处理
- 页面加载失败处理

## 🔍 待确认信息

### 必需信息
1. **登录页面元素**: 用户名、密码输入框的ID/Class
2. **流程管理页面**: 菜单链接的定位方式
3. **时间筛选控件**: 日期选择器的ID/Class
4. **流程类型选择**: 下拉框的选项值
5. **流程列表结构**: 表格的ID和列结构

### 可选信息
1. **流程详情页面**: 详情页的URL模式
2. **分页机制**: 是否有分页，如何翻页
3. **导出功能**: 是否有内置导出功能
4. **权限限制**: 是否有访问频率限制

## 📝 实施建议

### 第一步：页面分析
1. 手动访问OA系统
2. 分析页面结构和元素ID
3. 测试登录流程
4. 识别关键页面元素

### 第二步：原型开发
1. 编写简单的登录脚本
2. 测试基本的页面操作
3. 验证数据获取逻辑
4. 调试和优化

### 第三步：完整实现
1. 实现完整的提取逻辑
2. 添加错误处理和重试机制
3. 集成到FinancialSystem中
4. 测试和部署

## ⚠️ 注意事项

### 合规性
- 仅在授权范围内使用
- 遵守OA系统的使用条款
- 不要过度频繁访问系统

### 技术风险
- 页面结构变化可能导致脚本失效
- 网络不稳定可能导致操作失败
- 反爬虫机制可能阻止自动化操作

### 安全考虑
- 密码加密存储
- 访问日志记录
- 异常监控和告警

---

**下一步**: 需要您协助分析OA系统的页面结构，确定具体的元素定位方式，然后我们可以开始实施开发。