# 🔐 OA工作流提取系统验证码处理指南

## 📋 验证码处理方案

OA工作流提取系统现已支持智能验证码处理，提供多种处理模式以适应不同场景。

## 🎯 验证码处理模式

### 1. 🎮 交互式模式（推荐）

**使用场景**: 开发环境、首次使用、验证码复杂时

**特点**:
- 自动检测验证码
- 显示浏览器窗口
- 用户手动输入验证码
- 程序自动继续执行

**配置**:
```yaml
oa:
  workflow:
    web-driver:
      headless: false  # 显示浏览器窗口
```

**工作流程**:
1. 系统自动打开OA登录页面
2. 自动填入用户名和密码
3. 检测到验证码时暂停
4. 用户在浏览器中手动输入验证码
5. 程序检测到验证码输入后自动继续

### 2. 🤖 自动模式（开发中）

**使用场景**: 生产环境、批量处理、无人值守

**特点**:
- OCR自动识别验证码
- 无需人工干预
- 支持数字、字母验证码

**配置**:
```yaml
oa:
  workflow:
    web-driver:
      headless: true   # 后台运行
```

**状态**: 🚧 开发中，暂时使用交互式模式

## 🔧 配置说明

### 开发环境配置

```yaml
# application-oa-workflow.yml
spring:
  config:
    activate:
      on-profile: development

oa:
  workflow:
    base-url: http://10.25.1.18:8888/wui/index.html
    web-driver:
      headless: false          # 显示浏览器以便手动输入验证码
      window-width: 1920
      window-height: 1080
    timeout:
      login-timeout: 120       # 增加登录超时时间以便输入验证码
      element-wait-timeout: 30
    file:
      output-dir: src/main/resources/dev-oa-workflows
```

### 生产环境配置

```yaml
spring:
  config:
    activate:
      on-profile: production

oa:
  workflow:
    web-driver:
      headless: false          # 临时设为false直到OCR完成
    timeout:
      login-timeout: 180       # 更长的超时时间
```

## 🚀 使用步骤

### 通过Web界面

1. **启动系统**
   ```bash
   cd api-gateway
   mvn spring-boot:run
   ```

2. **访问OA工作流界面**
   ```
   http://localhost:3000/oa-workflow
   ```

3. **输入提取参数**
   - 用户名: `zhoulb`
   - 密码: `Zlb&198838`
   - 工作流类型: `日常留言`
   - 时间范围: 最近3天

4. **开始提取**
   - 点击"开始提取"按钮
   - 系统自动打开浏览器登录页面
   - **重要**: 浏览器窗口会显示登录页面

5. **处理验证码**
   - 如果出现验证码，在浏览器中手动输入
   - 输入完成后程序自动检测并继续
   - 等待登录成功提示

6. **查看结果**
   - 文件自动保存到: `src/main/resources/dev-oa-workflows/`
   - 按月份和文件类型分组存储

### 通过API调用

```bash
# 1. 登录获取令牌
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "laoshu198838",
    "password": "Zlb&198838"
  }'

# 2. 调用OA提取（会自动弹出浏览器处理验证码）
curl -X POST http://localhost:8080/api/oa/workflow/extract-sync \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "username": "zhoulb",
    "password": "Zlb&198838",
    "workflowType": "日常留言",
    "startDate": "2025-06-30",
    "endDate": "2025-07-03",
    "outputFormat": "PDF",
    "includeScreenshot": true
  }'
```

## ⚠️ 重要提示

### 验证码处理注意事项

1. **浏览器窗口不要关闭**
   - 提取过程中Chrome浏览器会自动打开
   - 请不要手动关闭浏览器窗口
   - 等待程序完成后自动关闭

2. **验证码输入时机**
   - 程序会自动填入用户名和密码
   - 只需在出现验证码时手动输入
   - 输入完成后无需点击登录按钮，程序会自动处理

3. **超时设置**
   - 默认等待验证码输入时间: 2分钟
   - 如需更长时间可在配置中调整 `login-timeout`

4. **多次尝试**
   - 如果验证码输入错误，程序会自动重试
   - 重试时会重新打开浏览器页面

## 🔍 故障排除

### 常见问题

1. **验证码识别失败**
   ```
   解决方案: 确保使用非无头模式 (headless: false)
   ```

2. **浏览器未显示**
   ```
   检查: Chrome浏览器是否已安装
   解决: 系统会自动下载WebDriver
   ```

3. **登录超时**
   ```
   原因: 验证码输入时间过长
   解决: 增加 login-timeout 配置值
   ```

4. **无法找到验证码输入框**
   ```
   原因: OA系统页面结构可能变化
   解决: 检查控制台日志，可能需要更新选择器
   ```

## 📁 文件保存位置

验证码处理成功后，提取的文件将保存到：

```
src/main/resources/dev-oa-workflows/
├── 202507/
│   ├── pdf/
│   │   ├── 关于XX的日常留言_20250703_143022.pdf
│   │   └── 工作汇报_日常留言_20250703_143156.pdf
│   ├── screenshots/
│   │   └── 相关截图文件
│   └── html/
│       └── HTML格式文件
└── README.md
```

## 🎉 验证码处理成功标志

当看到以下日志时，表示验证码处理成功：

```
[INFO] 检测到验证码，启用交互式处理模式
[INFO] 检测到验证码输入: ****
[INFO] 验证码处理完成
[INFO] 登录成功
[INFO] 成功导航到已办事项页面
```

---

## 📞 技术支持

如遇到验证码相关问题，请：

1. 检查浏览器是否正常显示
2. 确认验证码输入框可见
3. 查看控制台日志输出
4. 检查OA系统是否正常访问

**验证码处理现已完全支持，可以正常使用OA工作流提取功能！** 🎉