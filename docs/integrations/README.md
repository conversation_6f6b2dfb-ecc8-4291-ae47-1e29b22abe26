# 🔗 系统集成文档中心

## 📋 概述

本目录包含了FinancialSystem与外部系统集成的所有文档，包括技术实施指南、配置说明、API文档等。

## 📁 文档目录

### 1. 🏦 司库系统集成
- **[司库系统集成完成报告](treasury-integration-complete.md)**
  - 司库系统接口集成的完整实施文档
  - 包含代码实现、配置、测试和部署指南
  - 中信银行司库系统数据提取功能
  - 状态：✅ 已完成

### 2. 🏢 OA系统集成
- **[OA系统集成指南](oa-integration-guide.md)**
  - OA系统许可证注册认证指南
  - 前端后端数据提取方案
  - 泛微E9 OA系统集成方案
  - 状态：📋 文档已就绪

- **[OA系统技术实施文档](oa-technical-implementation.md)**
  - 详细的技术实施指南和代码示例
  - 包含前端React组件和后端Spring Boot服务
  - 数据同步和定时任务实现
  - 状态：📋 文档已就绪

## 🚀 快速开始

### 司库系统集成
```bash
# 1. 查看司库系统配置
cat integrations/treasury/src/main/resources/application-treasury.yml

# 2. 测试司库系统连接
curl http://localhost:8080/api/treasury/test

# 3. 查询账户余额
curl http://localhost:8080/api/treasury/balance
```

### OA系统集成
```bash
# 1. 配置OA系统许可证
export OA_LICENSE_KEY=your-license-key
export OA_LICENSE_SECRET=your-license-secret

# 2. 启动OA数据同步
curl -X POST http://localhost:8080/api/oa/extract/sync-data

# 3. 查询OA业务数据
curl http://localhost:8080/api/oa/extract/business-data
```

## 🔧 技术栈

### 共同技术栈
- **后端**: Spring Boot 3.1.12, Java 21
- **前端**: React 19, Material-UI v5
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **构建工具**: Maven, npm

### 司库系统专用
- **通信协议**: HTTP/HTTPS
- **数据格式**: XML (GBK编码)
- **认证方式**: 用户名/密码
- **网络**: 内网直连

### OA系统专用
- **平台**: 泛微E9 OA系统
- **API框架**: Jersey REST API
- **认证方式**: Token-based (access_token)
- **数据格式**: JSON

## 📊 集成状态

| 系统 | 状态 | 完成度 | 最后更新 |
|------|------|--------|----------|
| 司库系统 | ✅ 已完成 | 100% | 2025-06-XX |
| OA系统 | 📋 文档就绪 | 80% | 2025-07-03 |

## 🛠️ 配置文件位置

### 司库系统配置
```
integrations/treasury/src/main/resources/
├── application-treasury.yml      # 司库系统配置
├── treasury-config.md           # 配置说明
└── treasury-data-extraction-guide.md
```

### OA系统配置
```
# 主配置文件
api-gateway/src/main/resources/application.yml

# OA特定配置
oa:
  api:
    base-url: https://e-cloudstore.com/api
  license:
    key: ${OA_LICENSE_KEY}
    secret: ${OA_LICENSE_SECRET}
```

## 🔍 故障排除

### 常见问题
1. **网络连接问题**
   - 检查防火墙设置
   - 确认服务端口开放
   - 验证网络连通性

2. **认证失败**
   - 司库系统：检查用户名密码
   - OA系统：验证许可证密钥

3. **数据格式错误**
   - 司库系统：检查XML格式和GBK编码
   - OA系统：验证JSON数据结构

### 调试命令
```bash
# 查看系统日志
tail -f var/log/financial-system-all.log

# 检查网络连接
telnet ********** 6767  # 司库系统
curl -I https://e-cloudstore.com  # OA系统

# 验证服务状态
curl http://localhost:8080/actuator/health
```

## 📈 性能监控

### 监控指标
- API响应时间
- 数据同步成功率
- 错误率统计
- 系统资源使用情况

### 监控工具
- Spring Boot Actuator
- 自定义健康检查
- 日志分析
- 性能指标收集

## 🔒 安全考虑

### 数据安全
- 敏感信息加密存储
- 网络传输加密
- 访问权限控制
- 审计日志记录

### 系统安全
- 定期更新依赖
- 漏洞扫描
- 安全配置检查
- 备份和恢复策略

## 📚 参考资料

### 官方文档
- [中信银行司库系统接口说明](../integrations/treasury/src/main/resources/中信银行天元司库ERP接口说明书V7.7.pdf)
- [泛微E9 API文档](https://e-cloudstore.com/ec/api/applist/index.html)

### 技术文档
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [React官方文档](https://reactjs.org/)
- [Material-UI文档](https://mui.com/)

## 🤝 贡献指南

### 新增集成
1. 在 `integrations/` 目录下创建新的模块
2. 在 `docs/integrations/` 目录下创建相应文档
3. 更新本README.md文件
4. 提交完整的技术文档

### 文档更新
1. 遵循现有文档格式
2. 包含完整的代码示例
3. 提供详细的配置说明
4. 添加故障排除指南

---

*文档维护者*: FinancialSystem开发团队  
*最后更新*: 2025-07-03  
*版本*: v1.0