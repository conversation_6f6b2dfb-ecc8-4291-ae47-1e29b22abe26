# OA页面元素信息收集模板

## 📋 使用说明
请按照 [OA页面元素分析指南](oa-page-analysis-guide.md) 的步骤，逐一填写以下模板。

---

## 🔐 登录页面元素

### 用户名输入框
```
ID属性: ________________
Name属性: ______________
Class属性: _____________
Type属性: ______________
Placeholder文本: _______
XPath路径: _____________
CSS选择器: ____________
其他重要属性: __________
```

### 密码输入框
```
ID属性: ________________
Name属性: ______________
Class属性: _____________
Type属性: ______________
Placeholder文本: _______
XPath路径: _____________
CSS选择器: ____________
其他重要属性: __________
```

### 登录按钮
```
ID属性: ________________
Class属性: _____________
按钮类型: ______________
按钮文本: ______________
onclick事件: ___________
XPath路径: _____________
CSS选择器: ____________
其他重要属性: __________
```

### 登录页面其他元素
```
验证码（如果有）:
- 输入框ID: ____________
- 图片ID: ______________
- 刷新按钮: ____________

记住密码复选框（如果有）:
- ID: __________________
- Name: ________________

其他注意事项:
_____________________________
_____________________________
```

---

## 🏢 流程管理页面导航

### 主菜单定位
```
流程管理菜单文本: _______
菜单ID: ________________
菜单Class: _____________
菜单链接href: __________
父级菜单（如果有）: ____
XPath路径: _____________
CSS选择器: ____________
点击后的URL变化: ______
```

### 子菜单（如果有）
```
子菜单1:
- 文本: ________________
- ID: __________________
- Class: _______________
- 链接: ________________

子菜单2:
- 文本: ________________
- ID: __________________
- Class: _______________
- 链接: ________________

其他子菜单:
_____________________________
```

---

## 📊 流程列表页面结构

### 流程列表表格
```
表格ID: ________________
表格Class: _____________
表格容器ID: ____________
表格容器Class: _________
XPath路径: _____________
CSS选择器: ____________
```

### 表格列结构
```
第1列 - 列名: ___________
- th的Class: ___________
- td的Class: ___________

第2列 - 列名: ___________
- th的Class: ___________
- td的Class: ___________

第3列 - 列名: ___________
- th的Class: ___________
- td的Class: ___________

第4列 - 列名: ___________
- th的Class: ___________
- td的Class: ___________

第5列 - 列名: ___________
- th的Class: ___________
- td的Class: ___________

其他列:
_____________________________
```

### 流程行结构
```
每一行的Class: __________
行的选择器: _____________
流程ID列位置: ___________
流程标题列位置: _________
流程状态列位置: _________
创建时间列位置: _________
操作按钮列位置: _________
```

---

## 🔍 筛选条件元素

### 时间筛选
```
开始时间输入框:
- ID: __________________
- Name: ________________
- Class: _______________
- Placeholder: __________
- 日期格式: ____________
- XPath: _______________

结束时间输入框:
- ID: __________________
- Name: ________________
- Class: _______________
- Placeholder: __________
- 日期格式: ____________
- XPath: _______________

时间选择器类型:
□ 标准input[type="date"]
□ 第三方日期插件
□ 自定义日期选择器
□ 其他: ________________
```

### 流程类型筛选
```
流程类型选择器:
- ID: __________________
- Name: ________________
- Class: _______________
- 选择器类型: __________
- XPath: _______________

可选的流程类型选项:
1. 选项文本: ____________
   选项值: ______________

2. 选项文本: ____________
   选项值: ______________

3. 选项文本: ____________
   选项值: ______________

4. 选项文本: ____________
   选项值: ______________

5. 选项文本: ____________
   选项值: ______________

其他选项:
_____________________________
```

### 其他筛选条件
```
状态筛选（如果有）:
- ID: __________________
- 选项: ________________

创建人筛选（如果有）:
- ID: __________________
- 类型: ________________

部门筛选（如果有）:
- ID: __________________
- 选项: ________________

关键词搜索（如果有）:
- ID: __________________
- Placeholder: __________

其他筛选条件:
_____________________________
```

### 查询/搜索按钮
```
查询按钮:
- ID: __________________
- Class: _______________
- 按钮文本: ____________
- XPath: _______________

重置按钮（如果有）:
- ID: __________________
- Class: _______________
- 按钮文本: ____________

导出按钮（如果有）:
- ID: __________________
- Class: _______________
- 按钮文本: ____________
```

---

## 📄 流程详情页面

### 详情页面访问方式
```
详情链接类型:
□ 直接链接（href）
□ JavaScript点击事件
□ 按钮点击
□ 其他: ________________

详情链接定位:
- Class: _______________
- 链接文本: ____________
- XPath: _______________
- CSS选择器: __________

详情页面URL格式:
_____________________________

URL参数说明:
- 流程ID参数名: ________
- 其他参数: ____________
```

### 详情页面结构
```
页面标题元素:
- ID: __________________
- Class: _______________
- XPath: _______________

主要内容区域:
- ID: __________________
- Class: _______________
- XPath: _______________

流程信息区域:
- ID: __________________
- Class: _______________

流程步骤区域:
- ID: __________________
- Class: _______________

附件区域（如果有）:
- ID: __________________
- Class: _______________

审批意见区域:
- ID: __________________
- Class: _______________
```

---

## 🔄 分页机制

### 分页信息
```
是否有分页:
□ 是
□ 否

分页类型:
□ 数字分页
□ 上一页/下一页
□ 滚动加载
□ 其他: ________________

分页容器:
- ID: __________________
- Class: _______________

下一页按钮:
- ID: __________________
- Class: _______________
- 文本: ________________

页码输入框（如果有）:
- ID: __________________
- Class: _______________

每页显示数量选择器（如果有）:
- ID: __________________
- 选项: ________________
```

---

## ⚡ 页面加载特性

### 加载方式
```
页面加载类型:
□ 同步加载（页面刷新）
□ 异步加载（Ajax）
□ 单页应用（SPA）
□ 混合加载

数据加载完成标志:
- 元素出现: ____________
- 文本变化: ____________
- Class变化: ___________
- 其他标志: ____________

平均加载时间: __________
```

### 网络请求
```
登录请求URL: ___________
登录请求方法: __________
流程列表请求URL: _______
流程详情请求URL: _______

重要的Ajax请求:
1. URL: ________________
   用途: ________________

2. URL: ________________
   用途: ________________

3. URL: ________________
   用途: ________________
```

---

## 🛡️ 安全机制

### 认证相关
```
Session机制:
□ Cookie-based
□ Token-based
□ 其他: ________________

重要Cookie名称:
1. ______________________
2. ______________________
3. ______________________

CSRF Token（如果有）:
- 参数名: ______________
- 获取方式: ____________

验证码机制:
□ 登录时有验证码
□ 操作时有验证码
□ 无验证码
□ 其他: ________________
```

---

## 📝 特殊发现和注意事项

### 技术特点
```
前端框架判断:
□ Vue.js
□ React
□ Angular
□ jQuery
□ 原生JavaScript
□ 其他: ________________

特殊技术:
□ iframe嵌套
□ 弹窗（modal）
□ 拖拽功能
□ 文件上传
□ 其他: ________________
```

### 限制和风险
```
访问频率限制:
□ 有限制
□ 无限制
□ 不确定

单次登录有效期: ________

同时登录限制:
□ 允许多处登录
□ 只允许单处登录
□ 不确定

其他发现:
_____________________________
_____________________________
_____________________________
```

### 目标流程类型
```
您想获取的具体流程类型:
1. ______________________
2. ______________________
3. ______________________
4. ______________________
5. ______________________

时间范围偏好:
- 默认查询范围: ________
- 最大查询范围: ________

保存格式偏好:
□ 截图（PNG）
□ 网页文件（HTML）
□ PDF文档
□ 其他: ________________
```

---

## ✅ 验证检查清单

完成信息收集后，请验证：

- [ ] 能通过记录的选择器找到用户名输入框
- [ ] 能通过记录的选择器找到密码输入框  
- [ ] 能通过记录的选择器找到登录按钮
- [ ] 能成功导航到流程管理页面
- [ ] 能找到流程列表表格
- [ ] 能设置时间筛选条件
- [ ] 能设置流程类型筛选
- [ ] 能访问流程详情页面
- [ ] 了解分页机制（如果有）
- [ ] 确认目标流程类型

---

**完成时间**: _______________  
**分析人员**: _______________  
**备注**: ___________________

---

## 📞 后续支持

完成此模板后，请将填写好的信息提供给我，我将：

1. ✅ 根据您的信息编写自动化脚本
2. ✅ 创建测试用例验证功能
3. ✅ 集成到FinancialSystem项目
4. ✅ 提供详细的使用说明

如有任何疑问，随时联系！