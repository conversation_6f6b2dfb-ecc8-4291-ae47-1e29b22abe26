# 📁 FinancialSystem 文档归档中心

本目录包含项目的历史文档和已解决问题的记录，用于保留重要的技术决策和问题解决过程的历史记录。

## 📂 目录结构

```
archive/
├── README.md                    # 本文件
├── resolved-issues/             # 已解决的问题记录
│   ├── 债权删除双重扣减问题分析.md
│   ├── 债权删除余额恢复问题修复.md
│   └── 债权删除双重扣减修复方案.md
└── historical/                  # 历史文档归档
    ├── deployment-history/      # 历史部署记录
    ├── improvement-implementation-summary.md
    └── project-structure-optimization-plan.md
```

## 🗂️ 分类说明

### `resolved-issues/` - 已解决问题
存放已经完全解决的问题分析和修复记录：

- **保留原因**: 为未来遇到类似问题提供参考
- **查阅场景**: 问题回溯、技术决策审查、新团队成员学习
- **内容特点**: 包含问题分析过程、临时解决方案、最终修复方案

### `historical/` - 历史文档  
存放已过时但具有历史价值的文档：

- **保留原因**: 记录项目发展历程、重要里程碑
- **查阅场景**: 项目历史回顾、架构演进分析
- **内容特点**: 早期设计方案、废弃的实现方式、历史部署记录

## 📋 归档原则

### 何时归档文档
1. **问题已彻底解决** - 相关问题不再复现，解决方案已稳定运行
2. **文档内容过时** - 描述的系统/架构已不存在或被替换
3. **临时性文档完成使命** - 如临时修复方案被正式方案替代
4. **重复内容整合** - 多个文档合并后，原始文档需要归档

### 归档标准
- ✅ 文档内容完整，无关键信息丢失
- ✅ 问题解决过程记录清晰
- ✅ 有对应的新文档或解决方案
- ✅ 保留历史价值，便于后续参考

## 🔍 快速查找

### 债权删除相关问题
如果遇到债权删除功能问题，请优先查看：
1. **当前文档**: `/docs/bug-fixes/debt-deletion-issues-resolved.md`
2. **历史记录**: `/docs/archive/resolved-issues/` 目录下的相关文档

### 部署相关历史
如果需要了解部署历史演进：
1. **当前部署指南**: `/docs/deployment/` 目录
2. **历史部署记录**: `/docs/archive/historical/deployment-history/`

### MySQL复制配置
如果需要MySQL复制相关信息：
1. **当前配置**: `/docs/deployment/mysql-universal-bidirectional-sync.md`
2. **实施记录**: `/docs/deployment/mysql-replication-execution-guide.md`

## ⚠️ 使用注意事项

1. **归档文档仅供参考** - 不应作为当前实施的依据
2. **优先查看当前文档** - 确保使用最新的解决方案
3. **版本兼容性** - 归档文档可能基于旧版本系统
4. **联系维护者** - 如有疑问，请联系项目维护团队

## 📝 维护记录

| 日期 | 操作 | 说明 |
|------|------|------|
| 2025-07-24 | 创建归档结构 | 建立resolved-issues和historical目录 |
| 2025-07-24 | 债权删除问题归档 | 将已解决的债权删除相关问题移入归档 |
| 2025-07-24 | 历史文档整理 | 将过时的部署和改进文档移入历史归档 |

---
*本目录由文档整理自动维护 | 最后更新: 2025-07-24*