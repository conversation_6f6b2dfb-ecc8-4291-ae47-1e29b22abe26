# FinancialSystem项目Repository和SQL查询分布结构分析

## 📋 目录
1. [项目整体架构](#项目整体架构)
2. [Repository接口分布](#repository接口分布)
3. [SQL查询分布分析](#sql查询分布分析)
4. [数据源配置](#数据源配置)
5. [重构建议](#重构建议)

---

## 🏗️ 项目整体架构

### 模块结构
```
FinancialSystem/
├── api-gateway/                 # Web服务层 - 主启动模块
├── data-access/               # 数据访问层 - 统一Repository
├── data-processing/           # 数据处理层 - 定时任务和业务逻辑
├── services/          # 业务模块组
│   ├── debt-management/       # 债权管理
│   ├── account-management/    # 账户管理
│   ├── audit-management/      # 审计管理
│   └── report-management/     # 报表管理
├── common/                    # 公共组件层
├── mysql_data/               # MySQL数据处理（旧模块）
├── kingdee/                  # 金蝶系统集成
└── treasury/                 # 司库业务
```

### 技术栈
- **后端框架**: Spring Boot 3.1.2
- **数据库**: MySQL 8
- **ORM框架**: Spring Data JPA + Hibernate
- **安全框架**: Spring Security + JWT
- **Java版本**: JDK 21

---

## 📊 Repository接口分布

### 1. data-access模块 (统一数据访问层)

#### 1.1 逾期债权相关Repository
**路径**: `data-access/src/main/java/com/laoshu198838/repository/overdue_debt/`

| Repository接口 | 对应实体 | 数据源 | 主要功能 |
|---------------|---------|--------|----------|
| `OverdueDebtAddRepository` | OverdueDebtAdd | @DataSource("primary") | 新增债权数据访问 |
| `OverdueDebtDecreaseRepository` | OverdueDebtDecrease | @DataSource("primary") | 债权处置数据访问 |
| `LitigationClaimRepository` | LitigationClaim | @DataSource("primary") | 诉讼债权数据访问 |
| `NonLitigationClaimRepository` | NonLitigationClaim | @DataSource("primary") | 非诉讼债权数据访问 |
| `ImpairmentReserveRepository` | ImpairmentReserve | @DataSource("primary") | 减值准备数据访问 |
| `OverdueDebtSummaryRepository` | OverdueDebtSummary | @DataSource("primary") | 逾期债权汇总数据访问 |
| `UserRepository` | User | @DataSource("primary") | 用户数据访问 |
| `RoleRepository` | Role | @DataSource("primary") | 角色数据访问 |

#### 1.2 专用查询Repository
| Repository接口 | 功能描述 | 特点 |
|---------------|----------|------|
| `ConsistencyCheckRepository` | 数据一致性检查 | 复杂跨表查询 |
| `OptimizedConsistencyCheckRepository` | 优化版一致性检查 | 性能优化、缓存支持 |
| `DebtDecreaseQueryRepository` | 债权减少查询 | 专用查询DTO |
| `DebtTrackingRecordRepository` | 债务跟踪记录 | 基础CRUD |

### 2. api-gateway模块 (Web服务层)

#### 2.1 自定义Repository
**路径**: `api-gateway/src/main/java/com/laoshu198838/repository/`

| Repository接口 | 数据源 | 说明 |
|---------------|--------|------|
| `UserRepository` | 默认 | 用户管理（旧版本） |
| `RoleRepository` | 默认 | 角色管理（旧版本） |

#### 2.2 专用查询Repository
**路径**: `api-gateway/src/main/java/com/laoshu198838/repository/custom/`

| Repository接口 | 功能 | 特点 |
|---------------|------|------|
| `ConsistencyCheckRepository` | 一致性检查 | 与data-access重复 |
| `OverdueDebtSummaryRepository` | 债权汇总 | 与data-access重复 |
| `DebtDecreaseQueryRepository` | 债权减少查询 | 与data-access重复 |
| `DebtTrackingRecordRepository` | 债务跟踪 | 基础功能 |

### 3. mysql_data模块 (旧数据处理模块)

**路径**: `mysql_data/src/main/java/com/laoshu198838/repository/mysql/`

| Repository接口 | 说明 | 状态 |
|---------------|------|------|
| `NonLitigationClaimRepository` | 非诉讼债权 | 待迁移 |
| `OverdueDebtDecreaseRepository` | 债权处置 | 待迁移 |

---

## 🔍 SQL查询分布分析

### 1. 原生SQL查询 (@Query nativeQuery=true)

#### 1.1 数据一致性检查查询
**位置**: `ConsistencyCheckRepository`、`OptimizedConsistencyCheckRepository`

**主要查询类型**:
- 跨表汇总查询（新增表、诉讼表、非诉讼表、减值准备表）
- 期末余额对比查询
- 年初余额计算查询
- 处置金额汇总查询

**示例查询**:
```sql
-- 期末余额汇总
SELECT 'endingBalance' as checkType,
       SUM(a.债权余额) as addTableAmount,
       (SELECT SUM(l.本月末债权余额) FROM 诉讼表 l WHERE l.年份 = :year AND l.月份 = :month) as litigationAmount,
       (SELECT SUM(n.本月末本金) FROM 非诉讼表 n WHERE n.年份 = :year AND n.月份 = :month) as nonLitigationAmount,
       (SELECT SUM(i.本月末债权余额) FROM 减值准备表 i WHERE i.年份 = :year AND i.月份 = :month) as impairmentAmount
FROM 新增表 a WHERE a.年份 = :year
```

#### 1.2 汇总统计查询
**位置**: `OverdueDebtSummaryRepository`

**查询特点**:
- 简单聚合查询
- COALESCE函数处理NULL值
- 单表查询为主

**示例查询**:
```sql
-- 获取本年处置债权金额
SELECT COALESCE(SUM(本年减少债权金额), 0) FROM 汇总表

-- 获取期末债权余额
SELECT COALESCE(SUM(本年期末债权余额), 0) FROM 汇总表
```

#### 1.3 批量更新查询
**位置**: `ImpairmentReserveRepository`、`NonLitigationClaimRepository`

**查询特点**:
- 使用窗口函数计算累计值
- JOIN操作更新相关记录
- @Modifying + @Transactional

**示例查询**:
```sql
-- 批量更新年度累计回收
UPDATE 减值准备表 t1
JOIN (
    SELECT 债权人, 债务人, 期间, 年份, 月份,
           SUM(本月处置债权) OVER (
               PARTITION BY 债权人, 债务人, 期间, 年份
               ORDER BY 月份
               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
           ) AS 累计处置
    FROM 减值准备表
) t2 ON t1.债权人 = t2.债权人 AND t1.债务人 = t2.债务人
SET t1.本年度累计回收 = t2.累计处置
WHERE t1.本年度累计回收 <> t2.累计处置
```

### 2. JPQL查询

#### 2.1 基础查询
**位置**: 各Repository接口

**查询类型**:
- 按条件查询：`findByCreditorAndDebtor`
- 计数查询：`countByYearAndMonthForExists`
- 模糊查询：`findByCreditorAndDebtor`

**示例查询**:
```java
// JPQL查询示例
@Query("SELECT l FROM LitigationClaim l WHERE l.id.creditor = :creditor AND l.id.debtor = :debtor")
List<LitigationClaim> findByCreditorAndDebtor(@Param("creditor") String creditor, @Param("debtor") String debtor);

@Query("SELECT COUNT(l) FROM LitigationClaim l WHERE l.id.year = :year AND l.id.month = :month")
long countByYearAndMonthForExists(@Param("year") int year, @Param("month") int month);
```

### 3. 动态SQL查询

#### 3.1 Service层动态查询
**位置**: 各Service类中

**特点**:
- 使用EntityManager创建原生查询
- 参数化查询防止SQL注入
- 异常处理和日志记录

**示例**:
```java
// 动态原生SQL查询
String nativeQuery = "SELECT COUNT(*) FROM 非诉讼表 WHERE 年份 = ? AND 月份 = ?";
Query query = entityManager.createNativeQuery(nativeQuery);
query.setParameter(1, yearMonth.getYear());
query.setParameter(2, yearMonth.getMonthValue());
Number count = (Number) query.getSingleResult();
```

---

## ⚙️ 数据源配置

### 1. 多数据源架构

#### 1.1 数据源类型
| 数据源名称 | 标识 | 用途 | 配置位置 |
|-----------|------|------|----------|
| 主数据源 | primary | 逾期债权数据库 | MultiDataSourceConfig |
| 第二数据源 | secondary | 金蝶数据库 | MultiDataSourceConfig |
| MySQL数据源 | mysql | 数据处理专用 | MySqlDataSourceConfig |

#### 1.2 数据源切换机制
- **注解驱动**: `@DataSource("primary")` / `@DataSource("secondary")`
- **AOP切面**: `DataSourceAspect` 处理数据源切换
- **上下文管理**: `DynamicDataSourceContextHolder` 管理当前数据源

### 2. Repository数据源分配

#### 2.1 主数据源 (primary)
```java
@Repository
@DataSource("primary")
public interface ImpairmentReserveRepository extends JpaRepository<ImpairmentReserve, ImpairmentReserveKey> {
    // 减值准备表相关操作
}
```

#### 2.2 默认数据源
```java
@Repository
public interface ConsistencyCheckRepository extends JpaRepository<OverdueDebtAdd, OverdueDebtAdd.OverdueDebtAddKey> {
    // 一致性检查相关操作
}
```

---

## 🔧 重构建议

### 1. Repository重复问题

#### 1.1 问题识别
- **重复接口**: api-gateway和data-access模块存在相同功能的Repository
- **命名冲突**: 同名Repository在不同包中
- **维护困难**: 相同功能的SQL查询分散在多个地方

#### 1.2 解决方案
```java
// 建议：统一使用data-access模块的Repository
// 删除api-gateway/src/main/java/com/laoshu198838/repository/下的重复接口
// 更新import语句指向data-access模块
```

### 2. SQL查询优化

#### 2.1 性能优化建议
- **缓存支持**: 为频繁查询添加`@Cacheable`注解
- **查询提示**: 使用`@QueryHints`优化查询性能
- **分页查询**: 大数据量查询使用`Pageable`参数

#### 2.2 查询标准化
```java
// 建议的查询模式
@Query(value = "SELECT ... FROM table WHERE ...", nativeQuery = true)
@QueryHints({
    @QueryHint(name = "org.hibernate.cacheable", value = "true"),
    @QueryHint(name = "org.hibernate.readOnly", value = "true")
})
@Cacheable(value = "cacheName", key = "#param1 + '_' + #param2")
Map<String, Object> getOptimizedData(@Param("param1") String param1, @Param("param2") String param2);
```

### 3. 模块整合建议

#### 3.1 Repository统一化
1. **保留**: `data-access`模块作为唯一数据访问层
2. **迁移**: 将`mysql_data`模块的Repository迁移到`data-access`
3. **删除**: 清理`api-gateway`模块中的重复Repository

#### 3.2 包结构优化
```
data-access/src/main/java/com/laoshu198838/repository/
├── overdue_debt/           # 逾期债权相关
├── kingdee/               # 金蝶系统相关
├── system/                # 系统管理相关
└── custom/                # 自定义查询相关
```

### 4. 数据源管理优化

#### 4.1 配置简化
- 统一数据源配置到`data-access`模块
- 简化多数据源切换逻辑
- 优化连接池配置

#### 4.2 事务管理
- 统一事务管理策略
- 避免跨数据源事务问题
- 优化事务传播行为

---

## 📈 重构优先级

### 高优先级
1. **清理重复Repository**: 避免维护混乱
2. **统一数据源配置**: 简化配置管理
3. **SQL查询优化**: 提升系统性能

### 中优先级
1. **包结构重组**: 提高代码可读性
2. **缓存策略实施**: 优化查询性能
3. **事务管理优化**: 确保数据一致性

### 低优先级
1. **代码文档完善**: 提高可维护性
2. **单元测试补充**: 确保重构质量
3. **监控指标添加**: 便于性能调优

---

## 📋 详细Repository清单

### 1. data-access模块Repository详情

#### 1.1 核心业务Repository

**ImpairmentReserveRepository** (减值准备表)
```java
// 位置: data-access/src/main/java/com/laoshu198838/repository/overdue_debt/
// 主要功能: 减值准备数据管理
// 特殊查询:
- updateAnnualCumulativeRecovery(): 批量更新年度累计回收
- 使用窗口函数计算累计值
- @Modifying + @Transactional 批量更新
```

**LitigationClaimRepository** (诉讼表)
```java
// 位置: data-access/src/main/java/com/laoshu198838/repository/overdue_debt/
// 主要功能: 诉讼债权数据管理
// 特殊查询:
- findByCreditorAndDebtor(): 按债权人债务人查询
- countByYearAndMonthForExists(): 检查年月数据存在性
- updateAnnualCumulativeRecovery(): 年度累计回收更新
```

**NonLitigationClaimRepository** (非诉讼表)
```java
// 位置: data-access/src/main/java/com/laoshu198838/repository/overdue_debt/
// 主要功能: 非诉讼债权数据管理
// 特殊查询:
- updateAnnualCumulativeRecovery(): 批量更新年度累计回收
- 复杂的窗口函数查询
- 支持月度数据复制逻辑
```

**OverdueDebtAddRepository** (新增表)
```java
// 位置: data-access/src/main/java/com/laoshu198838/repository/overdue_debt/
// 主要功能: 新增债权数据管理
// 特殊查询:
- findByCreditorAndDebtor(): 支持模糊查询和NULL参数
- 使用JPQL进行条件查询
- ORDER BY period DESC 排序
```

**OverdueDebtDecreaseRepository** (处置表)
```java
// 位置: data-access/src/main/java/com/laoshu198838/repository/overdue_debt/
// 主要功能: 债权处置数据管理
// 注释的查询方法:
- findByUpdateTimeAfter(): 按更新时间查询
- findUnprocessedRecords(): 查找未处理记录
- findByDebtor(): 按债务人查询
```

#### 1.2 专用查询Repository

**ConsistencyCheckRepository** (一致性检查)
```java
// 位置: data-access/src/main/java/com/laoshu198838/repository/overdue_debt/
// 主要功能: 跨表数据一致性检查
// 复杂查询:
- getNewAmountSummary(): 新增金额汇总
- getDisposedAmountSummary(): 处置金额汇总
- getEndingBalanceSummary(): 期末余额汇总
- getInitialBalanceSummary(): 年初余额汇总
- 大量子查询和JOIN操作
```

**OptimizedConsistencyCheckRepository** (优化版一致性检查)
```java
// 位置: data-access/src/main/java/com/laoshu198838/repository/overdue_debt/
// 主要功能: 性能优化的一致性检查
// 优化特性:
- @QueryHints 查询提示
- @Cacheable 缓存支持
- 分页查询支持 (Pageable)
- JOIN替代子查询提升性能
- 批量获取多月份数据
```

### 2. api-gateway模块Repository详情

#### 2.1 重复Repository (需要清理)

**ConsistencyCheckRepository** (重复)
```java
// 位置: api-gateway/src/main/java/com/laoshu198838/repository/custom/
// 状态: 与data-access模块重复
// 建议: 删除，使用data-access版本
```

**OverdueDebtSummaryRepository** (重复)
```java
// 位置: api-gateway/src/main/java/com/laoshu198838/repository/custom/
// 状态: 与data-access模块重复
// 建议: 删除，使用data-access版本
```

#### 2.2 系统管理Repository

**UserRepository** (用户管理)
```java
// 位置: api-gateway/src/main/java/com/laoshu198838/repository/
// 功能: 用户数据访问
// 方法: findByUsername()
// 状态: 与data-access模块重复，建议迁移
```

**RoleRepository** (角色管理)
```java
// 位置: api-gateway/src/main/java/com/laoshu198838/repository/
// 功能: 角色数据访问
// 方法: findByRoleId()
// 状态: 与data-access模块重复，建议迁移
```

### 3. mysql_data模块Repository详情

#### 3.1 待迁移Repository

**NonLitigationClaimRepository** (非诉讼表)
```java
// 位置: mysql_data/src/main/java/com/laoshu198838/repository/mysql/
// 状态: 待迁移到data-access模块
// 功能: 与data-access版本相同
// 特殊查询: updateAnnualCumulativeRecovery()
```

**OverdueDebtDecreaseRepository** (处置表)
```java
// 位置: mysql_data/src/main/java/com/laoshu198838/repository/mysql/
// 状态: 待迁移到data-access模块
// 注释: 大量注释的查询方法
// 建议: 评估注释方法的必要性后迁移
```

---

## 🔍 SQL查询复杂度分析

### 1. 高复杂度查询

#### 1.1 跨表一致性检查
**复杂度**: ⭐⭐⭐⭐⭐
**位置**: ConsistencyCheckRepository
**特点**:
- 涉及4-5个表的JOIN操作
- 多层子查询嵌套
- 复杂的CASE WHEN逻辑
- 聚合函数和窗口函数混合使用

**示例查询结构**:
```sql
WITH combined_data AS (
    SELECT ... FROM 新增表 a
    UNION ALL
    SELECT ... FROM 诉讼表 l
    UNION ALL
    SELECT ... FROM 非诉讼表 n
    UNION ALL
    SELECT ... FROM 减值准备表 i
)
SELECT
    debtor, creditor, isLitigation,
    SUM(CASE WHEN addTableAmount IS NOT NULL THEN addTableAmount ELSE 0 END) as addTableAmount,
    SUM(CASE WHEN litigationAmount IS NOT NULL THEN litigationAmount ELSE 0 END) as litigationAmount,
    -- 更多复杂逻辑
FROM combined_data
GROUP BY debtor, creditor, isLitigation
HAVING ABS(addTableAmount - COALESCE(...)) > 0.01
```

#### 1.2 窗口函数批量更新
**复杂度**: ⭐⭐⭐⭐
**位置**: ImpairmentReserveRepository, NonLitigationClaimRepository
**特点**:
- 窗口函数计算累计值
- 自连接更新操作
- 分区和排序逻辑

**查询模式**:
```sql
UPDATE 表名 t1
JOIN (
    SELECT 主键字段,
           SUM(金额字段) OVER (
               PARTITION BY 分组字段
               ORDER BY 排序字段
               ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
           ) AS 累计值
    FROM 表名
) t2 ON 连接条件
SET t1.目标字段 = t2.累计值
WHERE 更新条件
```

### 2. 中等复杂度查询

#### 2.1 汇总统计查询
**复杂度**: ⭐⭐⭐
**位置**: OverdueDebtSummaryRepository
**特点**:
- 简单聚合函数
- COALESCE处理NULL值
- 单表或简单JOIN

#### 2.2 条件查询
**复杂度**: ⭐⭐
**位置**: 各Repository的基础查询方法
**特点**:
- JPQL条件查询
- 参数化查询
- 简单的WHERE条件

### 3. 低复杂度查询

#### 3.1 基础CRUD操作
**复杂度**: ⭐
**位置**: 继承自JpaRepository的默认方法
**特点**:
- Spring Data JPA自动生成
- 标准的增删改查操作

---

## 📊 性能优化分析

### 1. 已实施的优化

#### 1.1 查询提示 (@QueryHints)
```java
@QueryHints({
    @QueryHint(name = "org.hibernate.cacheable", value = "true"),
    @QueryHint(name = "org.hibernate.readOnly", value = "true")
})
```

#### 1.2 缓存支持 (@Cacheable)
```java
@Cacheable(value = "endingBalanceSummary", key = "#year + '_' + #month")
```

#### 1.3 分页查询
```java
Page<Map<String, Object>> findNewAmountDetailWithPagination(@Param("year") int year, Pageable pageable);
```

### 2. 待优化项目

#### 2.1 索引优化建议
- 为频繁查询的字段添加数据库索引
- 复合索引优化多字段查询
- 分析慢查询日志

#### 2.2 查询重构建议
- 将复杂子查询改为JOIN操作
- 减少不必要的数据传输
- 使用批量操作替代循环查询

#### 2.3 缓存策略扩展
- 为更多查询添加缓存支持
- 实施分布式缓存
- 缓存失效策略优化

---

## 🔧 重构实施计划

### 阶段1: 清理重复Repository (1-2天)

#### 1.1 识别重复
- [ ] 对比api-gateway和data-access模块的Repository
- [ ] 确认功能完全重复的接口
- [ ] 检查依赖关系

#### 1.2 迁移和删除
- [ ] 更新Service层的import语句
- [ ] 删除api-gateway模块的重复Repository
- [ ] 更新Spring Boot扫描配置

#### 1.3 测试验证
- [ ] 运行单元测试
- [ ] 集成测试验证
- [ ] 功能回归测试

### 阶段2: 统一数据源配置 (2-3天)

#### 2.1 配置整合
- [ ] 统一数据源配置到data-access模块
- [ ] 简化多数据源切换逻辑
- [ ] 优化连接池配置

#### 2.2 注解标准化
- [ ] 统一@DataSource注解使用
- [ ] 清理不必要的数据源切换
- [ ] 文档化数据源使用规范

### 阶段3: SQL查询优化 (3-5天)

#### 3.1 性能分析
- [ ] 分析慢查询
- [ ] 识别性能瓶颈
- [ ] 制定优化方案

#### 3.2 查询重构
- [ ] 复杂查询优化
- [ ] 添加缓存支持
- [ ] 实施分页查询

#### 3.3 监控和测试
- [ ] 性能测试
- [ ] 监控指标收集
- [ ] 优化效果评估

### 阶段4: 包结构重组 (1-2天)

#### 4.1 目录重构
- [ ] 按功能重组Repository包结构
- [ ] 更新包名和import语句
- [ ] 更新文档和注释

#### 4.2 代码规范
- [ ] 统一命名规范
- [ ] 添加详细注释
- [ ] 完善JavaDoc文档

---

---

## 🏢 Service层架构分析

### 1. 业务服务分布

#### 1.1 api-gateway模块Service层
**路径**: `api-gateway/src/main/java/com/laoshu198838/service/`

| Service类 | 功能描述 | 依赖Repository | 状态 |
|-----------|----------|----------------|------|
| `DataConsistencyCheckService` | 数据一致性检查 | ConsistencyCheckRepository | 核心服务 |
| `ExcelExportService` | Excel导出服务 | 多个Repository | 报表功能 |
| `PasswordUpdateService` | 密码更新服务 | UserRepository | 系统管理 |
| `OverdueDebtService` | 逾期债权服务 | 多个债权Repository | 核心业务 |

#### 1.2 services/debt-management Service层
**路径**: `services/debt-management/src/main/java/com/laoshu198838/service/`

| Service类 | 功能描述 | 特点 | 状态 |
|-----------|----------|------|------|
| `DebtManagementService` | 债权管理统一服务 | 实现BusinessService接口 | 重构后 |
| `BusinessService<T, ID>` | 统一业务服务接口 | 泛型接口定义 | 架构接口 |

#### 1.3 data-processing模块Service层
**路径**: `data-processing/src/main/java/com/laoshu198838/service/`

| Service类 | 功能描述 | 调度特点 | 状态 |
|-----------|----------|----------|------|
| `OverdueDebtUpdateService` | 逾期债权更新服务 | @Scheduled定时任务 | 核心调度 |
| `NonLitigationOverdueUpdateService` | 非诉讼逾期更新 | 月度数据复制 | 专用服务 |
| `ImpairmentReserveService` | 减值准备服务 | 数据同步监控 | 专用服务 |

### 2. Service层依赖关系

#### 2.1 DebtManagementService依赖图
```java
DebtManagementService
├── OverdueDebtAddService (旧版本)
├── OverdueDebtDecreaseService (旧版本)
├── RefactoredOverdueDebtAddService (重构版本)
├── RefactoredOverdueDebtDecreaseService (重构版本)
├── OverdueDebtService
└── DataConsistencyCheckService
```

#### 2.2 定时任务Service依赖
```java
OverdueDebtUpdateService
├── NonLitigationOverdueUpdateService
├── ImpairmentReserveService
└── LitigationOverdueUpdateService (隐含)
```

### 3. Service层设计模式

#### 3.1 统一业务接口模式
```java
public interface BusinessService<T, ID> {
    T save(T entity);
    T update(T entity);
    void delete(ID id);
    T findById(ID id);
    List<T> findAll();
}
```

#### 3.2 事务服务分离模式
```java
// 主服务类
@Service
public class ImpairmentReserveService {
    @Autowired
    private ImpairmentReserveTransactionalService transactionalService;

    public int copyLastMonthData() {
        return transactionalService.copyLastMonthData();
    }
}

// 事务服务类
@Service
@Transactional
public class ImpairmentReserveTransactionalService {
    // 具体的事务操作
}
```

---

## 🎮 Controller层架构分析

### 1. Controller分布

#### 1.1 api-gateway模块Controller层
**路径**: `api-gateway/src/main/java/com/laoshu198838/controller/`

| Controller类 | 路径映射 | 功能描述 | 跨域配置 |
|-------------|----------|----------|----------|
| `AuthController` | `/api/auth` | 用户认证登录 | ✅ |
| `ExcelExportController` | `/api/export` | Excel导出功能 | ✅ |
| `OverdueDebtGetController` | `/api/overdue-debt` | 债权查询接口 | ✅ |
| `OverdueDebtPostController` | `/api/overdue-debt` | 债权操作接口 | ✅ |
| `DataConsistencyController` | `/api/consistency` | 数据一致性检查 | ✅ |

#### 1.2 跨域配置标准
```java
@CrossOrigin(origins = {
    "http://localhost:3000",   // React开发服务器
    "http://localhost:3001",   // 备用端口
    "http://localhost:3002",   // 备用端口
    "http://localhost:5173",   // Vite开发服务器
    "http://localhost:8080"    // 生产环境
}, allowCredentials = "true")
```

### 2. API接口设计

#### 2.1 RESTful API规范
```java
// GET请求 - 查询数据
@GetMapping("/statistics")
public ResponseEntity<Map<String, Object>> getDebtStatistics(
    @RequestParam(required = false) String year,
    @RequestParam(required = false) String month,
    @RequestParam(required = false) String company
)

// POST请求 - 创建/更新数据
@PostMapping("/add")
public ResponseEntity<Map<String, Object>> addDebt(
    @RequestBody DebtAddRequest request
)

// DELETE请求 - 删除数据
@DeleteMapping("/{id}")
public ResponseEntity<Map<String, Object>> deleteDebt(
    @PathVariable String id
)
```

#### 2.2 响应格式标准
```java
// 成功响应
return ResponseEntity.ok()
    .body(Collections.singletonMap("data", result));

// 错误响应
return ResponseEntity.status(400)
    .body(Collections.singletonMap("error", "错误信息"));
```

### 3. 安全和异常处理

#### 3.1 认证机制
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @PostMapping("/login")
    public ResponseEntity<Map<String, String>> login(@RequestBody LoginRequest request) {
        // JWT token生成
    }

    @GetMapping("/protected")
    public ResponseEntity<Map<String, String>> getProtectedResource() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // 受保护资源访问
    }
}
```

#### 3.2 异常处理
```java
@ExceptionHandler(BadCredentialsException.class)
public ResponseEntity<Map<String, String>> handleBadCredentialsException(BadCredentialsException e) {
    logger.warn("Login failed: Invalid credentials", e);
    return ResponseEntity.status(401)
        .body(Collections.singletonMap("error", "Invalid username or password"));
}
```

---

## 📋 完整架构依赖图

### 1. 模块依赖关系
```
┌─────────────────────────────────────────────────────────────────┐
│                        FinancialSystem                          │
├─────────────────────────────────────────────────────────────────┤
│  Frontend (React/Vue)                                           │
│  ├── HTTP Requests                                              │
│  └── WebSocket (if any)                                         │
├─────────────────────────────────────────────────────────────────┤
│  api-gateway (Web Layer)                                         │
│  ├── Controllers (@RestController)                              │
│  │   ├── AuthController                                         │
│  │   ├── ExcelExportController                                  │
│  │   ├── OverdueDebtGetController                              │
│  │   └── OverdueDebtPostController                             │
│  ├── Services (@Service)                                        │
│  │   ├── DataConsistencyCheckService                           │
│  │   ├── ExcelExportService                                     │
│  │   └── PasswordUpdateService                                  │
│  └── Security Configuration                                     │
├─────────────────────────────────────────────────────────────────┤
│  services (Business Layer)                              │
│  ├── debt-management                                            │
│  │   ├── DebtManagementService                                  │
│  │   └── BusinessService<T, ID> (Interface)                     │
│  ├── account-management                                         │
│  ├── audit-management                                           │
│  └── report-management                                          │
├─────────────────────────────────────────────────────────────────┤
│  data-processing (Processing Layer)                             │
│  ├── Scheduled Services (@Scheduled)                            │
│  │   ├── OverdueDebtUpdateService                              │
│  │   ├── NonLitigationOverdueUpdateService                     │
│  │   └── ImpairmentReserveService                              │
│  └── Transactional Services (@Transactional)                   │
├─────────────────────────────────────────────────────────────────┤
│  data-access (Data Access Layer)                                │
│  ├── Repository Interfaces (@Repository)                        │
│  │   ├── overdue_debt/                                         │
│  │   │   ├── OverdueDebtAddRepository                          │
│  │   │   ├── LitigationClaimRepository                         │
│  │   │   ├── NonLitigationClaimRepository                      │
│  │   │   ├── ImpairmentReserveRepository                       │
│  │   │   └── ConsistencyCheckRepository                        │
│  │   └── custom/                                               │
│  ├── Data Source Configuration                                  │
│  │   ├── @DataSource("primary")                                │
│  │   └── @DataSource("secondary")                              │
│  └── Entity Classes (@Entity)                                   │
├─────────────────────────────────────────────────────────────────┤
│  common (Common Layer)                                          │
│  ├── Entity Definitions                                         │
│  ├── Utility Classes                                            │
│  ├── Exception Classes                                          │
│  └── Configuration Classes                                      │
├─────────────────────────────────────────────────────────────────┤
│  Database Layer                                                 │
│  ├── Primary Database (逾期债权数据库)                           │
│  │   ├── 新增表 (OverdueDebtAdd)                               │
│  │   ├── 处置表 (OverdueDebtDecrease)                          │
│  │   ├── 诉讼表 (LitigationClaim)                              │
│  │   ├── 非诉讼表 (NonLitigationClaim)                         │
│  │   ├── 减值准备表 (ImpairmentReserve)                        │
│  │   └── 汇总表 (OverdueDebtSummary)                           │
│  └── Secondary Database (金蝶数据库)                            │
│      ├── 用户表 (Users)                                         │
│      ├── 角色表 (Roles)                                         │
│      └── 其他业务表                                             │
└─────────────────────────────────────────────────────────────────┘
```

### 2. 数据流向图
```
Frontend Request
    ↓
Controller Layer (api-gateway)
    ↓
Service Layer (services/api-gateway)
    ↓
Repository Layer (data-access)
    ↓
Database Layer (MySQL)
    ↓
Response Data
    ↓
Frontend Display
```

### 3. 定时任务流向图
```
@Scheduled Trigger
    ↓
OverdueDebtUpdateService (data-processing)
    ↓
├── NonLitigationOverdueUpdateService
├── ImpairmentReserveService
└── LitigationOverdueUpdateService
    ↓
Repository Layer (data-access)
    ↓
Database Updates
    ↓
Log Output
```

---

*本文档为FinancialSystem项目重构提供详细的Repository和SQL查询分析，包含完整的Service层和Controller层架构分析，建议在重构前仔细阅读并制定详细的实施计划。*
