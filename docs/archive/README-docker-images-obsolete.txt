FinancialSystem Docker镜像手动部署包 (已过时)
====================================

⚠️  注意：此文档已过时，项目已采用自动化部署方式 ⚠️
⚠️  此文件已移动到归档目录，仅供历史参考 ⚠️

本目录包含FinancialSystem项目所需的所有Docker镜像文件，用于在Linux生产服务器上手动部署。

文件清单：
- mysql-8.0-amd64.tar (444MB) - MySQL 8.0数据库镜像
- nginx-alpine-amd64.tar (41MB) - Nginx Web服务器镜像
- node-18-alpine-amd64.tar (86MB) - Node.js 18运行环境镜像
- openjdk-21-jdk-amd64.tar (501MB) - OpenJDK 21 Java运行环境镜像
- maven-3.9-temurin-21-amd64.tar (460MB) - Maven构建工具镜像
- load-docker-images.sh - 手动加载指南（不再自动执行）
- verify-checksums.sh - 文件完整性校验脚本
- checksums.md5 - MD5校验和文件
- Docker镜像传输部署指南.md - 详细部署指南（已更新为手动模式）
- 手动部署步骤指南.md - 新增：完整的手动部署步骤
- README.txt - 本说明文件

总大小：约1.5GB

手动部署步骤：
1. 使用U盘将所有文件传输到Linux服务器的 /home/<USER>/下载/ 目录
2. SSH连接到Linux服务器：ssh admin@10.25.1.85
3. 查看手动部署指南：cat /home/<USER>/下载/手动部署步骤指南.md
4. 按照指南逐步手动执行每个命令

重要说明：
- 不再使用自动化脚本，所有步骤需要手动执行
- 每个命令执行后请检查输出，确认无错误
- 建议使用U盘传输，避免网络连接问题
- 详细步骤请参考：手动部署步骤指南.md

注意事项：
- 所有镜像都是AMD64架构，适用于Linux x86_64系统
- 确保Linux服务器有足够的磁盘空间（至少3GB可用空间）
- 建议在传输前验证文件MD5校验和
- 部署过程中请勿中断操作

生成时间：2025-06-20
版本：v2.0 - 手动部署版本
