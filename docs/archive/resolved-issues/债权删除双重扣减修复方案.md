# 债权删除双重扣减修复方案

## 问题总结

根据深入分析，发现导致双重扣减的主要原因包括：

1. **定时任务干扰**：每3分钟执行的定时任务可能在删除操作过程中重新计算余额
2. **前端防重复点击不足**：用户可能快速点击两次删除按钮
3. **余额计算逻辑复杂**：存在多个地方更新余额，可能造成重复计算

## 已实施的修复

### 1. 临时禁用定时任务

文件：`/api-gateway/src/main/java/com/laoshu198838/service/DebtUpdateSchedulerProxy.java`

```java
// @Scheduled(fixedRate = 180000, initialDelay = 10000) // 临时禁用
public void scheduleDebtUpdate() {
    // ...
}
```

### 2. 前端防重复点击

文件：`/FinancialSystem-web/src/layouts/debtmanagement/pages/OverdueReductionUpdate.js`

```javascript
const handleDeleteRecord = async record => {
    // 检查是否已经在处理中，防止重复点击
    if (deletingRecordIds[record.id]) {
        console.log('该记录已经在删除中，忽略重复请求');
        return;
    }
    // ...
}
```

### 3. 添加详细日志

文件：`/services/debt-management/src/main/java/com/laoshu198838/service/DebtDeletionService.java`

```java
// 在创建负数记录前后记录处置总额
BigDecimal beforeDisposal = calculateTotalDisposalForMonth(dto);
log.info("创建负数处置记录前，当月处置总额: {}", beforeDisposal);

// 创建并保存负数记录
OverdueDebtDecrease negativeRecord = createNegativeDisposalRecord(dto);
overdueDebtDecreaseRepository.save(negativeRecord);
log.info("已创建并保存负数处置记录: 金额={}", negativeRecord.getMonthlyReduceAmount());

// 记录创建后的处置总额
BigDecimal afterDisposal = calculateTotalDisposalForMonth(dto);
log.info("创建负数处置记录后，当月处置总额: {} (变化: {})", afterDisposal, 
        DebtCalculationUtils.safeSubtract(afterDisposal, beforeDisposal));
```

## 测试验证步骤

1. **重启系统**
   ```bash
   # 停止当前运行的系统
   # 重新启动后端服务
   cd api-gateway && mvn spring-boot:run
   ```

2. **执行测试操作**
   - 新增一个处置金额（如126万）
   - 查看减值准备表中的余额
   - 删除该处置记录
   - 再次查看余额，确认是否正确恢复

3. **检查日志**
   观察控制台日志，特别关注：
   - "创建负数处置记录前，当月处置总额"
   - "创建负数处置记录后，当月处置总额"
   - "查询到X条处置记录"
   - 每条处置记录的金额和备注

4. **使用测试脚本**
   ```bash
   # 设置JWT Token（从浏览器开发者工具获取）
   export JWT_TOKEN="your-jwt-token-here"
   
   # 运行测试脚本
   ./scripts/test-double-deduction.sh
   ```

## 进一步的优化建议

### 1. 数据库级别的原子操作

使用存储过程或数据库触发器来确保余额计算的原子性：

```sql
-- 创建存储过程处理删除操作
DELIMITER //
CREATE PROCEDURE DeleteDisposalRecord(
    IN p_creditor VARCHAR(50),
    IN p_debtor VARCHAR(50),
    IN p_year INT,
    IN p_month INT,
    IN p_amount DECIMAL(20,2)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 插入负数处置记录
    INSERT INTO 处置表 (债权人, 债务人, 年份, 月份, 每月处置金额, 备注, 更新时间)
    VALUES (p_creditor, p_debtor, p_year, p_month, -p_amount, '删除处置记录', NOW());
    
    -- 更新减值准备表余额
    UPDATE 减值准备表
    SET 本月末债权余额 = 本月末债权余额 + p_amount,
        本月处置债权 = 本月处置债权 - p_amount,
        更新时间 = NOW()
    WHERE 债权人 = p_creditor 
      AND 债务人 = p_debtor 
      AND 年份 = p_year 
      AND 月份 = p_month;
    
    COMMIT;
END//
DELIMITER ;
```

### 2. 使用分布式锁

在删除操作前获取分布式锁，防止并发操作：

```java
@Service
public class DebtDeletionService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    public DebtDeletionResult deleteDebt(DebtDeletionDTO dto) {
        String lockKey = String.format("debt:deletion:lock:%s:%s:%d:%d", 
            dto.getCreditor(), dto.getDebtor(), dto.getYear(), dto.getMonth());
        
        // 尝试获取锁，超时时间5秒
        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, "locked", 5, TimeUnit.SECONDS);
            
        if (!acquired) {
            return DebtDeletionResult.failure("操作正在进行中，请稍后重试");
        }
        
        try {
            // 执行删除操作
            return doDeleteDebt(dto);
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }
}
```

### 3. 添加幂等性支持

为每个删除操作生成唯一ID，防止重复执行：

```java
@Entity
@Table(name = "删除操作记录")
public class DeletionOperation {
    @Id
    private String operationId;
    
    private String creditor;
    private String debtor;
    private Integer year;
    private Integer month;
    private BigDecimal amount;
    private LocalDateTime operationTime;
    private String status;
}
```

### 4. 优化定时任务

如果需要恢复定时任务，应该：

1. 添加任务执行锁
2. 跳过正在处理中的记录
3. 优化执行时间（如改为凌晨执行）

```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void scheduleDebtUpdate() {
    if (!taskLockService.acquireLock("debt-update-task")) {
        logger.info("任务正在执行中，跳过本次执行");
        return;
    }
    
    try {
        overdueDebtUpdateService.scheduleOverdueYearUpdate();
    } finally {
        taskLockService.releaseLock("debt-update-task");
    }
}
```

## 监控和预警

建议添加以下监控指标：

1. **余额异常监控**：当余额出现负数时立即预警
2. **操作频率监控**：同一记录短时间内多次操作时预警
3. **日志监控**：监控"双重扣减"相关的错误日志

## 结论

通过以上修复和优化，可以有效解决债权删除双重扣减的问题。建议先验证基础修复的效果，再根据实际需要实施进一步的优化方案。