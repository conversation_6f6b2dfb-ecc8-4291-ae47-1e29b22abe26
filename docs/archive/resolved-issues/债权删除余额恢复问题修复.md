# 债权删除余额恢复问题修复报告

## 问题描述

### 问题1：余额字段混淆
在删除处置债权记录后，系统没有正确恢复债权余额。具体表现为：
- 删除2月份126万的处置记录后，后续月份出现了-25.2万的余额，而不是恢复到原来的126万

### 问题2：双重扣减
新增处置金额126万后删除，本月减少债权出现了双倍扣减（减少了2个126万），导致余额变成-126万

## 问题原因

### 原因1：字段混淆

经过分析，发现问题出在减值准备表的字段混淆：

1. **字段理解错误**：
   - `本月末余额` (currentMonthAmount)：这是**减值准备金额**
   - `本月末债权余额` (currentMonthBalance)：这才是真正的**债权余额**

2. **余额传递错误**：
   - 原代码在计算减值准备余额时，使用了当前记录的 `getLastMonthAmount()`
   - 这导致跨月份更新时，无法正确获取上个月的减值准备余额

### 原因2：双重更新

在删除处置债权的流程中，存在两次余额更新：

1. **第一次更新**：
   ```java
   // 更新减值准备表
   updateImpairmentReserveForDeletion(dto, "DISPOSAL", affectedRecords);
   // 更新诉讼/非诉讼表
   updateLitigationClaimForDeletion/updateNonLitigationClaimForDeletion
   ```
   这些方法会调用 `FiveTableUpdateHelper` 更新各表的处置金额

2. **第二次更新**：
   ```java
   // 重新计算删除月份的余额
   recalculateBalancesForMonth(dto);
   ```
   这个方法会重新计算所有处置总额，包括刚插入的负数记录

这导致了负数处置金额被计算了两次，造成双倍扣减。

## 解决方案

### 方案1：修复余额字段混淆

创建了两个独立的方法：
- `getLastMonthBalance()`：获取上月的**债权余额**
- `getLastMonthProvisionBalance()`：获取上月的**减值准备余额**

修正余额更新逻辑：

```java
// 获取上月债权余额
BigDecimal lastMonthBalance = getLastMonthBalance(dto);

// 获取上月的减值准备余额
BigDecimal lastMonthProvisionBalance = getLastMonthProvisionBalance(dto);

// 更新上月末余额字段（债权余额）
record.setLastMonthBalance(lastMonthBalance);

// 更新上月末减值准备余额
record.setPreviousMonthBalance(lastMonthProvisionBalance);

// 更新本月末债权余额
record.setCurrentMonthBalance(currentMonthBalance);

// 更新本月末减值准备余额（基于上月的减值准备余额）
BigDecimal currentProvisionBalance = DebtCalculationUtils.safeAdd(lastMonthProvisionBalance, monthlyChange);
record.setCurrentMonthAmount(currentProvisionBalance);
```

### 方案2：消除双重更新

移除了重复的更新步骤，统一使用 `recalculateBalancesForMonth` 方法：

**删除处置债权的优化流程**：
```java
// 1. 创建负数处置记录
OverdueDebtDecrease negativeRecord = createNegativeDisposalRecord(dto);
overdueDebtDecreaseRepository.save(negativeRecord);

// 2. 更新新增表的处置金额
updateAddTableDisposalAmount(dto, affectedRecords);

// 3. 重新计算删除月份的余额（统一更新所有相关表）
recalculateBalancesForMonth(dto);

// 4. 处理后续月份更新
if (needsSubsequentMonthsUpdate(dto)) {
    updateSubsequentMonthsWithBalanceTransfer(dto, affectedRecords);
}
```

删除了原来的 `updateImpairmentReserveForDeletion`、`updateLitigationClaimForDeletion` 等单独更新方法的调用，避免重复计算。

### 3. 跨年处理

对于1月份的数据，需要从去年12月获取余额：
```java
if (dto.getMonth() == 1) {
    // 1月份的上月减值准备余额需要从去年12月获取
    return getProvisionBalanceFromPreviousYear(dto);
}
```

## 余额计算公式

### 债权余额计算
```
本月末债权余额 = 上月末债权余额 + 本月新增债权 - 本月处置债权
```

### 减值准备余额计算
```
本月末减值准备余额 = 上月末减值准备余额 + 本月增减
其中：本月增减 = 本月新增债权 - 本月处置债权
```

## 测试要点

1. **删除处置记录后**：
   - 删除月份的债权余额应该正确恢复
   - 后续月份的债权余额应该基于新的余额传递
   - 减值准备余额也应该同步调整

2. **跨年测试**：
   - 测试1月份的数据是否能正确获取去年12月的余额

3. **多月份连续测试**：
   - 测试删除3月份的处置后，4、5、6、7月的余额是否都正确更新

## 影响范围

- 减值准备表的余额更新逻辑
- 诉讼表和非诉讼表的余额传递（已在之前的修改中处理）
- 年度累计回收金额的计算

## 后续建议

1. **字段命名优化**：建议在实体类中添加注释，明确区分债权余额和减值准备余额
2. **单元测试**：添加针对余额恢复的单元测试用例
3. **数据验证**：在删除操作后，添加余额一致性验证