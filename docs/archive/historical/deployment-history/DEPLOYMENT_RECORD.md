# FinancialSystem 生产环境部署记录

## 🚀 部署环境信息

- **服务器**: 10.25.1.85 (AlmaLinux 9)
- **用户**: admin / Wrkj2520.
- **Git仓库**: `/opt/FinancialSystem/FinancialSystem.git`
- **部署目录**: `/opt/FinancialSystem/deploy`
- **前端项目**: FinancialSystem-web
- **后端项目**: FinancialSystem (多模块Maven项目)

## 📝 部署实施记录

### 2025-06-19 11:00-12:00 部署环境配置完成

**执行的操作步骤：**

#### 1. SSH连接服务器
```bash
ssh admin@10.25.1.85
# 密码: Wrkj2520.
```

#### 2. 安装必要软件
```bash
# 安装Docker Compose插件和Git
sudo dnf install docker-compose-plugin git -y

# 验证安装
docker compose version  # v2.36.2
git --version           # 2.47.1
```

#### 3. 启动Docker服务
```bash
sudo systemctl enable docker && sudo systemctl start docker
sudo systemctl status docker  # 确认运行状态
```

#### 4. 配置防火墙端口
```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp --add-port=8080/tcp --add-port=3306/tcp
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
# 输出: 80/tcp 3306/tcp 8080/tcp
```

#### 5. 创建部署目录和Git仓库
```bash
# 创建部署目录
sudo mkdir -p /opt/FinancialSystem && sudo chown admin:admin /opt/FinancialSystem
cd /opt/FinancialSystem

# 初始化裸Git仓库
git init --bare FinancialSystem.git
```

#### 6. 配置自动部署钩子
```bash
# 创建post-receive钩子
cat > FinancialSystem.git/hooks/post-receive << 'EOF'
#!/bin/bash

# FinancialSystem 自动部署脚本
echo "🚀 开始自动部署 FinancialSystem..."

# 设置变量
DEPLOY_DIR="/opt/FinancialSystem/deploy"
GIT_DIR="/opt/FinancialSystem/FinancialSystem.git"

# 创建部署目录
mkdir -p $DEPLOY_DIR

# 检出代码到部署目录
echo "📦 检出最新代码..."
cd $DEPLOY_DIR
git --git-dir=$GIT_DIR --work-tree=$DEPLOY_DIR checkout -f

# 停止现有服务
echo "🛑 停止现有服务..."
docker compose down 2>/dev/null || true

# 启动新服务
echo "🔄 启动新服务..."
docker compose up -d --build

echo "✅ 部署完成！"
echo "📝 查看服务状态: docker compose ps"
echo "📊 查看日志: docker compose logs -f"
EOF

# 设置执行权限
chmod +x FinancialSystem.git/hooks/post-receive
```

#### 7. 创建部署检查脚本
```bash
cat > check-deployment.sh << 'EOF'
#!/bin/bash

echo "🔍 FinancialSystem 部署状态检查"
echo "================================"

echo "📁 Git 仓库状态:"
ls -la FinancialSystem.git/

echo ""
echo "🐳 Docker 服务状态:"
sudo systemctl status docker --no-pager -l

echo ""
echo "🔥 防火墙端口状态:"
sudo firewall-cmd --list-ports

echo ""
echo "💾 磁盘空间:"
df -h /opt

echo ""
echo "🌐 网络连接测试:"
ss -tlnp | grep -E ':(80|8080|3306)'

echo ""
echo "📊 系统资源:"
free -h

echo ""
echo "✅ 部署环境已就绪！"
echo "📝 Git 仓库地址: admin@10.25.1.85:/opt/FinancialSystem/FinancialSystem.git"
echo "🚀 推送代码即可自动部署"
EOF

chmod +x check-deployment.sh
```

**配置结果验证：**
- ✅ Docker Engine v28.2.2 运行正常
- ✅ Docker Compose Plugin v2.36.2 已安装
- ✅ Git 2.47.1 已安装
- ✅ 防火墙端口 80, 8080, 3306 已开放
- ✅ 系统资源充足 (15GB内存, 65GB可用磁盘)
- ✅ 自动部署钩子配置完成
- ✅ 部署检查脚本创建完成

## 📋 已完成的环境配置

### 1. 系统环境
- ✅ Docker Engine 已安装并启动
- ✅ Docker Compose Plugin v2.36.2 已安装
- ✅ Git 2.47.1 已安装
- ✅ 防火墙端口已开放 (80, 8080, 3306)

### 2. Git 仓库配置
- ✅ 裸仓库已创建: `/opt/FinancialSystem/FinancialSystem.git`
- ✅ 自动部署钩子已配置: `post-receive`
- ✅ 权限已设置: `admin:admin`

### 3. 自动部署流程
当代码推送到服务器时，会自动执行：
1. 检出最新代码到部署目录
2. 停止现有 Docker 服务
3. 构建并启动新的 Docker 服务

## 🔧 使用方法

### 从本地推送代码部署

```bash
# 添加生产环境远程仓库
git remote add production admin@10.25.1.85:/opt/FinancialSystem/FinancialSystem.git

# 推送代码到生产环境（自动触发部署）
git push production main
```

### 手动部署检查

```bash
# SSH 登录服务器
ssh admin@10.25.1.85

# 进入部署目录
cd /opt/FinancialSystem

# 运行部署状态检查
./check-deployment.sh

# 查看 Docker 服务状态
cd deploy
docker compose ps
docker compose logs -f
```

## 📊 系统资源

- **CPU**: 充足
- **内存**: 15GB 总计，14GB 可用
- **磁盘**: 70GB 根分区，65GB 可用
- **网络**: 端口 80, 8080, 3306 已开放

## 🔍 故障排除

### 查看部署日志
```bash
cd /opt/FinancialSystem/deploy
docker compose logs -f
```

### 重新部署
```bash
cd /opt/FinancialSystem/deploy
docker compose down
docker compose up -d --build
```

### 检查 Git 钩子
```bash
ls -la /opt/FinancialSystem/FinancialSystem.git/hooks/
cat /opt/FinancialSystem/FinancialSystem.git/hooks/post-receive
```

## 📝 注意事项

1. 确保项目根目录包含 `docker-compose.yml` 文件
2. 数据库连接配置需要指向正确的服务
3. 环境变量配置需要适配生产环境
4. 首次部署可能需要较长时间下载镜像

## 🎯 项目部署实施

### 2025-06-19 12:30 项目配置优化

**Docker配置优化：**

1. **修改docker-compose.yml**
   - 分离构建和运行服务
   - 使用Maven官方镜像进行构建
   - 优化依赖关系和启动顺序

2. **更新部署脚本**
   - 自动配置生产仓库地址
   - 增强错误处理和状态检查
   - 添加部署后验证指导

**配置文件状态：**
- ✅ docker-compose.yml 已优化
- ✅ nginx.conf 配置完整
- ✅ init-db.sql 数据库初始化脚本就绪
- ✅ deploy.sh 部署脚本已更新

## 🚀 立即部署

### 方法1: 使用部署脚本（推荐）

```bash
# 设置执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

### 方法2: 手动部署

```bash
# 添加生产仓库（仅首次需要）
git remote add production admin@10.25.1.85:/opt/FinancialSystem/FinancialSystem.git

# 推送代码到生产环境
git push production main
```

### 部署后验证

```bash
# SSH登录生产服务器
ssh admin@10.25.1.85

# 检查部署状态
cd /opt/FinancialSystem && ./check-deployment.sh

# 查看Docker服务
cd deploy
docker compose ps
docker compose logs -f
```

## 📊 预期部署流程

1. **代码推送** → Git仓库接收代码
2. **自动触发** → post-receive钩子执行
3. **代码检出** → 最新代码部署到/opt/FinancialSystem/deploy
4. **服务停止** → docker compose down
5. **构建启动** → docker compose up -d --build
   - MySQL数据库启动
   - Maven构建后端JAR包
   - 后端服务启动
   - Node.js构建前端
   - Nginx提供Web服务

## 🔍 故障排除

### 问题1: MySQL镜像架构不匹配 (2025-06-19 21:40)
- **现象**: ARM64镜像无法在AMD64服务器上运行，容器启动失败
- **错误**: `exec format error`
- **解决方案**: 配置Docker镜像源，下载AMD64架构的MySQL镜像
- **当前状态**: 🔄 进行中 (网络连接不稳定)
- **详细信息**: 参见 `docs/guides/FinancialSystem_部署方案.md` 中的"实际部署问题记录"部分

### 查看部署日志
```bash
cd /opt/FinancialSystem/deploy
docker compose logs -f
```

### 重新部署
```bash
cd /opt/FinancialSystem/deploy
docker compose down
docker compose up -d --build
```

### 检查 Git 钩子
```bash
ls -la /opt/FinancialSystem/FinancialSystem.git/hooks/
cat /opt/FinancialSystem/FinancialSystem.git/hooks/post-receive
```

## 📝 注意事项

1. 确保项目根目录包含 `docker-compose.yml` 文件 ✅
2. 数据库连接配置需要指向正确的服务 ✅
3. 环境变量配置需要适配生产环境 ✅
4. 首次部署可能需要较长时间下载镜像

## 📚 相关文档

### 主要部署文档
- **`docs/guides/FinancialSystem_部署方案.md`** - 完整的部署方案和技术文档
  - 包含详细的架构设计和配置说明
  - 完整的故障排除指南和解决方案
  - MySQL架构兼容性问题的详细分析

### 快速参考
- **Docker架构问题**: 参见主部署文档中的"实际部署问题记录"部分
- **一键修复命令**: 参见主部署文档中的"快速参考"部分
- **故障排除清单**: 参见主部署文档中的"故障排除检查清单"

---

**部署环境已完全就绪，现在可以执行 `./deploy.sh` 开始自动部署！** 🚀

**如遇问题，请优先查阅 `docs/guides/FinancialSystem_部署方案.md` 获取详细的解决方案。**
