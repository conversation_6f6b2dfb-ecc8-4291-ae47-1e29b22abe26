# FinancialSystem 手动部署步骤指南

## 概述
本指南提供完全手动的部署步骤，避免使用自动化脚本，确保每一步都可控。

## 前提条件
- Linux服务器已安装Docker和Docker Compose
- 所有Docker镜像文件已传输到服务器
- 具有管理员权限

## 第一阶段：准备工作

### 1. 连接到Linux服务器
```bash
ssh admin@10.25.1.85
```

### 2. 检查Docker服务状态
```bash
systemctl status docker
```

如果Docker未运行，启动它：
```bash
sudo systemctl start docker
sudo systemctl enable docker
```

### 3. 检查磁盘空间
```bash
df -h
```
确保至少有5GB可用空间。

## 第二阶段：加载Docker镜像

### 1. 进入镜像文件目录
```bash
cd /home/<USER>/下载/
```

### 2. 检查镜像文件
```bash
pwd
ls -lh *.tar
```

应该看到以下文件：
- mysql-8.0-amd64.tar
- nginx-alpine-amd64.tar
- node-18-alpine-amd64.tar
- openjdk-21-jdk-amd64.tar
- maven-3.9-temurin-21-amd64.tar

### 3. 手动加载每个镜像（逐一执行）

**加载MySQL镜像：**
```bash
docker load -i mysql-8.0-amd64.tar
```
等待完成，应该看到类似输出：
```
Loaded image: mysql:8.0
```

**加载Nginx镜像：**
```bash
docker load -i nginx-alpine-amd64.tar
```

**加载Node.js镜像：**
```bash
docker load -i node-18-alpine-amd64.tar
```

**加载OpenJDK镜像：**
```bash
docker load -i openjdk-21-jdk-amd64.tar
```

**加载Maven镜像：**
```bash
docker load -i maven-3.9-temurin-21-amd64.tar
```

### 4. 验证镜像加载
```bash
docker images
```

应该看到以下镜像：
- mysql:8.0
- nginx:alpine
- node:18-alpine
- openjdk:21-jdk
- maven:3.9-eclipse-temurin-21

## 第三阶段：部署FinancialSystem

### 1. 进入部署目录
```bash
cd /home/<USER>/下载/FinancialSystem-Production-Deploy
```

### 2. 检查部署文件
```bash
ls -la
```

应该看到：
- docker-compose.yml
- deploy-complete.sh
- 其他配置文件

### 3. 查看Docker Compose配置（可选）
```bash
cat docker-compose.yml
```

### 4. 停止现有容器（如果存在）
```bash
docker compose down
```

### 5. 启动服务
```bash
docker compose up -d
```

### 6. 检查容器状态
```bash
docker ps -a
```

所有容器状态应该是"Up"。

### 7. 检查服务日志
```bash
# 查看所有服务日志
docker compose logs

# 查看特定服务日志
docker compose logs mysql
docker compose logs nginx
docker compose logs app
```

## 第四阶段：验证部署

### 1. 检查端口监听
```bash
netstat -tlnp | grep -E ':(80|3306|8080)'
```

### 2. 测试Web访问
```bash
curl -I http://localhost
```

### 3. 检查数据库连接
```bash
docker exec -it <mysql_container_name> mysql -u root -p
```

## 故障排除

### 如果镜像加载失败
1. 检查文件完整性：
```bash
ls -lh *.tar
md5sum *.tar
```

2. 检查Docker磁盘空间：
```bash
docker system df
```

3. 清理Docker缓存：
```bash
docker system prune -f
```

### 如果容器启动失败
1. 查看详细日志：
```bash
docker compose logs <service_name>
```

2. 检查端口冲突：
```bash
netstat -tlnp
```

3. 重新启动服务：
```bash
docker compose down
docker compose up -d
```

### 如果Web服务无法访问
1. 检查防火墙：
```bash
sudo ufw status
```

2. 检查Nginx配置：
```bash
docker exec -it <nginx_container> nginx -t
```

## 清理工作

### 删除镜像文件（可选）
部署成功后，可以删除.tar文件：
```bash
cd /home/<USER>/下载/
rm -f *.tar
```

### 备份配置
```bash
cp -r /home/<USER>/下载/FinancialSystem-Production-Deploy /home/<USER>/backup/
```

## 注意事项
1. 每个命令执行后请检查输出，确认无错误
2. 如遇到错误，请记录错误信息
3. 建议在执行前备份现有配置
4. 部署过程中请勿中断操作

---
生成时间：2025-06-20
版本：v1.0 - 手动部署版本
