# MySQL全数据库双向同步架构方案

## 📋 方案概述

本方案实现本地MySQL与Linux Docker MySQL之间的全数据库双向同步，支持自动发现和同步所有数据库，包括未来新增的数据库。

## 🏗️ 架构设计

### 核心架构图
```
┌─────────────────────────────────────────────────────────────────────┐
│                     全数据库双向同步架构                               │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  ┌──────────────────┐  binlog   ┌──────────────────┐               │
│  │   本地MySQL      │ ◄──────► │  Linux MySQL     │               │
│  │ (***********)    │    ALL    │ (**********)     │               │
│  │                  │    DBS    │                  │               │
│  │ ├─overdue_debt_db│           │ ├─overdue_debt_db│               │
│  │ ├─user_system    │           │ ├─user_system    │               │
│  │ ├─kingdee        │           │ ├─kingdee        │               │
│  │ └─future_db_*    │           │ └─future_db_*    │               │
│  │                  │           │                  │               │
│  │ server_id: 1     │           │ server_id: 2     │               │
│  └──────────────────┘           └──────────────────┘               │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │                   同步管理组件                              │   │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │   │
│  │ │ 自动发现    │ │ 冲突检测    │ │ 状态监控    │           │   │
│  │ │ 新数据库    │ │ 处理模块    │ │ 告警系统    │           │   │
│  │ └─────────────┘ └─────────────┘ └─────────────┘           │   │
│  └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

## 🎯 技术特性

### 1. 全数据库同步
- **自动同步所有数据库**：无需手动配置每个数据库
- **新数据库自动发现**：新建数据库自动纳入同步范围
- **统一管理**：一次配置，全局生效

### 2. 智能冲突处理
- **主键冲突避免**：不同服务器使用不同的AUTO_INCREMENT步长
- **时间戳冲突解决**：基于最后修改时间的冲突解决策略
- **业务规则优先**：支持自定义业务冲突解决规则

### 3. 高可用性
- **故障自动恢复**：网络中断后自动重连和数据同步
- **数据完整性保证**：事务一致性和数据校验
- **监控告警**：实时监控同步状态，异常自动告警

## ⚙️ 详细配置

### 1. MySQL服务器配置

#### 本地MySQL配置 (/etc/mysql/mysql.conf.d/replication.cnf)
```ini
[mysqld]
# 服务器标识
server_id = 1

# 二进制日志配置
log_bin = mysql-bin
binlog_format = ROW
sync_binlog = 1
binlog_expire_logs_seconds = 604800

# 全数据库复制配置
binlog_do_db = overdue_debt_db
binlog_do_db = user_system  
binlog_do_db = kingdee
# 支持动态添加新数据库
binlog_ignore_db = mysql
binlog_ignore_db = information_schema
binlog_ignore_db = performance_schema
binlog_ignore_db = sys

# AUTO_INCREMENT配置（避免主键冲突）
auto_increment_increment = 2
auto_increment_offset = 1

# 性能优化
slave_parallel_workers = 4
slave_parallel_type = LOGICAL_CLOCK
slave_preserve_commit_order = ON
slave_pending_jobs_size_max = 128M

# 网络优化
slave_net_timeout = 60
master_info_repository = TABLE
relay_log_info_repository = TABLE
```

#### Linux MySQL配置
```ini
[mysqld]
# 服务器标识
server_id = 2

# 二进制日志配置
log_bin = mysql-bin
binlog_format = ROW
sync_binlog = 1
binlog_expire_logs_seconds = 604800

# 全数据库复制配置
replicate_do_db = overdue_debt_db
replicate_do_db = user_system
replicate_do_db = kingdee
# 动态复制新数据库
replicate_wild_do_table = overdue_debt_db.%
replicate_wild_do_table = user_system.%
replicate_wild_do_table = kingdee.%

# AUTO_INCREMENT配置（避免主键冲突）
auto_increment_increment = 2
auto_increment_offset = 2

# 性能优化（同本地配置）
slave_parallel_workers = 4
slave_parallel_type = LOGICAL_CLOCK
slave_preserve_commit_order = ON
slave_pending_jobs_size_max = 128M
```

### 2. 复制用户配置

```sql
-- 在两个MySQL服务器上都执行
CREATE USER 'repl_universal'@'%' IDENTIFIED WITH mysql_native_password BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_universal'@'%';
GRANT SELECT ON *.* TO 'repl_universal'@'%';  -- 用于数据校验
FLUSH PRIVILEGES;
```

### 3. 复制关系配置

#### 本地MySQL作为Linux的从服务器
```sql
-- 在本地MySQL执行
CHANGE MASTER TO
  MASTER_HOST='**********',
  MASTER_USER='repl_universal',
  MASTER_PASSWORD='Zlb&198838',
  MASTER_PORT=3306,
  MASTER_AUTO_POSITION=1;  -- 使用GTID自动定位

START SLAVE;
```

#### Linux MySQL作为本地的从服务器
```sql
-- 在Linux MySQL执行
CHANGE MASTER TO
  MASTER_HOST='***********',
  MASTER_USER='repl_universal', 
  MASTER_PASSWORD='Zlb&198838',
  MASTER_PORT=3306,
  MASTER_AUTO_POSITION=1;

START SLAVE;
```

## 🚀 自动化脚本

### 1. 数据库发现和配置脚本

创建自动发现新数据库的脚本，我将在下一步提供完整的实现。

### 2. 冲突检测和解决

### 3. 监控和告警系统

## 📊 同步策略

### 1. 主键冲突预防
```sql
-- 本地MySQL (奇数ID)
auto_increment_increment = 2
auto_increment_offset = 1
-- 生成ID: 1, 3, 5, 7, 9...

-- Linux MySQL (偶数ID)  
auto_increment_increment = 2
auto_increment_offset = 2
-- 生成ID: 2, 4, 6, 8, 10...
```

### 2. 业务数据冲突处理
- **用户数据**：基于最后登录时间优先
- **债权数据**：基于最后修改时间优先
- **系统配置**：手动解决，发送告警

### 3. 新数据库处理流程
1. **自动发现**：定期扫描新建数据库
2. **权限配置**：自动为复制用户授权
3. **同步启用**：动态添加到复制配置
4. **验证测试**：自动测试新数据库同步

## 🔍 监控指标

### 关键监控项
- **复制延迟**：目标 < 5秒
- **IO/SQL线程状态**：必须为Running
- **GTID一致性**：定期校验
- **数据库列表同步**：新数据库发现延迟

### 告警阈值
- 复制延迟 > 10秒：警告
- 复制延迟 > 30秒：严重
- 复制线程停止：立即告警
- 数据不一致：立即告警

## 💡 优势总结

1. **零配置扩展**：新数据库自动同步
2. **高可靠性**：基于MySQL原生复制
3. **低延迟**：秒级数据同步
4. **易维护**：统一的管理界面
5. **安全性**：支持SSL加密和权限控制

## 🔄 实施路径

1. **阶段1**：配置基础双向复制
2. **阶段2**：实现自动发现机制  
3. **阶段3**：部署监控告警系统
4. **阶段4**：优化和性能调优

这个方案确保了所有数据库的一致性，同时具备良好的扩展性，是金融系统的理想选择。