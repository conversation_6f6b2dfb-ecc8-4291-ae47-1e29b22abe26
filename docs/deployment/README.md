# 财务系统部署指南

本指南提供了财务系统的完整部署流程，包括自动化部署和手动部署两种方式。

## 🚀 快速部署

### 自动化部署 (推荐)
```bash
# 1. 合并代码到main分支即可触发自动部署
git checkout main
git merge feature-branch

# 2. 系统会自动完成以下步骤：
#    - 代码备份
#    - 项目构建
#    - 服务部署
#    - 健康检查
```

### 手动部署
```bash
# 1. 执行部署脚本
./scripts/deploy/deploy.sh

# 2. 检查部署状态
./scripts/deploy/health-check.sh
```

## 📋 部署前准备

### 系统要求
- **操作系统**: Linux (CentOS/Ubuntu)
- **Java**: OpenJDK 21+
- **Node.js**: 18.x+
- **Docker**: 20.x+
- **MySQL**: 8.0+

### 环境配置
1. **数据库配置**
   ```sql
   CREATE DATABASE 逾期债权数据库 CHARACTER SET utf8mb4;
   CREATE USER 'root'@'%' IDENTIFIED BY 'Zlb&198838';
   GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';
   ```

2. **端口配置**
   - 前端: 80 (Nginx)
   - 后端: 8080 (Spring Boot)
   - 数据库: 3306 (MySQL)
   - Webhook: 9000 (CI/CD)

## 🔧 部署方式

### 方式一: Docker部署 (推荐)
```bash
# 1. 启动所有服务
docker-compose up -d

# 2. 查看服务状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f
```

### 方式二: 传统部署
```bash
# 1. 构建后端
cd api-gateway
mvn clean package -DskipTests

# 2. 构建前端
cd ../FinancialSystem-web
npm install
npm run build

# 3. 部署服务
./scripts/deploy/deploy.sh
```

## 📊 部署验证

### 健康检查
```bash
# 执行健康检查脚本
./scripts/deploy/health-check.sh

# 手动检查服务状态
curl http://localhost:8080/actuator/health
curl http://localhost/
```

### 功能验证
1. **登录测试**
   - 用户名: laoshu198838
   - 密码: Zlb&198838

2. **数据库连接测试**
   ```bash
   mysql -h localhost -u root -p逾期债权数据库
   ```

3. **API接口测试**
   ```bash
   curl http://localhost:8080/api/health
   ```

## 🔄 CI/CD自动化

### 自动化流程
1. **代码合并** → main分支
2. **Webhook触发** → 自动部署流程
3. **代码备份** → 本地和服务器备份
4. **项目构建** → Maven + npm构建
5. **服务部署** → Docker容器部署
6. **健康检查** → 验证部署状态

### 监控和告警
- **Webhook服务**: 端口9000
- **日志监控**: `/var/log/financial-system/`
- **状态检查**: 自动健康检查

## 🛠️ 故障排除

### 常见问题
1. **端口占用**
   ```bash
   sudo netstat -tlnp | grep :8080
   sudo kill -9 <PID>
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务
   sudo systemctl status mysql

   # 重启MySQL
   sudo systemctl restart mysql
   ```

3. **Docker服务异常**
   ```bash
   # 重启Docker服务
   sudo systemctl restart docker

   # 重新构建容器
   docker-compose down
   docker-compose up -d --build
   ```

## 📝 部署记录

详细的部署历史记录请查看：
- [部署成功报告](../operations/deployment-success-report.md)
- [历史部署记录](../archive/deployment-history/)

## 🔗 相关文档

- [开发指南](../development/README.md)
- [故障排除](../troubleshooting/README.md)
- [API接口文档](../api/README.md)
- [项目完整指南](../guides/README.md)

---

*更新时间: 2025-06-23 - 建立标准化部署流程*
