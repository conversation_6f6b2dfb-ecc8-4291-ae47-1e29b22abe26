# 🚀 MySQL复制和Linux部署问题解决执行指南

## 📋 概述

本指南提供了解决FinancialSystem项目Linux自动部署中遇到的MySQL认证和数据同步问题的完整执行步骤。通过本指南，您将能够：

1. 解决caching_sha2_password认证插件导致的复制失败
2. 初始化用户数据解决登录401错误
3. 建立稳定的三层数据同步架构
4. 实现自动化监控和告警

## 🎯 目标架构

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Linux MySQL    │ ⟷  │  Local MySQL    │ ⟷  │  Docker MySQL   │
│  **********     │     │  localhost      │     │  Container      │
│  server_id=1    │     │  server_id=2    │     │  server_id=3    │
└─────────────────┘     └─────────────────┘     └─────────────────┘
    Production            Development              Testing
```

## 📝 前置条件

- SSH访问Linux服务器(**********)的权限
- 本地MySQL 8.0已安装并运行
- Docker Desktop已安装（用于Docker MySQL测试）
- Git仓库访问权限
- MySQL root密码：Zlb&198838

## 🔧 快速开始

### 第一步：解决认证问题（最高优先级）

```bash
# 1. 执行认证修复脚本
cd /Volumes/ExternalSSD-1T/08.program/FinancialSystem
./scripts/database/fix-mysql-authentication.sh

# 脚本将自动：
# - 在Linux服务器创建使用mysql_native_password的复制用户
# - 配置本地MySQL连接到Linux服务器
# - 验证复制状态
# - 测试数据同步
```

**预期结果：**
- 看到 "IO线程: 运行中" 和 "SQL线程: 运行中"
- 复制延迟应该在5秒以内
- 测试数据成功同步

### 第二步：初始化用户数据

```bash
# 2. 执行用户数据初始化
./scripts/database/init-user-data.sh all

# 选择操作：
# - 输入 "all" 初始化本地和远程数据库
# - 脚本会自动测试登录功能
```

**测试登录：**
```bash
# 本地测试
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 远程测试
curl -X POST http://**********:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

### 第三步：验证数据同步

```bash
# 3. 运行同步验证
./scripts/database/verify-data-sync.sh full

# 这将执行：
# - 复制状态检查
# - 实时同步测试
# - 数据一致性验证
# - 生成同步报告
```

### 第四步：配置Docker环境（可选）

```bash
# 4. 启动Docker三层同步
./scripts/database/docker-env-setup.sh setup

# 这将：
# - 启动Docker Desktop
# - 创建MySQL容器
# - 配置Docker复制
# - 测试三层同步
```

### 第五步：启动监控

```bash
# 5. 启动复制监控
./scripts/database/monitor-replication.sh monitor

# 或在后台运行
nohup ./scripts/database/monitor-replication.sh monitor > /dev/null 2>&1 &
```

## 📊 详细执行步骤

### 1. SSH远程执行（Linux服务器操作）

如果脚本执行失败，可以手动在Linux服务器上执行：

```bash
# SSH登录到Linux服务器
ssh admin@**********

# 创建复制用户
mysql -uroot -p'Zlb&198838' << EOF
-- 删除旧的复制用户
DROP USER IF EXISTS 'repl_native'@'%';

-- 创建新的复制用户（使用mysql_native_password）
CREATE USER 'repl_native'@'%' IDENTIFIED WITH mysql_native_password BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_native'@'%';
FLUSH PRIVILEGES;

-- 验证用户创建
SELECT User, Host, plugin FROM mysql.user WHERE User = 'repl_native';
EOF

# 查看Master状态
mysql -uroot -p'Zlb&198838' -e "SHOW MASTER STATUS\G"
```

### 2. 本地MySQL配置

在本地机器上执行：

```bash
# 停止当前复制
mysql -uroot -p'Zlb&198838' << EOF
STOP SLAVE;
RESET SLAVE ALL;

-- 配置新的复制（替换MASTER_LOG_FILE和MASTER_LOG_POS为实际值）
CHANGE MASTER TO 
    MASTER_HOST='**********',
    MASTER_USER='repl_native',
    MASTER_PASSWORD='Zlb&198838',
    MASTER_PORT=3306,
    MASTER_LOG_FILE='mysql-bin.000001',
    MASTER_LOG_POS=154;

START SLAVE;

-- 检查状态
SHOW SLAVE STATUS\G
EOF
```

### 3. 用户数据初始化（SQL命令）

如果初始化脚本失败，可以手动执行SQL：

```bash
# 本地执行
mysql -uroot -p'Zlb&198838' < scripts/database/init-user-data.sql

# 远程执行
scp scripts/database/init-user-data.sql admin@**********:/tmp/
ssh admin@********** "mysql -uroot -p'Zlb&198838' < /tmp/init-user-data.sql"
```

## 🐛 故障排查

### 问题1：复制IO线程无法连接

**症状：**
```
Slave_IO_Running: Connecting
Last_IO_Error: Authentication plugin 'caching_sha2_password' reported error
```

**解决方案：**
1. 确认在Linux服务器上创建了repl_native用户
2. 检查防火墙是否开放3306端口
3. 验证网络连接：`telnet ********** 3306`

### 问题2：复制SQL线程错误

**症状：**
```
Slave_SQL_Running: No
Last_SQL_Error: Error 'Duplicate entry' on query
```

**解决方案：**
```bash
# 跳过错误
mysql -uroot -p'Zlb&198838' -e "
STOP SLAVE;
SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1;
START SLAVE;
"
```

### 问题3：登录仍然返回401

**检查步骤：**
1. 验证用户表数据：
   ```sql
   SELECT username, real_name FROM user_system.user;
   ```

2. 检查密码加密：
   ```bash
   # 生成新的BCrypt密码
   htpasswd -bnBC 10 "" admin123 | tr -d ':\n'
   ```

3. 检查应用配置：
   - 确认application.yml中的数据源配置
   - 验证JWT配置是否正确

### 问题4：Docker容器无法启动

**解决方案：**
```bash
# 清理并重新创建
docker stop financial-mysql
docker rm financial-mysql
docker volume rm mysql_data

# 重新启动
docker-compose -f docker-compose.local.yml up -d mysql
```

## 📈 性能优化建议

### MySQL配置优化

创建 `/etc/mysql/mysql.conf.d/replication.cnf`：

```ini
[mysqld]
# 复制优化
server_id = 2
log_bin = mysql-bin
binlog_format = ROW
sync_binlog = 1
binlog_expire_logs_seconds = 604800

# 性能优化
slave_parallel_workers = 4
slave_parallel_type = LOGICAL_CLOCK
slave_preserve_commit_order = ON
slave_pending_jobs_size_max = 128M

# 缓存优化
binlog_cache_size = 1M
max_binlog_cache_size = 2G

# 网络优化
slave_net_timeout = 60
master_info_repository = TABLE
relay_log_info_repository = TABLE
```

### 监控阈值调整

编辑 `monitor-replication.sh` 中的阈值：

```bash
LAG_WARNING_THRESHOLD=30    # 降低警告阈值到30秒
LAG_CRITICAL_THRESHOLD=120  # 降低严重阈值到120秒
CHECK_INTERVAL=10           # 更频繁的检查
```

## 🔒 安全建议

1. **使用专用复制用户**
   - 不要使用root账户进行复制
   - 限制复制用户的来源IP

2. **启用SSL加密**
   ```sql
   CHANGE MASTER TO 
     MASTER_SSL=1,
     MASTER_SSL_CA='/path/to/ca.pem';
   ```

3. **定期轮换密码**
   - 每90天更换一次复制密码
   - 使用强密码策略

## 📝 检查清单

完成所有步骤后，验证以下项目：

- [ ] 复制状态显示 IO和SQL线程都在运行
- [ ] 复制延迟在可接受范围内（<60秒）
- [ ] 能够使用admin/admin123登录系统
- [ ] 数据在Linux和本地MySQL之间同步
- [ ] 监控脚本正在运行并记录指标
- [ ] 已设置告警通知（如果需要）
- [ ] Docker环境可以正常工作（如果使用）
- [ ] 生成了配置建议文档

## 🚨 紧急联系

如果遇到无法解决的问题：

1. 检查日志文件：
   - MySQL错误日志：`/var/log/mysql/error.log`
   - 应用日志：`/opt/FinancialSystem/logs/`
   - 监控日志：`/tmp/mysql_replication_monitor/`

2. 收集诊断信息：
   ```bash
   # 生成诊断报告
   ./scripts/database/monitor-replication.sh report
   ```

3. 回滚操作（如果需要）：
   ```bash
   # 停止复制
   mysql -uroot -p'Zlb&198838' -e "STOP SLAVE; RESET SLAVE ALL;"
   ```

## 📚 相关文档

- [MySQL 8.0 复制文档](https://dev.mysql.com/doc/refman/8.0/en/replication.html)
- [认证插件兼容性](https://dev.mysql.com/doc/refman/8.0/en/caching-sha2-pluggable-authentication.html)
- [项目部署指南](./README.md)
- [数据库迁移文档](./database-migration-summary.md)

---

**最后更新**: 2025-07-08  
**作者**: SuperClaude  
**版本**: 1.0