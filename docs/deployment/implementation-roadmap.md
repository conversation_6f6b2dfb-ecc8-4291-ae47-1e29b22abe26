# MySQL全数据库双向同步实施路线图

## 🎯 实施目标

实现本地MySQL、Linux Docker MySQL之间的**所有数据库**自动双向同步，支持：
- ✅ 现有三个数据库（overdue_debt_db, user_system, kingdee）
- ✅ 未来新增数据库的自动发现和同步
- ✅ 数据冲突检测和解决
- ✅ 实时监控和告警

## 📋 分阶段实施计划

### 阶段一：基础环境准备（预计1-2小时）

#### 1.1 备份现有数据
```bash
# 备份所有现有数据库
cd /Volumes/ExternalSSD-1T/08.program/FinancialSystem
mkdir -p backups/$(date +%Y%m%d_%H%M%S)

# 本地数据库备份
mysqldump -uroot -pZlb\&198838 --all-databases > backups/$(date +%Y%m%d_%H%M%S)/local_full_backup.sql

# Linux数据库备份（需要SSH访问）
ssh admin@********** "mysqldump -uroot -pZlb\\&198838 --all-databases" > backups/$(date +%Y%m%d_%H%M%S)/linux_full_backup.sql
```

#### 1.2 验证网络连通性
```bash
# 测试网络连接
telnet ********** 3306
ping **********

# 测试MySQL连接
mysql -h********** -uroot -p'Zlb&198838' -e "SELECT 1"
mysql -hlocalhost -uroot -p'Zlb&198838' -e "SELECT 1"
```

#### 1.3 检查现有配置
```bash
# 检查当前的server_id和binlog设置
mysql -hlocalhost -uroot -p'Zlb&198838' -e "SHOW VARIABLES LIKE '%server_id%'"
mysql -h********** -uroot -p'Zlb&198838' -e "SHOW VARIABLES LIKE '%server_id%'"
```

### 阶段二：配置双向同步（预计2-3小时）

#### 2.1 执行自动化配置脚本
```bash
# 设置执行权限
chmod +x scripts/database/universal-bidirectional-sync-setup.sh

# 执行配置（包含所有步骤）
bash scripts/database/universal-bidirectional-sync-setup.sh setup
```

#### 2.2 验证同步状态
```bash
# 检查复制状态
bash scripts/database/universal-bidirectional-sync-setup.sh status

# 预期结果：
# - 两个MySQL服务器的IO和SQL线程都显示"Yes"
# - 复制延迟小于5秒
# - 没有错误信息
```

#### 2.3 测试数据同步
```bash
# 脚本会自动执行同步测试
# 也可以手动测试：

# 在本地插入测试数据
mysql -uroot -p'Zl&198838' -e "
USE user_system;
INSERT INTO user (username, real_name) VALUES ('test_sync_$(date +%s)', '同步测试用户');
"

# 检查Linux是否同步（等待5秒）
sleep 5
mysql -h********** -uroot -p'Zlb&198838' -e "
SELECT username, real_name FROM user_system.user WHERE username LIKE 'test_sync_%' ORDER BY created_time DESC LIMIT 1;
"
```

### 阶段三：配置自动发现（预计1小时）

#### 3.1 启动数据库自动发现
```bash
# 手动运行一次数据库发现
bash scripts/database/database-auto-discovery.sh

# 设置定时任务（每5分钟检查一次）
echo "*/5 * * * * cd /Volumes/ExternalSSD-1T/08.program/FinancialSystem && bash scripts/database/database-auto-discovery.sh" | crontab -
```

#### 3.2 测试新数据库自动同步
```bash
# 在本地创建新数据库
mysql -uroot -p'Zlb&198838' -e "CREATE DATABASE test_new_db_$(date +%s);"

# 等待5分钟，检查Linux是否自动创建
sleep 300
mysql -h********** -uroot -p'Zlb&198838' -e "SHOW DATABASES LIKE 'test_new_db_%';"
```

### 阶段四：监控和告警（预计2小时）

#### 4.1 启动同步监控
```bash
# 启动监控服务（后台运行）
nohup bash scripts/database/sync-monitor.sh > /tmp/sync_monitor.log 2>&1 &

# 查看监控日志
tail -f /tmp/mysql_sync_monitor.log
```

#### 4.2 配置Docker环境同步（可选）
```bash
# 启动支持同步的Docker环境
docker-compose -f docker-compose.sync.yml up -d

# 配置Docker MySQL参与三主复制
# （详细步骤见下方）
```

## 🔧 详细配置步骤

### 手动配置步骤（如果自动化脚本失败）

#### 1. 本地MySQL配置
```sql
-- 连接本地MySQL
mysql -uroot -p'Zlb&198838'

-- 配置服务器参数
SET GLOBAL server_id = 1;
SET GLOBAL gtid_mode = ON;
SET GLOBAL enforce_gtid_consistency = ON;
SET GLOBAL auto_increment_increment = 2;
SET GLOBAL auto_increment_offset = 1;

-- 创建复制用户
CREATE USER 'repl_universal'@'%' IDENTIFIED WITH mysql_native_password BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_universal'@'%';
GRANT SELECT ON *.* TO 'repl_universal'@'%';
FLUSH PRIVILEGES;
```

#### 2. Linux MySQL配置
```sql
-- SSH到Linux服务器
ssh admin@**********
mysql -uroot -p'Zlb&198838'

-- 配置服务器参数
SET GLOBAL server_id = 2;
SET GLOBAL gtid_mode = ON;
SET GLOBAL enforce_gtid_consistency = ON;
SET GLOBAL auto_increment_increment = 2;
SET GLOBAL auto_increment_offset = 2;

-- 创建复制用户
CREATE USER 'repl_universal'@'%' IDENTIFIED WITH mysql_native_password BY 'Zlb&198838';
GRANT REPLICATION SLAVE ON *.* TO 'repl_universal'@'%';
GRANT SELECT ON *.* TO 'repl_universal'@'%';
FLUSH PRIVILEGES;
```

#### 3. 配置复制关系
```sql
-- 在本地MySQL执行（作为Linux的从服务器）
CHANGE MASTER TO
  MASTER_HOST='**********',
  MASTER_USER='repl_universal',
  MASTER_PASSWORD='Zlb&198838',
  MASTER_PORT=3306,
  MASTER_AUTO_POSITION=1;
START SLAVE;

-- 在Linux MySQL执行（作为本地的从服务器）
CHANGE MASTER TO
  MASTER_HOST='***********',  -- 替换为你的本地IP
  MASTER_USER='repl_universal',
  MASTER_PASSWORD='Zlb&198838',
  MASTER_PORT=3306,
  MASTER_AUTO_POSITION=1;
START SLAVE;
```

## 🐛 常见问题和解决方案

### 问题1：认证插件错误
```
Last_IO_Error: Authentication plugin 'caching_sha2_password' reported error
```

**解决方案：**
```sql
-- 修改用户认证插件
ALTER USER 'repl_universal'@'%' IDENTIFIED WITH mysql_native_password BY 'Zlb&198838';
FLUSH PRIVILEGES;
```

### 问题2：GTID配置错误
```
Last_SQL_Error: @@SESSION.GTID_NEXT cannot be set to ANONYMOUS when @@GLOBAL.GTID_MODE = ON
```

**解决方案：**
```sql
-- 重置GTID状态
STOP SLAVE;
RESET SLAVE ALL;
RESET MASTER;

-- 重新配置
CHANGE MASTER TO ... MASTER_AUTO_POSITION=1;
START SLAVE;
```

### 问题3：主键冲突
```
Last_SQL_Error: Duplicate entry '123' for key 'PRIMARY'
```

**解决方案：**
```sql
-- 检查AUTO_INCREMENT设置
SHOW VARIABLES LIKE 'auto_increment%';

-- 调整设置
SET GLOBAL auto_increment_increment = 2;
SET GLOBAL auto_increment_offset = 1;  -- 本地使用1，Linux使用2
```

## 📊 验证清单

完成配置后，请验证以下项目：

- [ ] 本地MySQL和Linux MySQL都能正常连接
- [ ] 复制用户`repl_universal`在两个服务器上都存在
- [ ] `SHOW SLAVE STATUS\G`显示IO和SQL线程都为"Yes"
- [ ] 复制延迟小于10秒
- [ ] 在任意一端插入数据，另一端能在5秒内同步
- [ ] 新建数据库能在5分钟内自动发现并同步
- [ ] 监控脚本正常运行，日志无错误
- [ ] AUTO_INCREMENT设置正确，不会产生主键冲突

## 🚨 回滚计划

如果同步配置失败，可以按以下步骤回滚：

```bash
# 1. 停止复制
mysql -uroot -p'Zlb&198838' -e "STOP SLAVE; RESET SLAVE ALL;"
ssh admin@********** "mysql -uroot -p'Zlb\\&198838' -e 'STOP SLAVE; RESET SLAVE ALL;'"

# 2. 删除复制用户
mysql -uroot -p'Zlb&198838' -e "DROP USER IF EXISTS 'repl_universal'@'%';"
ssh admin@********** "mysql -uroot -p'Zlb\\&198838' -e \"DROP USER IF EXISTS 'repl_universal'@'%';\""

# 3. 恢复数据（如果需要）
mysql -uroot -p'Zlb&198838' < backups/$(ls backups/ | tail -1)/local_full_backup.sql
```

## 📞 技术支持

如遇到问题，请：

1. 查看同步监控日志：`tail -f /tmp/mysql_sync_monitor.log`
2. 检查MySQL错误日志：`tail -f /var/log/mysql/error.log`
3. 运行诊断脚本：`bash scripts/database/universal-bidirectional-sync-setup.sh status`
4. 收集配置信息：`mysql -e "SHOW SLAVE STATUS\G" > /tmp/slave_status.txt`

---

**预计总实施时间：6-8小时**  
**建议实施时间：非业务高峰期**  
**风险等级：中等（已有完整备份和回滚方案）**