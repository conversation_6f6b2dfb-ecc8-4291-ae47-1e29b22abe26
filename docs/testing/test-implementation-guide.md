# 测试实施指南

## 1. 概述

本指南提供了在FinancialSystem项目中实施测试的详细步骤和最佳实践。涵盖从环境搭建到测试编写、执行和维护的完整流程。

## 2. 快速开始

### 2.1 环境准备

#### 2.1.1 开发环境要求
```bash
# Java 21
java -version
# 输出: openjdk version "21.0.1"

# Maven 3.8+
mvn -version
# 输出: Apache Maven 3.8.6

# MySQL 8.0（用于集成测试）
mysql --version
# 输出: mysql Ver 8.0.32

# Redis 7.0（用于缓存测试）
redis-server --version
# 输出: Redis server v=7.0.8
```

#### 2.1.2 测试环境配置
```yaml
# src/test/resources/application-test.yml
spring:
  profiles:
    active: test
  
  datasource:
    # 使用H2内存数据库进行单元测试
    url: jdbc:h2:mem:testdb;MODE=MySQL;DATABASE_TO_LOWER=TRUE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    
  redis:
    # 使用嵌入式Redis
    port: 6370
    
# 测试专用JWT配置
jwt:
  secret: test-secret-key-for-unit-testing-only
  expiration: 3600000

# 日志配置
logging:
  level:
    com.financial: DEBUG
    org.springframework.test: INFO
```

### 2.2 第一个测试

#### 2.2.1 简单的单元测试
```java
package com.financial.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

class DebtCalculationServiceTest {
    
    private DebtCalculationService service;
    
    @BeforeEach
    void setUp() {
        service = new DebtCalculationService();
    }
    
    @Test
    void should_calculateMonthlyInterest_when_validDebtProvided() {
        // Given
        BigDecimal principal = new BigDecimal("100000");
        BigDecimal annualRate = new BigDecimal("0.05");
        
        // When
        BigDecimal monthlyInterest = service.calculateMonthlyInterest(principal, annualRate);
        
        // Then
        assertEquals(new BigDecimal("416.67"), monthlyInterest);
    }
}
```

#### 2.2.2 集成测试示例
```java
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureMockMvc
class DebtControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private DebtRepository debtRepository;
    
    @Test
    @WithMockUser(roles = "USER")
    void should_returnDebtList_when_authorizedUserRequests() throws Exception {
        // Given
        DebtRecord debt = TestDataFactory.createDebtRecord();
        debtRepository.save(debt);
        
        // When & Then
        mockMvc.perform(get("/api/debts")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].creditor").value(debt.getCreditor()))
                .andExpect(jsonPath("$[0].amount").value(debt.getAmount().doubleValue()));
    }
}
```

## 3. 测试类型实施

### 3.1 单元测试实施

#### 3.1.1 Service层测试
```java
@ExtendWith(MockitoExtension.class)
class DebtManagementServiceTest {
    
    @Mock
    private DebtRepository debtRepository;
    
    @Mock
    private ImpairmentReserveRepository impairmentRepository;
    
    @Mock
    private TransactionManager transactionManager;
    
    @InjectMocks
    private DebtManagementService service;
    
    @Test
    void should_addDebtSuccessfully_when_allValidationsPass() {
        // Given
        DebtAddRequest request = TestDataFactory.createValidDebtRequest();
        DebtRecord expectedDebt = TestDataFactory.toDebtRecord(request);
        
        when(debtRepository.save(any(DebtRecord.class))).thenReturn(expectedDebt);
        when(impairmentRepository.save(any())).thenReturn(new ImpairmentReserve());
        
        // When
        DebtRecord result = service.addDebt(request);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedDebt.getCreditor(), result.getCreditor());
        
        // Verify interactions
        verify(debtRepository).save(argThat(debt -> 
            debt.getCreditor().equals(request.getCreditor()) &&
            debt.getAmount().compareTo(request.getAmount()) == 0
        ));
        
        verify(impairmentRepository).save(any());
        verify(transactionManager).executeInTransaction(any());
    }
    
    @Test
    void should_rollbackTransaction_when_impairmentSaveFails() {
        // Given
        DebtAddRequest request = TestDataFactory.createValidDebtRequest();
        
        when(debtRepository.save(any())).thenReturn(new DebtRecord());
        when(impairmentRepository.save(any()))
            .thenThrow(new DataIntegrityViolationException("Constraint violation"));
        
        // When & Then
        assertThrows(TransactionException.class, () -> service.addDebt(request));
        
        verify(transactionManager).rollback();
    }
}
```

#### 3.1.2 Repository层测试
```java
@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:test;MODE=MySQL",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class DebtRepositoryTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private DebtRepository debtRepository;
    
    @Test
    void should_findByCreditorAndPeriod_when_recordsExist() {
        // Given
        DebtRecord debt1 = TestDataFactory.createDebtRecord()
            .toBuilder()
            .creditor("测试公司A")
            .period("2024-06")
            .build();
            
        DebtRecord debt2 = TestDataFactory.createDebtRecord()
            .toBuilder()
            .creditor("测试公司A")
            .period("2024-07")
            .build();
            
        entityManager.persist(debt1);
        entityManager.persist(debt2);
        entityManager.flush();
        
        // When
        List<DebtRecord> results = debtRepository
            .findByCreditorAndPeriod("测试公司A", "2024-06");
        
        // Then
        assertEquals(1, results.size());
        assertEquals("2024-06", results.get(0).getPeriod());
    }
    
    @Test
    void should_calculateTotalDebt_when_multipleRecordsExist() {
        // Given
        createTestDebts();
        
        // When
        BigDecimal total = debtRepository.calculateTotalDebt(2024, 6);
        
        // Then
        assertEquals(new BigDecimal("300000.00"), total);
    }
}
```

### 3.2 集成测试实施

#### 3.2.1 Controller集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Sql(scripts = "/test-data/cleanup.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
class DebtControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private JwtTokenProvider tokenProvider;
    
    @Test
    void should_completeDebtLifecycle_when_normalFlow() throws Exception {
        String token = tokenProvider.generateToken("testuser", "ROLE_USER");
        
        // Step 1: 添加债权
        String addResponse = mockMvc.perform(post("/api/debts/add")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content("""
                    {
                        "creditor": "测试债权人",
                        "debtor": "测试债务人",
                        "amount": 100000,
                        "period": "2024-06",
                        "isLitigation": true
                    }
                    """))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Long debtId = JsonPath.parse(addResponse).read("$.id", Long.class);
        
        // Step 2: 查询债权
        mockMvc.perform(get("/api/debts/" + debtId)
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.creditor").value("测试债权人"))
                .andExpect(jsonPath("$.amount").value(100000));
        
        // Step 3: 处置债权
        mockMvc.perform(post("/api/debts/update/reduction")
                .header("Authorization", "Bearer " + token)
                .contentType(MediaType.APPLICATION_JSON)
                .content("""
                    {
                        "debtId": """ + debtId + """,
                        "disposalType": "CASH",
                        "amount": 50000,
                        "disposalDate": "2024-07-01"
                    }
                    """))
                .andExpect(status().isOk());
        
        // Step 4: 验证余额
        mockMvc.perform(get("/api/debts/" + debtId)
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.remainingAmount").value(50000));
    }
}
```

#### 3.2.2 跨模块集成测试
```java
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "test.integration.cross-module=true"
})
class CrossModuleIntegrationTest {
    
    @Autowired
    private DebtManagementService debtService;
    
    @Autowired
    private ReportGenerationService reportService;
    
    @Autowired
    private NotificationService notificationService;
    
    @MockBean
    private ExternalSystemClient externalSystemClient;
    
    @Test
    void should_generateReportAndNotify_when_monthlyDebtProcessed() {
        // Given - 准备月度数据
        List<DebtRecord> monthlyDebts = createMonthlyTestData();
        
        // Mock外部系统
        when(externalSystemClient.fetchAdditionalData(any()))
            .thenReturn(TestDataFactory.createExternalData());
        
        // When - 执行月度处理
        MonthlyProcessingResult result = debtService.processMonthlyDebts(2024, 6);
        
        // Then - 验证报表生成
        Report report = reportService.getMonthlyReport(2024, 6);
        assertNotNull(report);
        assertEquals(monthlyDebts.size(), report.getRecordCount());
        
        // 验证通知发送
        verify(notificationService).sendMonthlyReport(
            argThat(notification -> 
                notification.getRecipients().contains("<EMAIL>") &&
                notification.getSubject().contains("2024年6月债权月度报告")
            )
        );
    }
}
```

### 3.3 数据一致性测试实施

#### 3.3.1 快照测试实施
```java
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DebtQuerySnapshotTest {
    
    @Autowired
    private DebtQueryService queryService;
    
    @Autowired
    private SnapshotManager snapshotManager;
    
    @BeforeAll
    static void loadBaselineData() {
        TestDataLoader.loadBaseline("v1.2");
    }
    
    @Test
    @Order(1)
    void createOrVerifyQuerySnapshots() {
        // 定义需要验证的查询场景
        List<QueryScenario> scenarios = Arrays.asList(
            new QueryScenario("all_debts_2024", 
                () -> queryService.findAllDebts(2024)),
            
            new QueryScenario("litigation_debts_q2", 
                () -> queryService.findLitigationDebts(2024, 4, 6)),
            
            new QueryScenario("company_summary", 
                () -> queryService.getCompanySummary("测试公司A"))
        );
        
        // 执行快照验证
        scenarios.forEach(scenario -> {
            Object result = scenario.execute();
            
            if (snapshotManager.exists(scenario.getId())) {
                // 验证快照
                SnapshotAssertion.assertMatches(scenario.getId(), result);
            } else {
                // 创建快照
                snapshotManager.create(scenario.getId(), result);
                fail("Snapshot created for: " + scenario.getId() + 
                     ". Re-run test to verify.");
            }
        });
    }
}
```

#### 3.3.2 跨表一致性验证
```java
@SpringBootTest
@Transactional
@Rollback
class CrossTableConsistencyTest {
    
    @Autowired
    private ConsistencyValidator consistencyValidator;
    
    @Test
    void should_maintainConsistency_when_debtAdded() {
        // Given
        DebtAddRequest request = TestDataFactory.createDebtAddRequest();
        ConsistencySnapshot before = consistencyValidator.captureSnapshot();
        
        // When
        debtService.addDebt(request);
        
        // Then
        ConsistencySnapshot after = consistencyValidator.captureSnapshot();
        ConsistencyReport report = consistencyValidator.compare(before, after);
        
        assertTrue(report.isConsistent(), 
            "Inconsistency detected: " + report.getIssues());
        
        // 验证具体的一致性规则
        assertEquals(
            before.getDebtTableCount() + 1,
            after.getDebtTableCount()
        );
        
        assertEquals(
            before.getImpairmentTableCount() + 1,
            after.getImpairmentTableCount()
        );
        
        // 验证金额一致性
        BigDecimal debtAmount = after.getLatestDebtAmount();
        BigDecimal impairmentAmount = after.getLatestImpairmentAmount();
        assertEquals(debtAmount, impairmentAmount);
    }
}
```

## 4. 测试数据管理实施

### 4.1 测试数据构建器

```java
public class TestDataBuilder {
    
    private DebtRecord.DebtRecordBuilder debtBuilder = DebtRecord.builder();
    
    public TestDataBuilder withCreditor(String creditor) {
        debtBuilder.creditor(creditor);
        return this;
    }
    
    public TestDataBuilder withAmount(BigDecimal amount) {
        debtBuilder.amount(amount);
        return this;
    }
    
    public TestDataBuilder withPeriod(String period) {
        debtBuilder.period(period);
        return this;
    }
    
    public TestDataBuilder withLitigation(boolean isLitigation) {
        debtBuilder.isLitigation(isLitigation);
        return this;
    }
    
    public TestDataBuilder withMonthlyAmounts(Map<Integer, BigDecimal> monthlyAmounts) {
        monthlyAmounts.forEach((month, amount) -> {
            String monthField = month + "月";
            // 使用反射设置月份字段
            ReflectionTestUtils.setField(debtBuilder, monthField, amount);
        });
        return this;
    }
    
    public DebtRecord build() {
        // 设置默认值
        if (debtBuilder.build().getCreditor() == null) {
            debtBuilder.creditor("默认债权人");
        }
        if (debtBuilder.build().getAmount() == null) {
            debtBuilder.amount(BigDecimal.ZERO);
        }
        return debtBuilder.build();
    }
    
    // 便捷方法
    public static DebtRecord createDefault() {
        return new TestDataBuilder()
            .withCreditor("测试债权人")
            .withDebtor("测试债务人")
            .withAmount(new BigDecimal("100000"))
            .withPeriod("2024-06")
            .withLitigation(false)
            .build();
    }
    
    public static List<DebtRecord> createMultiple(int count) {
        return IntStream.range(0, count)
            .mapToObj(i -> new TestDataBuilder()
                .withCreditor("债权人" + i)
                .withDebtor("债务人" + i)
                .withAmount(new BigDecimal(10000 * (i + 1)))
                .withPeriod("2024-0" + ((i % 12) + 1))
                .build())
            .collect(Collectors.toList());
    }
}
```

### 4.2 测试数据加载器

```java
@Component
@Profile("test")
public class TestDataLoader {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private ResourceLoader resourceLoader;
    
    public void loadBaseline(String version) {
        String basePath = "classpath:test-data/baseline/" + version + "/";
        
        try {
            // 加载schema
            executeSqlScript(basePath + "schema.sql");
            
            // 加载数据
            executeSqlScript(basePath + "data.sql");
            
            // 加载索引
            executeSqlScript(basePath + "indexes.sql");
            
            log.info("Baseline {} loaded successfully", version);
            
        } catch (Exception e) {
            throw new TestDataLoadException("Failed to load baseline " + version, e);
        }
    }
    
    public void loadScenario(String scenario) {
        String scenarioPath = "classpath:test-data/scenarios/" + scenario + ".sql";
        
        try {
            executeSqlScript(scenarioPath);
            log.info("Scenario {} loaded successfully", scenario);
            
        } catch (Exception e) {
            throw new TestDataLoadException("Failed to load scenario " + scenario, e);
        }
    }
    
    private void executeSqlScript(String scriptPath) throws Exception {
        Resource resource = resourceLoader.getResource(scriptPath);
        
        try (Connection connection = dataSource.getConnection()) {
            ScriptUtils.executeSqlScript(
                connection,
                new EncodedResource(resource, StandardCharsets.UTF_8),
                false,  // continueOnError
                false,  // ignoreFailedDrops
                ScriptUtils.DEFAULT_COMMENT_PREFIX,
                ScriptUtils.DEFAULT_STATEMENT_SEPARATOR,
                ScriptUtils.DEFAULT_BLOCK_COMMENT_START_DELIMITER,
                ScriptUtils.DEFAULT_BLOCK_COMMENT_END_DELIMITER
            );
        }
    }
    
    @Transactional
    public void cleanupTestData() {
        // 清理测试数据，保留基准数据
        jdbcTemplate.execute("DELETE FROM overdue_debt_add WHERE creditor LIKE 'TEST_%'");
        jdbcTemplate.execute("DELETE FROM overdue_debt_decrease WHERE creditor LIKE 'TEST_%'");
        jdbcTemplate.execute("DELETE FROM impairment_reserve WHERE creditor LIKE 'TEST_%'");
        jdbcTemplate.execute("DELETE FROM litigation_claim WHERE creditor LIKE 'TEST_%'");
    }
}
```

## 5. Mock和Stub策略

### 5.1 外部服务Mock

```java
@TestConfiguration
public class ExternalServiceMockConfig {
    
    @Bean
    @Primary
    public KingdeeClient mockKingdeeClient() {
        KingdeeClient mock = Mockito.mock(KingdeeClient.class);
        
        // 设置默认行为
        when(mock.getCompanyInfo(anyString()))
            .thenAnswer(invocation -> {
                String companyCode = invocation.getArgument(0);
                return CompanyInfo.builder()
                    .code(companyCode)
                    .name("测试公司-" + companyCode)
                    .creditLimit(new BigDecimal("1000000"))
                    .build();
            });
        
        when(mock.syncFinancialData(any()))
            .thenReturn(SyncResult.success("同步成功"));
        
        return mock;
    }
    
    @Bean
    @Primary  
    public TreasuryService mockTreasuryService() {
        return new MockTreasuryService();
    }
    
    // Mock实现
    static class MockTreasuryService implements TreasuryService {
        
        @Override
        public PaymentResult processPayment(PaymentRequest request) {
            // 模拟支付处理
            if (request.getAmount().compareTo(new BigDecimal("1000000")) > 0) {
                return PaymentResult.failed("金额超限");
            }
            
            return PaymentResult.success(
                "MOCK_" + UUID.randomUUID().toString()
            );
        }
        
        @Override
        public List<Transaction> getTransactionHistory(String accountId) {
            // 返回模拟交易记录
            return Arrays.asList(
                createMockTransaction("收款", new BigDecimal("50000")),
                createMockTransaction("付款", new BigDecimal("-30000"))
            );
        }
    }
}
```

### 5.2 数据库Mock策略

```java
@TestComponent
public class DatabaseMockHelper {
    
    @MockBean
    private DataSource mockDataSource;
    
    public void setupMockDatabase() throws SQLException {
        Connection mockConnection = mock(Connection.class);
        PreparedStatement mockStatement = mock(PreparedStatement.class);
        ResultSet mockResultSet = mock(ResultSet.class);
        
        when(mockDataSource.getConnection()).thenReturn(mockConnection);
        when(mockConnection.prepareStatement(anyString())).thenReturn(mockStatement);
        when(mockStatement.executeQuery()).thenReturn(mockResultSet);
        
        // 设置查询结果
        when(mockResultSet.next()).thenReturn(true, true, false);
        when(mockResultSet.getString("creditor")).thenReturn("Mock债权人1", "Mock债权人2");
        when(mockResultSet.getBigDecimal("amount")).thenReturn(
            new BigDecimal("100000"),
            new BigDecimal("200000")
        );
    }
    
    public void verifyDatabaseInteractions() throws SQLException {
        verify(mockDataSource, atLeastOnce()).getConnection();
        verify(mockConnection).close();
    }
}
```

## 6. 测试执行和调试

### 6.1 测试执行命令

```bash
# 执行所有测试
mvn test

# 执行特定模块的测试
mvn test -pl api-gateway

# 执行特定测试类
mvn test -Dtest=DebtManagementServiceTest

# 执行特定测试方法
mvn test -Dtest=DebtManagementServiceTest#should_addDebtSuccessfully_when_allValidationsPass

# 执行带标签的测试
mvn test -Dgroups="integration"

# 跳过测试
mvn clean install -DskipTests

# 执行测试并生成覆盖率报告
mvn clean test jacoco:report

# 并行执行测试
mvn test -T 4  # 使用4个线程
```

### 6.2 测试调试技巧

#### 6.2.1 启用详细日志
```yaml
# application-test.yml
logging:
  level:
    org.springframework.test: DEBUG
    org.springframework.jdbc: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql: TRACE
    com.financial: TRACE
```

#### 6.2.2 测试断点调试
```java
@Test
void debugComplexScenario() {
    // 在IDE中设置断点
    System.out.println("设置断点在这里");
    
    // 打印关键变量
    log.debug("Request: {}", request);
    log.debug("Current state: {}", getCurrentState());
    
    // 使用条件断点
    for (int i = 0; i < 100; i++) {
        if (i == 50) {  // 条件断点: i == 50
            processItem(i);
        }
    }
}
```

#### 6.2.3 测试隔离问题排查
```java
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class IsolationDebugTest {
    
    @BeforeEach
    void checkState() {
        // 打印当前状态，排查测试间干扰
        log.info("Database record count: {}", getRecordCount());
        log.info("Cache state: {}", getCacheState());
    }
    
    @AfterEach
    void cleanupState() {
        // 确保测试后状态清理
        clearTestData();
        clearCache();
    }
}
```

## 7. 持续集成配置

### 7.1 GitHub Actions完整配置

```yaml
name: Comprehensive Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  JAVA_VERSION: '21'
  MAVEN_OPTS: -Xmx3072m -XX:+UseG1GC

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: Run unit tests
      run: |
        mvn clean test -P unit-tests
        
    - name: Generate coverage report
      run: mvn jacoco:report
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./target/site/jacoco/jacoco.xml
        flags: unittests
        
  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: financial_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
          
      redis:
        image: redis:7.0
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: Wait for services
      run: |
        until mysqladmin ping -h 127.0.0.1 --silent; do
          echo 'Waiting for MySQL...'
          sleep 3
        done
        
    - name: Setup test database
      run: |
        mysql -h 127.0.0.1 -uroot -proot < scripts/test-db-setup.sql
        
    - name: Run integration tests
      run: |
        mvn clean verify -P integration-tests \
          -Dspring.datasource.url=****************************************** \
          -Dspring.redis.host=localhost
          
    - name: Archive test results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-results
        path: |
          target/surefire-reports/
          target/failsafe-reports/
          
  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build application
      run: |
        mvn clean package -DskipTests
        docker build -t financial-system:test .
        
    - name: Start application
      run: |
        docker-compose -f docker-compose.test.yml up -d
        ./scripts/wait-for-app.sh
        
    - name: Run E2E tests
      run: |
        mvn test -P e2e-tests \
          -Dtest.app.url=http://localhost:8080
          
    - name: Collect logs
      if: failure()
      run: |
        docker-compose -f docker-compose.test.yml logs > docker-logs.txt
        
    - name: Stop application
      if: always()
      run: docker-compose -f docker-compose.test.yml down
      
  quality-gates:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Check test coverage
      run: |
        COVERAGE=$(curl -s https://codecov.io/api/gh/${{ github.repository }}/branch/main | jq -r '.commit.totals.coverage')
        echo "Current coverage: $COVERAGE%"
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "Coverage is below 80%"
          exit 1
        fi
        
    - name: Check code quality
      run: |
        mvn sonar:sonar \
          -Dsonar.projectKey=financial-system \
          -Dsonar.host.url=${{ secrets.SONAR_URL }} \
          -Dsonar.login=${{ secrets.SONAR_TOKEN }}
```

### 7.2 测试报告生成

```xml
<!-- pom.xml配置 -->
<build>
    <plugins>
        <!-- Surefire用于单元测试 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.0.0-M9</version>
            <configuration>
                <includes>
                    <include>**/*Test.java</include>
                    <include>**/*Tests.java</include>
                </includes>
                <excludes>
                    <exclude>**/*IntegrationTest.java</exclude>
                </excludes>
                <parallel>methods</parallel>
                <threadCount>4</threadCount>
            </configuration>
        </plugin>
        
        <!-- Failsafe用于集成测试 -->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-failsafe-plugin</artifactId>
            <version>3.0.0-M9</version>
            <configuration>
                <includes>
                    <include>**/*IntegrationTest.java</include>
                    <include>**/*IT.java</include>
                </includes>
            </configuration>
        </plugin>
        
        <!-- JaCoCo代码覆盖率 -->
        <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>0.8.8</version>
            <executions>
                <execution>
                    <goals>
                        <goal>prepare-agent</goal>
                    </goals>
                </execution>
                <execution>
                    <id>report</id>
                    <phase>test</phase>
                    <goals>
                        <goal>report</goal>
                    </goals>
                </execution>
                <execution>
                    <id>check</id>
                    <goals>
                        <goal>check</goal>
                    </goals>
                    <configuration>
                        <rules>
                            <rule>
                                <element>CLASS</element>
                                <limits>
                                    <limit>
                                        <counter>LINE</counter>
                                        <value>COVEREDRATIO</value>
                                        <minimum>0.80</minimum>
                                    </limit>
                                </limits>
                            </rule>
                        </rules>
                    </configuration>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>
```

## 8. 常见问题和解决方案

### 8.1 测试环境问题

#### 问题1：H2数据库不支持MySQL特定函数
```java
// 解决方案：创建H2函数别名
@TestConfiguration
public class H2Configuration {
    
    @Bean
    public DataSource dataSource() throws SQLException {
        EmbeddedDatabaseBuilder builder = new EmbeddedDatabaseBuilder();
        EmbeddedDatabase db = builder
            .setType(EmbeddedDatabaseType.H2)
            .addScript("classpath:h2-mysql-compatibility.sql")
            .build();
        
        // 注册MySQL兼容函数
        try (Connection conn = db.getConnection()) {
            Statement stmt = conn.createStatement();
            stmt.execute("CREATE ALIAS IF NOT EXISTS DATE_FORMAT FOR " +
                        "\"com.financial.test.H2Functions.dateFormat\"");
        }
        
        return db;
    }
}

public class H2Functions {
    public static String dateFormat(Timestamp date, String format) {
        // 实现MySQL DATE_FORMAT函数
        SimpleDateFormat sdf = new SimpleDateFormat(
            format.replace("%Y", "yyyy")
                  .replace("%m", "MM")
                  .replace("%d", "dd")
        );
        return sdf.format(date);
    }
}
```

#### 问题2：测试数据隔离失败
```java
// 解决方案：使用测试执行监听器
@TestExecutionListeners({
    DependencyInjectionTestExecutionListener.class,
    DirtiesContextTestExecutionListener.class,
    TransactionalTestExecutionListener.class,
    DbUnitTestExecutionListener.class
})
@Transactional
@Rollback
public abstract class BaseIntegrationTest {
    
    @Autowired
    private TestTransactionManager transactionManager;
    
    @BeforeEach
    void ensureCleanState() {
        // 确保每个测试开始时的清洁状态
        if (transactionManager.isActive()) {
            transactionManager.rollback();
        }
        transactionManager.begin();
    }
}
```

### 8.2 测试性能问题

#### 问题：测试执行缓慢
```java
// 解决方案1：使用测试切片
@WebMvcTest(DebtController.class)  // 只加载Web层
class DebtControllerTest {
    // 测试代码
}

@DataJpaTest  // 只加载JPA相关
class DebtRepositoryTest {
    // 测试代码
}

// 解决方案2：共享测试容器
@Testcontainers
public abstract class BaseContainerTest {
    
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
        .withDatabaseName("test")
        .withUsername("test")
        .withPassword("test")
        .withReuse(true);  // 重用容器
    
    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", mysql::getJdbcUrl);
        registry.add("spring.datasource.username", mysql::getUsername);
        registry.add("spring.datasource.password", mysql::getPassword);
    }
}
```

### 8.3 Mock相关问题

#### 问题：Mock对象行为不符合预期
```java
// 解决方案：使用ArgumentCaptor验证
@Test
void testComplexInteraction() {
    ArgumentCaptor<DebtRecord> debtCaptor = ArgumentCaptor.forClass(DebtRecord.class);
    ArgumentCaptor<NotificationRequest> notificationCaptor = 
        ArgumentCaptor.forClass(NotificationRequest.class);
    
    // 执行测试
    service.processDebt(request);
    
    // 捕获并验证参数
    verify(debtRepository).save(debtCaptor.capture());
    verify(notificationService).send(notificationCaptor.capture());
    
    DebtRecord savedDebt = debtCaptor.getValue();
    assertEquals(request.getAmount(), savedDebt.getAmount());
    
    NotificationRequest notification = notificationCaptor.getValue();
    assertTrue(notification.getMessage().contains(savedDebt.getCreditor()));
}
```

## 9. 测试最佳实践总结

### 9.1 测试命名规范
```java
// 单元测试命名：should_expectedBehavior_when_condition
@Test
void should_calculateInterestCorrectly_when_validRateProvided() {}

// 集成测试命名：test_scenario_expectedOutcome
@Test
void test_debtLifecycle_successfulCompletion() {}

// 数据一致性测试命名：verify_aspect_condition
@Test
void verify_crossTableConsistency_afterDebtAddition() {}
```

### 9.2 测试组织结构
```
src/test/java/
├── unit/                    # 单元测试
│   ├── service/
│   ├── util/
│   └── validator/
├── integration/             # 集成测试
│   ├── api/
│   ├── repository/
│   └── workflow/
├── consistency/             # 数据一致性测试
│   ├── snapshot/
│   └── validation/
├── performance/             # 性能测试
└── common/                  # 测试工具类
    ├── builder/
    ├── fixture/
    └── util/
```

### 9.3 测试代码质量标准

1. **可读性**：测试即文档，应清晰表达测试意图
2. **可维护性**：避免重复代码，提取公共方法
3. **独立性**：测试间不应有依赖关系
4. **完整性**：覆盖正常路径和异常路径
5. **及时性**：与生产代码同步开发

## 10. 下一步行动

1. **立即开始**：选择一个核心模块开始编写测试
2. **逐步覆盖**：优先覆盖高风险、高价值的功能
3. **持续改进**：定期审查和优化测试策略
4. **团队培训**：确保团队成员理解并遵循测试规范
5. **度量跟踪**：建立测试覆盖率和质量指标

记住：好的测试是系统质量的保障，也是开发效率的助推器！