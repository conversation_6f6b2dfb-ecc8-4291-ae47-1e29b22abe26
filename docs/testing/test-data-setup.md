# 测试数据准备指南

## 1. 概述

测试数据是自动化测试的基础。本指南详细说明如何准备、管理和维护FinancialSystem项目的测试数据，确保测试的可靠性和可重复性。

## 2. 测试数据分类

### 2.1 数据类型划分

```
测试数据
├── 基准数据 (Baseline Data)          # 稳定的参考数据集
├── 场景数据 (Scenario Data)          # 特定测试场景数据
├── 边界数据 (Boundary Data)          # 边界条件测试数据
├── 性能数据 (Performance Data)       # 大数据量性能测试
└── 异常数据 (Exception Data)         # 异常情况测试数据
```

### 2.2 数据特征要求

| 数据类型 | 数据量 | 更新频率 | 用途 | 特殊要求 |
|---------|--------|---------|------|----------|
| 基准数据 | 中等（~1000条） | 低（版本化） | 回归测试 | 覆盖所有业务场景 |
| 场景数据 | 小（~100条） | 中 | 功能测试 | 针对性强 |
| 边界数据 | 小（~50条） | 低 | 边界测试 | 极限值 |
| 性能数据 | 大（>10000条） | 低 | 性能测试 | 真实分布 |
| 异常数据 | 小（~50条） | 中 | 异常测试 | 违反约束 |

## 3. 基准数据集设计

### 3.1 数据集结构

```sql
-- test-data/baseline/v1.0/metadata.sql
CREATE TABLE IF NOT EXISTS test_metadata (
    version VARCHAR(10) PRIMARY KEY,
    created_date DATE NOT NULL,
    created_by VARCHAR(50) NOT NULL,
    description TEXT,
    data_stats JSON
);

INSERT INTO test_metadata VALUES (
    '1.0',
    '2024-01-01',
    'system',
    'Initial baseline dataset for FinancialSystem testing',
    '{
        "total_records": 1000,
        "debt_records": 500,
        "disposal_records": 300,
        "user_records": 50,
        "company_records": 20
    }'
);
```

### 3.2 核心业务数据

#### 3.2.1 债权基础数据
```sql
-- test-data/baseline/v1.0/debt_records.sql
-- 债权人主数据
INSERT INTO creditor_master (id, name, code, type, status) VALUES
(1, '测试银行A', 'BANK_A', 'BANK', 'ACTIVE'),
(2, '测试保险公司B', 'INSUR_B', 'INSURANCE', 'ACTIVE'),
(3, '测试信托C', 'TRUST_C', 'TRUST', 'ACTIVE'),
(4, '测试担保公司D', 'GUAR_D', 'GUARANTEE', 'ACTIVE'),
(5, '测试小贷公司E', 'LOAN_E', 'MICROLOAN', 'ACTIVE');

-- 债务人主数据
INSERT INTO debtor_master (id, name, identity_no, type, risk_level) VALUES
(1, '测试企业001', '91110000MA001XXX01', 'ENTERPRISE', 'HIGH'),
(2, '测试企业002', '91110000MA001XXX02', 'ENTERPRISE', 'MEDIUM'),
(3, '张三', '110101199001011234', 'INDIVIDUAL', 'LOW'),
(4, '李四', '110101199002022345', 'INDIVIDUAL', 'MEDIUM'),
(5, '测试集团003', '91110000MA001XXX03', 'GROUP', 'HIGH');

-- 逾期债权记录（覆盖各种场景）
INSERT INTO overdue_debt_add (
    creditor, debtor, amount, period, is_litigation, year,
    `1月`, `2月`, `3月`, `4月`, `5月`, `6月`, 
    `7月`, `8月`, `9月`, `10月`, `11月`, `12月`
) VALUES
-- 场景1：正常逐月增长
('测试银行A', '测试企业001', 1200000.00, '2024年新增', 1, 2024,
 100000, 100000, 100000, 100000, 100000, 100000,
 100000, 100000, 100000, 100000, 100000, 100000),

-- 场景2：季度性增长
('测试保险公司B', '测试企业002', 400000.00, '2024年新增', 0, 2024,
 0, 0, 100000, 0, 0, 100000,
 0, 0, 100000, 0, 0, 100000),

-- 场景3：一次性大额
('测试信托C', '张三', 500000.00, '2024年新增', 1, 2024,
 0, 0, 0, 0, 0, 500000,
 0, 0, 0, 0, 0, 0),

-- 场景4：不规则增长
('测试担保公司D', '李四', 350000.00, '2024年新增', 0, 2024,
 50000, 0, 100000, 0, 50000, 0,
 100000, 0, 50000, 0, 0, 0);
```

#### 3.2.2 债权处置数据
```sql
-- test-data/baseline/v1.0/disposal_records.sql
INSERT INTO overdue_debt_decrease (
    creditor, debtor, period, is_litigation, year, month,
    cash_disposal, installment_repayment, asset_debt, other_disposal,
    total_disposal
) VALUES
-- 现金处置
('测试银行A', '测试企业001', '2024年新增', 1, 2024, 6,
 50000.00, 0, 0, 0, 50000.00),

-- 分期还款
('测试保险公司B', '测试企业002', '2024年新增', 0, 2024, 6,
 0, 20000.00, 0, 0, 20000.00),

-- 资产抵债
('测试信托C', '张三', '2024年新增', 1, 2024, 7,
 0, 0, 100000.00, 0, 100000.00),

-- 混合处置
('测试担保公司D', '李四', '2024年新增', 0, 2024, 8,
 10000.00, 10000.00, 20000.00, 5000.00, 45000.00);
```

#### 3.2.3 减值准备数据
```sql
-- test-data/baseline/v1.0/impairment_records.sql
INSERT INTO impairment_reserve (
    creditor, debtor, period, is_litigation, year, month,
    beginning_balance, current_provision, current_reversal,
    ending_balance
) VALUES
-- 正常计提
('测试银行A', '测试企业001', '2024年新增', 1, 2024, 6,
 100000.00, 20000.00, 0, 120000.00),

-- 部分转回
('测试保险公司B', '测试企业002', '2024年新增', 0, 2024, 6,
 50000.00, 0, 10000.00, 40000.00),

-- 全额计提
('测试信托C', '张三', '2024年新增', 1, 2024, 6,
 0, 500000.00, 0, 500000.00);
```

### 3.3 辅助数据

```sql
-- test-data/baseline/v1.0/auxiliary_data.sql
-- 用户数据
INSERT INTO user_account (username, password, role, status) VALUES
('test_admin', '$2a$10$...', 'ADMIN', 'ACTIVE'),
('test_user', '$2a$10$...', 'USER', 'ACTIVE'),
('test_auditor', '$2a$10$...', 'AUDITOR', 'ACTIVE'),
('test_export', '$2a$10$...', 'EXPORT_USER', 'ACTIVE');

-- 公司组织数据
INSERT INTO company_organization (code, name, parent_code, level) VALUES
('HQ', '集团总部', NULL, 1),
('SUB_A', '子公司A', 'HQ', 2),
('SUB_B', '子公司B', 'HQ', 2),
('DEPT_A1', '部门A1', 'SUB_A', 3);

-- 系统配置数据
INSERT INTO system_config (config_key, config_value, description) VALUES
('test.mode', 'true', '测试模式标识'),
('data.validation.strict', 'false', '测试环境关闭严格验证'),
('export.template.path', '/test-templates/', '测试用导出模板路径');
```

## 4. 场景数据准备

### 4.1 业务场景数据

#### 4.1.1 债权生命周期场景
```sql
-- test-data/scenarios/debt_lifecycle.sql
-- 场景：完整的债权生命周期
BEGIN;

-- Step 1: 新增债权
INSERT INTO overdue_debt_add (creditor, debtor, amount, period, is_litigation, year, `6月`)
VALUES ('LIFECYCLE_TEST_BANK', 'LIFECYCLE_TEST_DEBTOR', 1000000.00, '2024年新增', 1, 2024, 1000000.00);

-- Step 2: 计提减值
INSERT INTO impairment_reserve (creditor, debtor, period, is_litigation, year, month, current_provision)
VALUES ('LIFECYCLE_TEST_BANK', 'LIFECYCLE_TEST_DEBTOR', '2024年新增', 1, 2024, 6, 200000.00);

-- Step 3: 部分处置
INSERT INTO overdue_debt_decrease (creditor, debtor, period, is_litigation, year, month, cash_disposal)
VALUES ('LIFECYCLE_TEST_BANK', 'LIFECYCLE_TEST_DEBTOR', '2024年新增', 1, 2024, 7, 300000.00);

-- Step 4: 转回减值
UPDATE impairment_reserve 
SET current_reversal = 60000.00
WHERE creditor = 'LIFECYCLE_TEST_BANK' AND debtor = 'LIFECYCLE_TEST_DEBTOR' AND month = 7;

COMMIT;
```

#### 4.1.2 跨表一致性场景
```sql
-- test-data/scenarios/cross_table_consistency.sql
-- 场景：验证跨表数据一致性
DELIMITER //

CREATE PROCEDURE setup_consistency_test_data()
BEGIN
    DECLARE v_creditor VARCHAR(100) DEFAULT 'CONSISTENCY_TEST_BANK';
    DECLARE v_debtor VARCHAR(100) DEFAULT 'CONSISTENCY_TEST_DEBTOR';
    DECLARE v_amount DECIMAL(15,2) DEFAULT 500000.00;
    DECLARE v_year INT DEFAULT 2024;
    
    -- 插入新增表
    INSERT INTO overdue_debt_add (creditor, debtor, amount, period, is_litigation, year, `1月`)
    VALUES (v_creditor, v_debtor, v_amount, '2024年新增', 1, v_year, v_amount);
    
    -- 插入减值准备表（必须一致）
    INSERT INTO impairment_reserve (creditor, debtor, period, is_litigation, year, month, beginning_balance)
    VALUES (v_creditor, v_debtor, '2024年新增', 1, v_year, 1, v_amount);
    
    -- 插入诉讼表（必须一致）
    INSERT INTO litigation_claim (creditor, debtor, year, month, claim_amount)
    VALUES (v_creditor, v_debtor, v_year, 1, v_amount);
    
    -- 验证一致性
    SELECT 'Consistency Check:' as Status,
           (SELECT `1月` FROM overdue_debt_add WHERE creditor = v_creditor) as debt_amount,
           (SELECT beginning_balance FROM impairment_reserve WHERE creditor = v_creditor) as impairment_amount,
           (SELECT claim_amount FROM litigation_claim WHERE creditor = v_creditor) as litigation_amount;
END //

DELIMITER ;
```

### 4.2 边界条件数据

```sql
-- test-data/scenarios/boundary_cases.sql
-- 边界值测试数据

-- 最大金额
INSERT INTO overdue_debt_add (creditor, debtor, amount, period, is_litigation, year, `12月`)
VALUES ('BOUNDARY_MAX', 'DEBTOR_MAX', 999999999999.99, '2024年新增', 1, 2024, 999999999999.99);

-- 最小金额（0.01）
INSERT INTO overdue_debt_add (creditor, debtor, amount, period, is_litigation, year, `1月`)
VALUES ('BOUNDARY_MIN', 'DEBTOR_MIN', 0.01, '2024年新增', 0, 2024, 0.01);

-- 零金额
INSERT INTO overdue_debt_add (creditor, debtor, amount, period, is_litigation, year, `1月`)
VALUES ('BOUNDARY_ZERO', 'DEBTOR_ZERO', 0.00, '2024年新增', 0, 2024, 0.00);

-- 名称长度边界
INSERT INTO overdue_debt_add (
    creditor, 
    debtor, 
    amount, period, is_litigation, year, `1月`
) VALUES (
    REPEAT('A', 100),  -- 最大长度债权人名称
    REPEAT('B', 100),  -- 最大长度债务人名称
    100000.00, '2024年新增', 1, 2024, 100000.00
);

-- 全部月份都有数据
INSERT INTO overdue_debt_add (
    creditor, debtor, amount, period, is_litigation, year,
    `1月`, `2月`, `3月`, `4月`, `5月`, `6月`,
    `7月`, `8月`, `9月`, `10月`, `11月`, `12月`
) VALUES (
    'BOUNDARY_ALL_MONTHS', 'DEBTOR_ALL_MONTHS', 1200000.00, '2024年新增', 1, 2024,
    100000, 100000, 100000, 100000, 100000, 100000,
    100000, 100000, 100000, 100000, 100000, 100000
);
```

### 4.3 异常数据

```sql
-- test-data/scenarios/exception_cases.sql
-- 异常数据用于测试错误处理

-- 缺失关联数据（孤立记录）
INSERT INTO overdue_debt_decrease (creditor, debtor, period, is_litigation, year, month, cash_disposal)
VALUES ('ORPHAN_CREDITOR', 'ORPHAN_DEBTOR', '2024年新增', 1, 2024, 6, 50000.00);
-- 注意：没有对应的overdue_debt_add记录

-- 数据不一致（金额不匹配）
INSERT INTO overdue_debt_add (creditor, debtor, amount, period, is_litigation, year, `6月`)
VALUES ('INCONSISTENT_BANK', 'INCONSISTENT_DEBTOR', 100000.00, '2024年新增', 1, 2024, 100000.00);

INSERT INTO impairment_reserve (creditor, debtor, period, is_litigation, year, month, beginning_balance)
VALUES ('INCONSISTENT_BANK', 'INCONSISTENT_DEBTOR', '2024年新增', 1, 2024, 6, 200000.00);
-- 注意：减值准备金额大于债权金额

-- 重复主键数据（用于测试唯一性约束）
-- 这些插入应该失败，用于测试错误处理
-- INSERT INTO overdue_debt_add (...) VALUES (...);
-- INSERT INTO overdue_debt_add (...) VALUES (...); -- 相同主键
```

## 5. 测试数据生成工具

### 5.1 数据生成器实现

```java
@Component
public class TestDataGenerator {
    
    private final Faker faker = new Faker(new Locale("zh-CN"));
    private final Random random = new Random();
    
    /**
     * 生成债权测试数据
     */
    public DebtRecord generateDebtRecord() {
        return DebtRecord.builder()
            .creditor(generateCreditorName())
            .debtor(generateDebtorName())
            .amount(generateAmount())
            .period(generatePeriod())
            .isLitigation(random.nextBoolean())
            .year(Year.now().getValue())
            .build();
    }
    
    /**
     * 生成批量测试数据
     */
    public List<DebtRecord> generateBatchDebtRecords(int count) {
        return Stream.generate(this::generateDebtRecord)
            .limit(count)
            .collect(Collectors.toList());
    }
    
    /**
     * 生成符合业务规则的债权人名称
     */
    private String generateCreditorName() {
        String[] prefixes = {"测试", "模拟", "演示"};
        String[] types = {"银行", "信托", "保险公司", "担保公司", "小贷公司"};
        String[] suffixes = {"A", "B", "C", "D", "E"};
        
        return prefixes[random.nextInt(prefixes.length)] +
               types[random.nextInt(types.length)] +
               suffixes[random.nextInt(suffixes.length)];
    }
    
    /**
     * 生成债务人名称
     */
    private String generateDebtorName() {
        if (random.nextBoolean()) {
            // 企业债务人
            return faker.company().name() + "有限公司";
        } else {
            // 个人债务人
            return faker.name().fullName();
        }
    }
    
    /**
     * 生成合理的金额分布
     */
    private BigDecimal generateAmount() {
        // 80% 小额 (1万-100万)
        // 15% 中额 (100万-1000万)
        // 5% 大额 (1000万以上)
        double probability = random.nextDouble();
        
        if (probability < 0.8) {
            return BigDecimal.valueOf(10000 + random.nextInt(990000));
        } else if (probability < 0.95) {
            return BigDecimal.valueOf(1000000 + random.nextInt(9000000));
        } else {
            return BigDecimal.valueOf(10000000 + random.nextInt(90000000));
        }
    }
    
    /**
     * 生成月度分布数据
     */
    public Map<String, BigDecimal> generateMonthlyDistribution(
            BigDecimal totalAmount, 
            DistributionPattern pattern) {
        
        Map<String, BigDecimal> monthlyData = new HashMap<>();
        
        switch (pattern) {
            case EVEN:
                // 平均分配
                BigDecimal monthlyAmount = totalAmount.divide(
                    BigDecimal.valueOf(12), 2, RoundingMode.DOWN
                );
                for (int i = 1; i <= 12; i++) {
                    monthlyData.put(i + "月", monthlyAmount);
                }
                break;
                
            case QUARTERLY:
                // 季度分配
                BigDecimal quarterlyAmount = totalAmount.divide(
                    BigDecimal.valueOf(4), 2, RoundingMode.DOWN
                );
                monthlyData.put("3月", quarterlyAmount);
                monthlyData.put("6月", quarterlyAmount);
                monthlyData.put("9月", quarterlyAmount);
                monthlyData.put("12月", quarterlyAmount);
                break;
                
            case RANDOM:
                // 随机分配
                List<BigDecimal> distributions = distributeRandomly(totalAmount, 12);
                for (int i = 0; i < 12; i++) {
                    if (distributions.get(i).compareTo(BigDecimal.ZERO) > 0) {
                        monthlyData.put((i + 1) + "月", distributions.get(i));
                    }
                }
                break;
                
            case GROWING:
                // 递增分配
                BigDecimal baseAmount = totalAmount.divide(
                    BigDecimal.valueOf(78), 2, RoundingMode.DOWN
                ); // 1+2+...+12=78
                for (int i = 1; i <= 12; i++) {
                    monthlyData.put(i + "月", baseAmount.multiply(BigDecimal.valueOf(i)));
                }
                break;
        }
        
        return monthlyData;
    }
    
    public enum DistributionPattern {
        EVEN,      // 平均分布
        QUARTERLY, // 季度分布
        RANDOM,    // 随机分布
        GROWING    // 递增分布
    }
}
```

### 5.2 数据验证工具

```java
@Component
public class TestDataValidator {
    
    /**
     * 验证测试数据完整性
     */
    public ValidationResult validateTestData(String scenario) {
        ValidationResult result = new ValidationResult();
        
        // 验证必需的表有数据
        validateTableData("overdue_debt_add", result);
        validateTableData("overdue_debt_decrease", result);
        validateTableData("impairment_reserve", result);
        
        // 验证数据关系
        validateDataRelationships(result);
        
        // 验证数据一致性
        validateDataConsistency(result);
        
        return result;
    }
    
    private void validateTableData(String tableName, ValidationResult result) {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        
        if (count == null || count == 0) {
            result.addError(tableName + " 表没有测试数据");
        } else {
            result.addInfo(tableName + " 表有 " + count + " 条测试数据");
        }
    }
    
    private void validateDataRelationships(ValidationResult result) {
        // 验证外键关系
        String sql = """
            SELECT COUNT(*) 
            FROM overdue_debt_decrease d
            WHERE NOT EXISTS (
                SELECT 1 FROM overdue_debt_add a
                WHERE a.creditor = d.creditor 
                AND a.debtor = d.debtor
                AND a.year = d.year
            )
        """;
        
        Integer orphanCount = jdbcTemplate.queryForObject(sql, Integer.class);
        if (orphanCount > 0) {
            result.addWarning("发现 " + orphanCount + " 条孤立的处置记录");
        }
    }
    
    private void validateDataConsistency(ValidationResult result) {
        // 验证金额一致性
        String sql = """
            SELECT creditor, debtor, year,
                   SUM(add_amount) as total_add,
                   SUM(decrease_amount) as total_decrease,
                   SUM(add_amount) - SUM(decrease_amount) as balance
            FROM (
                SELECT creditor, debtor, year, amount as add_amount, 0 as decrease_amount
                FROM overdue_debt_add
                UNION ALL
                SELECT creditor, debtor, year, 0 as add_amount, total_disposal as decrease_amount
                FROM overdue_debt_decrease
            ) t
            GROUP BY creditor, debtor, year
            HAVING balance < 0
        """;
        
        List<Map<String, Object>> inconsistencies = jdbcTemplate.queryForList(sql);
        if (!inconsistencies.isEmpty()) {
            result.addError("发现 " + inconsistencies.size() + " 条金额不一致的记录");
        }
    }
}
```

## 6. 数据加载策略

### 6.1 Spring Boot测试配置

```java
@TestConfiguration
public class TestDataConfiguration {
    
    @Bean
    @Profile("test")
    public DataSourceInitializer dataSourceInitializer(DataSource dataSource) {
        DataSourceInitializer initializer = new DataSourceInitializer();
        initializer.setDataSource(dataSource);
        
        // 设置schema初始化
        ResourceDatabasePopulator schemaPopulator = new ResourceDatabasePopulator();
        schemaPopulator.addScript(new ClassPathResource("test-data/schema.sql"));
        initializer.setDatabasePopulator(schemaPopulator);
        
        return initializer;
    }
    
    @Bean
    @Profile("test")
    public TestDataLoader testDataLoader() {
        return new TestDataLoader();
    }
    
    @EventListener(ApplicationReadyEvent.class)
    @Profile("test")
    public void loadTestDataOnStartup() {
        log.info("Loading test data...");
        testDataLoader().loadBaseline("v1.0");
        log.info("Test data loaded successfully");
    }
}
```

### 6.2 测试数据生命周期管理

```java
@Component
public class TestDataLifecycleManager {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 测试前准备数据
     */
    public void setupTestData(TestContext context) {
        // 清理旧数据
        cleanupTestData(context.getTestId());
        
        // 加载基准数据
        loadBaselineData(context.getBaselineVersion());
        
        // 加载场景数据
        if (context.hasScenarioData()) {
            loadScenarioData(context.getScenario());
        }
        
        // 记录数据快照
        createDataSnapshot(context.getTestId());
    }
    
    /**
     * 测试后清理数据
     */
    public void teardownTestData(TestContext context) {
        if (context.shouldPreserveData()) {
            // 保留数据用于调试
            markDataForPreservation(context.getTestId());
        } else {
            // 清理测试数据
            cleanupTestData(context.getTestId());
        }
    }
    
    /**
     * 创建数据快照
     */
    private void createDataSnapshot(String testId) {
        String snapshotTable = "test_snapshot_" + testId;
        
        // 创建快照表
        jdbcTemplate.execute(
            "CREATE TABLE " + snapshotTable + " AS " +
            "SELECT 'overdue_debt_add' as table_name, COUNT(*) as record_count " +
            "FROM overdue_debt_add " +
            "UNION ALL " +
            "SELECT 'overdue_debt_decrease', COUNT(*) FROM overdue_debt_decrease"
        );
    }
    
    /**
     * 验证数据状态
     */
    public boolean verifyDataState(String testId, ExpectedState expectedState) {
        String snapshotTable = "test_snapshot_" + testId;
        
        Map<String, Integer> currentState = getCurrentDataState();
        Map<String, Integer> snapshotState = getSnapshotState(snapshotTable);
        
        return compareStates(currentState, snapshotState, expectedState);
    }
}
```

## 7. 测试数据管理最佳实践

### 7.1 数据隔离策略

```java
@TestComponent
public class TestDataIsolation {
    
    /**
     * 为每个测试创建独立的数据命名空间
     */
    public String createTestNamespace(String testName) {
        String namespace = "TEST_" + testName.toUpperCase() + "_" + 
                          System.currentTimeMillis();
        
        // 所有测试数据都使用这个前缀
        return namespace;
    }
    
    /**
     * 使用命名空间创建测试数据
     */
    public DebtRecord createIsolatedDebtRecord(String namespace) {
        return DebtRecord.builder()
            .creditor(namespace + "_CREDITOR")
            .debtor(namespace + "_DEBTOR")
            .amount(new BigDecimal("100000"))
            .build();
    }
    
    /**
     * 清理特定命名空间的数据
     */
    @Transactional
    public void cleanupNamespace(String namespace) {
        // 清理所有表中该命名空间的数据
        jdbcTemplate.update(
            "DELETE FROM overdue_debt_add WHERE creditor LIKE ?",
            namespace + "%"
        );
        
        jdbcTemplate.update(
            "DELETE FROM overdue_debt_decrease WHERE creditor LIKE ?",
            namespace + "%"
        );
        
        // ... 清理其他表
    }
}
```

### 7.2 数据版本控制

```sql
-- test-data/migrations/V1.0__Initial_test_data.sql
-- Flyway格式的测试数据迁移脚本

-- 创建测试数据版本表
CREATE TABLE IF NOT EXISTS test_data_version (
    version VARCHAR(10) PRIMARY KEY,
    applied_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    checksum VARCHAR(64),
    description TEXT
);

-- 记录版本
INSERT INTO test_data_version (version, checksum, description) 
VALUES ('1.0', SHA2('initial_test_data', 256), 'Initial test data setup');

-- 执行数据导入
SOURCE test-data/baseline/v1.0/all_data.sql;
```

### 7.3 敏感数据处理

```java
@Component
public class TestDataMasker {
    
    /**
     * 脱敏生产数据用于测试
     */
    public void maskSensitiveData() {
        // 脱敏个人身份信息
        jdbcTemplate.update("""
            UPDATE debtor_master 
            SET identity_no = CONCAT('TEST_', SUBSTR(MD5(identity_no), 1, 18))
            WHERE identity_no IS NOT NULL
        """);
        
        // 脱敏银行账号
        jdbcTemplate.update("""
            UPDATE bank_account 
            SET account_no = CONCAT('****', SUBSTR(account_no, -4))
            WHERE account_no IS NOT NULL
        """);
        
        // 脱敏联系方式
        jdbcTemplate.update("""
            UPDATE contact_info 
            SET phone = CONCAT(SUBSTR(phone, 1, 3), '****', SUBSTR(phone, -4)),
                email = CONCAT('test_', MD5(email), '@example.com')
            WHERE phone IS NOT NULL OR email IS NOT NULL
        """);
    }
    
    /**
     * 生成符合格式的测试数据
     */
    public String generateTestIdentityNo() {
        // 生成测试用身份证号（不会与真实身份证冲突）
        return "999999" + // 测试地区代码
               "********" + // 测试出生日期
               String.format("%04d", random.nextInt(10000)); // 随机序号
    }
}
```

## 8. 数据准备脚本

### 8.1 一键准备脚本

```bash
#!/bin/bash
# scripts/prepare-test-data.sh

# 配置
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_NAME=${DB_NAME:-financial_test}
DB_USER=${DB_USER:-test_user}
DB_PASS=${DB_PASS:-test_pass}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}=== 测试数据准备工具 ===${NC}"

# 函数：执行SQL文件
execute_sql() {
    local file=$1
    local desc=$2
    
    echo -e "${YELLOW}正在执行: ${desc}${NC}"
    mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} < ${file}
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ ${desc} 完成${NC}"
    else
        echo -e "${RED}✗ ${desc} 失败${NC}"
        exit 1
    fi
}

# 函数：验证数据
verify_data() {
    echo -e "${YELLOW}验证数据完整性...${NC}"
    
    local result=$(mysql -h${DB_HOST} -P${DB_PORT} -u${DB_USER} -p${DB_PASS} ${DB_NAME} \
        -e "SELECT COUNT(*) as count FROM overdue_debt_add" -s -N)
    
    if [ $result -gt 0 ]; then
        echo -e "${GREEN}✓ 数据验证通过，共 ${result} 条记录${NC}"
    else
        echo -e "${RED}✗ 数据验证失败${NC}"
        exit 1
    fi
}

# 主流程
main() {
    # 1. 清理旧数据
    echo -e "${YELLOW}清理旧数据...${NC}"
    execute_sql "test-data/cleanup.sql" "清理旧数据"
    
    # 2. 创建表结构
    execute_sql "test-data/schema.sql" "创建表结构"
    
    # 3. 加载基准数据
    execute_sql "test-data/baseline/current/all_data.sql" "加载基准数据"
    
    # 4. 加载场景数据（可选）
    if [ "$1" == "--with-scenarios" ]; then
        for scenario in test-data/scenarios/*.sql; do
            execute_sql "$scenario" "加载场景: $(basename $scenario)"
        done
    fi
    
    # 5. 验证数据
    verify_data
    
    echo -e "${GREEN}=== 测试数据准备完成 ===${NC}"
}

# 执行主流程
main $@
```

### 8.2 数据重置脚本

```sql
-- test-data/reset-test-data.sql
-- 快速重置测试数据到初始状态

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清空所有数据表
TRUNCATE TABLE overdue_debt_add;
TRUNCATE TABLE overdue_debt_decrease;
TRUNCATE TABLE impairment_reserve;
TRUNCATE TABLE litigation_claim;
TRUNCATE TABLE non_litigation_claim;
TRUNCATE TABLE user_account;
TRUNCATE TABLE audit_log;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 重新加载基准数据
SOURCE test-data/baseline/current/all_data.sql;

-- 验证数据加载
SELECT 
    'overdue_debt_add' as table_name, 
    COUNT(*) as record_count 
FROM overdue_debt_add
UNION ALL
SELECT 'overdue_debt_decrease', COUNT(*) FROM overdue_debt_decrease
UNION ALL
SELECT 'impairment_reserve', COUNT(*) FROM impairment_reserve;
```

## 9. 性能测试数据准备

### 9.1 大数据量生成

```java
@Component
public class PerformanceTestDataGenerator {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 批量生成性能测试数据
     */
    public void generatePerformanceData(int targetRecords) {
        int batchSize = 1000;
        int batches = targetRecords / batchSize;
        
        log.info("生成 {} 条性能测试数据，分 {} 批执行", targetRecords, batches);
        
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        for (int i = 0; i < batches; i++) {
            generateBatch(i, batchSize);
            
            if (i % 10 == 0) {
                log.info("进度: {}/{}", i * batchSize, targetRecords);
            }
        }
        
        stopWatch.stop();
        log.info("数据生成完成，耗时: {} 秒", stopWatch.getTotalTimeSeconds());
    }
    
    private void generateBatch(int batchIndex, int batchSize) {
        String sql = """
            INSERT INTO overdue_debt_add 
            (creditor, debtor, amount, period, is_litigation, year, `1月`)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """;
        
        List<Object[]> batchArgs = new ArrayList<>();
        
        for (int i = 0; i < batchSize; i++) {
            int index = batchIndex * batchSize + i;
            
            batchArgs.add(new Object[]{
                "PERF_CREDITOR_" + index,
                "PERF_DEBTOR_" + index,
                generateRandomAmount(),
                "2024年新增",
                index % 2,  // 交替涉诉/非涉诉
                2024,
                generateRandomAmount()
            });
        }
        
        jdbcTemplate.batchUpdate(sql, batchArgs);
    }
    
    /**
     * 生成符合真实分布的金额
     */
    private BigDecimal generateRandomAmount() {
        // 使用正态分布生成更真实的金额分布
        double mean = 500000;  // 平均50万
        double stdDev = 200000;  // 标准差20万
        
        double amount = random.nextGaussian() * stdDev + mean;
        amount = Math.max(1000, amount);  // 最小1000
        amount = Math.min(10000000, amount);  // 最大1000万
        
        return BigDecimal.valueOf(amount).setScale(2, RoundingMode.HALF_UP);
    }
}
```

### 9.2 索引优化建议

```sql
-- test-data/performance/indexes.sql
-- 性能测试专用索引

-- 债权查询优化索引
CREATE INDEX idx_debt_creditor_year_month 
ON overdue_debt_add(creditor, year, period);

CREATE INDEX idx_debt_amount_range 
ON overdue_debt_add(amount);

-- 处置查询优化索引
CREATE INDEX idx_disposal_date_range 
ON overdue_debt_decrease(year, month, total_disposal);

-- 复合查询优化索引
CREATE INDEX idx_debt_composite 
ON overdue_debt_add(creditor, debtor, year, is_litigation);

-- 统计查询优化索引
CREATE INDEX idx_impairment_summary 
ON impairment_reserve(year, month, ending_balance);

-- 分析索引使用情况
ANALYZE TABLE overdue_debt_add;
ANALYZE TABLE overdue_debt_decrease;
ANALYZE TABLE impairment_reserve;
```

## 10. 数据维护和监控

### 10.1 数据健康检查

```java
@Component
@Slf4j
public class TestDataHealthChecker {
    
    @Scheduled(cron = "0 0 * * * *")  // 每小时执行
    public void checkTestDataHealth() {
        HealthCheckResult result = new HealthCheckResult();
        
        // 检查数据量
        checkDataVolume(result);
        
        // 检查数据分布
        checkDataDistribution(result);
        
        // 检查数据一致性
        checkDataConsistency(result);
        
        // 检查性能指标
        checkPerformanceMetrics(result);
        
        // 生成报告
        generateHealthReport(result);
    }
    
    private void checkDataVolume(HealthCheckResult result) {
        Map<String, Long> tableCounts = new HashMap<>();
        
        String[] tables = {
            "overdue_debt_add",
            "overdue_debt_decrease",
            "impairment_reserve",
            "litigation_claim"
        };
        
        for (String table : tables) {
            Long count = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM " + table, Long.class
            );
            tableCounts.put(table, count);
            
            if (count < 100) {
                result.addWarning(table + " 数据量过少: " + count);
            }
        }
        
        result.setTableCounts(tableCounts);
    }
}
```

## 11. 总结

### 11.1 测试数据准备检查清单

- [ ] 基准数据集已创建并版本化
- [ ] 场景数据覆盖所有业务流程
- [ ] 边界数据包含所有极限情况
- [ ] 异常数据用于错误处理测试
- [ ] 数据生成工具可用且高效
- [ ] 数据加载脚本已测试
- [ ] 敏感数据已脱敏处理
- [ ] 性能测试数据量充足
- [ ] 数据隔离机制正常工作
- [ ] 数据健康监控已配置

### 11.2 持续改进建议

1. **定期更新基准数据**：随业务发展更新测试数据
2. **收集真实场景**：从生产问题中提取测试场景
3. **优化数据生成**：提高大数据量生成效率
4. **完善数据验证**：增加更多数据质量检查
5. **自动化数据准备**：集成到CI/CD流程

记住：好的测试数据是测试成功的一半！