# FinancialSystem Docker Compose 配置 - 支持全数据库双向同步
# 此配置支持 Docker MySQL 参与多主复制架构

services:
  # MySQL数据库服务 - 启用同步支持
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: financial-mysql-sync
    pull_policy: never  # 禁止拉取，仅使用本地镜像
    environment:
      MYSQL_ROOT_PASSWORD: Zlb&198838
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
      # 复制相关环境变量
      MYSQL_REPLICATION_MODE: master
      MYSQL_REPLICATION_USER: repl_universal
      MYSQL_REPLICATION_PASSWORD: Zlb&198838
    ports:
      - "3307:3306"  # 使用不同端口避免冲突
    volumes:
      - mysql_sync_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf:ro
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
      - mysql_logs:/var/log/mysql
    command: >
      --default-authentication-plugin=mysql_native_password
      --server-id=3
      --log-bin=mysql-bin
      --binlog-format=ROW
      --gtid-mode=ON
      --enforce-gtid-consistency=ON
      --log-slave-updates=ON
      --auto-increment-increment=3
      --auto-increment-offset=3
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-pZlb&198838"]
      timeout: 20s
      retries: 10
      interval: 30s
    networks:
      - financial-network

  # 后端构建服务
  backend-builder:
    image: maven:3.9-eclipse-temurin-21
    platform: linux/amd64
    container_name: financial-backend-builder
    pull_policy: never
    volumes:
      - .:/app
      - maven_cache:/root/.m2
    working_dir: /app
    command: >
      sh -c "
        echo '开始构建后端项目...' &&
        mvn clean package -DskipTests -pl api-gateway -am &&
        echo '构建完成，JAR文件位置:' &&
        ls -la api-gateway/target/api-gateway-*.jar &&
        echo '构建服务完成，等待后端服务启动...'
      "
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - financial-network

  # 后端运行服务
  backend:
    image: openjdk:21-jdk
    platform: linux/amd64
    container_name: financial-backend
    pull_policy: never
    volumes:
      - ./api-gateway/target:/app
    working_dir: /app
    command: >
      sh -c "
        echo '等待JAR文件生成...' &&
        while [ ! -f api-gateway-1.0-SNAPSHOT.jar ]; do sleep 2; done &&
        echo '开始启动后端服务...' &&
        java -jar api-gateway-1.0-SNAPSHOT.jar
      "
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker-sync
      # 数据源配置 - 支持多环境
      - SPRING_DATASOURCE_PRIMARY_URL=*************************************************************************************************************************************************************
      - SPRING_DATASOURCE_SECONDARY_URL=*********************************************************************************************************************
      - SPRING_DATASOURCE_USER_SYSTEM_URL=*******************************************************************************************************************************************************
      # 外部MySQL连接配置（用于同步验证）
      - EXTERNAL_MYSQL_LOCAL_URL=***************************************************************************************************************************
      - EXTERNAL_MYSQL_LINUX_URL=*****************************************************************************************************************
    depends_on:
      backend-builder:
        condition: service_completed_successfully
      mysql:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - financial-network

  # 前端构建服务
  frontend-builder:
    image: node:18-alpine
    platform: linux/amd64
    container_name: financial-frontend-builder
    pull_policy: never
    volumes:
      - .:/app
      - node_modules_cache:/app/FinancialSystem-web/node_modules
    working_dir: /app/FinancialSystem-web
    command: >
      sh -c "
        echo '开始构建前端项目...' &&
        npm install &&
        npm run build &&
        echo '前端构建完成'
      "
    environment:
      - REACT_APP_API_URL=/api
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - financial-network

  # Nginx Web服务器
  nginx:
    image: nginx:alpine
    platform: linux/amd64
    container_name: financial-nginx
    pull_policy: never
    volumes:
      - ./FinancialSystem-web/build:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
    depends_on:
      - frontend-builder
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - financial-network

  # MySQL同步监控服务
  sync-monitor:
    image: mysql:8.0-client
    platform: linux/amd64
    container_name: financial-sync-monitor
    pull_policy: never
    volumes:
      - ./scripts/database:/scripts:ro
      - sync_logs:/var/log/sync
    working_dir: /scripts
    command: >
      sh -c "
        echo '等待MySQL服务启动...' &&
        sleep 60 &&
        echo '开始MySQL同步监控...' &&
        bash universal-bidirectional-sync-setup.sh monitor
      "
    environment:
      - MYSQL_PWD=Zlb&198838
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - financial-network

volumes:
  mysql_sync_data:
    driver: local
  mysql_logs:
    driver: local
  sync_logs:
    driver: local
  maven_cache:
    driver: local
  node_modules_cache:
    driver: local

networks:
  financial-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16