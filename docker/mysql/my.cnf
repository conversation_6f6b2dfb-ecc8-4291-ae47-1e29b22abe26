# MySQL 8.0 配置文件 - 支持全数据库双向同步
# 用于 Docker 容器中的 MySQL 实例

[mysqld]
# ====================================
# 基础配置
# ====================================
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
skip-external-locking

# ====================================
# 字符集配置
# ====================================
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# ====================================
# 复制配置（核心部分）
# ====================================
# 服务器唯一标识（根据部署环境调整）
server_id = 3

# 启用二进制日志
log_bin = mysql-bin
binlog_format = ROW
sync_binlog = 1
binlog_expire_logs_seconds = 604800

# GTID 全局事务标识符
gtid_mode = ON
enforce_gtid_consistency = ON
log_slave_updates = ON

# 全数据库复制配置
# 复制所有数据库（除系统数据库外）
replicate_wild_do_table = overdue_debt_db.%
replicate_wild_do_table = user_system.%
replicate_wild_do_table = kingdee.%
# 支持新增数据库的通配符匹配
replicate_wild_ignore_table = mysql.%
replicate_wild_ignore_table = information_schema.%
replicate_wild_ignore_table = performance_schema.%
replicate_wild_ignore_table = sys.%

# AUTO_INCREMENT 配置（避免主键冲突）
# Docker MySQL 使用第三组ID
auto_increment_increment = 3
auto_increment_offset = 3

# ====================================
# 性能优化配置
# ====================================
# 并行复制配置
slave_parallel_workers = 4
slave_parallel_type = LOGICAL_CLOCK
slave_preserve_commit_order = ON
slave_pending_jobs_size_max = 128M

# 连接池配置
max_connections = 200
max_connect_errors = 1000

# 缓存配置
query_cache_type = 0
query_cache_size = 0

# InnoDB 配置
innodb_buffer_pool_size = 512M
innodb_log_file_size = 128M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50
innodb_file_per_table = 1

# 网络优化
slave_net_timeout = 60
master_info_repository = TABLE
relay_log_info_repository = TABLE

# ====================================
# 日志配置
# ====================================
# 错误日志
log_error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 一般查询日志（开发环境可启用）
general_log = 0
general_log_file = /var/log/mysql/general.log

# ====================================
# 安全配置
# ====================================
# 默认认证插件
default_authentication_plugin = mysql_native_password

# 密码验证
validate_password.policy = LOW
validate_password.length = 6

# SQL 模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# ====================================
# 其他配置
# ====================================
# 时区设置
default_time_zone = '+8:00'

# 表名大小写敏感（Linux 系统建议设置为 1）
lower_case_table_names = 0

# 最大允许的数据包大小
max_allowed_packet = 128M

# 临时表大小
tmp_table_size = 64M
max_heap_table_size = 64M

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock