MIT License

Copyright (c) 2024-2025 FinancialSystem Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## Third-Party Licenses

This project includes third-party software components that are licensed under their own terms:

### Backend Dependencies

#### Spring Framework
- **License**: Apache License 2.0
- **Copyright**: Copyright (c) 2002-2024 Pivotal, Inc.
- **Website**: https://spring.io/

#### MySQL Connector/J
- **License**: GPL v2 with FOSS License Exception
- **Copyright**: Copyright (c) 2024, Oracle and/or its affiliates
- **Website**: https://dev.mysql.com/downloads/connector/j/

#### Hibernate ORM
- **License**: GNU Lesser General Public License v2.1
- **Copyright**: Copyright (c) 2001-2024 Red Hat, Inc.
- **Website**: https://hibernate.org/

#### Jackson JSON Processor
- **License**: Apache License 2.0
- **Copyright**: Copyright (c) 2007- Tatu Saloranta, <EMAIL>
- **Website**: https://github.com/FasterXML/jackson

#### SLF4J - Simple Logging Facade for Java
- **License**: MIT License
- **Copyright**: Copyright (c) 2004-2024 QOS.ch
- **Website**: http://www.slf4j.org/

#### Logback
- **License**: Eclipse Public License v1.0 and GNU Lesser General Public License v2.1
- **Copyright**: Copyright (C) 1999-2024, QOS.ch
- **Website**: http://logback.qos.ch/

#### Apache Maven
- **License**: Apache License 2.0
- **Copyright**: Copyright 2001-2024 The Apache Software Foundation
- **Website**: https://maven.apache.org/

#### JUnit 5
- **License**: Eclipse Public License v2.0
- **Copyright**: Copyright 2015-2024 the original author or authors
- **Website**: https://junit.org/junit5/

#### Mockito
- **License**: MIT License
- **Copyright**: Copyright (c) 2007 Mockito contributors
- **Website**: https://site.mockito.org/

### Frontend Dependencies

#### React
- **License**: MIT License
- **Copyright**: Copyright (c) Meta Platforms, Inc. and affiliates
- **Website**: https://reactjs.org/

#### Material-UI (MUI)
- **License**: MIT License
- **Copyright**: Copyright (c) 2014 Call-Em-All
- **Website**: https://mui.com/

#### Axios
- **License**: MIT License
- **Copyright**: Copyright (c) 2014-present Matt Zabriskie & Collaborators
- **Website**: https://axios-http.com/

#### React Router
- **License**: MIT License
- **Copyright**: Copyright (c) React Training LLC 2015-2019, Remix Software Inc. 2020-2024
- **Website**: https://reactrouter.com/

#### Emotion
- **License**: MIT License
- **Copyright**: Copyright (c) Emotion team and other contributors
- **Website**: https://emotion.sh/

#### JWT Decode
- **License**: MIT License
- **Copyright**: Copyright (c) 2015 Auth0, Inc.
- **Website**: https://github.com/auth0/jwt-decode

### Development Tools

#### Node.js
- **License**: MIT License
- **Copyright**: Copyright Node.js contributors. All rights reserved.
- **Website**: https://nodejs.org/

#### npm
- **License**: Artistic License 2.0
- **Copyright**: Copyright (c) npm, Inc. and Contributors
- **Website**: https://www.npmjs.com/

#### Webpack
- **License**: MIT License
- **Copyright**: Copyright JS Foundation and other contributors
- **Website**: https://webpack.js.org/

#### Babel
- **License**: MIT License
- **Copyright**: Copyright (c) 2014-present Sebastian McKenzie and other contributors
- **Website**: https://babeljs.io/

### Database

#### MySQL
- **License**: GPL v2 with FOSS License Exception
- **Copyright**: Copyright (c) 2000, 2024, Oracle and/or its affiliates
- **Website**: https://www.mysql.com/

### Container Technology

#### Docker
- **License**: Apache License 2.0
- **Copyright**: Copyright 2013-2024 Docker, Inc.
- **Website**: https://www.docker.com/

---

## License Compliance

This project complies with all applicable open source licenses. The use of third-party components is in accordance with their respective license terms.

### For Contributors

By contributing to this project, you agree that your contributions will be licensed under the MIT License. You also represent that you have the right to make such contributions and that your contributions do not violate any third-party rights.

### For Users

When using this software, please ensure compliance with all applicable licenses, including:

1. **Attribution**: Maintain copyright notices and license information
2. **Distribution**: Include license information when distributing the software
3. **Modifications**: Clearly mark any modifications made to the original software
4. **Commercial Use**: This software may be used for commercial purposes under the MIT License terms

### License Compatibility

The MIT License is compatible with most other open source licenses, including:
- Apache License 2.0
- BSD Licenses
- GPL v2 and v3 (when used as a library)
- LGPL v2.1 and v3

### Disclaimer

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

---

## Contact Information

For license-related questions or concerns, please contact:

- **Project Maintainer**: laoshu198838
- **Email**: <EMAIL>
- **GitHub**: https://github.com/laoshu198838/FinancialSystem

---

**Last Updated**: January 14, 2025
