# 数据库配置环境变量示例
# 复制此文件为 .env 并根据实际环境修改配置

# 数据库连接配置
DB_USERNAME=root
DB_PASSWORD=your_database_password

# 主数据源（逾期债权数据库）
DB_PRIMARY_URL=***********************************************************************************************************************************************************************

# 第二数据源（金蝶数据库）
DB_SECONDARY_URL=*************************************************************************************************************************

# 第三数据源（用户系统数据库）
DB_USER_SYSTEM_URL=***********************************************************************************************************************************************************

# JWT配置
JWT_SECRET=your-secure-jwt-secret-key-must-be-at-least-256-bits-long

# 跨域配置（开发环境）
APP_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# 生产环境示例
# APP_CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
