/**
 * 债权相关类型定义
 */

/**
 * 债权状态枚举
 */
export enum DebtStatus {
  NORMAL = 'normal',
  OVERDUE = 'overdue',
  LEGAL = 'legal',
  RESOLVED = 'resolved'
}

/**
 * 债权性质枚举
 */
export enum DebtNature {
  TRADE = 'trade', // 贸易债权
  LOAN = 'loan', // 借款债权
  OTHER = 'other' // 其他债权
}

/**
 * 逾期债权数据接口
 */
export interface OverdueDebtData {
  id: string;
  companyName: string;
  managementCompany: string;
  debtNature: DebtNature;
  principalAmount: number;
  overdueAmount: number;
  interestAmount: number;
  totalAmount: number;
  overdueDate: string;
  status: DebtStatus;
  createDate: string;
  updateDate: string;

  // 月度金额字段
  amountJan?: number;
  amountFeb?: number;
  amountMar?: number;
  amountApr?: number;
  amountMay?: number;
  amountJun?: number;
  amountJul?: number;
  amountAug?: number;
  amountSep?: number;
  amountOct?: number;
  amountNov?: number;
  amountDec?: number;
}

/**
 * 债权统计数据接口
 */
export interface DebtStatistics {
  totalAmount: number;
  overdueAmount: number;
  normalAmount: number;
  legalAmount: number;
  resolvedAmount: number;

  // 按公司分组统计
  byCompany: {
    companyName: string;
    amount: number;
    count: number;
  }[];

  // 按债权性质分组统计
  byNature: {
    nature: DebtNature;
    amount: number;
    count: number;
  }[];

  // 月度趋势数据
  monthlyTrend: {
    month: string;
    amount: number;
    count: number;
  }[];
}

/**
 * 债权新增数据接口
 */
export interface DebtAddData {
  companyName: string;
  managementCompany: string;
  debtNature: DebtNature;
  principalAmount: number;
  overdueDate: string;
  description?: string;

  // 月度分布
  monthlyAmounts: {
    [key: string]: number; // 月份 -> 金额
  };
}

/**
 * 债权处置数据接口
 */
export interface DebtDisposalData {
  id: string;
  debtId: string;
  disposalMethod: DisposalMethod;
  disposalAmount: number;
  disposalDate: string;
  effectiveDate: string;
  description?: string;
  status: DisposalStatus;
}

/**
 * 处置方式枚举
 */
export enum DisposalMethod {
  PAYMENT = 'payment', // 还款
  WRITE_OFF = 'write_off', // 核销
  TRANSFER = 'transfer', // 转让
  LEGAL = 'legal', // 诉讼
  NEGOTIATION = 'negotiation' // 协商
}

/**
 * 处置状态枚举
 */
export enum DisposalStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}
