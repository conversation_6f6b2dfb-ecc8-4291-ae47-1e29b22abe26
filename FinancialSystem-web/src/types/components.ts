/**
 * Material Dashboard 2 React - TypeScript definitions for MD components
 * This file provides proper TypeScript support for Material Dashboard components
 */

import { ReactNode, ComponentProps, HTMLAttributes } from 'react';
import { SxProps, Theme } from '@mui/material/styles';
import { BoxProps } from '@mui/material/Box';

/**
 * MDBox Component Props
 * Combines Material-UI Box props with custom MD styling props
 * BoxProps already includes all spacing (m*, p*), layout (display, alignItems, etc) props
 */
export interface MDBoxProps extends BoxProps {
  variant?: 'contained' | 'gradient';
  bgColor?: string;
  color?: string;
  opacity?: number;
  borderRadius?: string;
  shadow?: string;
  coloredShadow?:
    | 'primary'
    | 'secondary'
    | 'info'
    | 'success'
    | 'warning'
    | 'error'
    | 'light'
    | 'dark'
    | 'none';
  children?: ReactNode;
  sx?: SxProps<Theme>;
  // Note: All standard Box props (display, alignItems, justifyContent, textAlign, spacing, sizing, etc.)
  // are already included via BoxProps and don't need to be redefined here.
  // Only define custom props specific to MDBox.
}

/**
 * MDTypography Component Props
 */
export interface MDTypographyProps {
  variant?:
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'subtitle1'
    | 'subtitle2'
    | 'body1'
    | 'body2'
    | 'caption'
    | 'button'
    | 'overline';
  color?:
    | 'inherit'
    | 'primary'
    | 'secondary'
    | 'textPrimary'
    | 'textSecondary'
    | 'error'
    | 'info'
    | 'success'
    | 'warning'
    | 'text'
    | 'white'
    | 'dark'
    | 'light';
  fontWeight?: 'light' | 'regular' | 'medium' | 'bold';
  textTransform?: 'none' | 'capitalize' | 'uppercase' | 'lowercase';
  verticalAlign?:
    | 'unset'
    | 'baseline'
    | 'sub'
    | 'super'
    | 'text-top'
    | 'text-bottom'
    | 'middle'
    | 'top'
    | 'bottom';
  textGradient?: boolean;
  opacity?: number;
  children?: ReactNode;
  component?: React.ElementType;
  sx?: SxProps<Theme>;
  // Common typography props
  fontSize?: string | number;
  lineHeight?: string | number;
  letterSpacing?: string | number;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  display?: string;
  // Additional props
  className?: string;
  style?: React.CSSProperties;
}

/**
 * MDButton Component Props
 */
export interface MDButtonProps {
  variant?: 'text' | 'contained' | 'outlined' | 'gradient';
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'info'
    | 'success'
    | 'warning'
    | 'error'
    | 'light'
    | 'dark'
    | 'white';
  size?: 'small' | 'medium' | 'large';
  circular?: boolean;
  iconOnly?: boolean;
  children?: ReactNode;
  fullWidth?: boolean;
  disabled?: boolean;
  sx?: SxProps<Theme>;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  component?: React.ElementType;
  href?: string;
  target?: string;
  rel?: string;
}

/**
 * MDInput Component Props
 */
export interface MDInputProps {
  variant?: 'standard' | 'outlined' | 'filled';
  size?: 'small' | 'medium';
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error';
  disabled?: boolean;
  success?: boolean;
  error?: boolean;
  label?: string;
  placeholder?: string;
  value?: string | number;
  multiline?: boolean;
  rows?: number;
  maxRows?: number;
  fullWidth?: boolean;
  required?: boolean;
  helperText?: string;
  inputProps?: any;
  InputProps?: any;
  InputLabelProps?: any;
  FormHelperTextProps?: any;
  sx?: SxProps<Theme>;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  type?: string;
  name?: string;
  id?: string;
}

/**
 * MDAlert Component Props
 */
export interface MDAlertProps {
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark';
  dismissible?: boolean;
  children?: ReactNode;
  sx?: SxProps<Theme>;
}

/**
 * MDAvatar Component Props
 */
export interface MDAvatarProps {
  alt?: string;
  src?: string;
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  variant?: 'rounded' | 'square';
  children?: ReactNode;
  sx?: SxProps<Theme>;
}

/**
 * MDBadge Component Props
 */
export interface MDBadgeProps {
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark';
  variant?: 'gradient' | 'contained';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  circular?: boolean;
  indicator?: boolean;
  border?: boolean;
  container?: boolean;
  children?: ReactNode;
  sx?: SxProps<Theme>;
}

/**
 * MDProgress Component Props
 */
export interface MDProgressProps {
  variant?: 'determinate' | 'indeterminate' | 'gradient';
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark';
  value?: number;
  label?: boolean;
  sx?: SxProps<Theme>;
}

/**
 * MDSnackbar Component Props
 */
export interface MDSnackbarProps {
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'light' | 'dark';
  icon?: ReactNode;
  title?: string;
  content?: ReactNode;
  dateTime?: string;
  open?: boolean;
  onClose?: () => void;
  close?: () => void;
  bgWhite?: boolean;
  sx?: SxProps<Theme>;
}

/**
 * MDPagination Component Props
 */
export interface MDPaginationProps {
  item?: boolean;
  variant?: 'text' | 'outlined' | 'contained' | 'gradient';
  color?:
    | 'primary'
    | 'secondary'
    | 'info'
    | 'success'
    | 'warning'
    | 'error'
    | 'light'
    | 'dark'
    | 'white';
  size?: 'small' | 'medium' | 'large';
  active?: boolean;
  disabled?: boolean;
  children?: ReactNode;
  sx?: SxProps<Theme>;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

/**
 * Generic MD Component Props
 */
export interface MDComponentProps extends HTMLAttributes<HTMLElement> {
  children?: ReactNode;
  sx?: SxProps<Theme>;
  component?: React.ElementType;
}

/**
 * Common style props that most MD components accept
 */
export interface CommonMDProps {
  sx?: SxProps<Theme>;
  className?: string;
  style?: React.CSSProperties;
  children?: ReactNode;
}
