/**
 * 图表相关类型定义
 */

/**
 * 图表类型枚举
 */
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  DOUGHNUT = 'doughnut',
  AREA = 'area',
  SCATTER = 'scatter'
}

/**
 * 颜色主题枚举
 */
export enum ColorTheme {
  PRIMARY = 'primary',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  INFO = 'info'
}

/**
 * 图表数据点接口
 */
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: any;
}

/**
 * 线形图数据接口
 */
export interface LineChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    fill?: boolean;
    tension?: number;
  }[];
}

/**
 * 柱状图数据接口
 */
export interface BarChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

/**
 * 饼图数据接口
 */
export interface PieChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
    borderColor?: string[];
    borderWidth?: number;
  }[];
}

/**
 * 图表配置选项接口
 */
export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    title?: {
      display: boolean;
      text: string;
    };
    legend?: {
      display: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    tooltip?: {
      enabled: boolean;
      callbacks?: any;
    };
  };
  scales?: {
    x?: {
      display: boolean;
      title?: {
        display: boolean;
        text: string;
      };
    };
    y?: {
      display: boolean;
      title?: {
        display: boolean;
        text: string;
      };
      beginAtZero?: boolean;
    };
  };
}

/**
 * 债权统计图表数据接口
 */
export interface DebtStatisticsChartData {
  // 按公司分布
  companyDistribution: PieChartData;

  // 月度趋势
  monthlyTrend: LineChartData;

  // 债权性质分析
  natureAnalysis: BarChartData;

  // 状态分布
  statusDistribution: PieChartData;
}

/**
 * 图表样式主题接口
 */
export interface ChartTheme {
  colors: {
    primary: string[];
    success: string[];
    warning: string[];
    error: string[];
    info: string[];
  };
  fonts: {
    family: string;
    size: number;
    weight: number;
  };
  borders: {
    width: number;
    radius: number;
  };
}

/**
 * 图表导出选项接口
 */
export interface ChartExportOptions {
  format: 'png' | 'jpg' | 'pdf' | 'svg';
  width?: number;
  height?: number;
  backgroundColor?: string;
  quality?: number;
  filename?: string;
}
