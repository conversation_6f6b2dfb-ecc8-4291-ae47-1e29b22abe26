/**
 * TypeScript declarations for styled components with ownerState
 * This file extends the Material-UI styled component types to support ownerState
 */

import { BoxProps } from '@mui/material/Box';
import { TypographyProps } from '@mui/material/Typography';

declare module '@mui/material/Box' {
  interface BoxProps {
    ownerState?: any;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyProps {
    ownerState?: any;
  }
}

declare module '@mui/system' {
  interface BoxOwnProps {
    ownerState?: any;
  }
}

// Global styled component types
declare global {
  namespace React {
    interface HTMLAttributes<T> {
      ownerState?: any;
    }
  }
}

export {};
