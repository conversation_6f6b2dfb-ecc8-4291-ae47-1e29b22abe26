/**
 * 用户系统相关类型定义
 */

/**
 * 用户角色枚举
 */
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  USER = 'user',
  VIEWER = 'viewer'
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  LOCKED = 'locked',
  PENDING = 'pending'
}

/**
 * 权限类型枚举
 */
export enum PermissionType {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  EXPORT = 'export',
  ADMIN = 'admin'
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: number;
  username: string;
  realName: string;
  email?: string;
  phone?: string;
  status: UserStatus;
  roles: Role[];
  company: Company;
  department?: string;
  createDate: string;
  lastLoginDate?: string;
  permissions: Permission[];
}

/**
 * 角色信息接口
 */
export interface Role {
  id: number;
  roleName: string;
  roleCode: string;
  description?: string;
  permissions: Permission[];
  isDefault: boolean;
  createDate: string;
}

/**
 * 权限信息接口
 */
export interface Permission {
  id: number;
  permissionName: string;
  permissionCode: string;
  permissionType: PermissionType;
  resource: string;
  description?: string;
}

/**
 * 公司信息接口
 */
export interface Company {
  id: number;
  companyName: string;
  companyShortName?: string;
  companyCode: string;
  parentCompanyId?: number;
  level: number;
  isManagementCompany: boolean;
  status: CompanyStatus;
  address?: string;
  contactPerson?: string;
  contactPhone?: string;
  createDate: string;

  // 组织架构相关
  children?: Company[];
  parentCompany?: Company;
}

/**
 * 公司状态枚举
 */
export enum CompanyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

/**
 * 登录凭据接口
 */
export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
  captcha?: string;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  token: string;
  refreshToken?: string;
  userInfo: UserInfo;
  expiresIn: number;
}

/**
 * 用户注册数据接口
 */
export interface UserRegistration {
  username: string;
  password: string;
  confirmPassword: string;
  realName: string;
  email?: string;
  phone?: string;
  companyId: number;
  department?: string;
  roleIds: number[];
}

/**
 * 用户更新数据接口
 */
export interface UserUpdateData {
  realName?: string;
  email?: string;
  phone?: string;
  companyId?: number;
  department?: string;
  roleIds?: number[];
  status?: UserStatus;
}

/**
 * 密码修改数据接口
 */
export interface PasswordChangeData {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * 数据权限接口
 */
export interface DataPermission {
  id: number;
  userId: number;
  companyId: number;
  permissionType: PermissionType;
  grantedBy: number;
  grantDate: string;
  expiryDate?: string;
  isActive: boolean;
}
