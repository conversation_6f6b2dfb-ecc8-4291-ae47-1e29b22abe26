/**
 * 类型定义入口文件
 * 统一导出所有类型定义
 */

// API相关类型
export * from './api';

// 业务相关类型
export * from './business';

// 用户系统类型
export * from './user';

// 图表相关类型
export * from './chart';

// 组件相关类型
export * from './components';

/**
 * 通用工具类型
 */

/**
 * 可选属性
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 必需属性
 */
export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * 深度可选
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 键值对
 */
export interface KeyValuePair<T = any> {
  key: string;
  value: T;
}

/**
 * 下拉选项接口
 */
export interface SelectOption<T = any> {
  label: string;
  value: T;
  disabled?: boolean;
  group?: string;
}

/**
 * 表格列配置接口
 */
export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  dataIndex?: keyof T;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
}

/**
 * 分页配置接口
 */
export interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: boolean;
  pageSizeOptions?: string[];
}

/**
 * 排序配置接口
 */
export interface SortConfig {
  field: string;
  order: 'asc' | 'desc';
}

/**
 * 筛选配置接口
 */
export interface FilterConfig {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'between';
  value: any;
}

/**
 * 查询参数接口
 */
export interface QueryParams {
  page?: number;
  size?: number;
  sort?: SortConfig[];
  filters?: FilterConfig[];
  search?: string;
}

/**
 * 表单验证规则接口
 */
export interface ValidationRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  min?: number;
  max?: number;
  validator?: (value: any) => boolean | string;
}

/**
 * 表单字段配置接口
 */
export interface FormField {
  name: string;
  label: string;
  type:
    | 'text'
    | 'number'
    | 'email'
    | 'password'
    | 'select'
    | 'multiselect'
    | 'date'
    | 'datetime'
    | 'textarea'
    | 'checkbox'
    | 'radio';
  required?: boolean;
  placeholder?: string;
  disabled?: boolean;
  options?: SelectOption[];
  rules?: ValidationRule[];
  defaultValue?: any;
}

/**
 * 文件上传配置接口
 */
export interface UploadConfig {
  maxSize?: number; // bytes
  allowedTypes?: string[];
  multiple?: boolean;
  preview?: boolean;
}

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  fontFamily: string;
  borderRadius: number;
}

/**
 * 路由配置接口
 */
export interface RouteConfig {
  path: string;
  name: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  protected?: boolean;
  roles?: string[];
  redirect?: string;
  children?: RouteConfig[];
}
