/**
 * API响应通用接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
  timestamp?: string;
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    size: number;
    total: number;
    totalPages: number;
  };
}

/**
 * 错误响应接口
 */
export interface ErrorResponse {
  success: false;
  message: string;
  code: number;
  details?: any;
}

/**
 * 请求状态枚举
 */
export enum RequestStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * HTTP方法枚举
 */
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH'
}

/**
 * API端点常量
 */
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    USER_INFO: '/api/auth/user-info'
  },

  // 债权管理
  DEBT: {
    LIST: '/api/debt/list',
    ADD: '/api/debt/add',
    UPDATE: '/api/debt/update',
    DELETE: '/api/debt/delete',
    STATISTICS: '/api/debt/statistics',
    EXPORT: '/api/debt/export'
  },

  // 公司管理
  COMPANY: {
    LIST: '/api/company/list',
    ADD: '/api/company/add',
    UPDATE: '/api/company/update',
    DELETE: '/api/company/delete',
    TREE: '/api/company/tree'
  },

  // 用户管理
  USER: {
    LIST: '/api/user/list',
    ADD: '/api/user/add',
    UPDATE: '/api/user/update',
    DELETE: '/api/user/delete',
    PERMISSIONS: '/api/user/permissions'
  },

  // 数据导出
  EXPORT: {
    OVERDUE_DEBT: '/api/export/overdue-debt',
    STATISTICS: '/api/export/statistics',
    REPORTS: '/api/export/reports'
  },

  // 数据监控
  MONITOR: {
    HEALTH: '/api/monitor/health',
    METRICS: '/api/monitor/metrics',
    DATA_QUALITY: '/api/monitor/data-quality'
  }
} as const;
