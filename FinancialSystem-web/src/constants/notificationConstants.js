/**
 * 通知和弹窗配置常量
 * 基于行业最佳实践和用户体验标准
 */

// 自动隐藏时长配置（毫秒）
export const AUTO_HIDE_DURATIONS = {
  // 成功消息 - 用户需要确认操作成功，但不需要长时间停留
  success: 3000,

  // 信息消息 - 一般性信息，给用户足够时间阅读
  info: 4000,

  // 警告消息 - 需要用户注意，稍长时间显示
  warning: 5000,

  // 错误消息 - 重要信息，需要用户充分注意
  error: 6000,

  // 简单提示 - 快速反馈，短时间显示
  toast: 2000,

  // 加载状态 - 持续显示直到操作完成
  loading: 0,

  // 确认对话框 - 不自动消失，需要用户操作
  confirm: 0,

  // 表单验证错误 - 需要用户修正，较长时间显示
  validation: 7000,

  // 网络错误 - 重要错误信息，长时间显示
  network: 8000,
};

// 弹窗类型配置
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  LOADING: 'loading',
  CONFIRM: 'confirm',
  DELETE: 'delete',
  SAVE: 'save',
  TOAST: 'toast',
};

// 弹窗位置配置
export const NOTIFICATION_POSITIONS = {
  TOP_CENTER: { vertical: 'top', horizontal: 'center' },
  TOP_RIGHT: { vertical: 'top', horizontal: 'right' },
  TOP_LEFT: { vertical: 'top', horizontal: 'left' },
  BOTTOM_CENTER: { vertical: 'bottom', horizontal: 'center' },
  BOTTOM_RIGHT: { vertical: 'bottom', horizontal: 'right' },
  BOTTOM_LEFT: { vertical: 'bottom', horizontal: 'left' },
  CENTER: 'center', // 特殊标记，表示屏幕正中央
};

// 动画配置
export const ANIMATION_CONFIGS = {
  // 淡入淡出
  fade: {
    timeout: 300,
    easing: 'ease-in-out',
  },

  // 滑动进入
  slide: {
    timeout: 300,
    direction: 'down',
    easing: 'ease-out',
  },

  // 缩放进入
  zoom: {
    timeout: 250,
    easing: 'ease-out',
  },

  // 弹跳进入
  bounce: {
    timeout: 400,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
};

// 默认配置
export const DEFAULT_NOTIFICATION_CONFIG = {
  position: NOTIFICATION_POSITIONS.CENTER,
  animation: ANIMATION_CONFIGS.slide,
  showCloseButton: true,
  pauseOnHover: true,
  closeOnClick: false,
  preventDuplicates: true,
  maxStack: 5,
};

// 特定类型的默认配置
export const TYPE_SPECIFIC_CONFIGS = {
  [NOTIFICATION_TYPES.SUCCESS]: {
    duration: AUTO_HIDE_DURATIONS.success,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.slide,
    showIcon: true,
    closeOnClick: true,
  },

  [NOTIFICATION_TYPES.ERROR]: {
    duration: AUTO_HIDE_DURATIONS.error,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.bounce,
    showIcon: true,
    closeOnClick: false,
    pauseOnHover: true,
  },

  [NOTIFICATION_TYPES.WARNING]: {
    duration: AUTO_HIDE_DURATIONS.warning,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.slide,
    showIcon: true,
    closeOnClick: false,
  },

  [NOTIFICATION_TYPES.INFO]: {
    duration: AUTO_HIDE_DURATIONS.info,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.fade,
    showIcon: true,
    closeOnClick: true,
  },

  [NOTIFICATION_TYPES.TOAST]: {
    duration: AUTO_HIDE_DURATIONS.toast,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.zoom,
    showIcon: false,
    showCloseButton: false,
    closeOnClick: true,
  },

  [NOTIFICATION_TYPES.CONFIRM]: {
    duration: AUTO_HIDE_DURATIONS.confirm,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.fade,
    showIcon: true,
    showCloseButton: true,
    closeOnClick: false,
    disableBackdropClick: false,
    disableEscapeKeyDown: false,
  },

  [NOTIFICATION_TYPES.DELETE]: {
    duration: AUTO_HIDE_DURATIONS.confirm,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.bounce,
    showIcon: true,
    showCloseButton: true,
    closeOnClick: false,
    disableBackdropClick: true,
  },

  [NOTIFICATION_TYPES.LOADING]: {
    duration: AUTO_HIDE_DURATIONS.loading,
    position: NOTIFICATION_POSITIONS.CENTER,
    animation: ANIMATION_CONFIGS.fade,
    showIcon: true,
    showCloseButton: false,
    closeOnClick: false,
    disableBackdropClick: true,
    disableEscapeKeyDown: true,
  },
};

// Z-Index 层级配置
export const Z_INDEX_LEVELS = {
  TOAST: 1400,
  SNACKBAR: 1500,
  DIALOG: 1600,
  LOADING: 1700,
  CRITICAL: 1800,
};

// 颜色主题配置
export const NOTIFICATION_COLORS = {
  success: {
    main: '#4caf50',
    light: '#81c784',
    dark: '#388e3c',
    contrastText: '#ffffff',
  },
  error: {
    main: '#f44336',
    light: '#e57373',
    dark: '#d32f2f',
    contrastText: '#ffffff',
  },
  warning: {
    main: '#ff9800',
    light: '#ffb74d',
    dark: '#f57c00',
    contrastText: '#ffffff',
  },
  info: {
    main: '#2196f3',
    light: '#64b5f6',
    dark: '#1976d2',
    contrastText: '#ffffff',
  },
};

// 尺寸配置
export const NOTIFICATION_SIZES = {
  small: {
    minWidth: '280px',
    maxWidth: '400px',
    padding: '12px 16px',
    fontSize: '14px',
  },
  medium: {
    minWidth: '320px',
    maxWidth: '500px',
    padding: '16px 20px',
    fontSize: '14px',
  },
  large: {
    minWidth: '400px',
    maxWidth: '600px',
    padding: '20px 24px',
    fontSize: '16px',
  },
};

// 响应式断点配置
export const RESPONSIVE_CONFIGS = {
  mobile: {
    maxWidth: '90vw',
    position: NOTIFICATION_POSITIONS.TOP_CENTER,
    margin: '16px',
  },
  tablet: {
    maxWidth: '500px',
    position: NOTIFICATION_POSITIONS.CENTER,
    margin: '24px',
  },
  desktop: {
    maxWidth: '600px',
    position: NOTIFICATION_POSITIONS.CENTER,
    margin: '32px',
  },
};

// 可访问性配置
export const ACCESSIBILITY_CONFIGS = {
  // ARIA 标签
  ariaLabels: {
    close: '关闭通知',
    confirm: '确认操作',
    cancel: '取消操作',
    success: '成功消息',
    error: '错误消息',
    warning: '警告消息',
    info: '信息消息',
  },

  // 键盘导航
  keyboardNavigation: {
    closeKey: 'Escape',
    confirmKey: 'Enter',
    cancelKey: 'Escape',
  },

  // 屏幕阅读器
  screenReader: {
    announceOnShow: true,
    politeness: 'polite', // 'polite' | 'assertive'
  },
};

export default {
  AUTO_HIDE_DURATIONS,
  NOTIFICATION_TYPES,
  NOTIFICATION_POSITIONS,
  ANIMATION_CONFIGS,
  DEFAULT_NOTIFICATION_CONFIG,
  TYPE_SPECIFIC_CONFIGS,
  Z_INDEX_LEVELS,
  NOTIFICATION_COLORS,
  NOTIFICATION_SIZES,
  RESPONSIVE_CONFIGS,
  ACCESSIBILITY_CONFIGS,
};
