/**
 * Layout Components Index
 *
 * 统一的页面布局组件库，为FinancialSystem提供一致的页面结构和样式
 *
 * 组件说明：
 * - StandardPageLayout: 通用页面布局，适用于大多数页面
 * - FormPageLayout: 表单页面布局，优化表单交互和验证反馈
 * - DashboardPageLayout: 仪表板布局，适用于数据可视化和统计页面
 */

// 导出所有布局组件
export { default as StandardPageLayout } from './StandardPageLayout';
export { default as FormPageLayout } from './FormPageLayout';
export { default as DashboardPageLayout } from './DashboardPageLayout';

// 导出默认的标准布局
export { default } from './StandardPageLayout';

/**
 * 使用示例：
 *
 * // 1. 标准页面布局
 * import { StandardPageLayout } from 'components/layouts';
 *
 * function MyPage() {
 *   return (
 *     <StandardPageLayout
 *       title="页面标题"
 *       subtitle="页面副标题"
 *       actions={<Button>操作按钮</Button>}
 *     >
 *       页面内容
 *     </StandardPageLayout>
 *   );
 * }
 *
 * // 2. 表单页面布局
 * import { FormPageLayout } from 'components/layouts';
 *
 * function MyFormPage() {
 *   return (
 *     <FormPageLayout
 *       title="表单页面"
 *       searchSection={<SearchFilters />}
 *       resultsSection={<ResultsTable />}
 *       formValidation={{
 *         hasErrors: false,
 *         errors: [],
 *         warnings: ['注意事项'],
 *         success: '操作成功'
 *       }}
 *       sections={[
 *         { title: '基本信息', content: <BasicInfoForm /> },
 *         { title: '详细信息', content: <DetailForm /> }
 *       ]}
 *     >
 *       <FormContent />
 *     </FormPageLayout>
 *   );
 * }
 *
 * // 3. 仪表板页面布局
 * import { DashboardPageLayout } from 'components/layouts';
 *
 * function MyDashboard() {
 *   return (
 *     <DashboardPageLayout
 *       title="数据看板"
 *       filterSection={<DateRangeFilter />}
 *       metrics={[
 *         { value: '1,234', label: '总债权', change: '+5.2%', changeType: 'positive' },
 *         { value: '567', label: '处置金额', change: '-2.1%', changeType: 'negative' }
 *       ]}
 *       charts={[
 *         { title: '趋势图', content: <TrendChart />, width: 8 },
 *         { title: '分布图', content: <PieChart />, width: 4 }
 *       ]}
 *       tables={[
 *         { title: '详细数据', content: <DataTable />, actions: <ExportButton /> }
 *       ]}
 *     />
 *   );
 * }
 */
