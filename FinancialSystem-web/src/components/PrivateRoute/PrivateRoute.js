// PrivateRoute.js
import React, { useEffect } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import MDBox from 'components/MDBox';
import CircularProgress from '@mui/material/CircularProgress';

const PrivateRoute = () => {
  const { isAuthenticated, loading, checkAuthStatus } = useAuth();
  const location = useLocation();

  useEffect(() => {
    // 每次路由变化时检查认证状态
    checkAuthStatus();
  }, [location.pathname]);

  if (loading) {
    return (
      <MDBox display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </MDBox>
    );
  }

  return isAuthenticated ? (
    <Outlet />
  ) : (
    <Navigate to="/authentication/sign-in" state={{ from: location.pathname }} replace />
  );
};

export default PrivateRoute;
