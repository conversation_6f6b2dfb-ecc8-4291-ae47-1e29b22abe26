<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>日期选择器浏览器兼容性测试</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
          sans-serif;
        padding: 20px;
        max-width: 800px;
        margin: 0 auto;
      }
      .test-section {
        margin: 30px 0;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
      }
      .test-section h3 {
        margin-top: 0;
      }
      input {
        width: 100%;
        padding: 10px;
        font-size: 16px;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin: 10px 0;
      }
      .result {
        margin-top: 10px;
        padding: 10px;
        background-color: #f5f5f5;
        border-radius: 4px;
      }
      .browser-info {
        background-color: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      .support-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      .support-table th,
      .support-table td {
        border: 1px solid #ddd;
        padding: 10px;
        text-align: left;
      }
      .support-table th {
        background-color: #f5f5f5;
      }
      .supported {
        color: green;
        font-weight: bold;
      }
      .not-supported {
        color: red;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <h1>日期选择器浏览器兼容性测试</h1>

    <div class="browser-info">
      <h3>当前浏览器信息</h3>
      <div id="browserInfo"></div>
    </div>

    <div class="test-section">
      <h3>1. HTML5 原生月份选择器 (type="month")</h3>
      <label>选择月份:</label>
      <input type="month" id="monthInput" onchange="updateResult('monthResult', this.value)" />
      <div class="result" id="monthResult">尚未选择</div>
    </div>

    <div class="test-section">
      <h3>2. HTML5 原生日期选择器 (type="date")</h3>
      <label>选择日期:</label>
      <input type="date" id="dateInput" onchange="updateResult('dateResult', this.value)" />
      <div class="result" id="dateResult">尚未选择</div>
    </div>

    <div class="test-section">
      <h3>3. HTML5 原生日期时间选择器 (type="datetime-local")</h3>
      <label>选择日期时间:</label>
      <input
        type="datetime-local"
        id="datetimeInput"
        onchange="updateResult('datetimeResult', this.value)"
      />
      <div class="result" id="datetimeResult">尚未选择</div>
    </div>

    <div class="test-section">
      <h3>浏览器支持情况</h3>
      <table class="support-table">
        <thead>
          <tr>
            <th>浏览器</th>
            <th>type="date"</th>
            <th>type="month"</th>
            <th>type="datetime-local"</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Chrome / Edge</td>
            <td class="supported">✓ 支持</td>
            <td class="supported">✓ 支持</td>
            <td class="supported">✓ 支持</td>
          </tr>
          <tr>
            <td>Firefox</td>
            <td class="supported">✓ 支持</td>
            <td class="supported">✓ 支持</td>
            <td class="supported">✓ 支持</td>
          </tr>
          <tr>
            <td>Safari (macOS)</td>
            <td class="supported">✓ 支持</td>
            <td class="not-supported">✗ 不支持</td>
            <td class="not-supported">✗ 不支持</td>
          </tr>
          <tr>
            <td>Safari (iOS)</td>
            <td class="supported">✓ 支持</td>
            <td class="not-supported">✗ 不支持</td>
            <td class="supported">✓ 支持</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="test-section">
      <h3>功能检测结果</h3>
      <div id="featureDetection"></div>
    </div>

    <script>
      // 获取浏览器信息
      function getBrowserInfo() {
        const userAgent = navigator.userAgent;
        let browserName = 'Unknown';
        let browserVersion = 'Unknown';

        if (
          userAgent.indexOf('Chrome') > -1 &&
          userAgent.indexOf('Safari') > -1 &&
          userAgent.indexOf('Edge') === -1
        ) {
          browserName = 'Chrome';
          browserVersion = userAgent.match(/Chrome\/(\d+)/)[1];
        } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
          browserName = 'Safari';
          browserVersion = userAgent.match(/Version\/(\d+)/)[1];
        } else if (userAgent.indexOf('Firefox') > -1) {
          browserName = 'Firefox';
          browserVersion = userAgent.match(/Firefox\/(\d+)/)[1];
        } else if (userAgent.indexOf('Edge') > -1) {
          browserName = 'Edge';
          browserVersion = userAgent.match(/Edge\/(\d+)/)[1];
        }

        return {
          name: browserName,
          version: browserVersion,
          userAgent: userAgent,
          platform: navigator.platform
        };
      }

      // 检测输入类型支持
      function checkInputTypeSupport(type) {
        const input = document.createElement('input');
        input.setAttribute('type', type);
        return input.type === type;
      }

      // 更新结果显示
      function updateResult(elementId, value) {
        document.getElementById(elementId).textContent = `选择的值: ${value}`;
      }

      // 页面加载时执行
      window.onload = function () {
        // 显示浏览器信息
        const browserInfo = getBrowserInfo();
        document.getElementById('browserInfo').innerHTML = `
                <p><strong>浏览器:</strong> ${browserInfo.name} ${browserInfo.version}</p>
                <p><strong>平台:</strong> ${browserInfo.platform}</p>
                <p><strong>User Agent:</strong> ${browserInfo.userAgent}</p>
            `;

        // 功能检测
        const features = [
          { type: 'date', name: '日期选择器' },
          { type: 'month', name: '月份选择器' },
          { type: 'datetime-local', name: '日期时间选择器' },
          { type: 'time', name: '时间选择器' },
          { type: 'week', name: '周选择器' }
        ];

        let detectionHTML = '<h4>当前浏览器支持情况:</h4><ul>';
        features.forEach(feature => {
          const isSupported = checkInputTypeSupport(feature.type);
          const supportText = isSupported
            ? '<span style="color: green;">✓ 支持</span>'
            : '<span style="color: red;">✗ 不支持（降级为文本输入）</span>';
          detectionHTML += `<li>${feature.name} (type="${feature.type}"): ${supportText}</li>`;
        });
        detectionHTML += '</ul>';

        document.getElementById('featureDetection').innerHTML = detectionHTML;

        // 如果是Safari且不支持month类型，显示警告
        if (browserInfo.name === 'Safari' && !checkInputTypeSupport('month')) {
          const warning = document.createElement('div');
          warning.style.cssText =
            'background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 8px; margin-top: 20px;';
          warning.innerHTML =
            '<strong>⚠️ Safari浏览器提示:</strong><br>您的Safari浏览器不支持原生的月份选择器。在实际项目中，我们已使用自定义的FormMonthPicker组件来解决此兼容性问题。';
          document.querySelector('.browser-info').appendChild(warning);
        }
      };
    </script>
  </body>
</html>
