import React, { useMemo } from 'react';

// react-chartjs-2 components
import { Line } from 'react-chartjs-2';

// Chart.js 统一配置
import '../../../../utils/chartConfig';

// @mui material components
import Card from '@mui/material/Card';
import Divider from '@mui/material/Divider';
import Icon from '@mui/material/Icon';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

// ReportsLineChart configurations
import configs from 'examples/Charts/LineCharts/ReportsLineChart/configs';

interface ReportsLineChartProps {
  color?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'error' | 'dark';
  title: string;
  description?: string | React.ReactNode;
  date: string;
  chart: {
    labels: string[];
    datasets: any;
  };
}

function ReportsLineChart({ color, title, description, date, chart }: ReportsLineChartProps) {
  const { data, options } = configs(chart.labels || [], chart.datasets || {});

  return (
    <Card sx={{ height: '100%' }}>
      <MDBox sx={{ p: 2 }}>
        {useMemo(
          () => (
            <MDBox
              variant="gradient"
              bgColor={color}
              borderRadius="lg"
              coloredShadow={color}
              sx={{ py: 2, pr: 0.5, mt: -5, height: '12.5rem' }}
            >
              <Line data={data} options={options} redraw />
            </MDBox>
          ),
          [chart, color]
        )}
        <MDBox sx={{ pt: 3, pb: 1, px: 1 }}>
          <MDTypography variant="h6" textTransform="capitalize">
            {title}
          </MDTypography>
          <MDTypography component="div" variant="button" color="text" fontWeight="light">
            {description}
          </MDTypography>
          <Divider />
          <MDBox sx={{ display: 'flex', alignItems: 'center' }}>
            <MDTypography variant="button" color="text" lineHeight={1} sx={{ mt: 0.15, mr: 0.5 }}>
              <Icon>schedule</Icon>
            </MDTypography>
            <MDTypography variant="button" color="text" fontWeight="light">
              {date}
            </MDTypography>
          </MDBox>
        </MDBox>
      </MDBox>
    </Card>
  );
}

// Setting default values for the props of ReportsLineChart
ReportsLineChart.defaultProps = {
  color: 'info',
  description: ''
};

export default ReportsLineChart;
