/**
=========================================================
* Material Dashboard 2 React - v2.1.0
=========================================================

* Product Page: https://www.creative-tim.com/product/nextjs-material-dashboard-pro
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

 =========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

import { ChartOptions, ChartData, InteractionMode } from 'chart.js';

interface DatasetConfig {
  label: string;
  data: number[];
}

interface ConfigsReturn {
  data: ChartData<'line'>;
  options: ChartOptions<'line'>;
}

function configs(labels: string[], datasets: DatasetConfig): ConfigsReturn {
  return {
    data: {
      labels,
      datasets: [
        {
          label: datasets.label,
          tension: 0,
          pointRadius: 5,
          pointBorderColor: 'transparent',
          pointBackgroundColor: 'rgba(255, 255, 255, .8)',
          borderColor: 'rgba(255, 255, 255, .8)',
          borderWidth: 4,
          backgroundColor: 'transparent',
          fill: true,
          data: datasets.data
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      interaction: {
        intersect: false,
        mode: 'index' as InteractionMode
      },
      scales: {
        y: {
          grid: {
            // drawBorder is not a valid option in Chart.js v3+
            display: true,
            drawOnChartArea: true,
            drawTicks: false,
            // borderDash is not a valid option in Chart.js v3+
            color: 'rgba(255, 255, 255, .2)'
          },
          ticks: {
            display: true,
            color: '#f8f9fa',
            padding: 10,
            font: {
              size: 14,
              weight: '300' as const,
              family: 'Roboto',
              style: 'normal' as const,
              lineHeight: 2
            }
          }
        },
        x: {
          grid: {
            // drawBorder is not a valid option in Chart.js v3+
            display: false,
            drawOnChartArea: false,
            drawTicks: false
            // borderDash is not a valid option in Chart.js v3+
          },
          ticks: {
            display: true,
            color: '#f8f9fa',
            padding: 10,
            font: {
              size: 14,
              weight: '300' as const,
              family: 'Roboto',
              style: 'normal' as const,
              lineHeight: 2
            }
          }
        }
      }
    }
  };
}

export default configs;
