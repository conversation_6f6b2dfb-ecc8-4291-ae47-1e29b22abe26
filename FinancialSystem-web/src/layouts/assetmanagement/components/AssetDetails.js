import React, { useState } from 'react';
import {
  <PERSON>,
  Tabs,
  Tab,
  Typography,
  CircularProgress,
  IconButton,
  Toolt<PERSON>,
  Button,
} from '@mui/material';
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import AssetForm from './AssetForm';
import NotificationSnackbar from './NotificationSnackbar';
import ConfirmDialog from './ConfirmDialog';
import GenericDataTable from '../../../components/tables/GenericDataTable';

const AssetDetails = ({
  selectedTab,
  handleTabChange,
  properties,
  lands,
  loading,
  apiConnected,
  onAssetCreate,
  onAssetUpdate,
  onAssetDelete,
}) => {
  const [formOpen, setFormOpen] = useState(false);
  const [editingAsset, setEditingAsset] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  // 通知状态
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    onConfirm: null,
    loading: false,
  });

  const handleAddAsset = () => {
    setEditingAsset(null);
    setFormOpen(true);
  };

  const handleEditAsset = asset => {
    setEditingAsset(asset);
    setFormOpen(true);
  };

  const handleDeleteAsset = asset => {
    setConfirmDialog({
      open: true,
      title: '删除资产',
      message: `确定要删除资产"${asset.name}"吗？此操作不可撤销。`,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, loading: true }));
        try {
          await onAssetDelete(asset.id);
          setNotification({
            open: true,
            message: `资产"${asset.name}"删除成功`,
            severity: 'success',
          });
          setConfirmDialog({
            open: false,
            title: '',
            message: '',
            onConfirm: null,
            loading: false,
          });
        } catch (error) {
          setNotification({
            open: true,
            message: '删除失败：' + error.message,
            severity: 'error',
          });
          setConfirmDialog(prev => ({ ...prev, loading: false }));
        }
      },
      loading: false,
    });
  };

  const handleFormSubmit = async formData => {
    setFormLoading(true);
    try {
      if (editingAsset) {
        await onAssetUpdate(editingAsset.id, formData);
        setNotification({
          open: true,
          message: '资产更新成功',
          severity: 'success',
        });
      } else {
        await onAssetCreate(formData);
        setNotification({
          open: true,
          message: '资产创建成功',
          severity: 'success',
        });
      }
      setFormOpen(false);
      setEditingAsset(null);
    } catch (error) {
      setNotification({
        open: true,
        message: '保存失败：' + error.message,
        severity: 'error',
      });
    } finally {
      setFormLoading(false);
    }
  };

  const handleFormClose = () => {
    setFormOpen(false);
    setEditingAsset(null);
  };

  const handleNotificationClose = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  const handleConfirmDialogClose = () => {
    setConfirmDialog({ open: false, title: '', message: '', onConfirm: null, loading: false });
  };
  const renderTable = data => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>正在加载数据...</Typography>
        </Box>
      );
    }

    if (!data || data.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography color="text.secondary">
            暂无{selectedTab === 0 ? '房产' : '土地'}数据
          </Typography>
        </Box>
      );
    }

    // 定义表格列配置
    const columns = [
      { field: 'name', headerName: '资产名称', width: '15%' },
      { field: 'type', headerName: '类型', width: '10%' },
      { field: 'location', headerName: '位置', width: '15%' },
      {
        field: 'area',
        headerName: '总面积(㎡)',
        width: '10%',
        type: 'number',
        renderCell: params => params.value.toLocaleString(),
      },
      {
        field: 'purchasePrice',
        headerName: '购买价格(万元)',
        width: '12%',
        type: 'number',
        renderCell: params => (params.value / 10000).toLocaleString(),
      },
      {
        field: 'selfUseArea',
        headerName: '自用面积(㎡)',
        width: '10%',
        type: 'number',
        renderCell: params => params.value.toLocaleString(),
      },
      {
        field: 'rentalArea',
        headerName: '租赁面积(㎡)',
        width: '10%',
        type: 'number',
        renderCell: params => params.value.toLocaleString(),
      },
      {
        field: 'hasPropertyCertificate',
        headerName: '产权证',
        width: '12%',
        renderCell: params =>
          params.value ? (
            <Typography variant="caption" color="success.main">
              有证 ({params.row.propertyCertificateNo || '未填写'})
            </Typography>
          ) : (
            <Typography variant="caption" color="error.main">
              无证
            </Typography>
          ),
      },
    ];

    // 如果API连接，添加数据来源列
    if (apiConnected) {
      columns.push({
        field: 'dataSource',
        headerName: '数据来源',
        width: '8%',
        renderCell: () => (
          <Typography variant="caption" color="success.main">
            API数据
          </Typography>
        ),
      });
    }

    return (
      <GenericDataTable
        data={data}
        columns={columns}
        pagination
        pageSize={10}
        compact={true}
        rowHeight={45}
        fontSize={13}
        renderActions={params => (
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <Tooltip title="编辑">
              <IconButton
                size="small"
                onClick={() => handleEditAsset(params)}
                disabled={!apiConnected}
                sx={{ color: '#1976d2' }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="删除">
              <IconButton
                size="small"
                onClick={() => handleDeleteAsset(params)}
                disabled={!apiConnected}
                sx={{ color: '#d32f2f' }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        )}
        actionColumnWidth="8%"
        actionColumnTitle="操作"
        emptyMessage={`暂无${selectedTab === 0 ? '房产' : '土地'}数据`}
      />
    );
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', flexGrow: 1 }}>
          <Tabs value={selectedTab} onChange={handleTabChange}>
            <Tab label="房产信息" />
            <Tab label="土地信息" />
          </Tabs>
        </Box>
        {apiConnected && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddAsset}
            sx={{ ml: 2 }}
          >
            新增{selectedTab === 0 ? '房产' : '土地'}
          </Button>
        )}
      </Box>

      {selectedTab === 0 && renderTable(properties)}
      {selectedTab === 1 && renderTable(lands)}

      <AssetForm
        open={formOpen}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        initialData={editingAsset}
        loading={formLoading}
      />

      <NotificationSnackbar
        open={notification.open}
        message={notification.message}
        severity={notification.severity}
        onClose={handleNotificationClose}
      />

      <ConfirmDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onClose={handleConfirmDialogClose}
        onConfirm={confirmDialog.onConfirm}
        loading={confirmDialog.loading}
        severity="error"
      />
    </>
  );
};

export default AssetDetails;
