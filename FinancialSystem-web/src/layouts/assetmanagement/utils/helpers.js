import { Chip } from '@mui/material';
import { CheckCircle as CheckCircleIcon, Pending as PendingIcon } from '@mui/icons-material';

export const renderStatusChip = status => {
  const color = status === '已完成' ? 'success' : status === '进行中' ? 'primary' : 'warning';
  return (
    <Chip
      size="small"
      label={status}
      color={color}
      icon={status === '已完成' ? <CheckCircleIcon /> : <PendingIcon />}
      sx={{ marginLeft: 1 }}
    />
  );
};
