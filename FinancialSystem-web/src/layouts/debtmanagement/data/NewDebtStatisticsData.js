import api from '../../../utils/api';

/**
 * 获取新增债权情况统计（月度、年度）
 * @param {string} year 年份
 * @param {string} month 月份（可选，为空表示年度统计）
 * @param {string} company 公司
 * @returns {Promise<Object>} 新增债权趋势统计数据
 */
export const fetchNewDebtTrendStatistics = async (year, month, company) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/new-debt-trend', {
      params: {
        year,
        month,
        company,
      },
    });

    console.log('新增债权情况统计获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取新增债权情况统计失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

/**
 * 获取各单位新增债权余额统计
 * @param {string} year 年份
 * @param {string} month 月份
 * @param {string} company 公司（可选）
 * @returns {Promise<Array>} 各单位新增债权余额列表
 */
export const fetchNewDebtBalanceByCompany = async (year, month, company) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/new-debt-balance-by-company', {
      params: {
        year,
        month,
        company,
      },
    });

    console.log('各单位新增债权余额统计获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取各单位新增债权余额统计失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};

/**
 * 获取各子公司新增债权回收完成情况对比
 * @param {string} year 年份
 * @param {string} month 月份
 * @returns {Promise<Array>} 各子公司新增债权回收完成情况
 */
export const fetchNewDebtRecoveryComparison = async (year, month) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('未找到认证令牌');
    }

    const response = await api.get('/debts/statistics/new-debt-recovery-comparison', {
      params: {
        year,
        month,
      },
    });

    console.log('各子公司新增债权回收完成情况获取成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取各子公司新增债权回收完成情况失败:', error.message);
    if (error.response) {
      console.error('错误详情:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    } else if (error.request) {
      console.error('未收到响应', error.request);
    }
    throw error;
  }
};
