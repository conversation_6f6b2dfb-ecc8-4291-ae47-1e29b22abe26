import React, { useEffect, useState } from 'react';
import { Box, Grid, Typography } from '@mui/material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import OverdueStatisticsFilter from '../components/OverdueStatisticsFilter';
import {
  fetchNewDebtTrendStatistics,
  fetchNewDebtBalanceByCompany,
  fetchNewDebtRecoveryComparison,
} from '../data/NewDebtStatisticsData';
import DebtStatisticsChart from '../components/DebtStatisticsChart';
import BarStatisticsChart from '../components/BarStatisticsChart';
import CompanyProgressChart from '../components/CompanyProgressChart';

// 新增债权统计看板页面
const NewDebtStatistics = () => {
  // 统计数据状态
  const [trendData, setTrendData] = useState(null);
  const [balanceData, setBalanceData] = useState([]);
  const [recoveryData, setRecoveryData] = useState([]);

  // 当前筛选条件状态
  const [currentFilters, setCurrentFilters] = useState({
    year: new Date().getFullYear().toString(),
    month: (new Date().getMonth() + 1).toString().padStart(2, '0') + '月',
    company: '全部',
  });

  // 初始加载数据
  useEffect(() => {
    let isMounted = true;

    const fetchInitialData = async () => {
      try {
        const currentDate = new Date();
        const defaultYear = currentDate.getFullYear().toString();
        const defaultMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0') + '月';
        const defaultCompany = '全部';

        setCurrentFilters({
          year: defaultYear,
          month: defaultMonth,
          company: defaultCompany,
        });

        // 获取新增债权趋势数据
        const trend = await fetchNewDebtTrendStatistics(defaultYear, defaultMonth, defaultCompany);
        if (isMounted) {
          setTrendData(trend);
        }

        // 获取各单位新增债权余额
        const balance = await fetchNewDebtBalanceByCompany(
          defaultYear,
          defaultMonth,
          defaultCompany,
        );
        if (isMounted) {
          setBalanceData(balance || []);
        }

        // 获取各子公司回收完成情况
        const recovery = await fetchNewDebtRecoveryComparison(defaultYear, defaultMonth);
        if (isMounted) {
          setRecoveryData(recovery || []);
        }
      } catch (error) {
        if (isMounted) {
          console.error('Error fetching new debt statistics:', error);
        }
      }
    };

    fetchInitialData();

    return () => {
      isMounted = false;
    };
  }, []);

  // 处理筛选条件变化
  const handleFilterChange = async (year, month, company) => {
    try {
      setCurrentFilters({
        year,
        month,
        company,
      });

      // 获取新增债权趋势数据
      const trend = await fetchNewDebtTrendStatistics(year, month, company);
      setTrendData(trend);

      // 获取各单位新增债权余额
      const balance = await fetchNewDebtBalanceByCompany(year, month, company);
      setBalanceData(balance || []);

      // 获取各子公司回收完成情况
      const recovery = await fetchNewDebtRecoveryComparison(year, month);
      setRecoveryData(recovery || []);
    } catch (error) {
      console.error('Error fetching new debt statistics:', error);
    }
  };

  // 准备图表数据
  const prepareChartData = () => {
    if (!trendData || !trendData.data) {
      return [];
    }

    return trendData.data.map(item => ({
      name: item.period,
      新增金额: item.newAmount / 10000, // 转换为万元
      处置金额: item.reductionAmount / 10000,
      债权余额: item.balance / 10000,
    }));
  };

  const prepareBalanceChartData = () => {
    return balanceData.map(item => ({
      company: item.company,
      newDebtAmount: item.newDebtAmount / 10000, // 转换为万元
      reductionAmount: item.reductionAmount / 10000,
      balance: item.balance / 10000,
      reductionRate: item.reductionRate,
    }));
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Box
        sx={{
          maxWidth: '1600px',
          margin: '0 auto',
          padding: '24px',
          width: '100%',
        }}
      >
        {/* 筛选条件 */}
        <Box sx={{ width: '100%' }}>
          <OverdueStatisticsFilter onSearch={handleFilterChange} />
        </Box>

        <Box sx={{ marginBottom: '20px' }} />

        {/* 标题 */}
        <Typography
          variant="h4"
          gutterBottom
          align="center"
          sx={{
            fontWeight: 'bold',
            fontSize: '24px',
            color: 'black',
            marginBottom: '30px',
          }}
        >
          新增债权统计看板
        </Typography>

        {/* 新增债权情况统计图表 */}
        <Grid container spacing={3} sx={{ width: '100%', marginBottom: '20px' }}>
          <Grid item xs={12}>
            <Box
              sx={{
                width: '100%',
                border: '1px solid #d3d3d3',
                backgroundColor: '#FFFFFF',
                borderRadius: '12px',
                padding: '20px',
                boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 'bold',
                  marginBottom: '20px',
                  color: '#333',
                }}
              >
                新增债权情况统计（{trendData?.type === 'yearly' ? '年度' : '月度'}）
              </Typography>

              {trendData && (
                <Box sx={{ marginBottom: '20px' }}>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="textSecondary">
                          累计新增金额
                        </Typography>
                        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#2196F3' }}>
                          {(trendData.totalNewAmount / 10000).toFixed(2)} 万元
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="textSecondary">
                          累计处置金额
                        </Typography>
                        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4CAF50' }}>
                          {(trendData.totalReductionAmount / 10000).toFixed(2)} 万元
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={4}>
                      <Box sx={{ textAlign: 'center' }}>
                        <Typography variant="body2" color="textSecondary">
                          当前余额
                        </Typography>
                        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#FF9800' }}>
                          {(trendData.currentBalance / 10000).toFixed(2)} 万元
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              )}

              <div style={{ height: '400px' }}>
                <BarStatisticsChart
                  data={prepareChartData()}
                  chartTitle=""
                  chartDebtLabel="newAmount"
                />
              </div>
            </Box>
          </Grid>
        </Grid>

        {/* 各单位新增债权余额统计 */}
        <Grid container spacing={3} sx={{ width: '100%', marginBottom: '20px' }}>
          <Grid item xs={12}>
            <Box
              sx={{
                width: '100%',
                border: '1px solid #d3d3d3',
                backgroundColor: '#FFFFFF',
                borderRadius: '12px',
                padding: '20px',
                boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
              }}
            >
              <div style={{ height: '400px' }}>
                <DebtStatisticsChart
                  data={prepareBalanceChartData()}
                  chartTitle="各单位新增债权余额统计"
                  chartDebtLabel="新增债权"
                />
              </div>
            </Box>
          </Grid>
        </Grid>

        {/* 各子公司回收完成情况对比 */}
        <Grid container spacing={3} sx={{ width: '100%', marginBottom: '20px' }}>
          <Grid item xs={12}>
            <Box
              sx={{
                width: '100%',
                border: '1px solid #d3d3d3',
                backgroundColor: '#FFFFFF',
                borderRadius: '12px',
                padding: '20px',
                boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.1)',
              }}
            >
              <CompanyProgressChart data={recoveryData} title="各子公司新增债权回收完成情况" />
            </Box>
          </Grid>
        </Grid>
      </Box>
    </DashboardLayout>
  );
};

export default NewDebtStatistics;
