import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Tabs,
  Tab,
  IconButton,
  Chip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { DataGrid } from '@mui/x-data-grid';

/**
 * 存量债权清收情况明细弹窗组件
 * @param {Object} props
 * @param {Boolean} props.open - 是否打开弹窗
 * @param {Function} props.onClose - 关闭弹窗回调
 * @param {Object} props.data - 明细数据对象
 * @returns {JSX.Element}
 */
const CollectionStatusDetailModal = ({ open, onClose, data }) => {
  const [currentTab, setCurrentTab] = useState(0);

  // 处理标签页切换
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  // 定义表格列
  const columns = [
    { field: 'creditor', headerName: '债权人', width: 200, flex: 1 },
    { field: 'debtor', headerName: '债务人', width: 200, flex: 1 },
    { field: 'managementCompany', headerName: '管理公司', width: 150 },
    {
      field: 'isLitigation',
      headerName: '是否涉诉',
      width: 100,
      renderCell: params => (
        <Chip
          label={params.value}
          color={params.value === '是' ? 'error' : 'default'}
          size="small"
        />
      ),
    },
    { field: 'period', headerName: '期间', width: 150 },
    {
      field: 'amount',
      headerName: '金额（万元）',
      width: 120,
      type: 'number',
      valueFormatter: params => {
        if (params.value == null) {
          return '';
        }
        return `${Number(params.value).toFixed(2)}`;
      },
    },
  ];

  // 标签页数据配置
  const tabsData = [
    {
      label: '期初金额',
      data: data?.yearBeginDetails || [],
      color: 'rgba(46, 125, 50, 1)',
      totalAmount: data?.yearBeginAmount || 0,
    },
    {
      label: '本月清收处置',
      data: data?.monthCollectionDetails || [],
      color: 'rgba(76, 175, 80, 1)',
      totalAmount: data?.monthCollectionAmount || 0,
    },
    {
      label: '本年累计处置',
      data: data?.yearCumulativeDetails || [],
      color: 'rgba(129, 199, 132, 1)',
      totalAmount: data?.yearCumulativeCollectionAmount || 0,
    },
    {
      label: '期末余额',
      data: data?.periodEndDetails || [],
      color: 'rgba(165, 214, 167, 1)',
      totalAmount: data?.periodEndAmount || 0,
    },
  ];

  const currentTabData = tabsData[currentTab];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          width: '90vw',
          height: '80vh',
          maxWidth: '1200px',
        },
      }}
    >
      {/* 弹窗标题 */}
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 1,
        }}
      >
        <Typography variant="h6" component="div">
          存量债权清收情况明细
        </Typography>
        <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      {/* 弹窗内容 */}
      <DialogContent dividers sx={{ p: 0 }}>
        {/* 标签页导航 */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange} sx={{ px: 2 }}>
            {tabsData.map((tab, index) => (
              <Tab
                key={index}
                label={
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <Typography variant="body2">{tab.label}</Typography>
                    <Typography variant="caption" sx={{ color: tab.color, fontWeight: 'bold' }}>
                      {tab.totalAmount.toFixed(2)} 万元
                    </Typography>
                  </Box>
                }
              />
            ))}
          </Tabs>
        </Box>

        {/* 标签页内容 */}
        <Box sx={{ p: 2 }}>
          {/* 当前标签页统计信息 */}
          <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ color: currentTabData.color, mb: 1 }}>
              {currentTabData.label}汇总
            </Typography>
            <Box sx={{ display: 'flex', gap: 4 }}>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  记录数量
                </Typography>
                <Typography variant="h6" sx={{ color: currentTabData.color }}>
                  {currentTabData.data.length} 条
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  总金额
                </Typography>
                <Typography variant="h6" sx={{ color: currentTabData.color }}>
                  {currentTabData.totalAmount.toFixed(2)} 万元
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* 明细数据表格 */}
          <Box sx={{ height: 400, width: '100%' }}>
            {currentTabData.data.length > 0 ? (
              <DataGrid
                rows={currentTabData.data.map((row, index) => ({ id: index, ...row }))}
                columns={columns}
                pageSize={10}
                rowsPerPageOptions={[10, 25, 50]}
                disableSelectionOnClick
                sx={{
                  '& .MuiDataGrid-cell': {
                    fontSize: '0.875rem',
                  },
                  '& .MuiDataGrid-columnHeaderTitle': {
                    fontWeight: 'bold',
                  },
                }}
              />
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                  color: 'text.secondary',
                }}
              >
                <Typography>暂无{currentTabData.label}明细数据</Typography>
              </Box>
            )}
          </Box>
        </Box>
      </DialogContent>

      {/* 弹窗操作按钮 */}
      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};

CollectionStatusDetailModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  data: PropTypes.shape({
    yearBeginAmount: PropTypes.number,
    monthCollectionAmount: PropTypes.number,
    yearCumulativeCollectionAmount: PropTypes.number,
    periodEndAmount: PropTypes.number,
    yearBeginDetails: PropTypes.array,
    monthCollectionDetails: PropTypes.array,
    yearCumulativeDetails: PropTypes.array,
    periodEndDetails: PropTypes.array,
  }),
};

CollectionStatusDetailModal.defaultProps = {
  data: {
    yearBeginAmount: 0,
    monthCollectionAmount: 0,
    yearCumulativeCollectionAmount: 0,
    periodEndAmount: 0,
    yearBeginDetails: [],
    monthCollectionDetails: [],
    yearCumulativeDetails: [],
    periodEndDetails: [],
  },
};

export default CollectionStatusDetailModal;
