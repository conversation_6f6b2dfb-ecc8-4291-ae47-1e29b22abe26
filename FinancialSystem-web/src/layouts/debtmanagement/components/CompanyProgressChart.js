import React from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Chart } from 'react-chartjs-2';
import { Button, Box } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 各子公司存量债权回收完成情况图表组件
 * @param {Object} props
 * @param {Array} props.data - 公司进度数据数组
 * @param {String} props.title - 图表标题
 * @returns {JSX.Element}
 */
const CompanyProgressChart = ({ data, title = '各子公司存量债权回收完成情况' }) => {
  // 过滤掉无效数据
  const validData = (data || []).filter(item => item && item.companyName);

  // 如果没有数据，显示暂无数据提示
  if (!validData || validData.length === 0) {
    return (
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          minWidth: '650px',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '450px',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
          <Box sx={{ fontSize: '16px', mb: 1 }}>{title}</Box>
          <Box sx={{ fontSize: '14px' }}>暂无数据</Box>
        </Box>
      </Box>
    );
  }

  // 提取数据
  const labels = validData.map(item => item.companyName);
  const yearEndAmounts = validData.map(item => item.yearEndAmount || 0);
  const cumulativeRecoveries = validData.map(item => item.cumulativeRecovery || 0);
  const periodEndAmounts = validData.map(item => item.periodEndAmount || 0);
  const completionRates = validData.map(item => item.completionRate || 0);

  // 计算统计信息
  const totalCompanies = validData.length;
  const avgCompletionRate = completionRates.reduce((sum, rate) => sum + rate, 0) / totalCompanies;
  const maxCompletionItem = validData.reduce(
    (max, item) => (item.completionRate > max.completionRate ? item : max),
    validData[0],
  );
  const minCompletionItem = validData.reduce(
    (min, item) => (item.completionRate < min.completionRate ? item : min),
    validData[0],
  );

  // 图表数据配置
  const chartData = {
    labels,
    datasets: [
      {
        type: 'bar',
        label: '2024年末',
        data: yearEndAmounts,
        backgroundColor: 'rgba(54, 162, 235, 0.8)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
        yAxisID: 'y',
        order: 2,
      },
      {
        type: 'bar',
        label: '累计回收',
        data: cumulativeRecoveries,
        backgroundColor: 'rgba(76, 175, 80, 0.8)',
        borderColor: 'rgba(76, 175, 80, 1)',
        borderWidth: 1,
        yAxisID: 'y',
        order: 2,
      },
      {
        type: 'bar',
        label: '期末债权',
        data: periodEndAmounts,
        backgroundColor: 'rgba(255, 99, 132, 0.8)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
        yAxisID: 'y',
        order: 2,
      },
      {
        type: 'line',
        label: '完成进度',
        data: completionRates,
        borderColor: 'rgba(255, 159, 64, 1)',
        backgroundColor: 'transparent',
        borderWidth: 3,
        tension: 0.4,
        fill: false,
        yAxisID: 'y1',
        pointRadius: 6,
        pointHoverRadius: 8,
        pointBackgroundColor: 'rgba(255, 159, 64, 1)',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointHoverBackgroundColor: 'rgba(255, 159, 64, 1)',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 3,
        order: 1,
      },
    ],
  };

  // 图表选项配置
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index',
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
        labels: {
          boxWidth: 20,
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14,
          weight: 'medium',
        },
        bodyFont: {
          size: 12,
        },
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || '';
            if (context.datasetIndex === 3) {
              // 折线图 - 显示百分比
              return `${label}: ${context.parsed.y.toFixed(1)}%`;
            } else {
              // 柱状图 - 显示金额
              return `${label}: ${context.parsed.y.toFixed(2)} 万元`;
            }
          },
        },
      },
      datalabels: {
        display: function (context) {
          // 只在柱状图上显示数值，折线图不显示
          return context.dataset.type === 'bar';
        },
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: {
          size: 9,
        },
        formatter(value) {
          return Math.round(value).toLocaleString();
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 11,
          },
          maxRotation: labels.length > 8 ? 45 : 0,
          minRotation: labels.length > 8 ? 45 : 0,
          autoSkip: false,
          callback: function (value, index) {
            const label = labels[index];
            // 如果公司名称过长，截断显示
            return label.length > 8 ? label.substring(0, 8) + '...' : label;
          },
        },
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: '金额 (万元)',
          font: {
            size: 12,
          },
        },
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return value.toFixed(0);
          },
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: '完成进度 (%)',
          font: {
            size: 12,
            weight: 'bold',
          },
        },
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          callback: function (value) {
            return value.toFixed(0) + '%';
          },
          font: {
            weight: 'bold',
            size: 12,
          },
        },
      },
    },
    layout: {
      padding: {
        left: 20,
        right: 40,
        top: 20,
        bottom: 20,
      },
    },
  };

  // 处理更多信息按钮点击事件
  const handleMoreInfo = () => {
    // TODO: 实现显示更多信息的逻辑
    console.log('显示更多公司进度信息', validData);
  };

  return (
    <Box
      className="p-6 bg-white rounded-lg shadow-md"
      sx={{
        position: 'relative',
        marginBottom: '20px',
      }}
    >
      {/* 更多信息按钮 */}
      <Button
        variant="text"
        size="small"
        startIcon={<InfoOutlinedIcon />}
        onClick={handleMoreInfo}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 1,
          color: 'primary.main',
          '&:hover': {
            backgroundColor: 'rgba(76, 175, 80, 0.08)',
          },
        }}
      >
        更多信息
      </Button>

      {/* 图表容器 - 支持横向滚动 */}
      <Box
        sx={{
          overflowX: labels.length > 8 ? 'auto' : 'hidden',
          overflowY: 'hidden',
          width: '100%',
          pt: 2, // 为按钮留出空间
        }}
      >
        <Box
          sx={{
            minWidth: labels.length > 8 ? `${labels.length * 100}px` : '100%',
            width: '100%',
            height: '400px',
          }}
        >
          <Chart type="bar" data={chartData} options={options} />
        </Box>
      </Box>

      {/* 数据汇总信息 */}
      <Box
        sx={{
          mt: 3,
          pt: 2,
          borderTop: '1px solid #e0e0e0',
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        {/* 第一行：总体统计 */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>公司总数：</Box>
            <Box sx={{ fontSize: '16px', fontWeight: 'bold' }}>{totalCompanies} 家</Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>平均完成进度：</Box>
            <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(255, 159, 64, 1)' }}>
              {avgCompletionRate.toFixed(1)}%
            </Box>
          </Box>
        </Box>

        {/* 第二行：最高最低完成进度 */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-around',
            flexWrap: 'wrap',
            gap: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>完成进度最高：</Box>
            <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
              <Box sx={{ fontSize: '14px', fontWeight: 'medium' }}>
                {maxCompletionItem.companyName}
              </Box>
              <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(76, 175, 80, 1)' }}>
                {maxCompletionItem.completionRate.toFixed(1)}%
              </Box>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{ fontSize: '14px', color: 'text.secondary' }}>完成进度最低：</Box>
            <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
              <Box sx={{ fontSize: '14px', fontWeight: 'medium' }}>
                {minCompletionItem.companyName}
              </Box>
              <Box sx={{ fontSize: '16px', fontWeight: 'bold', color: 'rgba(255, 99, 132, 1)' }}>
                {minCompletionItem.completionRate.toFixed(1)}%
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

CompanyProgressChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      companyName: PropTypes.string.isRequired,
      yearEndAmount: PropTypes.number,
      cumulativeRecovery: PropTypes.number,
      periodEndAmount: PropTypes.number,
      completionRate: PropTypes.number,
      targetAmount: PropTypes.number,
    }),
  ),
  title: PropTypes.string,
};

CompanyProgressChart.defaultProps = {
  data: [],
  title: '各子公司存量债权回收完成情况',
};

export default CompanyProgressChart;
