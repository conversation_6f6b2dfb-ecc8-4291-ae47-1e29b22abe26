import React from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Chart } from 'react-chartjs-2';
import { Button, Box } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 存量债权清收处置方式统计图表组件
 * @param {Object} props
 * @param {Array} props.data - 处置方式数据数组，每项包含 method, amount, percentage
 * @param {String} props.title - 图表标题
 * @returns {JSX.Element}
 */
const DisposalMethodsChart = ({ data, title = '存量债权清收处置方式' }) => {
  // 过滤掉金额为0的数据项
  const filteredData = (data || []).filter(item => item.amount > 0);

  // 如果没有数据，显示暂无数据提示
  if (!filteredData || filteredData.length === 0) {
    return (
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
          <Box sx={{ fontSize: '16px', mb: 1 }}>{title}</Box>
          <Box sx={{ fontSize: '14px' }}>暂无数据</Box>
        </Box>
      </Box>
    );
  }

  // 提取标签和数据
  const labels = filteredData.map(item => item.method);
  const amounts = filteredData.map(item => item.amount);
  const percentages = filteredData.map(item => item.percentage);

  // 绿色系配色（4种不同深浅）
  const greenColors = [
    'rgba(46, 125, 50, 0.8)', // 深绿色
    'rgba(76, 175, 80, 0.8)', // 标准绿色
    'rgba(129, 199, 132, 0.8)', // 浅绿色
    'rgba(165, 214, 167, 0.8)', // 更浅绿色
  ];

  const greenBorderColors = [
    'rgba(46, 125, 50, 1)',
    'rgba(76, 175, 80, 1)',
    'rgba(129, 199, 132, 1)',
    'rgba(165, 214, 167, 1)',
  ];

  // 图表数据配置
  const chartData = {
    labels,
    datasets: [
      {
        type: 'bar',
        label: '清收处置金额',
        data: amounts,
        backgroundColor: filteredData.map((_, index) => greenColors[index % greenColors.length]),
        borderColor: filteredData.map(
          (_, index) => greenBorderColors[index % greenBorderColors.length],
        ),
        borderWidth: 1,
        yAxisID: 'y',
        borderRadius: 4,
        borderSkipped: false,
        maxBarThickness: 60,
      },
      {
        type: 'line',
        label: '占比',
        data: percentages,
        borderColor: '#FF6384',
        backgroundColor: 'transparent',
        borderWidth: 3,
        tension: 0.4,
        fill: false,
        yAxisID: 'y1',
        pointRadius: 6,
        pointHoverRadius: 8,
        pointBackgroundColor: '#FF6384',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointHoverBackgroundColor: '#FF6384',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 3,
      },
    ],
  };

  // 图表选项配置
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index',
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
        labels: {
          boxWidth: 20,
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14,
          weight: 'medium',
        },
        bodyFont: {
          size: 12,
        },
        cornerRadius: 8,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || '';
            if (context.datasetIndex === 0) {
              // 柱状图 - 显示金额
              return `${label}: ${context.parsed.y.toFixed(2)} 万元`;
            } else {
              // 折线图 - 显示百分比
              return `${label}: ${context.parsed.y.toFixed(2)}%`;
            }
          },
        },
      },
      datalabels: {
        display: function (context) {
          // 只在柱状图上显示数值
          return context.datasetIndex === 0;
        },
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: {
          size: 11,
          weight: 'bold',
        },
        formatter(value) {
          return value.toFixed(2);
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 12,
          },
          maxRotation: labels.length > 4 ? 45 : 0,
          minRotation: labels.length > 4 ? 45 : 0,
        },
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: '清收处置金额 (万元)',
          font: {
            size: 12,
          },
        },
        beginAtZero: true,
        ticks: {
          callback: function (value) {
            return value.toFixed(0);
          },
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: '占比 (%)',
          font: {
            size: 12,
          },
        },
        beginAtZero: true,
        max: 100,
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          callback: function (value) {
            return value.toFixed(0) + '%';
          },
        },
      },
    },
  };

  // 处理更多信息按钮点击事件
  const handleMoreInfo = () => {
    // TODO: 实现显示更多信息的逻辑
    console.log('显示更多处置方式统计信息', filteredData);
  };

  // 计算总金额
  const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0);

  return (
    <Box
      className="p-6 bg-white rounded-lg shadow-md"
      sx={{
        position: 'relative',
        width: '100%',
        maxWidth: '100%',
        marginBottom: '20px',
        overflow: 'hidden',
      }}
    >
      {/* 更多信息按钮 */}
      <Button
        variant="text"
        size="small"
        startIcon={<InfoOutlinedIcon />}
        onClick={handleMoreInfo}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          zIndex: 1,
          color: 'primary.main',
          '&:hover': {
            backgroundColor: 'rgba(76, 175, 80, 0.08)',
          },
        }}
      >
        详细信息
      </Button>

      {/* 图表容器 */}
      <Box
        sx={{
          height: '350px',
          width: '100%',
          pt: 2, // 为按钮留出空间
        }}
      >
        <Chart type="bar" data={chartData} options={options} />
      </Box>

      {/* 数据汇总信息 */}
      <Box
        sx={{
          mt: 2,
          pt: 2,
          borderTop: '1px solid #e0e0e0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          flexWrap: 'wrap',
          gap: 2,
        }}
      >
        {/* 总金额信息 */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <Box sx={{ fontSize: '12px', color: 'text.secondary', whiteSpace: 'nowrap' }}>
            清收处置总金额：
          </Box>
          <Box
            sx={{
              fontSize: '14px',
              fontWeight: 'bold',
              color: 'rgba(46, 125, 50, 1)',
              whiteSpace: 'nowrap',
            }}
          >
            {totalAmount.toFixed(2)} 万元
          </Box>
        </Box>

        {/* 各处置方式详情 */}
        <Box
          sx={{
            display: 'flex',
            gap: 1,
            flexWrap: 'wrap',
            justifyContent: 'center',
            maxWidth: '100%',
            overflow: 'hidden',
          }}
        >
          {filteredData.map((item, index) => (
            <Box
              key={item.method}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                flex: '1 1 auto',
                minWidth: '80px',
                maxWidth: '25%',
              }}
            >
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  backgroundColor: greenColors[index % greenColors.length],
                  borderRadius: '2px',
                  flexShrink: 0,
                }}
              />
              <Box
                sx={{
                  fontSize: '11px',
                  color: 'text.secondary',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {item.method}: {item.amount.toFixed(1)}万
              </Box>
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
};

DisposalMethodsChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      method: PropTypes.string.isRequired,
      amount: PropTypes.number.isRequired,
      percentage: PropTypes.number.isRequired,
    }),
  ),
  title: PropTypes.string,
};

DisposalMethodsChart.defaultProps = {
  data: [],
  title: '存量债权清收处置方式',
};

export default DisposalMethodsChart;
