import { Theme } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface Palette {
    white: {
      main: string;
      focus: string;
    };
    dark: {
      main: string;
      focus: string;
    };
    gradients: {
      primary: { main: string; state: string };
      secondary: { main: string; state: string };
      info: { main: string; state: string };
      success: { main: string; state: string };
      warning: { main: string; state: string };
      error: { main: string; state: string };
      light: { main: string; state: string };
      dark: { main: string; state: string };
    };
  }

  interface PaletteOptions {
    white?: {
      main: string;
      focus: string;
    };
    dark?: {
      main: string;
      focus: string;
    };
    gradients?: {
      primary: { main: string; state: string };
      secondary: { main: string; state: string };
      info: { main: string; state: string };
      success: { main: string; state: string };
      warning: { main: string; state: string };
      error: { main: string; state: string };
      light: { main: string; state: string };
      dark: { main: string; state: string };
    };
  }

  interface Typography {
    size: {
      xxs: number;
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
      '2xl': number;
      '3xl': number;
    };
  }

  interface TypographyOptions {
    size?: {
      xxs: number;
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
      '2xl': number;
      '3xl': number;
    };
  }

  interface Theme {
    functions: {
      pxToRem: (px: number) => string;
      linearGradient: (color1: string, color2: string) => string;
      rgba: (color: string, opacity: number) => string;
      hexToRgb: (hex: string) => string;
    };
    borders: {
      borderColor: string;
      borderWidth: {
        0: number;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
      };
      borderRadius: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        xxl: string;
        section: string;
      };
    };
    boxShadows: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      xxl: string;
      inset: string;
      colored: {
        primary: string;
        secondary: string;
        info: string;
        success: string;
        warning: string;
        error: string;
        light: string;
        dark: string;
        none: string;
      };
      navbarBoxShadow: string;
      sliderBoxShadow: {
        thumb: string;
      };
      tabsBoxShadow: {
        indicator: string;
      };
    };
  }

  interface ThemeOptions {
    functions?: {
      pxToRem?: (px: number) => string;
      linearGradient?: (color1: string, color2: string) => string;
      rgba?: (color: string, opacity: number) => string;
      hexToRgb?: (hex: string) => string;
    };
    borders?: {
      borderColor?: string;
      borderWidth?: {
        0: number;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
      };
      borderRadius?: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        xxl: string;
        section: string;
      };
    };
    boxShadows?: {
      xs?: string;
      sm?: string;
      md?: string;
      lg?: string;
      xl?: string;
      xxl?: string;
      inset?: string;
      colored?: {
        primary: string;
        secondary: string;
        info: string;
        success: string;
        warning: string;
        error: string;
        light: string;
        dark: string;
        none: string;
      };
      navbarBoxShadow?: string;
      sliderBoxShadow?: {
        thumb: string;
      };
      tabsBoxShadow?: {
        indicator: string;
      };
    };
  }

  interface TypeBackground {
    sidenav: string;
  }
}

export {};
