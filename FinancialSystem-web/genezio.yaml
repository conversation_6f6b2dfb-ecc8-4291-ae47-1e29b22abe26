# The name of the project.
name: material-dashboard-react
# The region where the project is deployed.
region: us-east-1
# The version of the Genezio YAML configuration to parse.
yamlVersion: 2
# Information about the frontend, including the path, language, and publish directory.
# It is optional. It can also be an array if there are multiple frontends you want to deploy.
frontend:
  # The folder where the frontend scripts will run.
  path: ./
  # The directory that will be published to the CDN. It is relative to the path.
  publish: ./build
  scripts:
    # List of scripts that build your frontend before deployment. It should populate the specified `publish` directory.
    build: npm run build
    # List of scripts to run before deploying the frontend.
    deploy: npm install
