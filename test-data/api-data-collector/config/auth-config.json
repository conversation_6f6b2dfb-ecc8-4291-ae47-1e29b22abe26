{"environment": {"development": {"baseUrl": "http://localhost:8080", "frontendUrl": "http://localhost:3000"}, "production": {"baseUrl": "http://**********:8080", "frontendUrl": "http://**********:3000"}}, "current": "development", "authentication": {"loginEndpoint": "/api/auth/login", "logoutEndpoint": "/api/auth/logout", "refreshEndpoint": "/api/auth/refresh", "userInfoEndpoint": "/api/user/info"}, "credentials": {"testUsers": [{"username": "admin", "password": "admin123", "role": "ADMIN", "description": "系统管理员"}, {"username": "manager", "password": "manager123", "role": "MANAGER", "description": "业务经理"}, {"username": "user", "password": "user123", "role": "USER", "description": "普通用户"}], "defaultUser": "admin"}, "token": {"headerName": "Authorization", "headerPrefix": "Bearer ", "storageKey": "jwt_token", "expirationBuffer": 300, "autoRefresh": true}, "request": {"timeout": 30, "retries": 3, "retryDelay": 1, "headers": {"Content-Type": "application/json", "Accept": "application/json", "User-Agent": "API-Data-Collector/1.0"}}, "cors": {"allowCredentials": true, "allowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:5173", "http://localhost:8080"]}}