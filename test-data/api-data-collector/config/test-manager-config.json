{"testManager": {"name": "FinancialSystem 统一测试管理器", "version": "1.0.0", "description": "统一管理和执行各种测试任务"}, "enabledTests": {"api_data_collection": true, "performance_test": true, "integration_test": true, "consistency_test": true, "security_test": false}, "testSequence": ["api_data_collection", "integration_test", "consistency_test", "performance_test"], "testConfiguration": {"api_data_collection": {"modules": ["auth", "debt", "export", "system", "monitoring", "user"], "timeout": 300, "retries": 2, "validateResponses": true}, "performance_test": {"endpoints": ["/api/debts/statistics", "/api/debts/statistics/detail", "/api/export/NewDebtDetails"], "loadLevels": [10, 50, 100], "duration": 60, "acceptableCriteria": {"maxResponseTime": 2000, "maxErrorRate": 0.05, "minThroughput": 100}}, "integration_test": {"testSuites": ["debt-management-integration", "auth-integration", "export-integration"], "parallelExecution": false}, "consistency_test": {"tables": ["新增表", "处置表", "减值准备表", "诉讼表", "非诉讼表"], "checkTypes": ["cross_table_consistency", "data_integrity", "referential_integrity", "business_rule_consistency"]}, "security_test": {"scanTypes": ["sql_injection", "xss_protection", "authentication_bypass", "authorization_check"], "skipEndpoints": ["/actuator/health"]}}, "execution": {"parallel": {"enabled": false, "maxWorkers": 3}, "continueOnError": true, "globalTimeout": 3600, "cleanup": {"enabled": true, "keepResults": true, "maxHistoryDays": 30}}, "reporting": {"generateReport": true, "includeDetails": true, "exportFormats": ["json", "html"], "autoOpen": false, "uploadResults": false}, "notifications": {"enabled": false, "onFailure": true, "onComplete": true, "channels": {"email": {"enabled": false, "recipients": []}, "webhook": {"enabled": false, "url": ""}}}, "integrations": {"jenkins": {"enabled": false, "webhookUrl": ""}, "jira": {"enabled": false, "projectKey": "", "issueType": "Bug"}, "sonarqube": {"enabled": false, "serverUrl": "", "projectKey": ""}}, "advanced": {"dataRetention": {"maxResultFiles": 50, "compressOldResults": true, "archiveAfterDays": 7}, "performance": {"enableProfiling": false, "memoryLimit": "2GB", "timeoutWarning": 0.8}, "debugging": {"verboseLogging": true, "saveDebugInfo": true, "includeStackTraces": true}}}