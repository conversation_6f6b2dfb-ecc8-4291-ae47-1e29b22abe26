{"collector": {"name": "FinancialSystem API Data Collector", "version": "1.0.0", "author": "<PERSON>", "description": "自动采集 FinancialSystem 所有 API 接口数据"}, "execution": {"maxRetries": 3, "requestTimeout": 30, "rateLimitDelay": 1.0, "batchSize": 5, "concurrency": 3, "enableCache": true, "validateResponses": true}, "output": {"formats": ["json", "excel", "csv"], "compression": true, "timestamped": true, "preserveHistory": true, "maxHistoryFiles": 10}, "modules": {"auth": {"enabled": true, "priority": 1, "description": "认证模块 - 必须首先执行"}, "user": {"enabled": true, "priority": 2, "description": "用户管理模块"}, "debt": {"enabled": true, "priority": 3, "description": "债权管理模块 - 核心业务"}, "export": {"enabled": true, "priority": 4, "description": "数据导出模块"}, "system": {"enabled": true, "priority": 5, "description": "系统管理模块"}, "monitoring": {"enabled": true, "priority": 6, "description": "监控模块"}}, "dataProcessing": {"sanitizeData": true, "removeNullFields": false, "formatDates": true, "roundNumbers": 2, "maxStringLength": 1000, "truncateLargeResponses": true, "maxResponseSize": "10MB"}, "validation": {"enableSchemaValidation": true, "checkDataTypes": true, "validateRequiredFields": true, "customValidators": {"amounts": "must_be_positive_number", "dates": "must_be_valid_date", "companies": "must_be_known_company"}}, "errorHandling": {"continueOnError": true, "logErrors": true, "savePartialResults": true, "retryFailedRequests": true, "skipKnownIssues": true}, "reporting": {"generateSummary": true, "includeMetrics": true, "createDashboard": false, "emailReport": false, "verboseLogging": true}, "testDataGeneration": {"createBoundaryData": true, "createErrorScenarios": true, "createPerformanceData": true, "includeEdgeCases": true, "randomSeed": 42}}