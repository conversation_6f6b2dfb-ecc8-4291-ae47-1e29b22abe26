{"endpoints": {"auth": {"module": "认证模块", "baseUrl": "/api", "endpoints": [{"id": "login", "path": "/auth/login", "method": "POST", "description": "用户登录", "requireAuth": false, "parameters": {"body": {"username": {"type": "string", "required": true}, "password": {"type": "string", "required": true}}}, "testCases": [{"username": "admin", "password": "admin123"}, {"username": "user", "password": "user123"}]}, {"id": "logout", "path": "/logout", "method": "POST", "description": "用户登出", "requireAuth": true, "parameters": {}, "testCases": [{}]}]}, "debt": {"module": "债权管理模块", "baseUrl": "/api/debts", "endpoints": [{"id": "getStatistics", "path": "/statistics", "method": "GET", "description": "获取债权统计数据", "requireAuth": true, "parameters": {"query": {"year": {"type": "string", "required": false}, "month": {"type": "string", "required": false}, "company": {"type": "string", "required": false}}}, "testCases": [{}, {"year": "2024"}, {"year": "2024", "month": "6"}, {"year": "2024", "month": "6", "company": "中筑天佑"}, {"year": "2024", "month": "6", "company": "万润智慧"}, {"year": "2025", "company": "所有公司"}, {"month": "全部月份", "company": "所有公司"}]}, {"id": "getStatisticsDetail", "path": "/statistics/detail", "method": "GET", "description": "获取债权统计详情", "requireAuth": true, "parameters": {"query": {"year": {"type": "string", "required": false}, "month": {"type": "string", "required": false}, "company": {"type": "string", "required": false}}}, "testCases": [{}, {"year": "2024"}, {"year": "2024", "month": "6"}, {"year": "2024", "month": "6", "company": "中筑天佑"}, {"year": "2024", "month": "6", "company": "万润智慧"}, {"year": "2025"}, {"month": "1", "company": "所有公司"}]}]}, "export": {"module": "数据导出模块", "baseUrl": "/api/export", "endpoints": [{"id": "exportNewDebtDetails", "path": "/NewDebtDetails", "method": "GET", "description": "导出新增债权明细", "requireAuth": true, "responseType": "binary", "parameters": {"query": {"year": {"type": "string", "required": false}, "month": {"type": "string", "required": false}, "company": {"type": "string", "required": false}}}, "testCases": [{"year": "2024", "month": "6"}, {"year": "2024", "company": "中筑天佑"}, {"year": "2025"}]}, {"id": "exportReductionDebtDetails", "path": "/ReductionDebtDetails", "method": "GET", "description": "导出减值债权明细", "requireAuth": true, "responseType": "binary", "parameters": {"query": {"year": {"type": "string", "required": false}, "month": {"type": "string", "required": false}, "company": {"type": "string", "required": false}}}, "testCases": [{"year": "2024", "month": "6"}, {"year": "2024", "company": "万润智慧"}, {"year": "2025"}]}, {"id": "exportCompleteOverdueReport", "path": "/completeOverdueReport", "method": "GET", "description": "导出完整逾期报告", "requireAuth": true, "responseType": "binary", "parameters": {"query": {"year": {"type": "string", "required": false}, "month": {"type": "string", "required": false}, "amount": {"type": "string", "required": false}}}, "testCases": [{"year": "2024", "month": "6", "amount": "10"}, {"year": "2025", "month": "2", "amount": "50"}, {"year": "2024"}]}]}, "system": {"module": "系统管理模块", "baseUrl": "/api/system", "endpoints": [{"id": "getDatabaseVersion", "path": "/database/version", "method": "GET", "description": "获取数据库版本信息", "requireAuth": true, "parameters": {}, "testCases": [{}]}, {"id": "getConfig", "path": "/config", "method": "GET", "description": "获取系统配置", "requireAuth": true, "parameters": {}, "testCases": [{}]}]}, "monitoring": {"module": "监控模块", "baseUrl": "/api/monitoring", "endpoints": [{"id": "getMetrics", "path": "/metrics", "method": "GET", "description": "获取系统指标", "requireAuth": true, "parameters": {}, "testCases": [{}]}, {"id": "getDataConsistency", "path": "/data/consistency", "method": "GET", "description": "数据一致性检查", "requireAuth": true, "parameters": {}, "testCases": [{}]}]}, "user": {"module": "用户管理模块", "baseUrl": "/api/user", "endpoints": [{"id": "getCompanies", "path": "/companies", "method": "GET", "description": "获取公司列表", "requireAuth": true, "parameters": {}, "testCases": [{}]}, {"id": "getUserInfo", "path": "/info", "method": "GET", "description": "获取用户信息", "requireAuth": true, "parameters": {}, "testCases": [{}]}]}}, "metadata": {"version": "1.0.0", "lastUpdated": "2025-07-24", "totalEndpoints": 15}}