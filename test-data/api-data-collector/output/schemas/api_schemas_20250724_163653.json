{"auth": {}, "user": {}, "debt": {"getStatisticsDetail": {"type": "object", "properties": {"reductionDebtDetailList": {"type": "array", "items": {"type": "unknown"}}, "newDebtDetailList": {"type": "array", "items": {"type": "mixed"}}}}, "getStatistics": {"type": "object", "properties": {"totalReductionAmount": {"type": "number", "example": 30699.37}, "totalDebtBalance": {"type": "number", "example": 41511.34}, "initialDebtBalance": {"type": "number", "example": 58442.42}, "initialDebtReductionAmount": {"type": "number", "example": 20549.11}, "newDebtAmount": {"type": "number", "example": 16198.14}, "newDebtReductionAmount": {"type": "number", "example": 10150.26}, "initialDebtEndingBalance": {"type": "number", "example": 37893.75}, "existingDebtSummaryByCompany": {"type": "array", "items": {"type": "mixed"}}, "monthNewReductionDebtByCompany": {"type": "array", "items": {"type": "unknown"}}, "newDebtSummaryByCompany": {"type": "array", "items": {"type": "mixed"}}, "newDebtBalance": {"type": "number", "example": 3617.59}}}}, "export": {}, "system": {}, "monitoring": {}}