
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告 - 20250724_164705</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; }
        .test-results { margin: 20px 0; }
        .test-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .status-completed { border-left: 5px solid #28a745; }
        .status-failed { border-left: 5px solid #dc3545; }
        .status-skipped { border-left: 5px solid #ffc107; }
    </style>
</head>
<body>
    <div class="header">
        <h1>FinancialSystem 测试报告</h1>
        <p>会话ID: 20250724_164705</p>
        <p>执行时间: 2025-07-24T16:47:05.152951</p>
        <p>状态: completed</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>总测试数</h3>
            <p style="font-size: 2em; margin: 0;">4</p>
        </div>
        <div class="metric">
            <h3>完成</h3>
            <p style="font-size: 2em; margin: 0; color: #28a745;">4</p>
        </div>
        <div class="metric">
            <h3>失败</h3>
            <p style="font-size: 2em; margin: 0; color: #dc3545;">0</p>
        </div>
        <div class="metric">
            <h3>跳过</h3>
            <p style="font-size: 2em; margin: 0; color: #ffc107;">0</p>
        </div>
    </div>
    
    <div class="test-results">
        <h2>测试详情</h2>

        <div class="test-item status-completed">
            <h3>api_data_collection</h3>
            <p><strong>状态:</strong> completed</p>
            <p><strong>开始时间:</strong> 2025-07-24T16:47:05.153204</p>
            <p><strong>持续时间:</strong> 13.34秒</p>
            
        </div>

        <div class="test-item status-completed">
            <h3>integration_test</h3>
            <p><strong>状态:</strong> completed</p>
            <p><strong>开始时间:</strong> 2025-07-24T16:47:18.490463</p>
            <p><strong>持续时间:</strong> 0.00秒</p>
            
        </div>

        <div class="test-item status-completed">
            <h3>consistency_test</h3>
            <p><strong>状态:</strong> completed</p>
            <p><strong>开始时间:</strong> 2025-07-24T16:47:18.490547</p>
            <p><strong>持续时间:</strong> 0.00秒</p>
            
        </div>

        <div class="test-item status-completed">
            <h3>performance_test</h3>
            <p><strong>状态:</strong> completed</p>
            <p><strong>开始时间:</strong> 2025-07-24T16:47:18.490616</p>
            <p><strong>持续时间:</strong> 0.00秒</p>
            
        </div>

    </div>
</body>
</html>
