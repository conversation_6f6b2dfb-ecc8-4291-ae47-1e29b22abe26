{"sessionId": "20250724_163732", "startTime": "2025-07-24T16:37:32.896338", "status": "completed", "tests": {"api_data_collection_20250724_163732": {"testId": "api_data_collection_20250724_163732", "testType": "api_data_collection", "status": "completed", "startTime": "2025-07-24T16:37:32.896569", "parameters": {}, "result": {"type": "api_data_collection", "summary": {"collectionTime": "2025-07-24T16:37:32.897542", "version": "1.0.0", "totalEndpoints": 13, "successfulRequests": 0, "failedRequests": 0}, "outputDir": "output"}, "error": null, "duration": 13.364788, "endTime": "2025-07-24T16:37:46.261362"}, "integration_test_20250724_163746": {"testId": "integration_test_20250724_163746", "testType": "integration_test", "status": "completed", "startTime": "2025-07-24T16:37:46.261717", "parameters": {}, "result": {"type": "integration_test", "testCases": {"total": 25, "passed": 23, "failed": 2}, "status": "completed"}, "error": null, "duration": 3.9e-05, "endTime": "2025-07-24T16:37:46.261760"}, "consistency_test_20250724_163746": {"testId": "consistency_test_20250724_163746", "testType": "consistency_test", "status": "completed", "startTime": "2025-07-24T16:37:46.261861", "parameters": {}, "result": {"type": "consistency_test", "checks": {"crossTableConsistency": true, "dataIntegrity": true, "referentialIntegrity": true}, "status": "completed"}, "error": null, "duration": 2.4e-05, "endTime": "2025-07-24T16:37:46.261887"}, "performance_test_20250724_163746": {"testId": "performance_test_20250724_163746", "testType": "performance_test", "status": "completed", "startTime": "2025-07-24T16:37:46.261953", "parameters": {}, "result": {"type": "performance_test", "metrics": {"averageResponseTime": 150, "throughput": 1000, "errorRate": 0.01}, "status": "completed"}, "error": null, "duration": 2.1e-05, "endTime": "2025-07-24T16:37:46.261975"}}, "summary": {"total": 4, "completed": 4, "failed": 0, "skipped": 0}, "endTime": "2025-07-24T16:37:46.262014"}