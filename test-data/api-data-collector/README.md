# API 数据采集器

## 概述

这个工具用于自动采集 FinancialSystem 所有 API 接口的真实数据，保存为测试数据集，方便后续的自动化测试。

## 功能特性

- 🚀 **自动发现接口**: 自动扫描所有 Controller 并提取接口信息
- 📊 **数据采集**: 批量调用接口并保存响应数据
- 🔐 **认证处理**: 自动处理 JWT 认证
- 📁 **数据组织**: 按模块和接口类型组织保存数据
- 🔄 **增量更新**: 支持增量更新和完整重新采集
- 📈 **进度监控**: 实时显示采集进度和状态

## 目录结构

```
api-data-collector/
├── README.md                  # 使用说明
├── config/
│   ├── api-endpoints.json     # 接口配置
│   ├── auth-config.json       # 认证配置
│   └── collector-config.json  # 采集器配置
├── scripts/
│   ├── collect-api-data.py    # 主采集脚本
│   ├── auth-handler.py        # 认证处理器
│   └── utils.py              # 工具函数
├── templates/
│   └── request-templates.json # 请求模板
└── output/
    ├── snapshots/            # 接口响应快照
    ├── schemas/              # 接口结构定义
    └── test-data/            # 生成的测试数据
```

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install requests pandas openpyxl python-dateutil

# 或使用 requirements.txt
pip install -r requirements.txt
```

### 2. 配置认证信息

编辑 `config/auth-config.json`:

```json
{
  "baseUrl": "http://localhost:8080",
  "loginEndpoint": "/api/login",
  "credentials": {
    "username": "your_username",
    "password": "your_password"
  },
  "tokenConfig": {
    "headerName": "Authorization",
    "headerPrefix": "Bearer ",
    "expiration": 3600
  }
}
```

### 3. 运行采集器

```bash
# 完整采集所有接口数据
python scripts/collect-api-data.py --mode=full

# 只采集指定模块
python scripts/collect-api-data.py --module=debt --mode=incremental

# 采集指定接口
python scripts/collect-api-data.py --endpoint="/api/debts/statistics"
```

## 接口分类

系统接口按以下模块分类：

### 1. 认证模块 (auth)
- POST `/api/login` - 用户登录
- POST `/api/logout` - 用户登出
- GET `/api/user/info` - 获取用户信息

### 2. 债权管理模块 (debt)
- GET `/api/debts/statistics` - 债权统计
- GET `/api/debts/statistics/detail` - 债权统计详情
- POST `/api/debts` - 创建债权记录
- PUT `/api/debts/{id}` - 更新债权记录
- DELETE `/api/debts/{id}` - 删除债权记录

### 3. 数据导出模块 (export)
- GET `/api/export/NewDebtDetails` - 导出新增债权明细
- GET `/api/export/ReductionDebtDetails` - 导出减值债权明细
- GET `/api/export/completeOverdueReport` - 导出完整逾期报告

### 4. 系统管理模块 (system)
- GET `/api/system/config` - 系统配置
- GET `/api/system/health` - 健康检查
- GET `/api/system/version` - 版本信息

### 5. 监控模块 (monitoring)
- GET `/api/monitoring/metrics` - 系统指标
- GET `/api/monitoring/logs` - 日志查询

## 数据保存格式

### 1. 接口响应快照
```json
{
  "endpoint": "/api/debts/statistics",
  "method": "GET",
  "timestamp": "2025-07-24T10:30:00Z",
  "parameters": {
    "year": "2024",
    "month": "6",
    "company": "中筑天佑"
  },
  "response": {
    "status": 200,
    "headers": {
      "Content-Type": "application/json"
    },
    "data": {
      "totalReductionAmount": 1500000.00,
      "totalDebtBalance": 5000000.00
    }
  },
  "metadata": {
    "responseTime": 150,
    "dataSize": 1024
  }
}
```

### 2. 接口结构定义
```json
{
  "endpoint": "/api/debts/statistics",
  "schema": {
    "parameters": {
      "year": {"type": "string", "required": false},
      "month": {"type": "string", "required": false},
      "company": {"type": "string", "required": false}
    },
    "response": {
      "totalReductionAmount": {"type": "number"},
      "totalDebtBalance": {"type": "number"},
      "initialDebtBalance": {"type": "number"}
    }
  }
}
```

## 使用方法

### 基本用法

```python
from scripts.collect_api_data import APIDataCollector

# 初始化采集器
collector = APIDataCollector(
    base_url="http://localhost:8080",
    auth_config="config/auth-config.json"
)

# 采集所有接口数据
collector.collect_all()

# 采集指定模块
collector.collect_module("debt")

# 采集指定接口
collector.collect_endpoint("/api/debts/statistics", 
                          params={"year": "2024", "month": "6"})
```

### 高级用法

```python
# 自定义参数组合
test_cases = [
    {"year": "2024", "month": "1", "company": "中筑天佑"},
    {"year": "2024", "month": "6", "company": "万润智慧"},
    {"year": "2025", "month": None, "company": None}
]

collector.collect_with_test_cases("/api/debts/statistics", test_cases)

# 性能测试数据采集
collector.collect_performance_data("/api/debts/statistics", 
                                  concurrency=10, 
                                  duration=60)
```

## 输出说明

采集完成后，会在 `output/` 目录生成以下文件：

1. **snapshots/**: 接口响应快照
   - `auth-snapshots.json` - 认证相关接口数据
   - `debt-snapshots.json` - 债权管理接口数据
   - `export-snapshots.json` - 导出接口数据

2. **schemas/**: 接口结构定义
   - `api-schemas.json` - 所有接口的结构定义

3. **test-data/**: 测试数据集
   - `boundary-test-data.json` - 边界值测试数据
   - `normal-test-data.json` - 正常场景测试数据
   - `error-test-data.json` - 错误场景测试数据

## 配置说明

### 采集器配置 (collector-config.json)
```json
{
  "maxRetries": 3,
  "requestTimeout": 30,
  "rateLimitDelay": 1,
  "batchSize": 10,
  "enableCache": true,
  "outputFormat": ["json", "excel"],
  "modules": {
    "auth": {"enabled": true, "priority": 1},
    "debt": {"enabled": true, "priority": 2},
    "export": {"enabled": true, "priority": 3}
  }
}
```

## 故障排除

### 常见问题

1. **认证失败**
   ```
   检查 auth-config.json 中的用户名和密码
   确保服务器正在运行
   ```

2. **接口无响应**
   ```
   检查网络连接
   验证接口 URL 是否正确
   查看服务器日志
   ```

3. **数据不完整**
   ```
   检查用户权限
   增加请求超时时间
   启用详细日志
   ```

## 扩展功能

### 自定义数据处理器
```python
def custom_processor(response_data):
    # 自定义数据处理逻辑
    processed_data = {
        "summary": calculate_summary(response_data),
        "validation": validate_data(response_data)
    }
    return processed_data

collector.add_processor("debt", custom_processor)
```

### 数据验证
```python
# 添加数据验证规则
validator = DataValidator()
validator.add_rule("amount", lambda x: x >= 0)
validator.add_rule("date", lambda x: is_valid_date(x))

collector.set_validator(validator)
```

---

*创建时间: 2025-07-24*
*作者: Claude*